'use client';

import { DD_RUM } from '@services/data-dog/data-dog-setup';
import NextError from 'next/error';
import { useEffect } from 'react';

export default function GlobalError({
  error
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    if (!DD_RUM) {
      return;
    }

    // Add additional context to the error
    const errorContext = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      digest: error.digest,
      timestamp: new Date().toISOString()
    };

    // Log to console for server-side visibility
    console.error('Client-side error captured:', errorContext);

    // Send to Datadog RUM
    DD_RUM.addError(error, {
      context: errorContext
    });
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
