'use client';

import useInitializeAnalytics from '@hooks/InitializeAnalytics';

import CatalogContext from '~/contexts/CatalogContext';
import { GuidedSearchProvider } from '~/contexts/GuidedSearchContext';
import { ProviderContextWrapper } from '~/contexts/Provider';
import { SessionContext } from '~/contexts/SessionContext';
import StoryContext from '~/contexts/StoryContext';
import { PageProps } from '~/types/Magnolia';

export function PageProviders({
  children,
  pageData
}: {
  children: React.ReactNode;
  pageData: PageProps;
}) {
  const { page, provider, catalog } = pageData;

  const sessionData = useInitializeAnalytics({
    page,
    provider: provider || undefined,
    catalog: catalog || undefined
  });

  return (
    <GuidedSearchProvider>
      <ProviderContextWrapper provider={pageData?.provider}>
        <CatalogContext.Provider value={pageData?.catalog}>
          <StoryContext.Provider value={pageData?.story}>
            <SessionContext.Provider value={sessionData}>
              {children}
            </SessionContext.Provider>
          </StoryContext.Provider>
        </CatalogContext.Provider>
      </ProviderContextWrapper>
    </GuidedSearchProvider>
  );
}
