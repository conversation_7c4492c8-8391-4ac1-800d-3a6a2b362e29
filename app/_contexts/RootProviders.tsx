'use client';

import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { useEffect, useState } from 'react';

import { ModalProvider } from '~/contexts/ModalContext';

export function RootProviders({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            cacheTime: 0,
            refetchOnWindowFocus: false
          }
        }
      })
  );

  useEffect(() => {
    try {
      const localStoragePersister = createSyncStoragePersister({
        storage: window.localStorage
      });

      persistQueryClient({
        queryClient,
        persister: localStoragePersister
      });
    } catch (err) {
      console.error("Can't create session in localStorage");
    }
  }, [queryClient]);

  return (
    <QueryClientProvider client={queryClient}>
      <ModalProvider>{children}</ModalProvider>

      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
