'use client';

import { ChakraProvider } from '@chakra-ui/react';
import { getSiteTheme } from '@services/magnolia/getTheme';
import { useMemo } from 'react';

import SiteContext from '~/contexts/SiteContext';
import { TenantFunctionsProvider } from '~/contexts/TenantFunctionsContext';
import { SiteDefinition } from '~/types/Domains';

export function DomainProviders({
  children,
  colors,
  fonts,
  site
}: {
  children: React.ReactNode;
  colors?: Record<string, Record<string, string>>;
  fonts?: Record<string, string>;
  site: SiteDefinition;
}) {
  const baseTheme = useMemo(() => getSiteTheme(site.path), [site.path]);
  const siteData = useMemo(() => ({ site }), [site]);
  const siteTheme = useMemo(
    () => ({
      ...baseTheme,
      colors: { ...baseTheme.colors, ...colors },
      fonts: { ...baseTheme.fonts, ...fonts }
    }),
    [baseTheme, colors, fonts]
  );

  return (
    <ChakraProvider theme={siteTheme}>
      <SiteContext.Provider value={siteData}>
        <TenantFunctionsProvider tenant={site.path}>
          {children}
        </TenantFunctionsProvider>
      </SiteContext.Provider>
    </ChakraProvider>
  );
}
