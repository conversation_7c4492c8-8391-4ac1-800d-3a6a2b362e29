'use client';

import config from '@components/config';
import { PageContext } from '@components/LayoutStructure/Contexts';
import { EditablePage } from '@components/LayoutStructure/EditablePage';
import { useMemo } from 'react';

import useCheckEditorBar from '~/app/_hooks/useCheckEditorBar';
import { PageProps } from '~/types/Magnolia';

type PageContainerProps = Omit<
  PageProps,
  'error' | 'pagePath' | 'redirect' | 'catalog' | 'provider' | 'story'
> & { isDraft: boolean };

const PageContainer: React.FC<PageContainerProps> = ({
  isDraft,
  page,
  templateAnnotations,
  context
}) => {
  const pageContext = useMemo(() => {
    return { page, templateAnnotations, context };
  }, [page, templateAnnotations, context]);

  // Ensure that the editor bar is visible when editing a page in Magnolia.
  useCheckEditorBar(isDraft);

  return (
    <>
      <PageContext.Provider value={pageContext}>
        <EditablePage
          content={page}
          config={config}
          templateAnnotations={templateAnnotations}
        />
      </PageContext.Provider>
    </>
  );
};

export default PageContainer;
