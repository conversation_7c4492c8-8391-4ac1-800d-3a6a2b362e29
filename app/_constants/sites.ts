import { CaringDomains, SiteDefinition } from '~/types/Domains';

export const CARING_SITE: SiteDefinition = {
  name: 'Caring.com',
  domains: [
    CaringDomains.LIVE,
    CaringDomains.DXP_LIVE,
    CaringDomains.LOCALHOST,
    CaringDomains.DEV_DXP,
    CaringDomains.DEV_AWS,
    CaringDomains.AUTHOR_DEV_AWS,
    CaringDomains.STG_DXP,
    CaringDomains.STG_AWS,
    CaringDomains.AUTHOR_STG_AWS,
    CaringDomains.PROD_DXP,
    CaringDomains.PROD_AWS,
    CaringDomains.AUTHOR_PROD_AWS,
    CaringDomains.AUTHOR_DEV_DXP,
    CaringDomains.AUTHOR_STG_DXP,
    CaringDomains.AUTHOR_PROD_DXP
  ],
  path: CaringDomains.LIVE,
  segmentWriteKey: process.env.NEXT_PUBLIC_SEGMENT_CARING_WRITE_KEY ?? '',
  segmentCdnURL: process.env.NEXT_PUBLIC_SEGMENT_CARING_CDN ?? '',
  partnerToken: process.env.NEXT_PUBLIC_CARING_PARTNER_TOKEN ?? '',
  publicFolder: 'caring_public',
  storyPaths: [
    'answers',
    'bestseniorliving',
    'caregivers',
    'medicare',
    'resources',
    'senior-products'
  ]
};
