describe('Health Page', () => {
  describe('generateMetadata', () => {
    test('should return metadata with correct static values', async () => {
      const { generateMetadata } = await import('./page');

      const metaData = await generateMetadata();

      expect(metaData).toEqual({
        title: 'Health Check',
        description: 'Application health status',
        robots: {
          follow: false,
          index: false
        }
      });
    });

    test('should have "index" set to false in robots metadata', async () => {
      const { generateMetadata } = await import('./page');
      const metaData = await generateMetadata();

      expect(metaData?.robots?.index).toBe(false);
    });

    test('should have "follow" set to false in robots metadata', async () => {
      const { generateMetadata } = await import('./page');
      const metaData = await generateMetadata();

      expect(metaData?.robots?.follow).toBe(false);
    });
  });

  describe('Page Component', () => {
    test('should render health status', async () => {
      const { default: HealthPage } = await import('./page');

      const result = HealthPage();

      expect(result).toEqual(<div>Status: ok</div>);
    });
  });
});
