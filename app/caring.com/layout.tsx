import Analytics from '@components/Analytics/Analytics';
import AnalyticsShim from '@components/Analytics/AnalyticsShim';
import { getThemeExtension } from '@services/magnolia/getTheme';
import { draftMode } from 'next/headers';

import { CARING_SITE } from '../_constants/sites';
import { DomainProviders } from '../_contexts/DomainProviders';

export const revalidate = 3600; // revalidate at most every hour

async function fetchLayoutData() {
  const site = CARING_SITE;
  const { isEnabled: isDraft } = draftMode();
  const themeExtension = await getThemeExtension(site);

  return { site, themeExtension, isDraft };
}

export default async function CaringLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { site, themeExtension, isDraft } = await fetchLayoutData();
  const { colors, fonts } = themeExtension;

  return (
    <DomainProviders site={site} colors={colors} fonts={fonts}>
      <Analytics
        segmentCdnURL={site.segmentCdnURL}
        segmentWriteKey={site.segmentWriteKey}
      />
      <AnalyticsShim preview={isDraft} />

      {children}
    </DomainProviders>
  );
}
