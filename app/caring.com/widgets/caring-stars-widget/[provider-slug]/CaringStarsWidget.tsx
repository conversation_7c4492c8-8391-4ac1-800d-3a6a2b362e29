'use client';

import Image from 'next/image';

import caringStarImage from '~/assets/badges/caring-star.png';
import { Provider } from '~/contexts/Provider';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { getCaringStarYears } from '~/utils/caringStars';

import styles from './CaringStarsWidget.module.css';

interface CaringStarsWidgetProps {
  provider: Provider | null;
}

const CaringStarsWidget: React.FC<CaringStarsWidgetProps> = ({ provider }) => {
  const { getProviderDetailsPath } = useTenantFunctions();

  if (!provider) {
    return (
      <div>
        <p className={styles.notFound}>Entity not found.</p>
      </div>
    );
  }

  const caringStars = provider?.awards?.filter(
    (award) => award.name?.toLowerCase() === 'caring stars' && award.year
  );

  const years = getCaringStarYears(caringStars || []);
  const yearsDisplay = years?.sort((a, b) => a - b).join(', ') || '';
  const providerPath = getProviderDetailsPath(provider);

  return (
    <div className={styles['caring-stars-widget']}>
      <p className={styles.header}>
        Best {provider.category?.name || ''} in {provider.address?.state || ''}
      </p>
      <Image
        src={caringStarImage}
        alt={`${provider.name} - Caring Stars Award Winner`}
        className={styles.badge}
      />
      <p className={styles.years}>{yearsDisplay}</p>
      <p className={styles.providerName}>{provider.name}</p>
      <a
        href={providerPath}
        target="_blank"
        className={styles.ctaButton}
        rel="noopener noreferrer"
      >
        Read Our Reviews
      </a>
    </div>
  );
};

export default CaringStarsWidget;
