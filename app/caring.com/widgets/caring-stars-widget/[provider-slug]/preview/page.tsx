import { ModularMonolithClient } from '@services/modular-monolith/client';
import { Metadata } from 'next';

import { CARING_SITE } from '~/app/_constants/sites';

import styles from './PreviewPage.module.css';

interface PreviewPageProps {
  params: {
    'provider-slug': string;
  };
}

export async function generateMetadata({
  params
}: PreviewPageProps): Promise<Metadata> {
  const providerSlug = params['provider-slug'];

  return {
    title: `Preview - Caring Stars Widget`,
    description: 'Preview page for Caring Stars widget',
    robots: {
      index: false,
      follow: false
    },
    viewport: {
      width: 'device-width',
      initialScale: 1
    }
  };
}

export default async function PreviewPage({ params }: PreviewPageProps) {
  const providerSlug = params['provider-slug'];

  // Fetch provider data for preview
  const monolithClient = new ModularMonolithClient(
    CARING_SITE.domains[0] as any
  );
  const provider = await monolithClient.getLocation(providerSlug);

  if (!provider) {
    return (
      <div>
        <p className={styles.notFound}>Entity not found.</p>
      </div>
    );
  }

  return (
    <div className={styles['caring-stars-widget-preview']}>
      <h1 className={styles.title}>Caring Stars Widget for {provider.name}</h1>

      <div className={styles.widgetSection}>
        <h4 className={styles.sectionTitle}>Widget:</h4>
        <div>
          <iframe
            src={`/widgets/caring-stars-widget/${providerSlug}`}
            style={{
              border: 0,
              width: 200,
              minHeight: 450
            }}
          />
        </div>
      </div>

      <div>
        <h4 className={styles.sectionTitle}>Code:</h4>
        <textarea
          rows={10}
          className={styles.codeTextarea}
          value={`<iframe src="https://www.caring.com/widgets/caring-stars-widget/${providerSlug}" style="border: 0; min-height: 450px; width: 200px;"></iframe>`}
          readOnly
        />
      </div>
    </div>
  );
}
