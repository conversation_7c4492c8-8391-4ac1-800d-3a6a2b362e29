import { render, screen } from '@testing-library/react';
import { mockProvider } from '@utils/test-utils/mocks/provider';
import React from 'react';

import axe from '~/axe-helper';

import CaringStarsWidget from './CaringStarsWidget';

jest.mock('~/contexts/TenantFunctionsContext', () => ({
  useTenantFunctions: () => ({
    getProviderDetailsPath: jest.fn(
      (provider) => `/senior-living/${provider.url}`
    )
  })
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text
    return <img {...props} />;
  }
}));

jest.mock('~/assets/badges/caring-star.png', () => ({
  src: '/mock-caring-star.png',
  default: '/mock-caring-star.png'
}));

describe('CaringStarsWidget', () => {
  const mockProviderWithAwards = {
    ...mockProvider,
    url: 'test-provider',
    address: {
      ...mockProvider.address,
      state: 'CA',
      city: 'Test City',
      formattedAddress: 'Test Address'
    },
    category: {
      name: 'Senior Living'
    },
    services: mockProvider.services
      ? [
          {
            ...mockProvider.services[0],
            category: {
              name: 'Senior Living',
              description: 'Senior Living services',
              imageURL: 'test.jpg'
            }
          }
        ]
      : [],
    awards: [
      {
        id: '1',
        name: 'Caring Stars',
        year: 2023,
        description: 'Excellence in care'
      },
      {
        id: '2',
        name: 'Caring Stars',
        year: 2022,
        description: 'Excellence in care'
      }
    ]
  } as any;

  const mockProviderWithoutAwards = {
    ...mockProviderWithAwards,
    awards: []
  } as any;

  const mockProviderWithNonCaringStarAwards = {
    ...mockProviderWithAwards,
    awards: [
      {
        id: '1',
        name: 'Other Award',
        year: 2023,
        description: 'Some other award'
      }
    ]
  } as any;

  describe('when provider is null', () => {
    it('renders entity not found message', () => {
      render(<CaringStarsWidget provider={null} />);

      expect(screen.getByText('Entity not found.')).toBeInTheDocument();
    });

    it('renders without accessibility violations', async () => {
      const { container } = render(<CaringStarsWidget provider={null} />);
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('when provider has no awards', () => {
    it('renders widget with empty years', () => {
      render(<CaringStarsWidget provider={mockProviderWithoutAwards} />);

      expect(
        screen.getByText(/Best.*Senior Living.*in.*CA/)
      ).toBeInTheDocument();
      expect(screen.getByText('Test Provider')).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: 'Read Our Reviews' })
      ).toBeInTheDocument();
      expect(
        screen.getByAltText('Test Provider - Caring Stars Award Winner')
      ).toBeInTheDocument();
    });

    it('renders without accessibility violations', async () => {
      const { container } = render(
        <CaringStarsWidget provider={mockProviderWithoutAwards} />
      );
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('when provider has non-Caring Stars awards', () => {
    it('renders widget with empty years (ignores non-Caring Stars awards)', () => {
      render(
        <CaringStarsWidget provider={mockProviderWithNonCaringStarAwards} />
      );

      expect(
        screen.getByText(/Best.*Senior Living.*in.*CA/)
      ).toBeInTheDocument();
      expect(screen.getByText('Test Provider')).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: 'Read Our Reviews' })
      ).toBeInTheDocument();
      expect(
        screen.getByAltText('Test Provider - Caring Stars Award Winner')
      ).toBeInTheDocument();
    });
  });

  describe('when provider has Caring Stars awards', () => {
    it('renders the widget with provider information', () => {
      render(<CaringStarsWidget provider={mockProviderWithAwards} />);

      expect(
        screen.getByText(/Best.*Senior Living.*in.*CA/)
      ).toBeInTheDocument();
      expect(screen.getByText('Test Provider')).toBeInTheDocument();
      expect(screen.getByText('2022, 2023')).toBeInTheDocument();
    });

    it('renders the caring star image with correct alt text', () => {
      render(<CaringStarsWidget provider={mockProviderWithAwards} />);

      const image = screen.getByAltText(
        'Test Provider - Caring Stars Award Winner'
      );
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute(
        'alt',
        'Test Provider - Caring Stars Award Winner'
      );
    });

    it('renders the read reviews link with correct href', () => {
      render(<CaringStarsWidget provider={mockProviderWithAwards} />);

      const link = screen.getByRole('link', { name: 'Read Our Reviews' });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/senior-living/test-provider');
      expect(link).toHaveAttribute('target', '_blank');
    });

    it('sorts years in ascending order', () => {
      const providerWithUnsortedYears = {
        ...mockProviderWithAwards,
        awards: [
          { id: '1', name: 'Caring Stars', year: 2023, description: 'Award' },
          { id: '2', name: 'Caring Stars', year: 2021, description: 'Award' },
          { id: '3', name: 'Caring Stars', year: 2022, description: 'Award' }
        ]
      };

      render(<CaringStarsWidget provider={providerWithUnsortedYears} />);

      expect(screen.getByText('2021, 2022, 2023')).toBeInTheDocument();
    });

    it('renders without accessibility violations', async () => {
      const { container } = render(
        <CaringStarsWidget provider={mockProviderWithAwards} />
      );
      expect(await axe(container)).toHaveNoViolations();
    });
  });
});
