import { ReactNode } from 'react';

interface LayoutProps {
  children: ReactNode;
}

export default function WidgetLayout({ children }: LayoutProps) {
  return (
    <div
      style={{
        margin: 0,
        padding: 0,
        fontFamily: 'Montser<PERSON>, sans-serif',
        background: 'transparent',
        boxSizing: 'border-box',
        height: 'auto',
        overflow: 'hidden'
      }}
    >
      {children}
    </div>
  );
}
