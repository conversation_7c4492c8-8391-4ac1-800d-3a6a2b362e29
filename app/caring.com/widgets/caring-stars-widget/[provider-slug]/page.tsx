import { ModularMonolithClient } from '@services/modular-monolith/client';
import { Metadata, Viewport } from 'next';

import { CARING_SITE } from '~/app/_constants/sites';

import CaringStarsWidget from './CaringStarsWidget';

export const revalidate = 0;
export const viewport: Viewport = {
  initialScale: 1,
  viewportFit: 'cover',
  width: 'device-width'
};

interface PageProps {
  params: {
    'provider-slug': string;
  };
}

export async function generateMetadata({
  params
}: PageProps): Promise<Metadata> {
  const providerSlug = params['provider-slug'];

  return {
    title: 'Caring Stars Widget',
    description: 'Caring Stars awards and recognition widget',
    keywords: 'caring stars, awards, recognition, senior care, senior living',
    openGraph: {},
    alternates: {
      canonical: `/widgets/caring-stars-widget/${providerSlug}`
    },
    icons: {
      shortcut: '/caring_public/favicon.ico'
    },
    robots: {
      follow: false,
      index: false
    },
    twitter: {
      card: 'summary_large_image'
    }
  };
}

export default async function Page({ params }: PageProps) {
  const providerSlug = params['provider-slug'];

  // Fetch provider data
  const monolithClient = new ModularMonolithClient(
    CARING_SITE.domains[0] as any
  );
  const provider = await monolithClient.getLocation(providerSlug);

  return <CaringStarsWidget provider={provider} />;
}
