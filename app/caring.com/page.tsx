import { PageTemplateKeys } from '@components/config';
import RootLayout from '@components/Layouts/RootLayout';
import FooterContainer from '@components/Navigation/Footer/FooterContainer';
import HeaderContainer from '@components/Navigation/Header/HeaderContainer';
import { getColor } from '@utils/getColor';
import { stringSanitizer } from '@utils/strings';
import { Metadata, Viewport } from 'next';

import PageContainer from '../_components/layout/PageContainer';
import { JsonLdNextSeo } from '../_components/seo/JsonLdNextSeo';
import { CARING_SITE } from '../_constants/sites';
import { PageProviders } from '../_contexts/PageProviders';
import { fetchPageData } from '../_helpers/fetchPageData';
export const revalidate = 3600; // Cache the page for 1 hour

export const viewport: Viewport = {
  initialScale: 1,
  viewportFit: 'cover',
  width: 'device-width'
};

export async function generateMetadata({
  params,
  searchParams
}): Promise<Metadata> {
  const { pageData } = await fetchPageData({ params, searchParams });

  const title = stringSanitizer(pageData.page.metaTitle ?? '');
  const description = stringSanitizer(pageData.page.metaDescription ?? '');
  const canonical = pageData.page.noindex ? '' : pageData.page.canonical;
  const extrasForRobots = pageData.page.noindex
    ? undefined
    : ({
        'max-image-preview': 'large',
        'max-snippet': -1,
        'max-video-preview': -1
      } as const);

  let openGraphData = {};

  try {
    const dataJsonPrepare = stringSanitizer(pageData.page.openGraph ?? '{}');
    openGraphData = !pageData.page.openGraph
      ? '{}'
      : JSON.parse(dataJsonPrepare);
  } catch {
    console.error(
      'Invalid openGraph: please check your data is a properly formatted JSON Array',
      pageData.page.openGraph
    );
  }

  return {
    title: title,
    description: description,
    keywords: pageData.page.metaKeywords,
    openGraph: openGraphData,
    alternates: { canonical: canonical },
    icons: {
      shortcut: '/caring_public/favicon.ico'
    },
    robots: {
      follow: !pageData.page.nofollow,
      index: !pageData.page.noindex,
      ...extrasForRobots
    },
    twitter: { card: 'summary_large_image' }
  };
}

export default async function Page({ params, searchParams }) {
  const { isAccountEnabled, isDraft, navigation, pageData } =
    await fetchPageData({ params, searchParams });
  const {
    header,
    headerGeoScrolledPage,
    headerProviderScrolledPage,
    headerSem,
    footer,
    footerSem
  } = navigation;
  const { page, context, templateAnnotations } = pageData;
  const { color, range } = page?.backgroundColor || {};
  const backgroundColor = getColor(color, range);

  // See CME-1383 - https://caring.atlassian.net/browse/CME-1383
  const schemas = page.jsonSchemas || [];
  if (schemas) {
    Object.keys(schemas).forEach((schema) => {
      schemas[schema] = JSON.parse(
        JSON.stringify(schemas[schema]).replace(
          '[search_term_string]',
          '{search_term_string}'
        )
      );
    });
  }

  return (
    <PageProviders pageData={pageData}>
      <JsonLdNextSeo json={schemas} />

      <RootLayout backgroundColor={backgroundColor}>
        <RootLayout.Header>
          <HeaderContainer
            headerGeoScrolledPage={headerGeoScrolledPage}
            headerProviderScrolledPage={headerProviderScrolledPage}
            headerSem={headerSem || null}
            isAccountEnabled={isAccountEnabled}
            mainHeader={header}
            page={page}
            story={pageData?.story}
            provider={pageData?.provider}
            site={CARING_SITE}
          />
        </RootLayout.Header>

        <RootLayout.Content>
          <PageContainer
            isDraft={isDraft}
            page={page}
            templateAnnotations={templateAnnotations}
            context={context}
          />
        </RootLayout.Content>

        {footer && footerSem && (
          <RootLayout.Footer>
            <FooterContainer
              mainFooter={footer}
              footerSem={footerSem}
              page={page}
              template={page?.['mgnl:template'] as PageTemplateKeys}
            />
          </RootLayout.Footer>
        )}
      </RootLayout>
    </PageProviders>
  );
}
