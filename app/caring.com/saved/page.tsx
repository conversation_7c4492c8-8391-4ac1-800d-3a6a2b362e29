import RootLayout from '@components/Layouts/RootLayout';
import FooterContainer from '@components/Navigation/Footer/FooterContainer';
import HeaderContainer from '@components/Navigation/Header/HeaderContainer';
import SavedProvidersContent from '@components/SavedProviders/SavedProvidersContent';
import { Metadata, Viewport } from 'next';

import { CARING_SITE } from '../../_constants/sites';
import { PageProviders } from '../../_contexts/PageProviders';
import { fetchPageData } from '../../_helpers/fetchPageData';

export const revalidate = 0;
export const viewport: Viewport = {
  initialScale: 1,
  viewportFit: 'cover',
  width: 'device-width'
};

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Saved',
    description: 'Your saved providers',
    keywords: '',
    openGraph: {},
    alternates: { canonical: '/saved' },
    icons: {
      shortcut: '/caring_public/favicon.ico'
    },
    robots: {
      follow: true,
      index: false
    },
    twitter: { card: 'summary_large_image' }
  };
}

export default async function Page({ params, searchParams }) {
  const { pageData, navigation } = await fetchPageData({
    params,
    searchParams,
    path: '/saved'
  });

  pageData.page = {
    ...pageData.page,
    topic: 'favorites',
    category: 'provider-lists',
    pageType: 'saved-page',
    theme: 'favorites'
  };

  const { header, footer } = navigation;

  return (
    <PageProviders pageData={pageData}>
      <RootLayout>
        <RootLayout.Header>
          <HeaderContainer mainHeader={header} site={CARING_SITE} />
        </RootLayout.Header>
        <RootLayout.Content marginX={{ base: 0, md: 8 }}>
          <SavedProvidersContent />
        </RootLayout.Content>
        {footer && (
          <RootLayout.Footer>
            <FooterContainer
              mainFooter={footer}
              footerSem={footer}
              template="spa-lm:pages/1column"
            />
          </RootLayout.Footer>
        )}
      </RootLayout>
    </PageProviders>
  );
}
