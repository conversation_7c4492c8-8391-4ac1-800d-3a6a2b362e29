import { act, render, screen, waitFor } from '@utils/test-utils';

jest.mock('next/headers', () => ({
  draftMode: jest.fn().mockResolvedValue('draftMode')
}));

jest.mock('@services/graphql/navigation', () => ({ getNavigation: jest.fn() }));

jest.mock('../../_helpers/fetchPageData', () => ({
  fetchPageData: jest.fn().mockResolvedValue({
    pageData: {
      page: {
        metaTitle: 'foo',
        metaDescription: 'bar',
        metaKeywords: 'quux',
        openGraph:
          '{"type":"website","url":"https://www.caring.com","title":"foo","description":"bar","images":[{"url":"OpenGraph-Image-1.png","width":2400,"height":1260,"alt":"Caring.com","type":"image/png"}]}',
        noindex: false,
        canonical: 'baz'
      }
    },
    navigation: {
      header: {
        name: 'header',
        intro: '',
        main: [],
        secondary: [],
        logo: {
          logo: { link: '/logo.png', caption: 'Logo Caption' },
          logoMaxWidth: '200',
          mobileLogo: {
            link: '/mobile-logo.png',
            caption: 'Mobile Logo Caption'
          },
          mobileLogoMaxWidth: '100',
          logoAlt: 'Test Logo',
          logoUrl: 'https://example.com'
        }
      },
      footer: {
        name: 'footer',
        intro: '',
        main: [],
        secondary: [],
        logo: {
          logo: { link: '/logo.png', caption: 'Logo Caption' },
          logoMaxWidth: '200',
          mobileLogo: {
            link: '/mobile-logo.png',
            caption: 'Mobile Logo Caption'
          },
          mobileLogoMaxWidth: '100',
          logoAlt: 'Test Logo',
          logoUrl: 'https://example.com'
        }
      }
    },
    isDraft: false,
    isAccountEnabled: false
  })
}));

const mockGetProviderIdsFromCookies = jest.fn();

jest.mock('@utils/getProviderIdsFromCookies', () => ({
  getProviderIdsFromCookies: (...args) => mockGetProviderIdsFromCookies(...args)
}));

jest.mock('@hooks/use-cookie-storage-value', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation((cookieName) => {
    // Return a mock value for the cookie
    if (cookieName === 'saved_providers') {
      return '["1"]'; // Mock the raw cookie value
    }
    return null;
  })
}));

jest.mock('@services/providers/getProvidersByIds', () => ({
  getProvidersByIds: jest.fn().mockImplementation((ids) => {
    return Promise.resolve(
      ids.map((id) => ({
        id,
        name: `Provider ${id}`,
        address: { formattedAddress: '123 Test St' },
        photos: [],
        reviewCount: 5,
        averageRating: 4.5,
        url: `/provider/${id}`
      }))
    );
  })
}));

describe('Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetProviderIdsFromCookies.mockImplementation((cookieName) => []);
  });

  test('should return metadata with correct static values', async () => {
    const { generateMetadata } = await import('./page');

    const metaData = await generateMetadata();

    expect(metaData).toEqual({
      title: 'Saved',
      description: 'Your saved providers',
      keywords: '',
      openGraph: {},
      alternates: { canonical: '/saved' },
      icons: {
        shortcut: '/caring_public/favicon.ico'
      },
      robots: {
        follow: true,
        index: false
      },
      twitter: { card: 'summary_large_image' }
    });
  });

  test('should have "index" set to false in robots metadata', async () => {
    const { generateMetadata } = await import('./page');
    const metaData = await generateMetadata();

    expect(metaData?.robots?.index).toBe(false);
  });

  test('should include a shortcut icon in metadata', async () => {
    const { generateMetadata } = await import('./page');
    const metaData = await generateMetadata();

    expect(metaData?.icons?.shortcut).toBe('/caring_public/favicon.ico');
  });

  test('should show no providers when no providers are saved', async () => {
    const { default: Page } = await import('./page');
    const page = await Page({ params: {}, searchParams: {} });

    await act(async () => {
      render(page);
    });

    await waitFor(() => {
      expect(screen.getByText(/Saved Providers \(0\)/)).toBeInTheDocument();
    });

    expect(
      screen.getByText('Add communities to your favorites')
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "You haven't added any communities to your favorites yet. Use the search to find communities to save below."
      )
    ).toBeInTheDocument();
  });

  test('should show saved providers when they are saved', async () => {
    mockGetProviderIdsFromCookies.mockImplementation((cookieName) => {
      if (cookieName === 'saved_providers') {
        return ['1'];
      }
      return [];
    });

    const { default: Page } = await import('./page');
    const page = await Page({ params: {}, searchParams: {} });

    await act(async () => {
      render(page);
    });

    // Use a more flexible text matcher with regex
    await waitFor(
      () => {
        expect(screen.getByText(/Saved Providers \(1\)/)).toBeInTheDocument();
      },
      { timeout: 2000 }
    );

    await waitFor(() => {
      expect(screen.getByTestId('provider-list')).toBeInTheDocument();
    });

    await waitFor(() => {
      const providerCards = screen.getAllByText(/Provider 1/);
      expect(providerCards.length).toBeGreaterThan(0);
    });
  });
  test('should show recently viewed providers when they are viewed', async () => {
    mockGetProviderIdsFromCookies.mockImplementation((cookieName) => {
      if (cookieName === 'recently_viewed_providers') {
        return ['1'];
      }
      return [];
    });

    const { default: Page } = await import('./page');
    const page = await Page({ params: {}, searchParams: {} });

    await act(async () => {
      render(page);
    });

    await waitFor(() => {
      expect(screen.getByText(/Saved Providers \(0\)/)).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Recently Viewed Providers')).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        'Be sure to save any of the providers you recently viewed that you want to view again later.'
      )
    ).toBeInTheDocument();

    await waitFor(() => {
      const providerList = screen.getByTestId('provider-list');
      expect(providerList).toBeInTheDocument();
    });

    await waitFor(() => {
      const providerCards = screen.getAllByText(/Provider 1/);
      expect(providerCards.length).toBeGreaterThan(0);
    });
  });
  test('should show both saved and recently viewed providers when they are both saved and viewed', async () => {
    mockGetProviderIdsFromCookies.mockImplementation((cookieName) => {
      if (cookieName === 'saved_providers') {
        return ['1'];
      }
      if (cookieName === 'recently_viewed_providers') {
        return ['2'];
      }
      return [];
    });

    const { default: Page } = await import('./page');
    const page = await Page({ params: {}, searchParams: {} });

    await act(async () => {
      render(page);
    });

    // Use a more flexible text matcher with regex
    await waitFor(
      () => {
        expect(screen.getByText(/Saved Providers \(1\)/)).toBeInTheDocument();
      },
      { timeout: 2000 }
    );

    await waitFor(() => {
      expect(screen.getByText('Recently Viewed Providers')).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        'Be sure to save any of the providers you recently viewed that you want to view again later.'
      )
    ).toBeInTheDocument();

    await waitFor(() => {
      const providerCards1 = screen.getAllByText(/Provider 1/);
      expect(providerCards1.length).toBeGreaterThan(0);
    });

    await waitFor(() => {
      const providerCards2 = screen.getAllByText(/Provider 2/);
      expect(providerCards2.length).toBeGreaterThan(0);
    });
  });
});
