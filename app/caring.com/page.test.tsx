// Make this file a module to satisfy --isolatedModules
export {};

jest.mock('@services/graphql/navigation', () => ({
  getNavigation: jest.fn().mockResolvedValue({
    header: {
      name: 'header',
      intro: '',
      main: [],
      secondary: [],
      logo: {
        logo: { link: '/logo.png', caption: 'Logo Caption' },
        logoMaxWidth: '200',
        mobileLogo: {
          link: '/mobile-logo.png',
          caption: 'Mobile Logo Caption'
        },
        mobileLogoMaxWidth: '100',
        logoAlt: 'Test Logo',
        logoUrl: 'https://example.com'
      }
    },
    footer: {
      name: 'footer',
      intro: '',
      main: [],
      secondary: [],
      logo: {
        logo: { link: '/logo.png', caption: 'Logo Caption' },
        logoMaxWidth: '200',
        mobileLogo: {
          link: '/mobile-logo.png',
          caption: 'Mobile Logo Caption'
        },
        mobileLogoMaxWidth: '100',
        logoAlt: 'Test Logo',
        logoUrl: 'https://example.com'
      }
    }
  })
}));

jest.mock('../_helpers/fetchPageData', () => ({
  fetchPageData: jest.fn().mockResolvedValue({
    pageData: {
      page: {
        metaTitle: 'foo',
        metaDescription: 'bar',
        metaKeywords: 'quux',
        openGraph:
          '{"type":"website","url":"https://www.caring.com","title":"foo","description":"bar","images":[{"url":"OpenGraph-Image-1.png","width":2400,"height":1260,"alt":"Caring.com","type":"image/png"}]}',
        noindex: false,
        canonical: 'baz'
      }
    },
    navigation: {
      header: {
        name: 'header',
        intro: '',
        main: [],
        secondary: [],
        logo: {
          logo: { link: '/logo.png', caption: 'Logo Caption' },
          logoMaxWidth: '200',
          mobileLogo: {
            link: '/mobile-logo.png',
            caption: 'Mobile Logo Caption'
          },
          mobileLogoMaxWidth: '100',
          logoAlt: 'Test Logo',
          logoUrl: 'https://example.com'
        }
      },
      footer: {
        name: 'footer',
        intro: '',
        main: [],
        secondary: [],
        logo: {
          logo: { link: '/logo.png', caption: 'Logo Caption' },
          logoMaxWidth: '200',
          mobileLogo: {
            link: '/mobile-logo.png',
            caption: 'Mobile Logo Caption'
          },
          mobileLogoMaxWidth: '100',
          logoAlt: 'Test Logo',
          logoUrl: 'https://example.com'
        }
      }
    },
    isDraft: false,
    isAccountEnabled: false
  })
}));

describe('generateMetadata', () => {
  test('Object should contain meta data with expected properties', async () => {
    const { generateMetadata } = await import('./page');
    const metaData = await generateMetadata({
      params: 'foo',
      searchParams: 'bar'
    });

    expect(metaData).toEqual({
      title: 'foo',
      description: 'bar',
      icons: {
        shortcut: '/caring_public/favicon.ico'
      },
      keywords: 'quux',
      openGraph: {
        type: 'website',
        url: 'https://www.caring.com',
        title: 'foo',
        description: 'bar',
        images: [
          {
            url: 'OpenGraph-Image-1.png',
            width: 2400,
            height: 1260,
            alt: 'Caring.com',
            type: 'image/png'
          }
        ]
      },
      alternates: { canonical: 'baz' },
      robots: {
        follow: true,
        index: true,
        'max-image-preview': 'large',
        'max-snippet': -1,
        'max-video-preview': -1
      },
      twitter: { card: 'summary_large_image' }
    });
  });
});
describe('Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateMetadata', () => {
    test('should return metadata with correct static values', async () => {
      const { generateMetadata } = await import('./saved/page');

      const metaData = await generateMetadata();

      expect(metaData).toEqual({
        title: 'Saved',
        description: 'Your saved providers',
        keywords: '',
        openGraph: {},
        alternates: { canonical: '/saved' },
        icons: {
          shortcut: '/caring_public/favicon.ico'
        },
        robots: {
          follow: true,
          index: false
        },
        twitter: { card: 'summary_large_image' }
      });
    });

    test('should have "index" set to false in robots metadata', async () => {
      const { generateMetadata } = await import('./saved/page');
      const metaData = await generateMetadata();

      expect(metaData?.robots?.index).toBe(false);
    });

    test('should include a shortcut icon in metadata', async () => {
      const { generateMetadata } = await import('./saved/page');
      const metaData = await generateMetadata();

      expect(metaData?.icons?.shortcut).toBe('/caring_public/favicon.ico');
    });

    describe('Page', () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      test('should render the page with navigation data', async () => {
        const { default: Page } = await import('./saved/page');
        const params = {};
        const searchParams = {};

        const page = await Page({ params, searchParams });

        expect(page.props.children).toBeDefined();

        if (Array.isArray(page.props.children)) {
          const [header, , footer] = page.props.children; // Skip unused content variable

          expect(header.props.children.props.mainHeader).toEqual(
            expect.objectContaining({
              name: 'header',
              logo: expect.any(Object)
            })
          );
          expect(footer.props.children.props.mainFooter).toEqual(
            expect.objectContaining({
              name: 'footer',
              logo: expect.any(Object)
            })
          );
        } else {
          expect(page.props.children).toMatchObject({
            props: expect.objectContaining({
              children: expect.anything()
            })
          });
        }
      });
    });
  });
});
