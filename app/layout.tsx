import '~/styles/globals.css';

import { MONTSERRAT } from '@styles/fonts';

import { RootProviders } from './_contexts/RootProviders';

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  // Caring.com is the only site that uses the app router, so this only this font is needed at this time.
  return (
    <html className={MONTSERRAT.variable} lang="en">
      <body>
        <RootProviders>{children} </RootProviders>
      </body>
    </html>
  );
}
