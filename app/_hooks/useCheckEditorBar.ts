'use client';
import { useEffect } from 'react';

// Temp fix for the green bar issue: MGNLFE-633 - https://jira.magnolia-cms.com/browse/MGNLFE-633
const useCheckEditorBar = (isDraftModeEnabled: boolean) => {
  const shouldSkipCheck = !isDraftModeEnabled;

  useEffect(() => {
    if (shouldSkipCheck) return;

    const checkEditorBar = () => {
      const editorBar = document.querySelector('.mgnlEditorBar');

      if (editorBar) {
        clearInterval(intervalId);
      } else {
        if (window.mgnlFrameReady) {
          window.mgnlFrameReady();
        }
      }
    };

    const intervalId = setInterval(checkEditorBar, 200);
  }, [shouldSkipCheck]);
};

export default useCheckEditorBar;
