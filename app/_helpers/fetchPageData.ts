import { ControllerAdapter } from '@controllers/controller.adapter';
import { getNavigation } from '@services/graphql/navigation';
import { GetServerSidePropsContext } from 'next';
import { draftMode } from 'next/headers';

import { Domain } from '~/types/Domains';

import { CARING_SITE } from '../_constants/sites';
// moved from page.tsx to here
async function fetchHeaderAndFooterConfig(sitePath: Domain) {
  return getNavigation(`/${sitePath}`);
}

export async function fetchPageData({ params, searchParams, path = '/home' }) {
  const { isEnabled } = draftMode();
  const isDraft = isEnabled;
  const isAccountEnabled = false;
  const site = CARING_SITE;
  const host = site.domains[0];

  const context: GetServerSidePropsContext = {
    resolvedUrl: path,
    query: searchParams,
    params: params,
    req: {
      headers: {
        host: host
      },
      url: path
    } as any,
    res: {} as any,
    preview: isDraft
  };

  const ca = new ControllerAdapter(site);

  const [pageData, navigation] = await Promise.all([
    ca.controller.getPageData(context),
    fetchHeaderAndFooterConfig(site.path)
  ]);

  return { pageData, isDraft, isAccountEnabled, navigation };
}
