/* store.js - Copyright (c) 2010-2017 <PERSON> */
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,e.store=t()}}(function(){var define,module,exports;return function t(e,n,r){function i(u,a){if(!n[u]){if(!e[u]){var s="function"==typeof require&&require;if(!a&&s)return s(u,!0);if(o)return o(u,!0);var c=new Error("Cannot find module '"+u+"'");throw c.code="MODULE_NOT_FOUND",c}var f=n[u]={exports:{}};e[u][0].call(f.exports,function(t){var n=e[u][1][t];return i(n||t)},f,f.exports,t,e,n,r)}return n[u].exports}for(var o="function"==typeof require&&require,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){"use strict";var r=t("../src/store-engine"),i=t("../storages/all"),o=t("../plugins/all");e.exports=r.createStore(i,o)},{"../plugins/all":2,"../src/store-engine":13,"../storages/all":15}],2:[function(t,e,n){"use strict";e.exports=[t("./defaults"),t("./dump"),t("./events"),t("./observe"),t("./expire"),t("./json2"),t("./operations"),t("./update"),t("./v1-backcompat")]},{"./defaults":3,"./dump":4,"./events":5,"./expire":6,"./json2":7,"./observe":9,"./operations":10,"./update":11,"./v1-backcompat":12}],3:[function(t,e,n){"use strict";function r(){function t(t,e){n=e}function e(t,e){var r=t();return r!==undefined?r:n[e]}var n={};return{defaults:t,get:e}}e.exports=r},{}],4:[function(t,e,n){"use strict";function r(){function t(t){var e={};return this.each(function(t,n){e[n]=t}),e}return{dump:t}}e.exports=r},{}],5:[function(t,e,n){"use strict";function r(){function t(t,e,n){return c.on(e,u(this,n))}function e(t,e){c.off(e)}function n(t,e,n){c.once(e,u(this,n))}function r(t,e,n){var r=this.get(e);t(),c.fire(e,n,r)}function o(t,e){var n=this.get(e);t(),c.fire(e,undefined,n)}function s(t){var e={};this.each(function(t,n){e[n]=t}),t(),a(e,function(t,e){c.fire(e,undefined,t)})}var c=i();return{watch:t,unwatch:e,once:n,set:r,remove:o,clearAll:s}}function i(){return s(f,{_id:0,_subSignals:{},_subCallbacks:{}})}var o=t("../src/util"),u=o.bind,a=o.each,s=o.create,c=o.slice;e.exports=r;var f={_id:null,_subCallbacks:null,_subSignals:null,on:function(t,e){return this._subCallbacks[t]||(this._subCallbacks[t]={}),this._id+=1,this._subCallbacks[t][this._id]=e,this._subSignals[this._id]=t,this._id},off:function(t){var e=this._subSignals[t];delete this._subCallbacks[e][t],delete this._subSignals[t]},once:function(t,e){var n=this.on(t,u(this,function(){e.apply(this,arguments),this.off(n)}))},fire:function(t){var e=c(arguments,1);a(this._subCallbacks[t],function(t){t.apply(this,e)})}}},{"../src/util":14}],6:[function(t,e,n){"use strict";function r(){function t(t,e,n,r){return this.hasNamespace(i)||a.set(e,r),t()}function e(t,e){return this.hasNamespace(i)||u.call(this,e),t()}function n(t,e){return this.hasNamespace(i)||a.remove(e),t()}function r(t,e){return a.get(e)}function o(t){var e=[];this.each(function(t,n){e.push(n)});for(var n=0;n<e.length;n++)u.call(this,e[n])}function u(t){a.get(t,Number.MAX_VALUE)<=(new Date).getTime()&&(this.raw.remove(t),a.remove(t))}var a=this.createStore(this.storage,null,this._namespacePrefix+i);return{set:t,get:e,remove:n,getExpiration:r,removeExpiredKeys:o}}var i="expire_mixin";e.exports=r},{}],7:[function(t,e,n){"use strict";function r(){return t("./lib/json2"),{}}e.exports=r},{"./lib/json2":8}],8:[function(require,module,exports){"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};"object"!==("undefined"==typeof JSON?"undefined":_typeof(JSON))&&(JSON={}),function(){function f(t){return t<10?"0"+t:t}function this_value(){return this.valueOf()}function quote(t){return rx_escapable.lastIndex=0,rx_escapable.test(t)?'"'+t.replace(rx_escapable,function(t){var e=meta[t];return"string"==typeof e?e:"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+t+'"'}function str(t,e){var n,r,i,o,u,a=gap,s=e[t];switch(s&&"object"===(void 0===s?"undefined":_typeof(s))&&"function"==typeof s.toJSON&&(s=s.toJSON(t)),"function"==typeof rep&&(s=rep.call(e,t,s)),void 0===s?"undefined":_typeof(s)){case"string":return quote(s);case"number":return isFinite(s)?String(s):"null";case"boolean":case"null":return String(s);case"object":if(!s)return"null";if(gap+=indent,u=[],"[object Array]"===Object.prototype.toString.apply(s)){for(o=s.length,n=0;n<o;n+=1)u[n]=str(n,s)||"null";return i=0===u.length?"[]":gap?"[\n"+gap+u.join(",\n"+gap)+"\n"+a+"]":"["+u.join(",")+"]",gap=a,i}if(rep&&"object"===(void 0===rep?"undefined":_typeof(rep)))for(o=rep.length,n=0;n<o;n+=1)"string"==typeof rep[n]&&(r=rep[n],(i=str(r,s))&&u.push(quote(r)+(gap?": ":":")+i));else for(r in s)Object.prototype.hasOwnProperty.call(s,r)&&(i=str(r,s))&&u.push(quote(r)+(gap?": ":":")+i);return i=0===u.length?"{}":gap?"{\n"+gap+u.join(",\n"+gap)+"\n"+a+"}":"{"+u.join(",")+"}",gap=a,i}}var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value);var gap,indent,meta,rep;"function"!=typeof JSON.stringify&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(t,e,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=e,e&&"function"!=typeof e&&("object"!==(void 0===e?"undefined":_typeof(e))||"number"!=typeof e.length))throw new Error("JSON.stringify");return str("",{"":t})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){function walk(t,e){var n,r,i=t[e];if(i&&"object"===(void 0===i?"undefined":_typeof(i)))for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(r=walk(i,n),r!==undefined?i[n]=r:delete i[n]);return reviver.call(t,e,i)}var j;if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(t){return"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)})),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},{}],9:[function(t,e,n){"use strict";function r(){function t(t,e,n){var r=this.watch(e,n);return n(this.get(e)),r}function e(t,e){this.unwatch(e)}return{observe:t,unobserve:e}}var i=t("./events");e.exports=[i,r]},{"./events":5}],10:[function(t,e,n){"use strict";function r(){function t(t,e,n,r,i,o){return s.call(this,"push",arguments)}function e(t,e){return s.call(this,"pop",arguments)}function n(t,e){return s.call(this,"shift",arguments)}function r(t,e,n,r,i,o){return s.call(this,"unshift",arguments)}function o(t,e,n,r,o,s){var c=u(arguments,2);return this.update(e,{},function(t){if("object"!=(void 0===t?"undefined":i(t)))throw new Error('store.assign called for non-object value with key "'+e+'"');return c.unshift(t),a.apply(Object,c)})}function s(t,e){var n,r=e[1],i=u(e,2);return this.update(r,[],function(e){n=Array.prototype[t].apply(e,i)}),n}return{push:t,pop:e,shift:n,unshift:r,assign:o}}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=t("../src/util"),u=o.slice,a=o.assign,s=t("./update");e.exports=[s,r]},{"../src/util":14,"./update":11}],11:[function(t,e,n){"use strict";function r(){function t(t,e,n,r){3==arguments.length&&(r=n,n=undefined);var i=this.get(e,n),o=r(i);this.set(e,o!=undefined?o:i)}return{update:t}}e.exports=r},{}],12:[function(t,e,n){"use strict";function r(){return this.disabled=!this.enabled,{has:i,transact:o,clear:u,forEach:a,getAll:s,serialize:c,deserialize:f}}function i(t,e){return this.get(e)!==undefined}function o(t,e,n,r){null==r&&(r=n,n=null),null==n&&(n={});var i=this.get(e,n),o=r(i);this.set(e,o===undefined?i:o)}function u(t){return this.clearAll.call(this)}function a(t,e){return this.each.call(this,function(t,n){e(n,t)})}function s(t){return this.dump.call(this)}function c(t,e){return JSON.stringify(e)}function f(t,e){if("string"!=typeof e)return undefined;try{return JSON.parse(e)}catch(n){return e||undefined}}var l=t("./dump"),p=t("./json2");e.exports=[l,p,r]},{"./dump":4,"./json2":7}],13:[function(t,e,n){"use strict";function r(){var t="undefined"==typeof console?null:console;if(t){(t.warn?t.warn:t.log).apply(t,arguments)}}function i(t,e,n){n||(n=""),t&&!l(t)&&(t=[t]),e&&!l(e)&&(e=[e]);var i=n?"__storejs_"+n+"_":"",o=n?new RegExp("^"+i):null;if(!/^[a-zA-Z0-9_\-]*$/.test(n))throw new Error("store.js namespaces can only have alphanumerics + underscores and dashes");var g={_namespacePrefix:i,_namespaceRegexp:o,_testStorage:function(t){try{var e="__storejs__test__";t.write(e,e);var n=t.read(e)===e;return t.remove(e),n}catch(r){return!1}},_assignPluginFnProp:function(t,e){var n=this[e];this[e]=function(){function e(){if(n)return s(arguments,function(t,e){r[e]=t}),n.apply(i,r)}var r=u(arguments,0),i=this,o=[e].concat(r);return t.apply(i,o)}},_serialize:function(t){return JSON.stringify(t)},_deserialize:function(t,e){if(!t)return e;var n="";try{n=JSON.parse(t)}catch(r){n=t}return n!==undefined?n:e},_addStorage:function(t){this.enabled||this._testStorage(t)&&(this.storage=t,this.enabled=!0)},_addPlugin:function(t){var e=this;if(l(t))return void s(t,function(t){e._addPlugin(t)});if(!a(this.plugins,function(e){return t===e})){if(this.plugins.push(t),!p(t))throw new Error("Plugins must be function values that return objects");var n=t.call(this);if(!d(n))throw new Error("Plugins must return an object of function properties");s(n,function(n,r){if(!p(n))throw new Error("Bad plugin property: "+r+" from plugin "+t.name+". Plugins should only return functions.");e._assignPluginFnProp(n,r)})}},addStorage:function(t){r("store.addStorage(storage) is deprecated. Use createStore([storages])"),this._addStorage(t)}},v=f(g,h,{plugins:[]});return v.raw={},s(v,function(t,e){p(t)&&(v.raw[e]=c(v,t))}),s(t,function(t){v._addStorage(t)}),s(e,function(t){v._addPlugin(t)}),v}var o=t("./util"),u=o.slice,a=o.pluck,s=o.each,c=o.bind,f=o.create,l=o.isList,p=o.isFunction,d=o.isObject;e.exports={createStore:i};var h={version:"2.0.12",enabled:!1,get:function(t,e){var n=this.storage.read(this._namespacePrefix+t);return this._deserialize(n,e)},set:function(t,e){return e===undefined?this.remove(t):(this.storage.write(this._namespacePrefix+t,this._serialize(e)),e)},remove:function(t){this.storage.remove(this._namespacePrefix+t)},each:function(t){var e=this;this.storage.each(function(n,r){t.call(e,e._deserialize(n),(r||"").replace(e._namespaceRegexp,""))})},clearAll:function(){this.storage.clearAll()},hasNamespace:function(t){return this._namespacePrefix=="__storejs_"+t+"_"},createStore:function(){return i.apply(this,arguments)},addPlugin:function(t){this._addPlugin(t)},namespace:function(t){return i(this.storage,this.plugins,t)}}},{"./util":14}],14:[function(t,e,n){(function(t){"use strict";function n(t,e){return function(){return e.apply(t,Array.prototype.slice.call(arguments,0))}}function r(t,e){return Array.prototype.slice.call(t,e||0)}function i(t,e){u(t,function(t,n){return e(t,n),!1})}function o(t,e){var n=a(t)?[]:{};return u(t,function(t,r){return n[r]=e(t,r),!1}),n}function u(t,e){if(a(t)){for(var n=0;n<t.length;n++)if(e(t[n],n))return t[n]}else for(var r in t)if(t.hasOwnProperty(r)&&e(t[r],r))return t[r]}function a(t){return null!=t&&"function"!=typeof t&&"number"==typeof t.length}function s(t){return t&&"[object Function]"==={}.toString.call(t)}function c(t){return t&&"[object Object]"==={}.toString.call(t)}var f=function(){return Object.assign?Object.assign:function(t,e,n,r){for(var o=1;o<arguments.length;o++)i(Object(arguments[o]),function(e,n){t[n]=e});return t}}(),l=function(){if(Object.create)return function(t,e,n,i){var o=r(arguments,1);return f.apply(this,[Object.create(t)].concat(o))};var t=function(){};return function(e,n,i,o){var u=r(arguments,1);return t.prototype=e,f.apply(this,[new t].concat(u))}}(),p=function(){return String.prototype.trim?function(t){return String.prototype.trim.call(t)}:function(t){return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}(),d="undefined"!=typeof window?window:t;e.exports={assign:f,create:l,trim:p,bind:n,slice:r,each:i,map:o,pluck:u,isList:a,isFunction:s,isObject:c,Global:d}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(t,e,n){"use strict";e.exports=[t("./localStorage"),t("./oldFF-globalStorage"),t("./oldIE-userDataStorage"),t("./cookieStorage"),t("./sessionStorage"),t("./memoryStorage")]},{"./cookieStorage":16,"./localStorage":17,"./memoryStorage":18,"./oldFF-globalStorage":19,"./oldIE-userDataStorage":20,"./sessionStorage":21}],16:[function(t,e,n){"use strict";function r(t){if(!t||!s(t))return null;var e="(?:^|.*;\\s*)"+escape(t).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*((?:[^;](?!;))*[^;]?).*";return unescape(p.cookie.replace(new RegExp(e),"$1"))}function i(t){for(var e=p.cookie.split(/; ?/g),n=e.length-1;n>=0;n--)if(l(e[n])){var r=e[n].split("="),i=unescape(r[0]),o=unescape(r[1]);t(o,i)}}function o(t,e){t&&(p.cookie=escape(t)+"="+escape(e)+"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/")}function u(t){t&&s(t)&&(p.cookie=escape(t)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}function a(){i(function(t,e){u(e)})}function s(t){return new RegExp("(?:^|;\\s*)"+escape(t).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(p.cookie)}var c=t("../src/util"),f=c.Global,l=c.trim;e.exports={name:"cookieStorage",read:r,write:o,each:i,remove:u,clearAll:a};var p=f.document},{"../src/util":14}],17:[function(t,e,n){"use strict";function r(){return f.localStorage}function i(t){return r().getItem(t)}function o(t,e){return r().setItem(t,e)}function u(t){for(var e=r().length-1;e>=0;e--){var n=r().key(e);t(i(n),n)}}function a(t){return r().removeItem(t)}function s(){return r().clear()}var c=t("../src/util"),f=c.Global;e.exports={name:"localStorage",read:i,write:o,each:u,remove:a,clearAll:s}},{"../src/util":14}],18:[function(t,e,n){"use strict";function r(t){return s[t]}function i(t,e){s[t]=e}function o(t){for(var e in s)s.hasOwnProperty(e)&&t(s[e],e)}function u(t){delete s[t]}function a(t){s={}}e.exports={name:"memoryStorage",read:r,write:i,each:o,remove:u,clearAll:a};var s={}},{}],19:[function(t,e,n){"use strict";function r(t){return f[t]}function i(t,e){f[t]=e}function o(t){for(var e=f.length-1;e>=0;e--){var n=f.key(e);t(f[n],n)}}function u(t){return f.removeItem(t)}function a(){o(function(t,e){delete f[t]})}var s=t("../src/util"),c=s.Global;e.exports={name:"oldFF-globalStorage",read:r,write:i,each:o,remove:u,clearAll:a};var f=c.globalStorage},{"../src/util":14}],20:[function(t,e,n){"use strict";function r(t,e){if(!h){var n=s(t);d(function(t){t.setAttribute(n,e),t.save(l)})}}function i(t){if(!h){var e=s(t),n=null;return d(function(t){n=t.getAttribute(e)}),n}}function o(t){d(function(e){for(var n=e.XMLDocument.documentElement.attributes,r=n.length-1;r>=0;r--){var i=n[r];t(e.getAttribute(i.name),i.name)}})}function u(t){var e=s(t);d(function(t){t.removeAttribute(e),t.save(l)})}function a(){d(function(t){var e=t.XMLDocument.documentElement.attributes;t.load(l);for(var n=e.length-1;n>=0;n--)t.removeAttribute(e[n].name);t.save(l)})}function s(t){return t.replace(/^\d/,"___$&").replace(g,"___")}var c=t("../src/util"),f=c.Global;e.exports={name:"oldIE-userDataStorage",write:r,read:i,each:o,remove:u,clearAll:a};var l="storejs",p=f.document,d=function(){if(!p||!p.documentElement||!p.documentElement.addBehavior)return null;var t,e,n;try{e=new ActiveXObject("htmlfile"),e.open(),e.write('<script>document.w=window<\/script><iframe src="/favicon.ico"></iframe>'),e.close(),t=e.w.frames[0].document,n=t.createElement("div")}catch(r){n=p.createElement("div"),t=p.body}return function(e){var r=[].slice.call(arguments,0);r.unshift(n),t.appendChild(n),n.addBehavior("#default#userData"),n.load(l),e.apply(this,r),t.removeChild(n)}}(),h=(f.navigator?f.navigator.userAgent:"").match(/ (MSIE 8|MSIE 9|MSIE 10)\./),g=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g")},{"../src/util":14}],21:[function(t,e,n){"use strict";function r(){return f.sessionStorage}function i(t){return r().getItem(t)}function o(t,e){return r().setItem(t,e)}function u(t){for(var e=r().length-1;e>=0;e--){var n=r().key(e);t(i(n),n)}}function a(t){return r().removeItem(t)}function s(){return r().clear()}var c=t("../src/util"),f=c.Global;e.exports={name:"sessionStorage",read:i,write:o,each:u,remove:a,clearAll:s}},{"../src/util":14}]},{},[1])(1)});