name: CDK Prod Deploy (manual)

on:
  workflow_dispatch:
    inputs:
      ref:
        description: "Git ref (branch / tag / SHA) to deploy"
        default: "main"
        required: false

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "22"
  CDK_VERSION: "2.1019.2"

permissions:
  id-token: write
  contents: read
  packages: read

# ──────────────────────────────────────────────────────────────────
# Job 1: CD<PERSON> diff  (requires caring-prod approval)
# ──────────────────────────────────────────────────────────────────
jobs:
  diff:
    name: "CD<PERSON> diff for caring-prod"
    environment: caring-prod    # ← exposes AWS_ROLE_ARN; triggers approval
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: infrastructure

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref || 'main' }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: infrastructure/package-lock.json
          registry-url: https://npm.pkg.github.com/
          scope: '@caring'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: "Configure AWS credentials"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      - name: "CDK diff"
        run: |
          export STAGE=prod
          npm run cdk -- diff

# ──────────────────────────────────────────────────────────────────
# Job 2: Deploy Prod Environment (runs after diff, same environment/secret)
# ──────────────────────────────────────────────────────────────────
  deploy-prod:
    name: "Deploy to caring-prod"
    needs: diff
    environment: caring-prod     # ← exposes AWS_ROLE_ARN; triggers approval
    concurrency: deploy-prod
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: infrastructure
    env:
      ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref || 'main' }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: infrastructure/package-lock.json
          registry-url: https://npm.pkg.github.com/
          scope: '@caring' 

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: "Configure AWS credentials"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      - name: "CDK bootstrap (prod)"
        run: |
          export STAGE=prod
          npx cdk@${{ env.CDK_VERSION }} bootstrap aws://$ACCOUNT_ID/$AWS_REGION

      - name: "CDK deploy (prod)"
        run: |
          export STAGE=prod
          npm run cdk -- deploy --require-approval never --all