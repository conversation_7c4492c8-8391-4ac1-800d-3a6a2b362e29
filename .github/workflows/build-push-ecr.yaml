name: Build & Push to ECR

on:
  push:
    branches:
      - ss/ci-cd-updates      # dev.  ticket: https://caring.atlassian.net/browse/WPLAT-17 
      - main                  # staging + prod
  workflow_dispatch:
    inputs:
      ref:
        description: "Git ref (branch / tag / SHA) to build"
        default: "main"
        required: true

concurrency:
  group: build-push-${{ github.ref_name }}
  # if true, any in-progress build for the same branch is cancelled in favor of the new one;
  # if false, only one build can be queued, and older queued runs get cancelled
  cancel-in-progress: true

permissions:
  id-token: write
  contents: read

jobs:
  build-dev-preview:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: dev-caring-website-preview-service
      environment: caring-dev
      is_preview: true
    secrets: inherit
    if: github.ref == 'refs/heads/ss/ci-cd-updates'

  build-dev:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: dev-caring-website-service
      environment: caring-dev
      is_preview: false
    secrets: inherit
    if: github.ref == 'refs/heads/ss/ci-cd-updates'

  build-stg-preview:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: stg-caring-website-preview-service
      environment: caring-stg
      is_preview: true
    secrets: inherit
    if: github.ref == 'refs/heads/main'

  build-stg:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: stg-caring-website-service
      environment: caring-stg
      is_preview: false
    secrets: inherit
    if: github.ref == 'refs/heads/main'