name: Build & Push to ECR Production

on:
  workflow_dispatch:
    inputs:
      ref:
        description: "Git ref (branch / tag / SHA) to build"
        default: "main"
        required: true

concurrency:
  group: build-push-${{ github.ref_name }}
  # if true, any in-progress build for the same branch is cancelled in favor of the new one;
  # if false, only one build can be queued, and older queued runs get cancelled
  cancel-in-progress: true

permissions:
  id-token: write
  contents: read

jobs:
  build-prod-preview:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: prod-caring-website-preview-service
      environment: caring-prod
      is_preview: true
    secrets: inherit
    if: github.ref == 'refs/heads/main'

  build-prod:
    uses: ./.github/workflows/build-push-reusable-workflow.yaml
    with:
      ecr_repo: prod-caring-website-service
      environment: caring-prod
      is_preview: false
    secrets: inherit
    if: github.ref == 'refs/heads/main'