name: Reusable workflow for <PERSON>uild & Push to ECR

on:
  workflow_call:
    inputs:
      ecr_repo:
        description: "The name of the ECR repository to push to"
        required: true
        type: string
      environment:
        description: "Deployment environment (e.g., caring-dev, caring-stg)"
        required: true
        type: string
      is_preview:
        description: "For Magnolia Author preview environments?"
        required: false
        type: boolean
        default: false

jobs:
  build-and-push:
    name: Build & Push ${{ inputs.environment }} (${{ inputs.ecr_repo }})
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    env:
      ECR_REGISTRY: ${{ vars.AWS_ACCOUNT_ID }}.dkr.ecr.${{ vars.AWS_REGION }}.amazonaws.com
      ECR_REPO: ${{ inputs.ecr_repo }}
      MGNL_HOST: ${{ inputs.is_preview == true && format('{0}/author', vars.NEXT_PUBLIC_MGNL_HOST) || vars.NEXT_PUBLIC_MGNL_HOST }}

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ vars.AWS_REGION }}

      - name: Login to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Docker meta tags
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPO }}
          tags: |
            type=sha,format=short
            latest

      - name: Build & Push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile.aws-ecs
          platforms: linux/amd64
          provenance: false
          push: true
          load: true
          tags:   ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            # Shared build args
            BUILDKIT_STEP_LOG_MAX_SIZE=10485760
            BUILDKIT_STEP_LOG_MAX_SPEED=10485760
            NEXT_PUBLIC_CANARIO_HOST=${{ vars.NEXT_PUBLIC_CANARIO_HOST }}
            NEXT_PUBLIC_MGNL_HOST=${{ env.MGNL_HOST }}
            NEXT_PUBLIC_MGNL_LANGUAGES=${{ vars.NEXT_PUBLIC_MGNL_LANGUAGES }}
            NEXT_PUBLIC_MGNL_NODE_NAME=${{ vars.NEXT_PUBLIC_MGNL_NODE_NAME }}
            NEXT_PUBLIC_MAP_BOX_KEY=${{ secrets.NEXT_PUBLIC_MAP_BOX_KEY }}
            NEXT_PUBLIC_ALGOLIA_APPLICATION_ID=${{ vars.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID }}
            NEXT_PUBLIC_ALGOLIA_API_KEY=${{ secrets.NEXT_PUBLIC_ALGOLIA_API_KEY }}
            NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID=${{ vars.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID }}
            NEXT_PUBLIC_GOOGLE_ANALYTICS_MEASUREMENT_ID=${{ vars.NEXT_PUBLIC_GOOGLE_ANALYTICS_MEASUREMENT_ID }}
            NEXT_PUBLIC_TWILIO_PROVIDER_PHONE_NUMBER=${{ vars.NEXT_PUBLIC_TWILIO_PROVIDER_PHONE_NUMBER }}
            NEXT_PUBLIC_ASSISTEDLIVING_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_ASSISTEDLIVING_PARTNER_TOKEN }}
            NEXT_PUBLIC_CARING_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_CARING_PARTNER_TOKEN }}
            NEXT_PUBLIC_HOMECARE_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_HOMECARE_PARTNER_TOKEN }}
            NEXT_PUBLIC_MEDICALALERT_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_MEDICALALERT_PARTNER_TOKEN }}
            NEXT_PUBLIC_MEMORYCARE_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_MEMORYCARE_PARTNER_TOKEN }}
            NEXT_PUBLIC_PAYINGFORSENIORCARE_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_PAYINGFORSENIORCARE_PARTNER_TOKEN }}
            NEXT_PUBLIC_SENIORADVICE_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_SENIORADVICE_PARTNER_TOKEN }}
            NEXT_PUBLIC_SENIORHOMES_PARTNER_TOKEN=${{ secrets.NEXT_PUBLIC_SENIORHOMES_PARTNER_TOKEN }}
            NEXT_PUBLIC_CARING_API_HOST=${{ vars.NEXT_PUBLIC_CARING_API_HOST }}
            CARING_API_CLIENT_ID=${{ secrets.CARING_API_CLIENT_ID }}
            CARING_API_CLIENT_SECRET=${{ secrets.CARING_API_CLIENT_SECRET }}
            NEXT_PUBLIC_SEGMENT_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_CDN }}
            NEXT_PUBLIC_SEGMENT_CARING_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_CARING_CDN }}
            NEXT_PUBLIC_SEGMENT_SENIORHOMES_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_SENIORHOMES_CDN }}
            NEXT_PUBLIC_SEGMENT_ASSISTEDLIVING_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_ASSISTEDLIVING_CDN }}
            NEXT_PUBLIC_SEGMENT_MEMORYCARE_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_MEMORYCARE_CDN }}
            NEXT_PUBLIC_SEGMENT_MEDICALALERT_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_MEDICALALERT_CDN }}
            NEXT_PUBLIC_SEGMENT_PAYINGFORSENIORCARE_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_PAYINGFORSENIORCARE_CDN }}
            NEXT_PUBLIC_SEGMENT_HOMECARE_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_HOMECARE_CDN }}
            NEXT_PUBLIC_SEGMENT_SENIORADVICE_CDN=${{ vars.NEXT_PUBLIC_SEGMENT_SENIORADVICE_CDN }}
            NEXT_PUBLIC_SEGMENT_CARING_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_CARING_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_SENIORHOMES_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_SENIORHOMES_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_ASSISTEDLIVING_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_ASSISTEDLIVING_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_MEMORYCARE_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_MEMORYCARE_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_MEDICALALERT_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_MEDICALALERT_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_PAYINGFORSENIORCARE_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_PAYINGFORSENIORCARE_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_HOMECARE_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_HOMECARE_WRITE_KEY }}
            NEXT_PUBLIC_SEGMENT_SENIORADVICE_WRITE_KEY=${{ secrets.NEXT_PUBLIC_SEGMENT_SENIORADVICE_WRITE_KEY }}
            NEXT_PUBLIC_WRITE_KEY=${{ secrets.NEXT_PUBLIC_WRITE_KEY }}
            NEXT_PUBLIC_GOOGLE_MAP_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_MAP_API_KEY }}
            NEXT_PUBLIC_GOOGLE_PLACE_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_PLACE_API_KEY }}
            NEXT_PUBLIC_CARING_THANK_YOU_PAGE_URL=${{ vars.NEXT_PUBLIC_CARING_THANK_YOU_PAGE_URL }}
            NEXT_PUBLIC_SUFFIX_ALGOLIA_INDEX=${{ vars.NEXT_PUBLIC_SUFFIX_ALGOLIA_INDEX }}
            NEXT_PUBLIC_PLASMIC_PROJECT_ID=${{ vars.NEXT_PUBLIC_PLASMIC_PROJECT_ID }}
            NEXT_PUBLIC_PLASMIC_API_TOKEN=${{ secrets.NEXT_PUBLIC_PLASMIC_API_TOKEN }}
            NEXT_PUBLIC_PLASMIC_VERSION=${{ vars.NEXT_PUBLIC_PLASMIC_VERSION }}
            NEXT_PUBLIC_LAUNCHDARKLY_SDK_KEY=${{ secrets.NEXT_PUBLIC_LAUNCHDARKLY_SDK_KEY }}
            GHOST_INSPECTOR_API_KEY=${{ secrets.GHOST_INSPECTOR_API_KEY }}
            GHOST_INSPECTOR_SUITE_ID=${{ vars.GHOST_INSPECTOR_SUITE_ID }}
            NEXT_PUBLIC_MAPBOX_API_KEY=${{ secrets.NEXT_PUBLIC_MAPBOX_API_KEY }}
            NEXT_PUBLIC_GOOGLE_EMBED_API_KEY=${{ secrets.NEXT_PUBLIC_GOOGLE_EMBED_API_KEY }}
            NEXT_PUBLIC_DATADOG_RUM_APPLICATION_ID=${{ vars.NEXT_PUBLIC_DATADOG_RUM_APPLICATION_ID }}
            NEXT_PUBLIC_DATADOG_RUM_CLIENT_TOKEN=${{ secrets.NEXT_PUBLIC_DATADOG_RUM_CLIENT_TOKEN }}
            NEXT_PUBLIC_DATADOG_RUM_ENVIRONMENT=${{ vars.NEXT_PUBLIC_DATADOG_RUM_ENVIRONMENT }}
            NEXT_PUBLIC_DXP_VERSION=$CI_COMMIT_TAG
            NEXT_PUBLIC_GOOGLE_MAPS_LOCATION_MAP_ID=${{ vars.NEXT_PUBLIC_GOOGLE_MAPS_LOCATION_MAP_ID }}
            NEXT_PUBLIC_GOOGLE_MAPS_MAP_VIEW_MAP_ID=${{ vars.NEXT_PUBLIC_GOOGLE_MAPS_MAP_VIEW_MAP_ID }}
            NEXT_PUBLIC_SSR_MAPBOX_API_KEY=${{ secrets.NEXT_PUBLIC_SSR_MAPBOX_API_KEY }}
            NEXT_PUBLIC_GROWTHBOOK_API_KEY=${{ secrets.NEXT_PUBLIC_GROWTHBOOK_API_KEY }}
