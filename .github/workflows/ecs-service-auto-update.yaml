name: ECS Service Rollout

on:
  workflow_run:
    workflows: ["Build & Push to ECR"]
    types:
      - completed

env:
  AWS_REGION: us-east-1

permissions:
  id-token: write
  contents: read

jobs:
  ecs-dev-rollout:
    uses: ./.github/workflows/ecs-service-rollout-reusable-workflow.yaml
    with:
      head_branch: ss/ci-cd-updates  # related ticket:  https://caring.atlassian.net/browse/WPLAT-17
      cluster_name: dev-caring-website-cluster
      preview_service_name: dev-caring-website-preview-service
      website_service_name: dev-caring-website-service
      environment: caring-dev
    secrets: inherit

  ecs-stg-rollout:
    uses: ./.github/workflows/ecs-service-rollout-reusable-workflow.yaml
    with:
      head_branch: main
      cluster_name: stg-caring-website-cluster
      preview_service_name: stg-caring-website-preview-service
      website_service_name: stg-caring-website-service
      environment: caring-stg
    secrets: inherit
