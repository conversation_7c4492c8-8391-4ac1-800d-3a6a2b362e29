name: ECS Rollout Production (manual)

on:
  workflow_run:
    workflows: ["Build & Push to ECR Production"]
    types:
      - completed
  workflow_dispatch:

env:
  AWS_REGION: us-east-1

permissions:
  id-token: write
  contents: read

jobs:

# ──────────────────────────────── Production Rollout ────────────────────────────────────
  ecs-prod-rollout:
    uses: ./.github/workflows/ecs-service-rollout-reusable-workflow.yaml
    with:
      head_branch: main
      cluster_name: prod-caring-website-cluster
      preview_service_name: prod-caring-website-preview-service
      website_service_name: prod-caring-website-service
      environment: caring-prod
    secrets: inherit