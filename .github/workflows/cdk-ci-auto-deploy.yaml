# -----------------------------------------------------------------
# CDK deployments
#   • ss/ci-cd-updates → caring-dev Environment / account
#   • main             → caring-stg Environment / account
# -----------------------------------------------------------------

name: CD<PERSON> Deploy

on:
  push:
    branches:
      - ss/ci-cd-updates     # dev.  linked ticket : https://caring.atlassian.net/browse/WPLAT-17 
      - main                 # staging
    paths:
      - "infrastructure/**"

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "22"
  CDK_VERSION: "2.1019.2"

permissions:
  id-token: write
  contents: read
  packages: read

jobs:

# ──────────────────────────────── DEV DEPLOY ────────────────────────────────────
  cdk-deploy-dev:
   # linked ticket : https://caring.atlassian.net/browse/WPLAT-17 
    if: github.ref == 'refs/heads/ss/ci-cd-updates'
    runs-on: ubuntu-latest
    environment: caring-dev
    concurrency: cdk-deploy-dev
    defaults:
      run:
        working-directory: infrastructure
    env:
      AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: infrastructure/package-lock.json
          registry-url: https://npm.pkg.github.com/
          scope: '@caring' 

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: "Configure AWS creds (dev)"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      - name: "CDK bootstrap (caring-dev)"
        run: |
          export STAGE=dev
          npx cdk@${{ env.CDK_VERSION }} bootstrap aws://$AWS_ACCOUNT_ID/$AWS_REGION

      - name: "CDK deploy (dev)"
        env:
          STAGE: dev
        run: |
          npm run cdk -- deploy --require-approval never --all

# ──────────────────────────────── STG DEPLOY ────────────────────────────────────
  cdk-deploy-stg:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: caring-stg
    concurrency: cdk-deploy-stg
    defaults:
      run:
        working-directory: infrastructure
    env:
      AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: infrastructure/package-lock.json
          registry-url: https://npm.pkg.github.com/
          scope: '@caring' 

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: "Configure AWS creds (stg)"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      - name: "CDK bootstrap (caring-stg)"
        run: |
          export STAGE=stg
          npx cdk@${{ env.CDK_VERSION }} bootstrap aws://$AWS_ACCOUNT_ID/$AWS_REGION

      - name: "CDK deploy (stg)"
        env:
          STAGE: stg
        run: |
          npm run cdk -- deploy --require-approval never --all
