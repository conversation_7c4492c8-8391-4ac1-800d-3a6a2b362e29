# -----------------------------------------------------------------
# CDK pull-request guard
#   • Picks the GitHub Environment based on the PR’s target branch
#       - PR into   main  ->   caring-stg  environment
#       - all other PRs   ->   caring-dev  environment
#   • Uses environment-scoped secret `AWS_ROLE_ARN`.
# -----------------------------------------------------------------

name: CDK PR Diff

on:
  pull_request:
    paths:
      - "infrastructure/**"

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "20"

permissions:
  id-token: write
  contents: read
  packages: read

jobs:
  diff:
    # Select the Environment dynamically
    environment: ${{ github.base_ref == 'main' && 'caring-stg' || 'caring-dev' }}
    runs-on: ubuntu-latest
    name: "CDK diff"

    # Set a default working-directory for all run: steps in this job
    defaults:
      run:
        working-directory: infrastructure

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: infrastructure/package-lock.json
          registry-url: https://npm.pkg.github.com/
          scope: '@caring'          

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      #  Configure AWS creds using the env secret AWS_ROLE_ARN
      - name: "Configure AWS credentials (OIDC)"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      #  Run CDK diff against the pertinent stage
      - name: "CDK diff"
        env:
          STAGE: ${{ github.event.pull_request.base.ref == 'main' && 'stg' || 'dev' }}
        run: |
          npm run cdk -- diff