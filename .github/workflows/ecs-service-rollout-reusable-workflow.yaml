name: Reusable workflow for ECS Service Rollout

on:
  workflow_call:
    inputs:
      head_branch:
        description: "Branch name that triggered the previous workflow"
        required: true
        type: string
      cluster_name:
        description: "ECS cluster name"
        required: true
        type: string
      preview_service_name:
        description: "Name of the preview ECS service"
        required: true
        type: string
      website_service_name:
        description: "Name of the website ECS service"
        required: true
        type: string
      environment:
        description: "Deployment environment (e.g., caring-dev, caring-stg)"
        required: true
        type: string

env:
  AWS_REGION: us-east-1

permissions:
  id-token: write
  contents: read

jobs:
  rollout:
    name: ECS rollout → ${{ inputs.environment }}
    if: ${{ github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.head_branch == inputs.head_branch }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    concurrency: ecs-rollout-${{ inputs.environment }}

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region:     ${{ env.AWS_REGION }}

      - name: ECS rollout – preview service
        run: |
          aws ecs update-service \
            --cluster "${{ inputs.cluster_name }}" \
            --service "${{ inputs.preview_service_name }}" \
            --force-new-deployment \
            --region $AWS_REGION

      - name: Wait for preview to become stable
        run: |
          aws ecs wait services-stable \
            --cluster "${{ inputs.cluster_name }}" \
            --services "${{ inputs.preview_service_name }}" \
            --region $AWS_REGION

      - name: Invalidate CloudFront – preview
        run: |
          aws cloudfront create-invalidation \
            --distribution-id "${{ vars.CLOUDFRONT_DISTRIBUTION_ID_PREVIEW }}" \
            --paths "/*" \
            --query 'Invalidation.Id' \
            --output text |
            xargs -I{} echo "Invalidation: {}"

      - name: ECS rollout – website service
        run: |
          aws ecs update-service \
            --cluster "${{ inputs.cluster_name }}" \
            --service "${{ inputs.website_service_name }}" \
            --force-new-deployment \
            --region $AWS_REGION

      - name: Wait for website to become stable
        run: |
          aws ecs wait services-stable \
            --cluster "${{ inputs.cluster_name }}" \
            --services "${{ inputs.website_service_name }}" \
            --region $AWS_REGION

      - name: Invalidate CloudFront – website
        run: |
          aws cloudfront create-invalidation \
            --distribution-id "${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}" \
            --paths "/*" \
            --query 'Invalidation.Id' \
            --output text |
            xargs -I{} echo "Invalidation: {}"
