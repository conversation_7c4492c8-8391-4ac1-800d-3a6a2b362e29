'root':
  'hideInNav': false
  'jcr:primaryType': 'mgnl:page'
  'jcr:uuid': 'ccdf90bb-2946-435c-b8ac-435966c6f391'
  'mgnl:created': 2022-10-31T19:58:22.778+01:00
  'mgnl:createdBy': 'superuser'
  'mgnl:lastModified': 2022-10-31T19:58:22.779+01:00
  'mgnl:lastModifiedBy': 'superuser'
  'mgnl:template': 'spa-lm:pages/default'
  'noCache': false
  'caring.com':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': 'bbbfa401-94e1-4932-a9d2-370509ce4d44'
    'mgnl:activationStatus': true
    'mgnl:created': 2022-10-31T17:30:38.225+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastActivated': 2022-10-31T18:20:00.764+01:00
    'mgnl:lastActivatedBy': 'superuser'
    'mgnl:lastActivatedVersion': '1.0'
    'mgnl:lastActivatedVersionCreated': 2022-10-31T18:09:25.843+01:00
    'mgnl:lastModified': 2022-10-31T19:59:58.977+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '1e7af274-0631-40d7-86f2-7389259c39b1'
      'mgnl:activationStatus': true
      'mgnl:created': 2022-10-31T17:30:47.331+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastActivated': 2022-10-31T18:20:01.114+01:00
      'mgnl:lastActivatedBy': 'superuser'
      'mgnl:lastActivatedVersion': '1.0'
      'mgnl:lastActivatedVersionCreated': 2022-10-31T18:09:26.066+01:00
      'mgnl:lastModified': 2022-10-31T19:59:58.977+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': '340f487d-fb10-4182-9c28-f47ea9ecd3c9'
        'mgnl:activationStatus': true
        'mgnl:created': 2022-10-31T17:44:38.002+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2022-10-31T18:20:01.115+01:00
        'mgnl:lastActivatedBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:43:03.441+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'ed21fa50-b6b7-45fa-9532-ff4f3dc8a8b5'
          'mgnl:activationStatus': true
          'mgnl:created': 2022-10-31T17:44:38.006+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastActivated': 2022-10-31T18:20:01.119+01:00
          'mgnl:lastActivatedBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:43:03.441+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the Caring.com homepage.</p>

            '
  'senioradvice.com':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '18bcc038-ee41-427f-8c1b-1bf2310af7cf'
    'mgnl:created': 2022-10-31T19:31:05.734+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:58.994+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': 'f9ecde11-1de6-4073-bea0-e1234553b351'
      'mgnl:created': 2022-10-31T19:31:05.734+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:58.994+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': '5492b402-e943-462f-a429-137f83413d49'
        'mgnl:created': 2022-10-31T19:31:05.734+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:53:35.256+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'bec04dc9-2859-4e0d-9679-be72f7a73d07'
          'mgnl:created': 2022-10-31T19:31:05.734+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:53:35.256+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the senioradvice.com homepage.</p>

            '
  'homecare.org':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '0b4e2336-68e8-4a1a-bce9-d119a99ffb1e'
    'mgnl:created': 2022-10-31T19:31:04.530+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:59.008+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': 'c0dd6ece-04c0-4774-be94-d49f9aa0a0d6'
      'mgnl:created': 2022-10-31T19:31:04.530+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:59.008+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': 'c6b284f4-2090-49aa-8952-9f9025aed158'
        'mgnl:created': 2022-10-31T19:31:04.530+01:00
        'mgnl:createdBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': '6f1366c6-31b8-4ade-88c3-44e35750dacd'
          'mgnl:created': 2022-10-31T19:31:04.530+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is Caring.com homepage.</p>

            '
  'payingforseniorcare.com':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '6e208e34-db17-4b34-b72f-7a2ffe68e6aa'
    'mgnl:created': 2022-10-31T19:31:03.229+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:59.022+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': 'b8edb6df-ae00-411c-81e0-a84c8355b5c4'
      'mgnl:created': 2022-10-31T19:31:03.229+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:59.022+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': 'cdccdec2-7b6d-47a0-9f49-f89f0ec8e897'
        'mgnl:created': 2022-10-31T19:31:03.229+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:52:56.172+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'f4cd5493-8705-474a-b828-cb4ba8280182'
          'mgnl:created': 2022-10-31T19:31:03.229+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:52:56.172+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the payingforseniorcare.com homepage.</p>

            '
  'medicalalert.org':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '980fd7c0-f002-4308-acce-6e41bab32750'
    'mgnl:created': 2022-10-31T19:31:00.979+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:59.045+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '7478f727-33b1-4c4f-973b-a6f6d8b2892c'
      'mgnl:created': 2022-10-31T19:31:00.979+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:59.045+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': 'ed704aa4-83d3-437c-9b9f-70dff1befdbe'
        'mgnl:created': 2022-10-31T19:31:00.979+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:52:32.067+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'af9c4bfb-57a9-45b8-bfc6-2f9661b3d054'
          'mgnl:created': 2022-10-31T19:31:00.979+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:52:32.067+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the medicalalert.org homepage.</p>

            '
  'memorycare.com':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '10163258-fb8b-4a5a-98e4-eceabb0e0dec'
    'mgnl:created': 2022-10-31T19:30:52.592+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:59.067+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '8edc06fb-113a-4b6b-8395-6ace97d8893e'
      'mgnl:created': 2022-10-31T19:30:52.592+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:59.067+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': 'e81a758e-a1d3-4299-896c-b417b03d65e9'
        'mgnl:created': 2022-10-31T19:30:52.592+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:43:51.029+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': '9eba4054-3ade-4d83-a8eb-06522d231732'
          'mgnl:created': 2022-10-31T19:30:52.592+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:43:51.029+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the memorycare.com homepage.</p>

            '
  'assistedliving.org':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '3753b0cc-8339-415c-ae38-9a7c3762b216'
    'mgnl:created': 2022-10-31T19:29:23.974+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2022-10-31T19:59:59.092+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'main':
      'jcr:primaryType': 'mgnl:area'
      'jcr:uuid': 'fcac8d25-71e7-49f3-88f8-8dc5d2e47eee'
      'mgnl:created': 2022-10-31T19:42:05.624+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:42:05.624+01:00
      'mgnl:lastModifiedBy': 'superuser'
    'footer':
      'jcr:primaryType': 'mgnl:area'
      'jcr:uuid': '62aa1ac1-e9c7-495f-8423-7b771ab59bf8'
      'mgnl:created': 2022-10-31T19:42:05.646+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:42:05.646+01:00
      'mgnl:lastModifiedBy': 'superuser'
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '885c7c3c-599d-4a09-af7a-48021f163771'
      'mgnl:created': 2022-10-31T19:29:23.974+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2022-10-31T19:59:59.092+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': '02430c82-bd9b-4266-810a-cffc76c512a4'
        'mgnl:created': 2022-10-31T19:29:23.974+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:42:46.028+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'f3284ce8-e928-48ba-869f-4925e4a6822e'
          'mgnl:created': 2022-10-31T19:29:23.974+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:42:46.028+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the assistedliving.org homepage.</p>

            '
  'seniorhomes.com':
    'hideInNav': false
    'jcr:mixinTypes': ['mgnl:hasVersion']
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '674742d9-8823-4a31-a451-b2078d1c21d1'
    'mgnl:activationStatus': true
    'mgnl:created': 2022-10-31T17:45:42.412+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastActivated': 2022-10-31T18:19:09.328+01:00
    'mgnl:lastActivatedBy': 'superuser'
    'mgnl:lastActivatedVersion': '1.0'
    'mgnl:lastActivatedVersionCreated': 2022-10-31T18:09:44.389+01:00
    'mgnl:lastModified': 2022-10-31T19:59:59.131+01:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/default'
    'noCache': false
    'home':
      'jcr:mixinTypes': ['mgnl:hasVersion']
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '68c9ac21-a800-4862-abdc-4130e21cad5a'
      'mgnl:activationStatus': true
      'mgnl:created': 2022-10-31T17:45:54.509+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastActivated': 2022-10-31T18:19:10.030+01:00
      'mgnl:lastActivatedBy': 'superuser'
      'mgnl:lastActivatedVersion': '1.0'
      'mgnl:lastActivatedVersionCreated': 2022-10-31T18:09:44.538+01:00
      'mgnl:lastModified': 2022-10-31T19:59:59.131+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/geo'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': 'a2f8c32d-0c4e-4f32-b687-877f11a8c67c'
        'mgnl:activationStatus': true
        'mgnl:created': 2022-10-31T17:47:23.288+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2022-10-31T18:19:10.031+01:00
        'mgnl:lastActivatedBy': 'superuser'
        'mgnl:lastModified': 2022-10-31T19:43:19.921+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': '404e9fc0-47c1-41e0-98bb-be6f0e8cc6af'
          'mgnl:activationStatus': true
          'mgnl:created': 2022-10-31T17:47:23.289+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastActivated': 2022-10-31T18:19:10.032+01:00
          'mgnl:lastActivatedBy': 'superuser'
          'mgnl:lastModified': 2022-10-31T19:43:19.921+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'richText': '<p>This is the seniorhomes.com homepage.</p>

            '
