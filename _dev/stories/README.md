# Story App aka (Articles App)



## Getting Started


### Configuring the Story App

Apply ./stories-modified.yaml to /.magnolia/admincentral#app:resources:view;/stories-app/apps/stories.yaml:edit
Publish the changes

### Rest API
Add Read-only access to the rest-anonymous user
All stories are routed under /.rest/delivery/stories/

In Magnolia -> Roles /.magnolia/admincentral#app:security:roles;/rest-anonymous:treeview:
Access control lists
Stories -> Add New -> Read-only, Selected and sub nodes -> /

### Loading Templates

There is a Yaml file located in the ./template-config/pages directory `website.story-templates.yaml` this is used to import the default Story Templates for each site. In the Magnolia interface browse to pages app and at the root of a domain select import. Click upload and browse to this directory and select website.story-templates.yaml to import. You must do this for every site.

### Loading Template Configuration

There are three Yaml files located in the ./template-config directory used to import the configuration enablement of the story templates. In the Magnolia interface browse to storytemplate app and select import. 
Click upload and browse to the ./template-config/ directory and select each of the three yaml files to import.
This must be done one at a time. storytemplates.one-column-template.yaml, storytemplates.two-column-template-left-rail.yaml and storytemplates.two-column-template.yaml are all required to be uploaded.

### Load categorization additions
Import ./categorisation/*.* into there appropriate apps.

./categorisation/caretype.-story..careType..name-.yaml -> Care Types
./categorisation/segmenttheme.Use-story-value--story..theme-.yaml -> Page Theme
./categorisation/segmenttopic.Use-story-value--story..topic-.yaml -> Page Topic

### Load Story Authors and Autor assets

Import ./authors/*yaml into the Story Authors App
Make sure to publish each author once imported.

Import ./authors/assest/dam.authors.xml into the dam Assets
/author/.magnolia/admincentral#app:dam:jcrBrowser
This will create an authors folder with author bio photos and some sample article images.
Feel free to delete the 1153656956ID-7053-Chartsv2-01 and 2021_Wills_Cover or use them in test Stories while you dev.

Publish the authors folder by selecting it and clicking on Publish incl. subnodes

### Load Story schemas

Import /scheams/schemas.StoryArticle.yaml in the  Schema App
/.magnolia/admincentral#app:schema-content:browser;::

### Block overrides.
If for some reason your system fails to load the block configurations for the following code blocks, you can apply the configuration changes manually.

#### image
path /.magnolia/admincentral#app:resources:browser;/content-editor/blocks/image.yaml
apply ./blocks/image
publish

#### text
path /.magnolia/admincentral#app:resources:browser;/content-editor/blocks/text.yaml::
apply ./blocks/text
publish
