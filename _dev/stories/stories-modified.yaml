icon: icon-stories-app
class: info.magnolia.ui.contentapp.configuration.ContentAppDescriptor
appClass: info.magnolia.ui.framework.app.BaseApp

datasource: &datasource
  $type: jcrDatasource
  workspace: stories
  allowedNodeTypes:
    - mgnl:composition
    - mgnl:folder
subApps:
  browser:
    class: info.magnolia.ui.contentapp.configuration.BrowserDescriptor
    workbench:
      contentViews:
        - name: tree
          $type: treeView
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mgnl:composition
          columns: &defaultColumns
            - name: jcrName
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
            - name: urlPath
              label: urlPath
              filterComponent:
                $type: textField
            - name: mgnl:lastModified
              $type: dateColumn
            - name: jcrPublishingStatus
              $type: jcrStatusColumn
        - name: list
          $type: listView
          columns: *defaultColumns
    actions:
      addFolder:
        icon: icon-add-folder
        $type: addNodeAction
        nodeType: mgnl:folder
        availability:
          writePermissionRequired: true
          root: true
          nodeTypes:
            folder: mgnl:folder
          rules:
            notDeleted: &notDeleted
              $type: jcrIsDeletedRule
              negate: true
      moveFolder:
        $type: moveAction
        icon: icon-move
        availability:
          writePermissionRequired: true
          nodeTypes:
            folder: mgnl:folder
          rules:
            notDeleted: *notDeleted
      editFolder:
        dialogId: ui-framework:folder
        icon: icon-edit
        $type: openDialogAction
        availability:
          root: true
          writePermissionRequired: true
          nodeTypes:
            folder: mgnl:folder
          rules:
            notDeleted: *notDeleted
      addStory:
        appName: stories
        subAppName: editor
        icon: icon-add-item
        $type: openDetailSubappAction
        viewType: add
        availability:
          writePermissionRequired: true
          root: true
          nodeTypes:
            folder: mgnl:folder
          rules:
            notDeleted: *notDeleted
      editStory:
        appName: stories
        subAppName: editor
        icon: icon-edit
        $type: openDetailSubappAction
        viewType: edit
        availability:
          writePermissionRequired: true
          nodeTypes:
            article: mgnl:composition
          rules:
            notDeleted: *notDeleted
      renameStory:
        icon: icon-edit
        dialogId: ui-framework:rename
        $type: openDialogAction
        availability:
          writePermissionRequired: true
          nodeTypes:
            article: mgnl:composition
          rules:
            notDeleted: *notDeleted
      moveStory:
        $type: moveAction
        icon: icon-move
        availability:
          writePermissionRequired: true
          multiple: true
          rules:
            notDeleted: *notDeleted
      copy:
        $type: copyContentAction
        icon: icon-copy
        availability:
          multiple: true
          rules:
            copyRule:
              $type: canCopyContentRule
            notDeleted: *notDeleted
      paste:
        $type: pasteContentAction
        icon: icon-paste
        availability:
          root: true
          writePermissionRequired: true
          rules:
            pasteRule:
              $type: canPasteContentRule
            notDeleted: *notDeleted
      delete:
        icon: icon-delete
        $type: markAsDeletedAction
        availability: &notDeletedAvailability
          rules:
            notDeleted: *notDeleted
      publish:
        icon: icon-publish
        $type: jcrCommandAction
        command: publish
        catalog: versioned
        availability:
          rules:
            notDeleted: *notDeleted
            isPublishable: &publishableRule
              $type: jcrPublishableRule
      publishDeletion:
        icon: icon-publish
        $type: jcrCommandAction
        command: publish
        availability:
          multiple: true
          writePermissionRequired: true
          rules:
            isDeleted: &isDeleted
              $type: jcrIsDeletedRule
              negate: false
      publishRecursive:
        catalog: versioned
        command: publish
        icon: icon-publish-incl-sub
        $type: jcrCommandAction
        asynchronous: true
        params:
          recursive: true
        availability:
          writePermissionRequired: true
          rules:
            notDeleted: *notDeleted
            isPublishable: *publishableRule
      unpublish:
        catalog: versioned
        command: unpublish
        icon: icon-unpublish
        $type: jcrCommandAction
        availability:
          writePermissionRequired: true
          rules:
            notDeleted: *notDeleted
            isPublished:
              $type: jcrPublishedRule
      export:
        icon: icon-export
        $type: exportAction
        availability: *notDeletedAvailability
      import:
        icon: icon-import
        $type: openDialogAction
        dialogId: ui-framework-jcr:import
        availability:
          writePermissionRequired: true
          root: true
          rules:
            notDeleted: *notDeleted
      restorePreviousVersion:
        icon: icon-undo
        parentNodeTypeOnly: true
        $type: restorePreviousJcrVersionAction
        availability:
          writePermissionRequired: true
          rules:
            hasVersionsRule: &hasVersionsRule
              class: info.magnolia.ui.availability.rule.JcrHasVersionsRuleDefinition
            isDeleted: *isDeleted
      confirmDeleteStory:
        successActionName: delete
        icon: icon-delete
        $type: confirmationAction
        availability:
          multiple: true
          writePermissionRequired: true
          rules:
            notDeleted: *notDeleted
      confirmDeleteFolder:
        icon: icon-delete
        successActionName: delete
        $type: confirmationAction
        availability:
          multiple: true
          writePermissionRequired: true
          rules:
            notDeleted: *notDeleted
      restoreVersion:
        icon: icon-retrieve-versions
        $type: openDialogAction
        dialogId: ui-framework-jcr:restoreVersion
        availability:
          writePermissionRequired: true
          rules:
            hasVersionsRule: *hasVersionsRule
            isDeleted: *notDeleted
      duplicateStory:
        icon: icon-duplicate
        $type: duplicateNodeAction
        availability:
          writePermissionRequired: true
          nodeTypes:
            article: mgnl:composition
          rules:
            notDeleted: *notDeleted
    actionbar:
      defaultAction: editStory
      sections:
        - name: root
          availability:
            nodes: false
            root: true
            rules:
              notDeleted: *notDeleted
          groups:
            - name: addActions
              items:
                - name: addStory
                - name: addFolder
            - name: editActions
              items:
                - name: editStory
                - name: paste
            - name: publishingActions
              items:
                - name: publish
                - name: publishRecursive
                - name: unpublish
            - name: eximActions
              items:
                - name: export
                - name: import
        - name: deletedStory
          availability:
            nodeTypes:
              article: mgnl:composition
            rules:
              isDeleted: *isDeleted
          groups:
            - name: addActions
              items:
                - name: addStory
                - name: addFolder
            - name: editActions
              items:
                - name: editStory
                - name: renameStory
                - name: confirmDeleteStory
            - name: publishingActions
              items:
                - name: publishDeletion
                - name: unpublish
            - name: eximActions
              items:
                - name: export
            - name: deletedActions
              items:
                - name: restorePreviousVersion
        - name: deletedFolder
          availability:
            nodeTypes:
              folder: mgnl:folder
            rules:
              isDeleted: *isDeleted
          groups:
            - name: addActions
              items:
                - name: addStory
                - name: addFolder
            - name: editActions
              items:
                - name: editFolder
                - name: confirmDeleteFolder
            - name: publishingActions
              items:
                - name: publishDeletion
                - name: unpublish
            - name: eximActions
              items:
                - name: export
            - name: deletedActions
              items:
                - name: restorePreviousVersion
        - name: folder
          availability:
            nodeTypes:
              folder: mgnl:folder
            rules:
              notDeleted: *notDeleted
          groups:
            - name: addActions
              items:
                - name: addStory
                - name: addFolder
            - name: editActions
              items:
                - name: editFolder
                - name: copy
                - name: paste
                - name: moveFolder
                - name: confirmDeleteFolder
            - name: publishingActions
              items:
                - name: publish
                - name: publishRecursive
                - name: unpublish
            - name: eximActions
              items:
                - name: export
                - name: import
        - name: story
          availability:
            nodeTypes:
              article: mgnl:composition
            rules:
              notDeleted: *notDeleted
          groups:
            - name: addActions
              items:
                - name: addStory
                - name: addFolder
            - name: editActions
              items:
                - name: editStory
                - name: renameStory
                - name: duplicateStory
                - name: copy
                - name: paste
                - name: moveStory
                - name: confirmDeleteStory
            - name: publishingActions
              items:
                - name: publish
                - name: publishRecursive
                - name: unpublish
            - name: eximActions
              items:
                - name: export
                - name: import
            - name: versionActions
              items:
                - name: restoreVersion
  editor:
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    subAppClass: info.magnolia.editor.app.ContentEditorDetailSubApp
    footerLayout:
      $type: contentEditorActionLayout
    actions:
      close:
        class: info.magnolia.editor.action.CloseContentEditorActionDefinition
      save:
        class: info.magnolia.editor.action.SaveContentActionDefinition
        availability: &saveActionAvailability
          rules:
            isI18nizedMultiJcrBlock:
              $type: isMultiBlockStructureCompatibleWithI18nConfigRule
      saveAndPublish:
        class: info.magnolia.editor.action.SavePublishContentActionDefinition
        availability: *saveActionAvailability
      copyContent:
        class: info.magnolia.editor.action.CopyContentActionDefinition
        availability:
          rules:
            isNotDefaultLocale:
              class: info.magnolia.editor.action.availability.IsNotDefaultLocaleRuleDefinition
    datasource: *datasource
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mgnl:composition
    form:
      properties:
        title:
          $type: expandingTextField
          i18n: true
          rows: 2
          required: true
          styleName: text-field-large
          label: stories.editor.title.label
          description: stories.editor.title.description
          placeholder: stories.editor.title.placeholder
        lead:
          $type: expandingTextField
          i18n: true
          rows: 2
          required: true
          styleName: text-field-medium
          label: stories.editor.lead.label
          description: stories.editor.lead.description
          placeholder: stories.editor.lead.placeholder
        leadVisual:
          $type: collapsibleCompositeField
          collapsed: true
          label: stories.editor.leadVisual.label
          itemProvider:
            $type: currentItemProvider
          properties:
            visualType:
              label: stories.editor.leadVisual.visualType.label
              $type: switchableField
              field:
                $type: radioButtonGroupField
                layout: horizontal
                styleName: content-editor-switchable #generic to separate def?
                name: visualType
                defaultValue: image
                datasource:
                  $type: optionListDatasource
                  options:
                    - name: image
                      value: image
                      label: stories.editor.leadVisual.visualType.options.image
                    - name: externalImage
                      value: externalImage
                      label: External Image
              itemProvider:
                $type: currentItemProvider
              forms:
                - name: image
                  properties:
                    imageSource:
                      $type: damLinkField
                      i18n: true
                      label: stories.editor.leadVisual.visualType.image.source.label
                      placeholder: stories.editor.leadVisual.visualType.embed.source.image.desc
                      buttonSelectNewLabel: stories.editor.leadVisual.visualType.embed.source.image.select.new.label
                    imageAltText:
                      $type: textField
                      i18n: true
                      label: stories.editor.leadVisual.visualType.image.altText.label
                      placeholder: stories.editor.leadVisual.visualType.image.altText.placeholder
                - name: externalImage
                  properties:
                    externalImageSource:
                      label: Url to image
                      $type: textField   
                    externalAltText:
                      label: Alt Text
                      $type: textField
                      placeholder: stories.editor.leadVisual.visualType.image.altText.placeholder
            caption:
              $type: textField
              i18n: true
              label: stories.editor.leadVisual.caption.label
              placeholder: stories.editor.leadVisual.caption.placeholder
            credit:
              $type: textField
              i18n: true
              label: stories.editor.leadVisual.credit.label
              placeholder: stories.editor.leadVisual.credit.placeholder
        metadata:
          $type: collapsibleCompositeField
          collapsed: true
          label: Metadata
          itemProvider:
            $type: currentItemProvider
          properties:
            metaTitle:
              label: Meta Title
              $type: textField
            metaDescription:
              label: Meta Description
              $type: expandingTextField
              rows: 4
            metaKeywords:
              label: Meta Keywords
              $type: textField
            metaImage:
              label: Meta Image
              $type: textField
            noindex:
              $type: checkBoxField
              label: ''
              buttonLabel: NoIndex
              defaultValue: false
            nofollow:
              $type: checkBoxField
              label: ''
              buttonLabel: NoFollow
              defaultValue: false
            jsonSchemas:
              $type: jcrMultiLinkField
              label: Schemas
              field:
                $type: linkField
                label: Schemas
                datasource:
                  $type: jcrDatasource
                  workspace: schemas
        categorisation:
          $type: collapsibleCompositeField
          collapsed: true
          label: Categorisation
        authorInfo:
          $type: collapsibleCompositeField
          collapsed: true
          label: author(s)
        relatedContent:
          $type: collapsibleCompositeField
          collapsed: true
          itemProvider:
            $type: currentItemProvider
          label: stories.editor.relatedContent.label
          properties:
            stories:
              label: stories.editor.relatedContent.stories.label
              $type: jcrMultiValueField
              buttonSelectAddLabel: stories.editor.relatedContent.stories.add.label
              field:
                $type: linkField
                buttonSelectNewLabel: stories.editor.relatedContent.stories.add.new.label
                chooserId: stories-app:chooserStory
        urlPath:
          label: URL Path
          $type: textField
          required: true
          description: The urlPath to the story
        canonical:
          label: canonical
          $type: textField
        template:
          required: true
        hasAdvertisingDisclosure:
          label: Has Advertising Disclosure
          $type: checkBoxField
          buttonLabel: Select to display advertising disclosure
          defaultValue: false
        blocks:
          $type: multiJcrBlock
          i18n: true
          initialBlock: text
          defaultBlock: image
          blocks:
            - text
            - image
            # - video
            # - externalLink
          itemProvider:
            $type: CompatibleBlockProvider
  