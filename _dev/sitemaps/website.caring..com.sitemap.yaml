'sitemap':
  'enableBreadcrumb': false
  'enableTOC': false
  'includeTopPadding': true
  'jcr:mixinTypes': ['mgnl:hasVersion']
  'jcr:primaryType': 'mgnl:page'
  'jcr:uuid': 'eb601c29-e5d3-4e21-9dbd-2ff750474698'
  'jsonSchemas': []
  'metaTitle': ' Seniorhomes.com Site Map '
  'mgnl:created': 2023-10-11T11:34:40.192-07:00
  'mgnl:createdBy': 'superuser'
  'mgnl:lastModified': 2023-10-18T14:01:56.664-07:00
  'mgnl:lastModifiedBy': 'superuser'
  'mgnl:template': 'spa-lm:pages/1column'
  'nofollow': false
  'noindex': false
  'openGraph': '{}'
  'pageType': 'hub'
  'title': 'Caring.com Site Map '
  'main':
    'jcr:primaryType': 'mgnl:area'
    'jcr:uuid': 'ee0879fd-75d5-40cd-9ecf-************'
    'mgnl:created': 2023-10-11T11:34:40.192-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-18T14:01:56.664-07:00
    'mgnl:lastModifiedBy': 'superuser'
    '00':
      'headingElement': 'h2'
      'headingSize': 'xl'
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '2e04a9e2-39fe-457e-8ed4-5662c30284d6'
      'mgnl:created': 2023-10-13T15:14:56.677-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-16T08:18:33.340-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/heading'
      'title': 'Caring.com SiteMap'
      'titleAlignment': 'left'
      'withContainer': true
    '01':
      'alertStyle': 'default'
      'clientSideRendering': false
      'headingElement': 'h2'
      'hideIfTextIsEmpty': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': 'f5f67d19-5bf1-4833-8b4b-0a51ef09097b'
      'mgnl:created': 2023-10-18T13:55:36.331-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T13:56:21.112-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sectionTitle'
      'text': "<ul>\n\t<li><a href=\"https://www.caring.com/senior-living/assisted-living/\"\
        >Assisted Living</a></li>\n\t<li><a href=\"https://www.caring.com/senior-living/independent-living/\"\
        >Independent Living</a></li>\n\t<li><a href=\"https://www.caring.com/senior-living/memory-care-facilities/\"\
        >Alzheimer&#39;s and Memory Care</a></li>\n</ul>\n"
      'textAlignment': 'left'
      'textColor': 'primary'
      'textColorRange': '900'
      'title': 'Senior Living'
      'titleAlignment': 'left'
      'titleColor': 'primary'
      'titleColorRange': '900'
    '06':
      'alertStyle': 'default'
      'clientSideRendering': false
      'headingElement': 'h2'
      'hideIfTextIsEmpty': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '8da53ea9-bf0d-4909-ba59-8ef02b3ea435'
      'mgnl:created': 2023-10-18T13:57:11.666-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T13:57:11.714-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sectionTitle'
      'text': "<ul>\n\t<li><a href=\"https://www.caring.com/senior-care/in-home-care/\"\
        >In Home Care</a></li>\n\t<li><a href=\"https://www.caring.com/senior-care/home-health-agencies/\"\
        >Home Health Agencies</a></li>\n\t<li><a href=\"https://www.caring.com/senior-care/hospices/\"\
        >Hospice</a></li>\n</ul>\n"
      'textAlignment': 'left'
      'textColor': 'primary'
      'textColorRange': '900'
      'title': 'Senior Care'
      'titleAlignment': 'left'
      'titleColor': 'primary'
      'titleColorRange': '900'
    '07':
      'alertStyle': 'default'
      'clientSideRendering': false
      'headingElement': 'h2'
      'hideIfTextIsEmpty': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': 'ac43614c-5a55-47bf-bf43-a3e6836add9a'
      'mgnl:created': 2023-10-18T13:58:06.001-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T13:58:06.045-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sectionTitle'
      'text': "<ul>\n\t<li><a href=\"https://www.caring.com/articles/dementia-caregiver-story/\"\
        >A 70-Year Love Story Eases the Challenges of Caregiving</a></li>\n\t<li><a\
        \ href=\"https://www.caring.com/articles/midterm-races-to-watch-heller-vs-rosen/\"\
        >Where the Candidates Stand on Senior Citizen Issues: Dean Heller vs. Jacky\
        \ Rosen</a></li>\n\t<li><a href=\"https://www.caring.com/articles/midterm-races-to-watch-sinema-vs-mcsally/\"\
        >Where the Candidates Stand on Senior Issues: Kyrsten Sinema vs. Martha McSally</a></li>\n\
        \t<li><a href=\"https://www.caring.com/articles/caringstars2019/\">Best Senior\
        \ Living: Caring Stars 2019</a></li>\n\t<li><a href=\"https://www.caring.com/articles/caringstars2019-in-home-care/\"\
        >Best In-Home Care: Caring Stars 2019</a></li>\n</ul>\n"
      'textAlignment': 'left'
      'textColor': 'primary'
      'textColorRange': '900'
      'title': 'Articles'
      'titleAlignment': 'left'
      'titleColor': 'primary'
      'titleColorRange': '900'
    '09':
      'alertStyle': 'default'
      'clientSideRendering': false
      'headingElement': 'h2'
      'hideIfTextIsEmpty': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '3cbc689d-b6fa-4170-ba4d-5805999b2fa6'
      'mgnl:created': 2023-10-18T13:59:24.405-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T13:59:24.451-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sectionTitle'
      'text': "<ul>\n\t<li><a href=\"https://www.caring.com/about/mission\">Our Mission</a></li>\n\
        \t<li><a href=\"https://www.caring.com/about/management\">Management Team</a></li>\n\
        \t<li><a href=\"https://www.caring.com/about/editors\">Editors and Writers</a></li>\n\
        \t<li><a href=\"https://www.caring.com/experts\">About our Experts</a></li>\n\
        \t<li><a href=\"https://www.caring.com/about/news\">Caring.com in the News</a></li>\n\
        \t<li><a href=\"https://www.caring.com/about/advertising\">Advertising on\
        \ Caring.com</a></li>\n\t<li><a href=\"https://www.caring.com/about/contact\"\
        >Contact Caring.com</a></li>\n</ul>\n"
      'textAlignment': 'left'
      'textColor': 'primary'
      'textColorRange': '900'
      'title': 'About Caring.com'
      'titleAlignment': 'left'
      'titleColor': 'primary'
      'titleColorRange': '900'
    '08':
      'alertStyle': 'default'
      'clientSideRendering': false
      'headingElement': 'h2'
      'hideIfTextIsEmpty': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '444e28cc-f328-49fb-9cd7-7332a26b5029'
      'mgnl:created': 2023-10-18T13:58:44.902-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T14:00:41.481-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sectionTitle'
      'text': "<ul>\n\t<li><a href=\"https://www.caring.com/scholarship\">Student\
        \ Caregiver Scholarship (#CaringScholar)</a></li>\n\t<li><a href=\"https://www.caring.com/articles/caringstars2018\"\
        >Caring Stars - Best Senior Living Communities</a></li>\n\t<li><a href=\"\
        https://www.caring.com/alzheimers-disease\">Alzheimer&#39;s Disease</a></li>\n\
        \t<li><a href=\"https://www.caring.com/healthy-eating-nutrition\">Senior Health\
        \ and Nutrition</a></li>\n\t<li><a href=\"https://www.caring.com/vision-health\"\
        >Senior Vision</a></li>\n</ul>\n"
      'textAlignment': 'left'
      'textColor': 'primary'
      'textColorRange': '900'
      'title': 'Special Recognition Programs'
      'titleAlignment': 'left'
      'titleColor': 'primary'
      'titleColorRange': '900'
    '0':
      'careTypes': ['e5156057-9c68-46fa-bfea-41b5735d9c20', '98c40926-f8b1-4625-a69f-93786ec59335',
        '17880004-4989-4326-aa36-55904eb088d5', 'ed7ab0e7-802e-4d6f-b858-fef445d4ae3e',
        '4d43c681-b61e-47ac-91bc-d707b14defc6']
      'careTypesHeadingElement': 'h2'
      'careTypesTitle': 'Facilities '
      'careTypesTitleAlignment': 'left'
      'careTypesTitleSize': 'lg'
      'hideCareTypes': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '44a6cd4c-1917-470e-8910-1a952289a6bc'
      'mgnl:created': 2023-10-17T10:09:08.539-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T13:53:40.138-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sitemapIndex'
      'statesHeadingElement': 'h2'
      'statesTitleAlignment': 'left'
      'statesTitleSize': 'lg'
  'param1':
    'enableBreadcrumb': false
    'enableTOC': false
    'includeTopPadding': true
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': 'f1113f83-1460-4de3-88b3-31078d7943eb'
    'jsonSchemas': []
    'mgnl:created': 2023-10-11T11:34:40.192-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-18T08:32:26.425-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/1column'
    'openGraph': '{}'
    'title': ' {params.param1} - Seniorhomes.com Site Map'
    'param2':
      'enableBreadcrumb': false
      'enableTOC': false
      'includeTopPadding': false
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '41dd6f9c-a2dd-40b1-9105-e16acd98af09'
      'jsonSchemas': []
      'mgnl:created': 2023-10-11T11:34:40.192-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T10:23:41.205-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/1column'
      'nofollow': false
      'noindex': false
      'openGraph': '{}'
      'redirectTo': '/sitemap/{params.param1}'
      'title': '{startCase({params.param1} - {params.param2})}'
      'param3':
        'enableBreadcrumb': false
        'enableTOC': false
        'includeTopPadding': true
        'jcr:primaryType': 'mgnl:page'
        'jcr:uuid': 'bac2e6dc-9274-4da0-b0f4-d8fb53969af9'
        'jsonSchemas': []
        'mgnl:created': 2023-10-11T11:34:40.192-07:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2023-10-18T10:14:23.834-07:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:pages/1column'
        'nofollow': false
        'noindex': false
        'openGraph': '{}'
        'title': '{startCase({params.param2} in {params.param3} {stateAbbreviation({params.param1})})}
          - Site Map | Caring.com'
        'main':
          'jcr:primaryType': 'mgnl:area'
          'jcr:uuid': 'da45c37a-9d07-4f0f-bb86-d0745ae5e93f'
          'mgnl:created': 2023-10-11T11:34:40.192-07:00
          'mgnl:createdBy': 'superuser'
          '0':
            'headingElement': 'h2'
            'jcr:primaryType': 'mgnl:component'
            'jcr:uuid': 'f3268972-523a-426a-a9c7-845e965341c7'
            'mgnl:created': 2023-10-11T11:34:40.192-07:00
            'mgnl:createdBy': 'superuser'
            'mgnl:template': 'spa-lm:components/sitemapCareType'
            'titleAlignment': 'left'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': '780f7fb3-8f9a-4a68-8d8d-c1a1bcce0786'
        'mgnl:created': 2023-10-11T11:34:40.192-07:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2023-10-18T10:23:41.205-07:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'clientSideRendering': false
          'enableBreadcrumb': false
          'enableTOC': false
          'includeTopPadding': false
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': '53b33e76-c982-4408-9415-e0a47669e844'
          'jsonSchemas': []
          'mgnl:created': 2023-10-11T11:34:40.192-07:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastModified': 2023-10-18T10:23:41.205-07:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'nofollow': false
          'noindex': false
          'openGraph': '{}'
          'redirectTo': '/sitemap/{param1}'
          'text': '<p>This will redirect to parent state.</p>

            '
          'textAlignment': 'left'
          'title': '{startCase({params.param1} - {params.param2})}'
    'main':
      'jcr:primaryType': 'mgnl:area'
      'jcr:uuid': '1459dd79-95ec-405b-a733-f5b2482b0886'
      'mgnl:created': 2023-10-11T11:34:40.192-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T08:32:26.425-07:00
      'mgnl:lastModifiedBy': 'superuser'
      '00':
        'headingElement': 'h2'
        'jcr:primaryType': 'mgnl:component'
        'jcr:uuid': 'ec242915-1688-435a-afd9-82c44b999f32'
        'mgnl:created': 2023-10-11T11:34:40.192-07:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2023-10-18T08:32:26.425-07:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:components/heading'
        'title': '{startCase({params.param1})} - Site Map | Caring.com'
        'titleAlignment': 'left'
        'withContainer': true
      '0':
        'headingElement': 'h2'
        'jcr:primaryType': 'mgnl:component'
        'jcr:uuid': '88457cf4-d3a1-42f0-a9a4-4444db014b7a'
        'mgnl:created': 2023-10-11T11:34:40.192-07:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastModified': 2023-10-11T11:41:39.229-07:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:components/sitemapState'
        'title': '{param1} - Caring.com Site Map'
        'titleAlignment': 'left'
