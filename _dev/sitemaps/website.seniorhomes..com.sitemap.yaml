'sitemap':
  'enableBreadcrumb': false
  'enableTOC': false
  'includeTopPadding': true
  'jcr:primaryType': 'mgnl:page'
  'jcr:uuid': '731e0cf5-33f4-4aa4-85a2-7e26468915f3'
  'jsonSchemas': []
  'metaTitle': ' Seniorhomes.com Site Map '
  'mgnl:activationStatus': true
  'mgnl:created': 2023-02-06T20:47:53.522+01:00
  'mgnl:createdBy': 'dcreswick'
  'mgnl:lastActivated': 2023-02-22T16:51:35.766+01:00
  'mgnl:lastActivatedBy': 'dcreswick'
  'mgnl:lastModified': 2023-10-18T14:08:41.733-07:00
  'mgnl:lastModifiedBy': 'superuser'
  'mgnl:template': 'spa-lm:pages/2column'
  'openGraph': '{}'
  'pageType': 'hub'
  'title': 'Seniorhomes.com Site Map '
  'main':
    'jcr:primaryType': 'mgnl:area'
    'jcr:uuid': 'b618d6e8-5ad0-4f4b-b039-18b704d54674'
    'mgnl:activationStatus': true
    'mgnl:created': 2023-02-14T19:14:22.978+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastActivated': 2023-02-22T16:51:35.767+01:00
    'mgnl:lastActivatedBy': 'dcreswick'
    'mgnl:lastModified': 2023-10-18T14:08:41.733-07:00
    'mgnl:lastModifiedBy': 'superuser'
    '00':
      'headingElement': 'h2'
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '4fa34744-b9c3-4521-8678-51b4d1445f03'
      'mgnl:activationStatus': true
      'mgnl:created': 2023-02-15T22:06:21.159+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastActivated': 2023-02-22T16:51:35.767+01:00
      'mgnl:lastActivatedBy': 'dcreswick'
      'mgnl:lastModified': 2023-02-15T22:07:01.296+01:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/heading'
      'title': 'Seniorhomes.com Site Map'
      'titleAlignment': 'left'
    '01':
      'careTypes': ['bb82d297-1bbf-4eb0-8162-5ba992d3a7fc', '30ec901e-d3fd-4e0f-94fa-0c2eaa1c7557',
        '523f6456-2819-457d-8663-628a58f3b8a8', 'e5156057-9c68-46fa-bfea-41b5735d9c20',
        '1f07b027-3c94-4277-8d1f-f82faf56ae9c', '83846bef-b54d-4340-b113-38ef41243acc']
      'careTypesHeadingElement': 'h3'
      'careTypesTitle': 'Facilities'
      'careTypesTitleAlignment': 'left'
      'careTypesTitleSize': 'lg'
      'hideCareTypes': false
      'jcr:primaryType': 'mgnl:component'
      'jcr:uuid': '975052ec-2ea7-4d34-ae91-608cfbec3291'
      'mgnl:created': 2023-10-18T14:05:52.341-07:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastModified': 2023-10-18T14:07:31.244-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:components/sitemapIndex'
      'statesHeadingElement': 'h3'
      'statesTitle': 'States'
      'statesTitleAlignment': 'left'
      'statesTitleSize': 'lg'
  'param1':
    'enableBreadcrumb': false
    'enableTOC': false
    'includeTopPadding': true
    'jcr:primaryType': 'mgnl:page'
    'jcr:uuid': '4bfec0c7-67f0-422b-a21b-39e24a5e4340'
    'jsonSchemas': []
    'mgnl:activationStatus': true
    'mgnl:created': 2023-02-14T23:03:36.038+01:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastActivated': 2023-02-17T00:30:01.573+01:00
    'mgnl:lastActivatedBy': 'ninman'
    'mgnl:lastModified': 2023-08-15T11:31:38.826-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'mgnl:template': 'spa-lm:pages/1column'
    'openGraph': '{}'
    'title': ' {params.param1} - Seniorhomes.com Site Map'
    'param2':
      'enableBreadcrumb': false
      'enableTOC': false
      'includeTopPadding': false
      'jcr:primaryType': 'mgnl:page'
      'jcr:uuid': '34f4b72e-b6ec-4bca-b488-41fd86a5047c'
      'jsonSchemas': []
      'mgnl:activationStatus': true
      'mgnl:created': 2023-02-14T23:17:09.299+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastActivated': 2023-02-17T00:30:01.729+01:00
      'mgnl:lastActivatedBy': 'ninman'
      'mgnl:lastModified': 2023-08-15T11:31:38.826-07:00
      'mgnl:lastModifiedBy': 'superuser'
      'mgnl:template': 'spa-lm:pages/1column'
      'openGraph': '{}'
      'title': '{startCase({params.param1} - {params.param2})}'
      'param3':
        'enableBreadcrumb': false
        'enableTOC': false
        'includeTopPadding': true
        'jcr:primaryType': 'mgnl:page'
        'jcr:uuid': '563dec8d-970a-42a8-abf0-0bbbc8a738ff'
        'jsonSchemas': []
        'mgnl:activationStatus': true
        'mgnl:created': 2023-02-14T23:19:57.370+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2023-02-17T00:30:01.881+01:00
        'mgnl:lastActivatedBy': 'ninman'
        'mgnl:lastModified': 2023-10-12T10:09:34.764-07:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:pages/1column'
        'openGraph': '{}'
        'title': '{startCase({params.param3} in {params.param2})}  - Seniorhomes.com
          Site Map'
        'main':
          'jcr:primaryType': 'mgnl:area'
          'jcr:uuid': 'f23ac64d-7064-417b-b2dd-cc076d80d7de'
          'mgnl:activationStatus': true
          'mgnl:created': 2023-02-15T18:22:23.375+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastActivated': 2023-02-17T00:30:01.881+01:00
          'mgnl:lastActivatedBy': 'ninman'
          'mgnl:lastModified': 2023-10-12T10:09:34.764-07:00
          'mgnl:lastModifiedBy': 'superuser'
          '0':
            'headingElement': 'h2'
            'jcr:primaryType': 'mgnl:component'
            'jcr:uuid': 'ac3c6085-6bb6-48cc-b1c2-31d865f36951'
            'mgnl:activationStatus': true
            'mgnl:created': 2023-02-15T18:22:23.377+01:00
            'mgnl:createdBy': 'superuser'
            'mgnl:lastActivated': 2023-02-17T00:30:01.882+01:00
            'mgnl:lastActivatedBy': 'ninman'
            'mgnl:lastModified': 2023-10-12T10:09:34.764-07:00
            'mgnl:lastModifiedBy': 'superuser'
            'mgnl:template': 'spa-lm:components/sitemapCareType'
            'title': 'Param 3 in Param 2 - Seniorhomes.com Site Map'
            'titleAlignment': 'left'
      'main':
        'jcr:primaryType': 'mgnl:area'
        'jcr:uuid': '6daa6b8f-4af2-4e56-919c-ba700d300ccb'
        'mgnl:activationStatus': true
        'mgnl:created': 2023-02-15T16:52:45.378+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2023-02-17T00:30:01.730+01:00
        'mgnl:lastActivatedBy': 'ninman'
        'mgnl:lastModified': 2023-02-15T17:01:23.193+01:00
        'mgnl:lastModifiedBy': 'superuser'
        '0':
          'clientSideRendering': false
          'jcr:primaryType': 'mgnl:component'
          'jcr:uuid': 'd9cb9f08-5e47-42d2-86bb-8f5e5e09dcfc'
          'mgnl:activationStatus': true
          'mgnl:created': 2023-02-15T16:52:45.391+01:00
          'mgnl:createdBy': 'superuser'
          'mgnl:lastActivated': 2023-02-17T00:30:01.730+01:00
          'mgnl:lastActivatedBy': 'ninman'
          'mgnl:lastModified': 2023-02-15T17:01:23.193+01:00
          'mgnl:lastModifiedBy': 'superuser'
          'mgnl:template': 'spa-lm:components/paragraph'
          'text': '<p>{startCase({params.param1} - {params.param2})}</p>

            '
          'textAlignment': 'left'
    'main':
      'jcr:primaryType': 'mgnl:area'
      'jcr:uuid': '75194e44-9bb4-4544-9b32-c210b6804c6a'
      'mgnl:activationStatus': true
      'mgnl:created': 2023-02-15T00:26:14.060+01:00
      'mgnl:createdBy': 'superuser'
      'mgnl:lastActivated': 2023-02-17T00:30:01.573+01:00
      'mgnl:lastActivatedBy': 'ninman'
      'mgnl:lastModified': 2023-02-15T21:37:41.002+01:00
      'mgnl:lastModifiedBy': 'superuser'
      '00':
        'headingElement': 'h2'
        'jcr:primaryType': 'mgnl:component'
        'jcr:uuid': '9959a49a-b400-441f-b4b2-6a8e360f60fa'
        'mgnl:activationStatus': true
        'mgnl:created': 2023-02-15T21:37:19.035+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2023-02-17T00:30:01.574+01:00
        'mgnl:lastActivatedBy': 'ninman'
        'mgnl:lastModified': 2023-02-15T21:37:41.002+01:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:components/heading'
        'title': '{startCase({params.param1})}  - Seniorhomes.com Site Map'
        'titleAlignment': 'left'
      '0':
        'headingElement': 'h2'
        'jcr:primaryType': 'mgnl:component'
        'jcr:uuid': 'dff66a1d-414b-48b0-a5e4-db23fed5ce8e'
        'mgnl:activationStatus': true
        'mgnl:created': 2023-02-15T02:36:17.484+01:00
        'mgnl:createdBy': 'superuser'
        'mgnl:lastActivated': 2023-02-17T00:30:01.574+01:00
        'mgnl:lastActivatedBy': 'ninman'
        'mgnl:lastModified': 2023-02-15T02:36:17.484+01:00
        'mgnl:lastModifiedBy': 'superuser'
        'mgnl:template': 'spa-lm:components/sitemapState'
        'title': '{param1} - Seniorhomes.com Site Map'
        'titleAlignment': 'left'
