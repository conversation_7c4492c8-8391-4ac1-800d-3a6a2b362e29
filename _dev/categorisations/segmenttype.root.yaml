'root':
  'jcr:primaryType': 'mgnl:folder'
  'jcr:uuid': '07c163ca-76f2-4a1f-8d59-f55bcf56de8d'
  'mgnl:created': 2023-10-05T15:32:38.543-07:00
  'mgnl:createdBy': 'superuser'
  'mgnl:lastModified': 2023-10-05T15:32:47.814-07:00
  'mgnl:lastModifiedBy': 'superuser'
  'name': 'root'
  'Provider':
    'category': 'f7400631-4952-441f-98b3-5547fabddce6'
    'dataName': 'provider'
    'description': 'A page that has content giving information about a specific provider
      of senior living or senior care.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'ac02d5d7-17b7-4745-8de1-d6b3148222cd'
    'mgnl:created': 2023-10-02T15:44:13.490-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.924-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Provider'
  'Chain-Detail':
    'category': 'f7400631-4952-441f-98b3-5547fabddce6'
    'dataName': 'chain-detail'
    'description': ' review of a national chain of providers'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '1b3a2135-5fda-4eca-84f6-4d3ff17d8d1a'
    'mgnl:created': 2023-10-05T11:41:46.303-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.927-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Chain Detail'
  'Geo-City':
    'category': '2056e40d-1628-484a-9d93-f43440c36c7c'
    'dataName': 'geo-city'
    'description': 'A page that lists providers in a specific city. Typically has
      other content relevenant to that location (such as cost of care, resources,
      etc.) '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '4c5a946f-88ef-4afc-b313-e4d0faf1f77c'
    'mgnl:created': 2023-10-03T09:25:13.330-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.929-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Geo City'
  'Geo-County':
    'category': '2056e40d-1628-484a-9d93-f43440c36c7c'
    'dataName': 'geo-county'
    'description': 'A page that lists providers in a specific county. Typically has
      other content relevenant to that location (such as cost of care, resources,
      etc.) '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '1cba99a5-ca76-4c42-976b-d2d351e9edb7'
    'mgnl:created': 2023-10-03T09:27:59.503-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.932-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Geo County'
  'Geo-State':
    'category': '2056e40d-1628-484a-9d93-f43440c36c7c'
    'dataName': 'geo-state'
    'description': 'A page that gives information about senior living/care in a state
      - often houses links to geo pages in that state.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'ac1765ef-8750-4bff-a9e3-8a4e16eafb52'
    'mgnl:created': 2023-10-03T09:28:37.889-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.934-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Geo State'
  'Geo-Faceted-Search':
    'category': '2056e40d-1628-484a-9d93-f43440c36c7c'
    'dataName': 'geo-faceted-search'
    'description': 'We do not have this type of page/content today, so not sure what
      we want to call this long-term, but we imagine creating dynamic pages based
      on faceted search. “Pet-Friendly Assisted Living Communities in Dallas”, for
      example.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '1ff34e85-4651-4ec6-8bf2-9971c171cb5b'
    'mgnl:created': 2023-10-05T11:40:14.697-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.935-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Geo Faceted Search'
  'Geo-Near-Me':
    'category': '2056e40d-1628-484a-9d93-f43440c36c7c'
    'dataName': 'geo-near-me'
    'description': 'Pages that load content about a geography based on geo-location
      of user based on IP address. This is a page that would load a list of providers
      like a standard “locale” page based on the user’s IP.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '40da676a-e893-4129-9a53-7fc3cac72069'
    'mgnl:created': 2023-10-05T11:43:09.491-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.936-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Geo Near Me'
  'Search':
    'category': 'fe614b12-c581-46b3-8e3e-53802a9b1641'
    'dataName': 'search'
    'description': 'A unique page that displays the results of a search for senior
      living/care in a specific location (city/zip/etc.) '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'c736c222-d48b-40d2-bc7d-28f700f45af0'
    'mgnl:created': 2023-10-05T11:43:41.207-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.938-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Search'
  'Product-Category-Review':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'product-category-review'
    'description': 'Long-form content that reviews three or more brands (products,
      services, etc.) in a single vertical. This page serves as the “parent” for all
      the content in that vertical. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '59339c47-2e81-43be-84ff-f602975c7e7a'
    'mgnl:created': 2023-10-05T11:44:38.942-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.939-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Product Category Review'
  'Product-Subcategory-Review':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'product-subcategory-review'
    'description': 'A subcategory of a product category. For example this could be
      a page that reviews walk-in soaker tubs which is a sub category of walk in tubs.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'd1d4147f-0a2e-473e-94c0-6991a8dfde02'
    'mgnl:created': 2023-10-05T12:24:22.545-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.941-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Product Subcategory Review'
  'Product-Review':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'product-review'
    'description': 'A page that reviews an individual brand (product, service, etc.).
      Typically will be the child of a category-review page. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '2c956367-d19c-4b51-b4c2-083030a91941'
    'mgnl:created': 2023-10-05T12:25:25.816-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.947-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Product Review'
  'Topical-Authority':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'topical-authority'
    'description': 'Medium-length, supporting content that gives information about
      a specific topic related to a vertical. Typically the child of a category-review. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '53347f7b-fbe0-4f4c-9186-ee509238066a'
    'mgnl:created': 2023-10-05T12:26:09.918-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.950-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Topical Authority'
  'Question-Answer':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'question-answer'
    'description': 'A short / very short piece of content that answers a specific,
      long-tail query. It is created using a specific formula tailored to quickly
      answering a user’s specific question. Typically the child of a category-review. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '6f6133d6-1d14-4f0e-ac22-2e53413b5d09'
    'mgnl:created': 2023-10-05T12:26:59.325-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.951-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Question Answer'
  'Brand-Comparison':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'brand-comparison'
    'description': 'A head to head comparison of two different brands within a specific
      vertical. Typically the child of a category-review. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'b78f3ce6-1b0b-4859-985a-1b04bdc83522'
    'mgnl:created': 2023-10-05T12:27:31.491-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.953-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Brand Comparison'
  'Hub':
    'category': '3f71682e-d6a8-4d2a-a980-49c72fbd1661'
    'dataName': 'hub'
    'description': 'This kind of page doesn’t really exist yet, but you could think
      of it like the caregivers hub but for caregiver wellbeing services. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'cd722e2f-cd29-4bda-b84f-4306b5e4d63b'
    'mgnl:created': 2023-10-05T12:28:07.094-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.954-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Hub'
  'Thank-You-Page':
    'category': '3b6dbcd9-8e83-4830-b515-ebd34708d708'
    'dataName': 'thank-you-page'
    'description': 'An example is the page shown after the consumer submits a review
      or inquiry. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'a89b6721-0de2-4bcb-9bab-c88332fc1cdd'
    'mgnl:created': 2023-10-05T14:09:04.684-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.956-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Thank You Page'
  'Author-Bio':
    'category': '3b6dbcd9-8e83-4830-b515-ebd34708d708'
    'dataName': 'author-bio'
    'description': 'Details of authors and contributors to website content.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '76bcd52c-04fd-4e76-acd4-67c63bd20634'
    'mgnl:created': 2023-10-05T14:09:47.070-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.957-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Author Bio'
  'Home-Page':
    'category': 'c72f7719-8fbd-4156-a12e-2925c6be09dd'
    'dataName': 'home-page'
    'description': 'the home page of a site.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'c4964b19-4468-4eff-981c-15ccdb90ee3f'
    'mgnl:created': 2023-10-05T14:29:40.834-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.959-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Home Page'
  'About-Us':
    'category': 'c72f7719-8fbd-4156-a12e-2925c6be09dd'
    'dataName': 'about-us'
    'description': 'Pages that give information about the organization/site (including
      management team, etc.) '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '5d4e6290-2156-4b14-a262-48f71decf2c0'
    'mgnl:created': 2023-10-05T14:30:20.876-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.961-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'About Us'
  'Terms':
    'category': 'c72f7719-8fbd-4156-a12e-2925c6be09dd'
    'dataName': 'terms'
    'description': 'Terms and conditions, privacy policy, etc.'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '2321a081-8734-4385-b558-964f5a17696f'
    'mgnl:created': 2023-10-05T14:30:44.828-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.967-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Terms'
  'Contact':
    'category': 'c72f7719-8fbd-4156-a12e-2925c6be09dd'
    'dataName': 'contact'
    'description': 'Pages with a contact form or information about contacting the
      site/orgainzation. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'f2293904-b57c-4c21-9a17-d7aeac0fc45c'
    'mgnl:created': 2023-10-05T14:31:19.810-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.969-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Contact'
  'News-Room':
    'category': 'c72f7719-8fbd-4156-a12e-2925c6be09dd'
    'dataName': 'news-room'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '52ee13bc-fe3d-4f98-8b56-d518de831841'
    'mgnl:created': 2023-10-05T14:31:51.645-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.971-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'News Room'
  'Awards-Page':
    'category': '2e07b261-e119-44b2-8b0e-08d0ae3b77dc'
    'dataName': 'awards-page'
    'description': 'A page outlining the winners for specific years'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '5d1d43bb-9374-4f5d-9b86-6c1ebe45920b'
    'mgnl:created': 2023-10-05T14:32:20.999-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.972-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Awards Page'
  'Overview':
    'category': '2e07b261-e119-44b2-8b0e-08d0ae3b77dc'
    'dataName': 'overview'
    'description': 'An overview explaining the program, giving rules, methodology,
      etc. '
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': 'e0bde41f-142c-47d1-9723-271ee51da4e2'
    'mgnl:created': 2023-10-05T14:32:48.648-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.973-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Overview'
  'Cart':
    'category': '2264b8ec-8bd5-4ab7-937b-1880a2185c9e'
    'dataName': 'cart'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '0069efea-136b-4c1b-87af-2a7ee6e629ca'
    'mgnl:created': 2023-10-05T14:33:47.224-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.975-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Cart'
  'Checkout':
    'category': '2264b8ec-8bd5-4ab7-937b-1880a2185c9e'
    'dataName': 'checkout'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '4b5dfaa2-5aad-4077-9cd5-99d89cc38c39'
    'mgnl:created': 2023-10-05T14:34:06.124-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.978-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Checkout'
  'Checkout-Confirmation':
    'category': '2264b8ec-8bd5-4ab7-937b-1880a2185c9e'
    'dataName': 'checkout-confirmation'
    'jcr:primaryType': 'catalog:segmenttype'
    'jcr:uuid': '9156a4ea-2685-47bc-92e1-381aff7e5be0'
    'mgnl:created': 2023-10-05T14:34:30.661-07:00
    'mgnl:createdBy': 'superuser'
    'mgnl:lastModified': 2023-10-05T15:32:57.980-07:00
    'mgnl:lastModifiedBy': 'superuser'
    'name': 'Checkout Confirmation'
