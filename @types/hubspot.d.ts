export {};

declare global {
  interface Window {
    HubSpotConversations?: {
      widget: {
        load: () => void;
        status: () => { loaded: boolean };
      };
      on: (event: string, handler: (payload: unknown) => void) => void;
      off: (event: string, handler: (payload: unknown) => void) => void;
    };
    hsConversationsSettings?: {
      loadImmediately: boolean;
    };
    hsConversationsOnReady?: Array<() => void>;
    hsConversationsCleanup?: () => void;
  }
}
