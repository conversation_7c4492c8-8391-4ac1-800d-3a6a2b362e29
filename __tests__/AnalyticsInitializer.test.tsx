import useInitializeAnalytics from '@hooks/InitializeAnalytics';
import { render } from '@testing-library/react';

import { AnalyticsInitializer } from '../pages/_app';

jest.mock('@hooks/InitializeAnalytics', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    sessionId: 'test-session-id',
    pageSessionId: 'test-page-session-id'
  }))
}));

describe('AnalyticsInitializer', () => {
  const defaultProps = {
    children: jest.fn((data) => (
      <div data-testid="child">{data.sessionId}</div>
    )),
    page: { title: 'Test Page' },
    provider: { name: 'Test Provider' },
    catalog: { id: 'test-catalog' },
    site: { path: 'caring.com' }
  };

  test('initializes analytics with correct parameters', () => {
    render(<AnalyticsInitializer {...defaultProps} />);

    expect(useInitializeAnalytics).toHaveBeenCalledWith({
      page: defaultProps.page,
      provider: defaultProps.provider,
      catalog: defaultProps.catalog,
      site: defaultProps.site
    });
  });

  test('passes session data to children', () => {
    const { getByTestId } = render(<AnalyticsInitializer {...defaultProps} />);

    expect(defaultProps.children).toHaveBeenCalledWith({
      sessionId: 'test-session-id',
      pageSessionId: 'test-page-session-id'
    });

    expect(getByTestId('child')).toHaveTextContent('test-session-id');
  });
});
