import {
  Container,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Radio,
  RadioGroup,
  Stack,
  VStack
} from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { zodResolver } from '@hookform/resolvers/zod';
import { ReportMoveInFormData } from '@services/review';
import Link from 'next/link';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

const ReportMoveInSchema = zod.object({
  token: zod.string(),
  utmParams: zod.string(),
  locationId: zod
    .string()
    .nullable()
    .refine((value) => value !== null, {
      message: 'Please select a community'
    }),
  moveInDate: zod
    .string()
    .min(1, { message: 'Please select the move-in date' }),
  careRecipientName: zod
    .string()
    .min(4, { message: 'Name must be at least 4 characters long' })
});

export type ReportMoveInSchemaType = zod.infer<typeof ReportMoveInSchema>;

interface Props {
  data: ReportMoveInFormData;
  onSuccess: () => void;
}

export default function ReportMoveInForm({ data, onSuccess }: Props) {
  const {
    handleSubmit,
    register,
    setError,
    formState: { errors }
  } = useForm<ReportMoveInSchemaType>({
    mode: 'onSubmit',
    resolver: zodResolver(ReportMoveInSchema)
  });

  const onSubmit = handleSubmit(async (data) => {
    const response = await fetch('/api/move-in', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...data,
        moveInDate: data.moveInDate + 'T00:00:00Z'
      })
    });

    if (response.ok) onSuccess();
    else
      setError('careRecipientName', {
        message:
          "We can't handle your request right now, please try again later"
      });
  });

  return (
    <Container maxWidth="container.xl" marginTop={10}>
      <form onSubmit={onSubmit}>
        <Stack direction={{ base: 'column', lg: 'row' }} marginBottom={20}>
          <Stack width="full" pl={{ base: 2, lg: 6 }} spacing={10}>
            <Heading size="xl">Have you moved in?</Heading>
            <input type="hidden" value={data.token} {...register('token')} />
            <input
              type="hidden"
              value={data.utmParams}
              {...register('utmParams')}
            />
            <FormControl isInvalid={Boolean(errors.locationId)}>
              <FormLabel>
                1. If you or your loved one moved into a community we
                recommended, please select that community and the move-in date
                below, and you’ll be entered into a raffle to get a free month’s
                rent on us!
              </FormLabel>
              <small>
                <Link href="/about/free-rent-move-in-incentive">
                  Full Rules
                </Link>{' '}
                &amp; <Link href="/about/privacy">Privacy Policy</Link>
              </small>
              <RadioGroup>
                <VStack alignItems="flex-start" spacing={6} marginTop={6}>
                  {data.locations.map((location) => (
                    <Radio
                      key={location.id}
                      {...register('locationId')}
                      value={location.id}
                    >
                      {location.name}
                    </Radio>
                  ))}
                </VStack>
              </RadioGroup>
              {errors.locationId && (
                <FormErrorMessage>{errors.locationId.message}</FormErrorMessage>
              )}
            </FormControl>
            <FormControl isInvalid={Boolean(errors.moveInDate)}>
              <FormLabel>2. When did you or your loved one move in?</FormLabel>
              <Input type="date" {...register('moveInDate')} maxWidth={400} />
              {errors.moveInDate && (
                <FormErrorMessage>{errors.moveInDate.message}</FormErrorMessage>
              )}
            </FormControl>
            <FormControl isInvalid={Boolean(errors.careRecipientName)}>
              <FormLabel>
                3. What is the full name of a person that moved into selected
                community?
              </FormLabel>
              <Input
                type="text"
                {...register('careRecipientName')}
                maxWidth={400}
                maxLength={100}
              />
              {errors.careRecipientName && (
                <FormErrorMessage>
                  {errors.careRecipientName.message}
                </FormErrorMessage>
              )}
            </FormControl>
            <Button
              elementAction={ElementActions.INTERNAL_LINK}
              elementName={ElementNames.GENERIC_BUTTON}
              elementType={ElementTypes.BUTTON}
              destinationUrl="/report/thank-you"
              type="submit"
              bg="primary.500"
              _hover={{ bg: 'primary.600' }}
              color="white"
              size="md"
              maxWidth={200}
              width="auto"
            >
              Submit
            </Button>
          </Stack>
        </Stack>
      </form>
    </Container>
  );
}
