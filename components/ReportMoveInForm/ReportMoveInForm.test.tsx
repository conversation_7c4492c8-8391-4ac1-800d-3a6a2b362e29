import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';

import ReportMoveInForm from './ReportMoveInForm';

const data = {
  token: 'token',
  utmParams: 'utmParams',
  locations: [
    { id: '1', name: 'Location 1' },
    { id: '2', name: 'Location 2' }
  ]
};

describe('ReportMoveInForm', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  it('renders locations from data', () => {
    render(<ReportMoveInForm data={data} onSuccess={jest.fn()} />);

    expect(screen.getByLabelText('Location 1')).toBeInTheDocument();
    expect(screen.getByLabelText('Location 2')).toBeInTheDocument();
  });

  it("doesn't submit when form is in an invalid state", async () => {
    render(<ReportMoveInForm data={data} onSuccess={jest.fn()} />);

    fireEvent.click(screen.getByRole('button', { name: 'Submit' }));

    await waitFor(() => {
      expect(screen.getByText('Please select a community')).toBeInTheDocument();

      expect(
        screen.getByText('Please select the move-in date')
      ).toBeInTheDocument();

      expect(
        screen.getByText('Name must be at least 4 characters long')
      ).toBeInTheDocument();
    });
  });

  it('submits when form is valid', async () => {
    const navigateSpy = jest.fn();
    const fetchSpy = jest
      .spyOn(window, 'fetch')
      .mockResolvedValue({ ok: true } as Response);
    const locationLabel = 'Location 1';
    const dateLabel = '2. When did you or your loved one move in?';
    const nameLabel =
      '3. What is the full name of a person that moved into selected community?';

    render(<ReportMoveInForm data={data} onSuccess={navigateSpy} />);

    fireEvent.click(screen.getByLabelText(locationLabel));
    fireEvent.change(screen.getByLabelText(dateLabel), {
      target: { value: '2022-01-01' }
    });
    fireEvent.change(screen.getByLabelText(nameLabel), {
      target: { value: 'John Doe' }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Submit' }));

    await waitFor(() => {
      expect(fetchSpy).toHaveBeenCalledWith('/api/move-in', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: expect.any(String)
      });

      expect(navigateSpy).toBeCalled();
    });
  });
});
