import {
  getReportMoveInLocations,
  ReportMoveInFormData
} from '@services/review/api';
import { GetServerSidePropsContext } from 'next';

export const getServerSideComponentProps = async (
  _,
  context: GetServerSidePropsContext
): Promise<ReportMoveInFormData | null> => {
  const { token } = context.query;
  const parsedToken = token?.toString() || '';
  const utmParams = context.req.url?.search.toString() || '';
  const response = await getReportMoveInLocations(parsedToken);

  if (!response) {
    return { locations: [], token: parsedToken, utmParams };
  }

  return { locations: response?.locations, token: parsedToken, utmParams };
};
