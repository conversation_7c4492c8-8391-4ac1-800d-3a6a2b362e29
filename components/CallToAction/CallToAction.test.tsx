import { fireEvent, render, screen } from '@utils/test-utils';
import React from 'react';

import CallToAction, { CallToActionProps } from './CallToAction';

jest.mock('~/contexts/ModalContext');

import { useModalDispatch } from '~/contexts/ModalContext';

describe('CallToAction', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  const mockShowModal = jest.fn();
  const mockUseModalDispatch = useModalDispatch as jest.Mock;
  mockUseModalDispatch.mockReturnValue({ showModal: mockShowModal });

  const inquiryId = 'exampleInquiryId';
  const url = 'https://www.google.com';
  const phone = '+1234567890';

  beforeEach(() => {
    mockShowModal.mockClear();
  });

  it('renders button with text and solid state', () => {
    const mockCta: CallToActionProps = {
      text: 'Example CTA',
      textColor: 'white',
      bgColor: 'primary',
      isInquiry: true,
      inquiryId: inquiryId,
      type: 'tel',
      state: 'solid',
      behavior: '_self',
      rel: ['noreferrer'],
      url: url
    };
    render(<CallToAction {...mockCta} />);
    const buttonElement = screen.getByText(/Example CTA/i);
    expect(buttonElement).toBeInTheDocument();
    buttonElement.click();
    expect(mockShowModal).toHaveBeenCalledWith(inquiryId);
  });

  it('renders link with ghost state', () => {
    const mockCta: CallToActionProps = {
      text: 'Example CTA',
      isInquiry: false,
      inquiryId: 'exampleInquiryId',
      state: 'ghost',
      url: url
    };
    render(<CallToAction {...mockCta} />);
    const buttonElement = screen.getByText(/Example CTA/i);
    expect(buttonElement).toBeInTheDocument();
  });

  it('renders link with outline state', () => {
    const mockCta: CallToActionProps = {
      text: 'Example CTA',
      isInquiry: false,
      inquiryId: inquiryId,
      state: 'outline',
      url: url
    };
    render(<CallToAction {...mockCta} />);
    const linkElement = screen.getByText(/Example CTA/i);
    expect(linkElement).toBeInTheDocument();
  });

  it('renders link with tel type and opener rel', () => {
    render(
      <CallToAction text="Google" url={phone} rel={['opener']} type="tel" />
    );
    const linkElement = screen.getByRole('link', { name: /google/i });
    expect(linkElement).toHaveAttribute('href', `tel:${phone}`);
    expect(linkElement).toHaveAttribute('rel', 'opener');
  });

  it('renders link with href, _blank target and noopener rel', () => {
    render(
      <CallToAction
        text="Google"
        url={url}
        behavior="_blank"
        inquiryId={inquiryId}
        rel={['noopener']}
      />
    );
    const linkElement = screen.getByRole('link', { name: /google/i });
    expect(linkElement).toHaveAttribute('href', url);
    expect(linkElement).toHaveAttribute('target', '_blank');
    expect(linkElement).toHaveAttribute('rel', 'noopener');
    fireEvent.click(screen.getByRole('link'));
    expect(mockShowModal).toHaveBeenCalledWith(inquiryId);
  });

  it('renders link with href, _self target and external rel', () => {
    render(
      <CallToAction
        text="Google"
        url={url}
        behavior="_self"
        rel={['external']}
      />
    );
    const linkElement = screen.getByRole('link', { name: /google/i });
    expect(linkElement).toHaveAttribute('href', 'https://www.google.com');
    expect(linkElement).toHaveAttribute('target', '_self');
    expect(linkElement).toHaveAttribute('rel', 'external');
  });

  it('renders link with sms type, _parent target and noreferrer rel', () => {
    render(
      <CallToAction
        text="Google"
        url={phone}
        behavior="_parent"
        rel={['noreferrer']}
        type="sms"
      />
    );
    const linkElement = screen.getByRole('link', { name: /google/i });
    expect(linkElement).toHaveAttribute('href', `sms:${phone}`);
    expect(linkElement).toHaveAttribute('target', '_parent');
    expect(linkElement).toHaveAttribute('rel', 'noreferrer');
  });

  it('renders link with mailto type, _top target and nofollow rel', () => {
    render(
      <CallToAction
        text="Google"
        url="<EMAIL>"
        behavior="_top"
        rel={['nofollow']}
        type="mailto"
      />
    );
    const linkElement = screen.getByRole('link', { name: /google/i });
    expect(linkElement).toHaveAttribute('href', 'mailto:<EMAIL>');
    expect(linkElement).toHaveAttribute('target', '_top');
    expect(linkElement).toHaveAttribute('rel', 'nofollow');
  });

  it('renders button with icon', () => {
    let { container } = render(
      <CallToAction text="Call" icon="MdPhone" isInquiry={true} />
    );
    const icons = container.getElementsByClassName('chakra-button__icon');
    expect(icons.length).toBe(1);
    const svg = icons[0].getElementsByTagName('svg');
    expect(svg[0]).toBeInTheDocument();
  });
});
