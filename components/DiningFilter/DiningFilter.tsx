import CheckboxInput from '@components/CheckboxInput';

interface DiningFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const diningFilterItems = [
  {
    value: 'dining-room',
    label: 'Dining Room'
  },
  {
    value: 'guest-meals',
    label: 'Guest Meals'
  },
  {
    value: 'room-services',
    label: 'Room Service'
  },
  {
    value: 'special-diets',
    label: 'Special Diets Supported'
  }
];

function DiningFilter({ onChange, value }: DiningFilterProps) {
  return (
    <CheckboxInput
      name="dining"
      onChange={onChange}
      items={diningFilterItems}
      value={value}
    />
  );
}

export default DiningFilter;
