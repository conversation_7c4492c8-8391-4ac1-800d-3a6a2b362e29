import { Box, Heading, Stack, Text, VStack } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const ReviewStars = dynamic(() => import('@components/ReviewStars'));
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { StringToIconKeys } from '@components/RenderIcon';
import { useContext } from 'react';

import SiteContext from '~/contexts/SiteContext';
import { Metadata } from '~/types/Magnolia';
import { pluralize } from '~/utils/strings';

import ReviewWidgetEmptyState from './ReviewWidgetEmptyState';
import WriteAReviewLink from './WriteAReviewLink';

interface Props {
  averageRating: string;
  reviewCount: number;
  provider?: string;
  includeCountOfReview?: boolean;
  includeAggregateReview?: boolean;
  includeFallbackIfNoReviewAvailable?: boolean;
  icon?: StringToIconKeys;
  text?: string;
  reviewGuidelinesURL?: string;
  contactUsURL?: string;
  defaultOpenModal?: boolean;
  ratingColor?: string;
  ratingColorRange?: string;
  metadata: Pick<Metadata, '@id'>;
}

const ReviewWidget: React.FC<Props> = ({
  includeCountOfReview = true,
  includeAggregateReview = true,
  includeFallbackIfNoReviewAvailable = true,
  averageRating,
  reviewCount,
  provider,
  icon,
  text,
  reviewGuidelinesURL,
  contactUsURL,
  defaultOpenModal = false,
  ratingColor = 'tertiary',
  ratingColorRange = '400',
  metadata
}) => {
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path || '';
  if (includeFallbackIfNoReviewAvailable && reviewCount === 0)
    return <ReviewWidgetEmptyState />;
  const pluralizedReviewCount = pluralize(reviewCount, 'review');
  switch (domain) {
    case 'caring.com':
      return (
        <Stack direction="row">
          {includeAggregateReview && (
            <Stack align="start" width={'100%'}>
              {provider && (
                <WriteAReviewLink
                  providerName={provider}
                  icon={icon}
                  text={text}
                  buttonSize="lg"
                  fontSize="md"
                  colorScheme="secondary"
                  px={6}
                  mb={{ base: '5', lg: '7' }}
                  width={{ base: '100%', lg: 'min-content' }}
                  reviewGuidelinesURL={reviewGuidelinesURL}
                  contactUsURL={contactUsURL}
                  defaultOpenModal={defaultOpenModal}
                  metadata={metadata}
                  elementAction={ElementActions.OPEN_MODAL}
                  elementName={ElementNames.WRITE_A_REVIEW}
                  elementType={ElementTypes.BUTTON}
                />
              )}
              <Stack direction="row" align="center">
                <Heading
                  as="p"
                  size={{ base: 'md', lg: 'lg' }}
                  aria-label={`${averageRating} star rating`}
                  color={`${ratingColor}.${ratingColorRange}`}
                >
                  {averageRating}
                </Heading>
                <Box lineHeight={1}>
                  <ReviewStars
                    rating={averageRating}
                    size={{ base: '6', lg: '8' }}
                    color={`${ratingColor}.${ratingColorRange}`}
                  />
                </Box>
                {includeCountOfReview && (
                  <Text color="gray.800" fontSize={{ base: 'sm', lg: 'md' }}>
                    ({reviewCount} {pluralizedReviewCount})
                  </Text>
                )}
              </Stack>
            </Stack>
          )}
        </Stack>
      );
    default:
      return (
        <Stack direction="row" alignItems="center">
          {includeAggregateReview && (
            <VStack align="start">
              <Heading
                as="p"
                size="3xl"
                aria-label={`${averageRating} star rating`}
                color={`${ratingColor}.${ratingColorRange}`}
              >
                {averageRating}
              </Heading>
              <Box alignSelf="center">
                <ReviewStars
                  rating={averageRating}
                  size="3.5"
                  color={`${ratingColor}.${ratingColorRange}`}
                />
              </Box>
            </VStack>
          )}
          <VStack align="start" spacing="2.5">
            {includeCountOfReview && (
              <Text color="gray.800" fontSize="lg">
                {reviewCount} {pluralizedReviewCount}
              </Text>
            )}
            {provider && (
              <WriteAReviewLink
                providerName={provider}
                icon={icon}
                text={text}
                fontSize="sm"
                reviewGuidelinesURL={reviewGuidelinesURL}
                contactUsURL={contactUsURL}
                defaultOpenModal={defaultOpenModal}
                metadata={metadata}
                elementAction={ElementActions.OPEN_MODAL}
                elementName={ElementNames.WRITE_A_REVIEW}
                elementType={ElementTypes.BUTTON}
              />
            )}
          </VStack>
        </Stack>
      );
  }
};

export default ReviewWidget;
