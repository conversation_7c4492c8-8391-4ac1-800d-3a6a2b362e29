import { Box, Heading, Stack } from '@chakra-ui/layout';
import ReviewStars from '@components/ReviewStars';

const ProviderDetailsReviewsEmptyState: React.FC<{}> = () => {
  return (
    <Stack direction="row" alignItems="center">
      <Heading as="p" size="md" flexShrink="0">
        0 Reviews
      </Heading>
      <Box lineHeight={1}>
        <ReviewStars
          rating={'0'}
          size={{ base: '6', lg: '8' }}
          color="tertiary.400"
        />
      </Box>
    </Stack>
  );
};

export default ProviderDetailsReviewsEmptyState;
