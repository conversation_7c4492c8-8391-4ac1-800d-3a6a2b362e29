import { Flex, Text } from '@chakra-ui/react';
import { MdOutlineStarBorder, MdStar } from 'react-icons/md';

import CheckboxInput from '../CheckboxInput';

interface ReviewsFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

const renderIcons = (filled: number) => {
  const icons: JSX.Element[] = [];

  for (let i = 0; i < 5; i++) {
    i < filled
      ? icons.push(<MdStar key={i} size={20} />)
      : icons.push(<MdOutlineStarBorder key={i} size={20} />);
  }

  return icons;
};

export const reviewsFilterItems = [
  {
    value: '4-plus',
    component: (
      <Flex alignItems="center" sx={{ lineHeight: 0.1 }} color="gray.500">
        {renderIcons(4)}
        <Text ml={1}>4+</Text>
      </Flex>
    )
  },
  {
    value: '3-plus',
    component: (
      <Flex alignItems="center" sx={{ lineHeight: 0.1 }} color="gray.500">
        {renderIcons(3)}
        <Text ml={1}>3+</Text>
      </Flex>
    )
  }
];

function ReviewsFilter({ onChange, value = [] }: ReviewsFilterProps) {
  return (
    <CheckboxInput
      name="reviews"
      onChange={onChange}
      items={reviewsFilterItems}
      value={value}
    />
  );
}

export default ReviewsFilter;
