import { GridItem } from '@chakra-ui/layout';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';

import { Provider } from '~/contexts/Provider';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { modifyTrailingSlash } from '~/utils/modifyTrailingSlash';
import { calculateProviderMinPrice } from '~/utils/providers';

import SearchResult from '../Search/SearchResult';

export interface ProvidersNearMeCardsProps {
  providers: Provider[];
  enableTrailingSlash?: boolean;
  numberOfItemsPerRow?: string;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  displayBadges?: boolean;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  readMoreButton?: string;
  modalId?: string;
  requestInfoButtonColorScheme?: string;
  learnMoreButtonColorScheme?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  queryId?: string;
  listId?: string;
  dontOpenInNewTab: boolean;
}

const ProvidersNearMeCards = ({
  providers,
  enableTrailingSlash = false,
  numberOfItemsPerRow = '3',
  providerTitleColor,
  providerTitleColorRange,
  displayBadges = false,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton = false,
  requestInfoButtonText,
  readMoreButton,
  modalId,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor,
  ratingStarsColorRange,
  queryId = '',
  listId = '',
  dontOpenInNewTab
}: ProvidersNearMeCardsProps): JSX.Element => {
  const showPrice = useInquiryFormSubmitted();
  const { getProviderDetailsPath } = useTenantFunctions();

  return (
    <>
      {providers.map((result) => {
        const minPrice = calculateProviderMinPrice({ result });
        const path = modifyTrailingSlash(
          enableTrailingSlash,
          getProviderDetailsPath(result)
        );
        const formattedAddress = `${result.address?.street}, <br/> ${result.address?.city}, ${result.address?.state} ${result.address?.zipCode}`;
        return (
          <GridItem
            colSpan={{
              base: 12,
              md: 6,
              lg: Number(numberOfItemsPerRow)
            }}
            key={result.id}
            display="grid"
            gridAutoRows="1fr"
          >
            <SearchResult
              id={result.id}
              legacyId={result.legacyId ?? result.id}
              title={result.name}
              address={formattedAddress}
              images={result?.images ?? []}
              averageRating={result.averageRating}
              reviewCount={result.reviewCount}
              price={minPrice}
              showPrice={showPrice}
              element={'p'}
              path={path}
              caringStars={result.awards || []}
              providerTitleColor={providerTitleColor}
              providerTitleColorRange={providerTitleColorRange}
              displayBadges={displayBadges}
              displayLearnMoreButton={displayLearnMoreButton}
              learnMoreButtonText={learnMoreButtonText}
              displayRequestInfoButton={displayRequestInfoButton}
              requestInfoButtonText={requestInfoButtonText}
              readMoreButton={readMoreButton}
              modalId={modalId}
              requestInfoButtonColorScheme={requestInfoButtonColorScheme}
              learnMoreButtonColorScheme={learnMoreButtonColorScheme}
              ratingStarsColor={ratingStarsColor}
              ratingStarsColorRange={ratingStarsColorRange}
              queryId={queryId}
              listId={listId}
              // Every provider returned in the nearby search results is 'Verified'. If this changes, we'll need to pass through the props needed to determine if the provider is 'Verified'.
              showVerifiedBadge={true}
              dontOpenInNewTab={dontOpenInNewTab}
            />
          </GridItem>
        );
      })}
    </>
  );
};

export default ProvidersNearMeCards;
