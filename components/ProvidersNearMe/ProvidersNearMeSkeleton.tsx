import { GridItem } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const SearchResultSkeleton = dynamic(
  () => import('@components/Search/SearchResultSkeleton')
);

const ProvidersNearMeSkeleton = (): JSX.Element => {
  return (
    <>
      {Array.from({ length: 8 }).map((_, index) => (
        <GridItem colSpan={3} key={index}>
          <SearchResultSkeleton />
        </GridItem>
      ))}
    </>
  );
};

export default ProvidersNearMeSkeleton;
