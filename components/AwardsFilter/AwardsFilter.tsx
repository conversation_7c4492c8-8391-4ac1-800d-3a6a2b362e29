import { Box } from '@chakra-ui/react';
import CheckboxInput from '@components/CheckboxInput';
import Tooltip from '@components/Tooltip/Tooltip';
import { IconType } from 'react-icons';

interface AwardsFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
  tooltipText?: string;
  tooltipIcon?: IconType;
}

export const awardsFilterItems = [
  {
    value: 'caring-stars-winners',
    label: 'Caring Stars Award Winners',
    component: (
      <Box display="flex" alignItems="center" gap={2}>
        <span>Caring Stars Award Winners</span>
      </Box>
    )
  }
];

function AwardsFilter({
  onChange,
  value,
  tooltipText,
  tooltipIcon
}: AwardsFilterProps) {
  const itemsWithTooltip = tooltipText
    ? awardsFilterItems.map((item) => ({
        ...item,
        component: (
          <Box display="flex" alignItems="center" gap={2}>
            <span>Caring Stars Award Winners</span>
            <Tooltip text={tooltipText} icon={tooltipIcon} />
          </Box>
        )
      }))
    : awardsFilterItems;

  return (
    <CheckboxInput
      name="awards"
      onChange={onChange}
      items={itemsWithTooltip}
      value={value}
    />
  );
}

export default AwardsFilter;
