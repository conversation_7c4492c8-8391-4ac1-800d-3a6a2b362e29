import { ChakraProvider, theme } from '@chakra-ui/react';
import { render, screen } from '@testing-library/react';
import {
  setDesktopScreen,
  setMobileScreen
} from '@utils/test-utils/test-utils';

import Heading from './Heading';

const mockUseBreakpointValue = jest.fn();
jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: (...args) => mockUseBreakpointValue(...args)
}));

const customRender = (ui: React.ReactElement) => {
  return render(<ChakraProvider theme={theme}>{ui}</ChakraProvider>);
};

//Test Heading
describe('Heading', () => {
  beforeEach(() => {
    setMobileScreen();
    mockUseBreakpointValue.mockReturnValue(true);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('renders nothing when title is empty', () => {
      const { container } = customRender(<Heading title="" />);
      expect(container.firstChild?.childNodes.length).toBe(0);
    });

    it('renders title with default settings', () => {
      customRender(<Heading title="Test Heading" />);

      const heading = screen.getByRole('heading');
      expect(heading).toHaveTextContent('Test Heading');
      expect(heading.tagName.toLowerCase()).toBe('h2');
    });

    it('renders HTML content in title', () => {
      customRender(<Heading title="Test <strong>Heading</strong>" />);

      const heading = screen.getByRole('heading');
      const strongElement = heading.querySelector('strong');
      expect(strongElement).toHaveTextContent('Heading');
    });
  });

  describe('heading elements', () => {
    it.each(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] as const)(
      'renders as %s when specified',
      (element) => {
        customRender(<Heading title="Test" headingElement={element} />);
        const heading = screen.getByRole('heading');
        expect(heading.tagName.toLowerCase()).toBe(element);
      }
    );
  });

  describe('responsive sizing', () => {
    it('uses mobile size on mobile screens', () => {
      mockUseBreakpointValue.mockReturnValue(true);
      customRender(
        <Heading title="Test" mobileHeadingSize="sm" headingSize="xl" />
      );

      const heading = screen.getByRole('heading');
      expect(heading.classList.toString()).toContain('chakra-heading');
    });

    it('uses desktop size on desktop screens', () => {
      mockUseBreakpointValue.mockReturnValue(false);
      customRender(
        <Heading title="Test" mobileHeadingSize="sm" headingSize="xl" />
      );

      setDesktopScreen();

      const heading = screen.getByRole('heading');
      expect(heading.classList.toString()).toContain('chakra-heading');
    });
  });

  describe('container', () => {
    it('renders without container when specified', () => {
      const { container } = customRender(
        <Heading title="Test" withContainer={false} />
      );
      expect(
        container.querySelector('.chakra-container')
      ).not.toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('uses custom id when provided', () => {
      customRender(<Heading title="Test" id="custom-id" />);
      const heading = screen.getByRole('heading');
      expect(heading).toHaveAttribute('id', 'custom-id');
    });

    it('generates id from title when not provided', () => {
      customRender(<Heading title="Test Heading" />);
      const heading = screen.getByRole('heading');
      expect(heading).toHaveAttribute('id', 'test-heading');
    });
  });
});
