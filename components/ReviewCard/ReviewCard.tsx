import { Button, ButtonProps } from '@chakra-ui/button';
import { Divider, HStack, Text, TextProps, VStack } from '@chakra-ui/layout';
import { ComponentWithAs } from '@chakra-ui/system';
import { Tag } from '@chakra-ui/tag';
import RatingStars from '@components/RatingStars';
import { safeFormat } from '@utils/dates';

interface ReviewCardProps {
  children?: React.ReactNode;
}

interface CompoundComponents {
  Button: typeof ReviewButton;
  CareType: typeof CareTypeTag;
  Content: typeof ReviewContent;
  Link: typeof ReviewLink;
  Response: typeof ProviderResponse;
  Section: typeof ReviewSection;
  Summary: typeof ReviewSummary;
}

const BaseButton: ComponentWithAs<'button', ButtonProps> = ({
  children,
  ...rest
}) => {
  return (
    <Button
      colorScheme="secondary"
      size="sm"
      variant="outline"
      fontSize="xs"
      fontWeight="bold"
      lineHeight="150%"
      {...rest}
    >
      {children}
    </Button>
  );
};

const ReviewButton = ({
  children,
  onClick
}: {
  children: React.ReactNode;
  onClick: ButtonProps['onClick'];
}) => {
  return <BaseButton onClick={onClick}>{children}</BaseButton>;
};

const ReviewLink = ({
  children,
  href,
  color
}: {
  children: React.ReactNode;
  href: string;
  color?: string;
}) => {
  return (
    <BaseButton as="a" href={href} color={color}>
      {children}
    </BaseButton>
  );
};

const CareTypeTag = ({
  color = 'blue.600',
  children
}: {
  color?: string;
  children: TextProps['children'];
}) => {
  return (
    <Text
      casing="uppercase"
      color={color}
      fontSize="xs"
      fontWeight="normal"
      lineHeight="150%"
    >
      {children}
    </Text>
  );
};

const ReviewSummary = ({
  reviewer,
  tag,
  rating,
  date,
  starColor
}: {
  reviewer: string;
  tag: string;
  rating: number;
  date: string;
  starColor: string;
}) => {
  const formattedDate = safeFormat(date, 'MMMM d, yyy');

  return (
    <VStack spacing="2" align="flex-start">
      <VStack spacing="2px" align="flex-start">
        <Text fontSize="md" fontWeight="bold" lineHeight="120%">
          {reviewer}
        </Text>

        <HStack
          alignItems="center"
          spacing="3"
          wrap="wrap"
          divider={
            <Divider
              orientation="vertical"
              h="12px"
              borderColor="gray.800"
              opacity="1"
              aria-hidden="true"
            />
          }
        >
          <RatingStars rating={rating} starColor={starColor} />

          <Text
            as="time"
            dateTime={date}
            fontSize="xs"
            fontWeight="normal"
            lineHeight="150%"
            whiteSpace="nowrap"
          >
            {formattedDate}
          </Text>
        </HStack>
      </VStack>

      <Tag color="gray.900" background="gray.200">
        {tag}
      </Tag>
    </VStack>
  );
};

const ReviewSection = ({
  children,
  title
}: {
  children: React.ReactNode;
  title?: string;
}) => {
  return (
    <VStack spacing="4" align="stretch">
      {title && (
        <Text fontSize="sm" fontWeight="bold" lineHeight="120%">
          {title}
        </Text>
      )}

      {children}
    </VStack>
  );
};

const ReviewContent = ({
  children,
  noOfLines,
  title
}: {
  children: React.ReactNode;
  title?: string;
  noOfLines?: TextProps['noOfLines'];
}) => {
  return (
    <ReviewSection title={title}>
      <Text noOfLines={noOfLines}>{children}</Text>
    </ReviewSection>
  );
};

const ProviderResponse = ({
  children,
  title = 'Provider response',
  noOfLines
}: {
  children: React.ReactNode;
  title?: string;
  noOfLines?: TextProps['noOfLines'];
}) => {
  return (
    <ReviewSection title={title}>
      <Text
        borderLeftColor="gray.200"
        borderLeftWidth="8px"
        paddingLeft="4"
        fontSize="sm"
        noOfLines={noOfLines}
      >
        {children}
      </Text>
    </ReviewSection>
  );
};

const ReviewCard: React.FC<ReviewCardProps> & CompoundComponents = ({
  children
}) => {
  return (
    <VStack
      as="article"
      align="flex-start"
      bg="white"
      borderRadius="xl"
      boxShadow="lg"
      p="5"
      spacing="4"
    >
      {children}
    </VStack>
  );
};

ReviewCard.Button = ReviewButton;
ReviewCard.CareType = CareTypeTag;
ReviewCard.Content = ReviewContent;
ReviewCard.Link = ReviewLink;
ReviewCard.Response = ProviderResponse;
ReviewCard.Section = ReviewSection;
ReviewCard.Summary = ReviewSummary;

export default ReviewCard;
