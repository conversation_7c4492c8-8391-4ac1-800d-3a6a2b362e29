import { render, screen } from '@testing-library/react';

import axe from '~/axe-helper';

import ReviewCard from './ReviewCard';

const mockReview = {
  reviewer: '<PERSON>',
  careType: 'Assisted Living',
  content: 'This is a review',
  date: '2023-03-12T15:41:11.000Z',
  provider: 'Provider Name',
  rating: 4,
  response: 'This is a response',
  tag: 'This is a tag'
};

const formattedDate = 'March 12, 2023';

describe('ReviewCard', () => {
  it('renders the card without violations', async () => {
    const { container } = render(
      <ReviewCard>
        <ReviewCard.CareType>{mockReview.careType}</ReviewCard.CareType>
        <ReviewCard.Summary
          date={mockReview.date}
          rating={mockReview.rating}
          reviewer={mockReview.reviewer}
          tag={mockReview.tag}
          starColor="yellow"
        />
        <ReviewCard.Content title={`Review of ${mockReview.provider}`}>
          {mockReview.content}
        </ReviewCard.Content>
        <ReviewCard.Response>{mockReview.response}</ReviewCard.Response>
      </ReviewCard>
    );

    expect(screen.getByText(mockReview.reviewer)).toBeVisible();
    expect(screen.getByText(mockReview.careType)).toBeVisible();
    expect(screen.getByText(mockReview.content)).toBeInTheDocument();
    expect(screen.getByText(formattedDate)).toBeVisible();
    expect(screen.getByText(`Review of ${mockReview.provider}`)).toBeVisible();
    expect(screen.getByText('4.0')).toBeVisible();
    expect(screen.getByText(mockReview.response)).toBeInTheDocument();
    expect(screen.getByText(mockReview.tag)).toBeVisible();
    expect(await axe(container)).toHaveNoViolations();
  });
});
