import { render } from '@testing-library/react';
import { DAM_IMAGE_FIXTURE } from '@utils/test-utils/mocks/images';

import {
  DamChooserImage,
  ExternalImage,
  ExternalSourceImage
} from '~/types/Magnolia';

import HeroImage, { HeroImageProps } from './HeroImage';

const dummyImage: ExternalImage = {
  '@id': 'img1',
  imageAlt: 'image 1',
  imageUrl: 'https://example.com/image1.jpg',
  '@name': '',
  '@path': '',
  '@nodeType': '',
  'mgnl:lastModified': '',
  'mgnl:template': '',
  'mgnl:created': '',
  '@nodes': []
};
const dummyExternalSource: ExternalSourceImage = {
  '@name': '',
  '@path': '',
  '@id': '',
  '@nodeType': '',
  'mgnl:lastModified': '',
  'mgnl:template': '',
  'mgnl:created': '',
  '@nodes': []
};
const dummyDamChooser: DamChooserImage = {
  '@name': '',
  '@path': '',
  '@id': '',
  '@nodeType': '',
  'mgnl:lastModified': '',
  'mgnl:template': '',
  'mgnl:created': '',
  '@nodes': []
};

jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: jest.fn()
}));

describe('HeroImage', () => {
  function renderHero(props: HeroImageProps) {
    return render(<HeroImage {...props} />);
  }

  it('should render without content', () => {
    const { queryAllByRole } = renderHero({});
    expect(queryAllByRole('img')).toHaveLength(0);
  });

  it('should not render invalid external source images', () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const externalSource: ExternalSourceImage = {
      ...dummyExternalSource,
      '@nodes': ['img1', 'img2'],
      img1: {
        ...dummyImage,
        '@id': 'img1',
        imageAlt: 'image 1',
        imageUrl: 'https://example.com/image1.jpg'
      },
      img2: {
        ...dummyImage,
        '@id': 'img2',
        imageAlt: 'image 2',
        imageUrl: 'https://example.com/image2.jpg'
      }
    };
    const { queryAllByRole } = renderHero({ externalSource });
    expect(queryAllByRole('img')).toHaveLength(0);
  });

  it('should render valid external source images', () => {
    const externalSource: ExternalSourceImage = {
      ...dummyExternalSource,
      '@nodes': ['img1'],
      img1: {
        ...dummyImage,
        '@id': 'img1',
        imageAlt: 'image 1',
        imageUrl:
          'https://dlyhjlf6lts50.cloudfront.net/app/uploads/2021/06/hear.com-product-image-main-page-1024x696.jpg'
      }
    };
    const { queryAllByRole } = renderHero({ externalSource });
    expect(queryAllByRole('img')).toHaveLength(1);
  });

  it('should render Asset images alongside valid external source images', () => {
    const externalSource: ExternalSourceImage = {
      ...dummyExternalSource,
      '@nodes': ['img1'],
      img1: {
        ...dummyImage,
        '@id': 'img1',
        imageAlt: 'image 1',
        imageUrl:
          'https://dlyhjlf6lts50.cloudfront.net/app/uploads/2021/06/hear.com-product-image-main-page-1024x696.jpg'
      }
    };
    const damChooser: DamChooserImage = {
      ...dummyDamChooser,
      '@nodes': ['img2'],
      img2: {
        image: DAM_IMAGE_FIXTURE
      }
    };
    const { queryAllByRole } = renderHero({
      externalSource,
      damChooser,
      hideClipPath: true
    });
    expect(queryAllByRole('img')).toHaveLength(2);
  });
});
