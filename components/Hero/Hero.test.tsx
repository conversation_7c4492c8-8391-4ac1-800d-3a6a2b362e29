import { render } from '@utils/test-utils';

import Hero from '.';

jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: jest.fn()
}));

const fakeMetadata = {
  '@index': 7,
  '@name': '02',
  '@path':
    '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/provider/main/02',
  '@id': '0a03fc04-1a55-4abf-a07f-ae674a963dc0',
  '@nodeType': 'mgnl:component',
  'mgnl:created': '2023-09-12T13:53:13.448+02:00',
  'mgnl:template': 'spa-lm:components/reviews',
  'mgnl:lastModified': '2023-12-13T17:10:12.510+01:00',
  '@nodes': []
};

type TestHeroProps = {
  fullBleedImage?: boolean;
  domain?: string;
};

describe('Hero', () => {
  function renderHero({ fullBleedImage = false }: TestHeroProps) {
    return render(
      <Hero
        headingElement={'h1'}
        title={'Test Hero'}
        fullBleedImage={fullBleedImage}
        search={{
          field: 'enableSearch',
          heroSearchId: '',
          searchTitle: undefined,
          searchTitleColor: undefined,
          inputAlignment: undefined,
          searchButton: undefined
        }}
        secondaryCta={{
          field: 'enableSecondaryCta',
          ctaType: {
            field: '',
            button: undefined,
            text: '',
            textColor: '',
            textColorRange: '',
            secondText: undefined,
            secondTextColor: undefined,
            secondTextColorRange: undefined,
            thirdText: undefined,
            thirdTextColor: undefined,
            thirdTextColorRange: undefined,
            url: undefined,
            behavior: undefined,
            rel: undefined,
            type: undefined
          }
        }}
        metadata={fakeMetadata}
      />
    );
  }

  it('should render successfully', () => {
    const { baseElement, queryByTestId } = renderHero({
      fullBleedImage: false
    });
    const heroImageContainer = queryByTestId('full-bleed-image-container');
    expect(baseElement).toBeTruthy();
    expect(heroImageContainer).not.toBeInTheDocument();
  });

  it('should render with fullBleedImage', () => {
    const { baseElement, queryByTestId } = renderHero({ fullBleedImage: true });
    const heroImageContainer = queryByTestId('full-bleed-image-container');
    expect(baseElement).toBeTruthy();
    expect(heroImageContainer).toBeInTheDocument();
  });
});
