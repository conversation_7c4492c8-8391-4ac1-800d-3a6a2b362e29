'use client';

import { Box, Stack } from '@chakra-ui/layout';
import Container from '@components/LayoutStructure/Container';
import FullWidthContainer from '@components/LayoutStructure/FullWidthContainer';
import { getColor } from '@utils/getColor';

import { DamChooserImage, ExternalSourceImage } from '~/types/Magnolia';

import HeroImage from './HeroImage';
import { HeroInquiryForm, HeroInquiryFormProps } from './HeroInquiryForm';
interface HeroProps extends HeroInquiryFormProps {
  backgroundColor?: string;
  backgroundColorRange?: string;
  fullBleedImage?: boolean;
  damChooser?: DamChooserImage;
  damChooserMobile?: DamChooserImage;
  externalSource?: ExternalSourceImage;
}

const Hero = (props: HeroProps): JSX.Element => {
  const {
    backgroundColor,
    backgroundColorRange,
    damChooser,
    damChooserMobile,
    externalSource,
    fullBleedImage = false,
    ...inquiryProps
  } = props;

  if (fullBleedImage) {
    return (
      <FullWidthContainer
        bg={getColor(backgroundColor, backgroundColorRange)}
        px={0}
        py={0}
      >
        <Box
          data-testid="full-bleed-image-container"
          position="relative"
          margin="0 auto"
        >
          <HeroImage
            externalSource={externalSource}
            damChooser={damChooser}
            damChooserMobile={damChooserMobile}
            hideClipPath
            imageHeight={{ base: '1012px', md: '936px', lg: '850px' }}
          />
          <Container
            position="absolute"
            margin="auto"
            left="50%"
            top={{ base: '85%', lg: '50%' }}
            transform={{
              base: 'translate(-50%, -85%)',
              lg: 'translate(-50%, -50%)'
            }}
          >
            <HeroInquiryForm {...inquiryProps} />
          </Container>
        </Box>
      </FullWidthContainer>
    );
  }

  return (
    <FullWidthContainer
      bg={getColor(backgroundColor, backgroundColorRange)}
      px={0}
      py={0}
    >
      <Stack direction={{ base: 'column', lg: 'row' }}>
        <Box
          p={8}
          width={{ base: '100%', md: '70%' }}
          display="flex"
          alignItems="center"
          justifyContent="flex-end"
        >
          <HeroInquiryForm {...inquiryProps} />
        </Box>
        <HeroImage externalSource={externalSource} damChooser={damChooser} />
      </Stack>
    </FullWidthContainer>
  );
};

export default Hero;
