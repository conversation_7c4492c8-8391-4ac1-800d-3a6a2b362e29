import { useBreakpointValue } from '@chakra-ui/media-query';
import { render } from '@testing-library/react';

import { HeroInquiryForm, HeroInquiryFormProps } from './HeroInquiryForm';

jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: jest.fn()
}));

type HeroFormTestProps = {
  showDivider?: boolean;
  buttonOutsideInput?: boolean;
  tileBackground?: boolean;
  tileBackgroundOpacity?: number;
};

describe('HeroInquiryForm', () => {
  function renderHeroForm(props?: HeroFormTestProps) {
    const baseProps: HeroInquiryFormProps = {
      headingElement: 'h1',
      title: '',
      tileBackground: props?.tileBackground,
      tileBackgroundOpacity: props?.tileBackgroundOpacity,
      search: {
        field: 'enableSearch',
        heroSearchId: '',
        searchTitle: undefined,
        searchTitleColor: undefined,
        inputAlignment: undefined,
        showDivider: props?.showDivider,
        buttonOutsideInput: props?.buttonOutsideInput,
        searchButton: {
          text: 'Default',
          mobileText: 'Mobile',
          buttonTextColor: '',
          buttonBgColor: '',
          state: 'solid',
          behavior: '_blank'
        }
      },
      secondaryCta: {
        field: 'enableSecondaryCta',
        ctaType: {
          field: '',
          button: undefined,
          text: '',
          textColor: '',
          textColorRange: '',
          secondText: undefined,
          secondTextColor: undefined,
          secondTextColorRange: undefined,
          thirdText: undefined,
          thirdTextColor: undefined,
          thirdTextColorRange: undefined,
          url: undefined,
          behavior: undefined,
          rel: undefined,
          type: undefined
        }
      },
      metadata: {
        '@id': '',
        '@name': '',
        '@path': '',
        '@nodeType': '',
        'mgnl:lastModified': '',
        'mgnl:template': '',
        'mgnl:created': '',
        '@nodes': []
      }
    };
    return render(<HeroInquiryForm {...baseProps} />);
  }

  it('should render with default props', () => {
    const { queryByTestId } = renderHeroForm();
    const container = queryByTestId('hero-form-container');
    const divider = queryByTestId('search-bar-divider');
    const searchButton = queryByTestId('mini-self-search-button');
    expect(searchButton).toHaveTextContent('Default');
    expect(divider).not.toBeInTheDocument();
    expect(container).toBeInTheDocument();
    expect(container).not.toHaveStyle(
      'background-color: rgba(255, 255, 255, 0.2)'
    );
    expect(container).not.toHaveStyle('border-radius: md');
  });

  it('should render with tiled background', () => {
    const { queryByTestId } = renderHeroForm({ tileBackground: true });
    const container = queryByTestId('hero-form-container');
    expect(container).toBeInTheDocument();
    expect(container).toHaveStyle('background-color: rgba(255, 255, 255, 0.2)');
    expect(container).toHaveStyle('border-radius: md');
  });

  it('should render with custom opacity', () => {
    const tileBackgroundOpacity = 50;
    const { queryByTestId } = renderHeroForm({
      tileBackground: true,
      tileBackgroundOpacity
    });
    const container = queryByTestId('hero-form-container');
    expect(container).toBeInTheDocument();
    expect(container).toHaveStyle(
      `background-color: rgba(255, 255, 255, ${tileBackgroundOpacity / 100})`
    );
  });

  it('should render with search bar divider', () => {
    const { queryByTestId } = renderHeroForm({ showDivider: true });
    const divider = queryByTestId('search-bar-divider');
    expect(divider).toBeInTheDocument();
  });

  it('should renders the cta button outside the search input on mobile', () => {
    useBreakpointValue.mockReturnValueOnce(true);
    const { queryByTestId } = renderHeroForm({ tileBackground: true });
    const searchButton = queryByTestId('mini-self-search-button');
    expect(searchButton).toHaveTextContent('Mobile');
  });
});
