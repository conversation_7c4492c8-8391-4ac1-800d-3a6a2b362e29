import { LocationResponse } from '@services/modular-monolith/types/location.type';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { isCallCenterOpen } from '@utils/callCenterBusinessHours';
import { ComponentProps } from 'react';

import { Provider } from '~/contexts/Provider';

import InquiryProviderContactModal from './InquiryProviderContactModal';

jest.mock('@utils/callCenterBusinessHours', () => ({
  isCallCenterOpen: jest.fn()
}));

const mockedIsCallCenterOpen = isCallCenterOpen as jest.Mock;

describe('components::InquiryForm::InquiryProviderContactModal', () => {
  const queryClient = new QueryClient();

  const renderComponent = (
    props: Partial<ComponentProps<typeof InquiryProviderContactModal>> = {}
  ) =>
    render(
      <QueryClientProvider client={queryClient}>
        <InquiryProviderContactModal
          phone="**********"
          onClose={jest.fn()}
          provider={
            { id: 'providerId', name: 'The Provider Name' } as LocationResponse
          }
          {...props}
        />
      </QueryClientProvider>
    );

  // Reset mocks before each test to ensure isolation
  beforeEach(() => {
    jest.resetAllMocks();
    mockedIsCallCenterOpen.mockReturnValue(true);
  });

  test("shows the provider's name", () => {
    renderComponent();
    expect(screen.getByText(/Costs for The Provider Name/)).toBeInTheDocument();
  });

  describe('when no service is listed', () => {
    test('shows the correct message', () => {
      renderComponent();
      expect(
        screen.getByText('Pricing unavailable from this provider')
      ).toBeInTheDocument();
    });

    test('closing will redirect to comparison', () => {
      Object.defineProperty(window, 'location', {
        value: { href: '' },
        writable: true // possibility to override
      });

      renderComponent({ redirectURL: '/URL/' });

      screen.getByRole('button').click();
      expect(window.location.href).toEqual(
        '/URL/#provider-comparison-providerId'
      );
    });
  });

  describe('when services are listed', () => {
    const provider = {
      id: 'providerId',
      name: 'The Provider Name',
      services: [
        {
          id: 'serviceId-1',
          category: { name: 'Service Category #1' },
          costs: { startingPriceCents: 1000 }
        },
        {
          id: 'serviceId-2',
          category: { name: 'Service Category #2' },
          costs: { startingPriceCents: 3500 }
        }
      ] as unknown as Provider['services']
    } as LocationResponse;

    test('shows prices', () => {
      renderComponent({ provider });

      expect(screen.getByText('Service Category #1')).toBeInTheDocument();
      expect(screen.getByText('$10/mo')).toBeInTheDocument();

      expect(screen.getByText('Service Category #2')).toBeInTheDocument();
      expect(screen.getByText('$35/mo')).toBeInTheDocument();
    });

    test('closing will redirect to costs', () => {
      Object.defineProperty(window, 'location', {
        value: { href: '' },
        writable: true // possibility to override
      });

      renderComponent({ provider, redirectURL: '/URL/' });

      screen.getByRole('button').click();
      expect(window.location.href).toEqual('/URL/#provider-costs-providerId');
    });
  });

  test('shows only the "will call" message during business hours', () => {
    renderComponent();

    expect(
      screen.getByText('A Caring Family Advisor will call')
    ).toBeInTheDocument();
  });

  test('shows only the "on business hours" message outside of business hours', () => {
    mockedIsCallCenterOpen.mockReturnValue(false);

    renderComponent();

    expect(
      screen.getByText('Get in touch with us during business hours:')
    ).toBeInTheDocument();
  });
});
