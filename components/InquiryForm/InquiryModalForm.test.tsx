import { render, screen } from '~/utils/test-utils';

import InquiryModalForm from './InquiryModalForm';

const mockShowModal = jest.fn();
const mockHideModal = jest.fn();
jest.mock('~/contexts/ModalContext', () => ({
  useModal: jest.fn(() => ({
    visible: true,
    show: mockShowModal,
    hide: mockHideModal
  }))
}));

describe('InquiryModalForm', () => {
  it('renders an inquiry modal form without scroll Y', () => {
    render(
      <InquiryModalForm visible onClose={jest.fn} size="full-page-modal">
        Modal Content
      </InquiryModalForm>
    );

    const modalContent = screen.getByTestId('modal-content');
    expect(modalContent).toBeInTheDocument();
    expect(modalContent).not.toHaveClass('custom-scrollbar');
    expect(modalContent).not.toHaveStyle('maxHeight: 80vh');
    expect(modalContent).not.toHaveStyle('overflowY: scroll');
  });

  it('renders an inquiry modal form with scroll Y', () => {
    render(
      <InquiryModalForm
        visible
        onClose={jest.fn}
        size="full-page-modal"
        allowInnerScroll
      >
        Modal Content
      </InquiryModalForm>
    );

    const modalContent = screen.getByTestId('modal-content');
    expect(modalContent).toBeInTheDocument();
    expect(modalContent).toHaveClass('custom-scrollbar');
    expect(modalContent).toHaveStyle('maxHeight: 80vh');
    expect(modalContent).toHaveStyle('overflowY: scroll');
  });
});
