'use client';

import { Box } from '@chakra-ui/layout';
import Modal from '@components/Modal';
import { PropsWithChildren } from 'react';

interface Props extends PropsWithChildren {
  preTitle?: string;
  title?: string;
  postTitle?: string;
  size: string;
  allowInnerScroll?: boolean;
  onClose: () => void;
  visible: boolean;
}

const InquiryModalForm: React.FC<Props> = ({
  children,
  preTitle,
  title = 'Get costs',
  postTitle,
  size,
  allowInnerScroll = false,
  onClose,
  visible
}) => {
  const isFull = size === 'full' || size === 'full-page-modal';

  return (
    <Modal
      size={isFull ? 'full' : 'md'}
      visible={visible}
      onClose={onClose}
      preTitle={preTitle}
      title={title}
      postTitle={postTitle}
      allowInnerScroll={allowInnerScroll}
    >
      <Box display="flex" width="100%" justifyContent="center">
        <Box width="xl">{children}</Box>
      </Box>
    </Modal>
  );
};

export default InquiryModalForm;
