import { PageContext } from '@components/LayoutStructure/Contexts';
import { PageContextType } from '@components/LayoutStructure/Contexts.types';
import userEvent from '@testing-library/user-event';
import { getSEMParameters } from '@utils/semParameters';
import { getUTMParameters } from '@utils/utmParameters';
import { act } from 'react';

import segmentEvents from '~/config/segment-events';
import ProviderContext, { ProviderProps } from '~/contexts/Provider';
import { SessionContext, SessionContextType } from '~/contexts/SessionContext';
import { RollUpType } from '~/types/RollUpType';
import {
  fireEvent,
  mockInquiryForm,
  render,
  screen,
  waitFor
} from '~/utils/test-utils';

import InquiryForm from './InquiryForm';
import { CTAAction, Display } from './InquiryForm.types';

const mockInquiryFormWithoutMedicaidCheckbox = {
  ...mockInquiryForm,
  showPayingWithMedicaid: false
};

jest.mock('next/router', () => ({
  useRouter() {
    return {
      asPath: '/senior-living/nursing-homes/alabama'
    };
  }
}));

jest.mock('next/navigation', () => ({
  usePathname() {
    return {
      asPath: '/senior-living/nursing-homes/alabama'
    };
  }
}));
// Avoid inquiry form values saved on cookie to mess up with these tests
jest.mock('@utils/cookieStorage', () => ({
  enabled: false
}));

jest.mock('~/hooks/useTracking', () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue({
    anonId: 'mockedId',
    trackingNotes: 'segment_anonid:mockedId|page_session_id:mockedPageSessionId'
  })
}));

jest.mock('@utils/utmParameters', () => ({
  getUTMParameters: jest.fn(() => ({
    utm_source: 'testSource',
    utm_campaign: 'testCampaign',
    utm_content: 'testContent',
    utm_medium: 'testMedium',
    utm_term: 'testTerm'
  })),
  setUTMParameters: jest.fn(),
  getLandingPageUrl: jest.fn(
    () =>
      'https://www.caring.com/assisted-living/california/los-angeles?utm_source=google'
  ),
  setLandingPageUrl: jest.fn()
}));

jest.mock('@utils/semParameters', () => ({
  getSEMParameters: jest.fn(() => ({
    msclkid: 'testMsclkid',
    gclid: 'testGclid'
  })),
  setSEMParameters: jest.fn()
}));

describe('InquiryForm', () => {
  const analyticsMock = {
    ready: jest.fn(),
    track: jest.fn(),
    user: jest.fn(() => ({
      anonymousId: jest.fn().mockReturnValue('mockedId')
    }))
  };

  const setFieldByName = (container, name, value) => {
    const input = container.querySelector(
      `[name="${name}"]`
    ) as HTMLInputElement;
    fireEvent.change(input, { target: { value } });
  };

  beforeEach(() => {
    window.tracking = analyticsMock;
  });
  it('renders an inquiry form with provided props', () => {
    render(<InquiryForm {...mockInquiryForm} />);

    const formTitle = screen.getByText(/Request Information/i);
    expect(formTitle).toBeInTheDocument();

    const submitButton = screen.getByText(/Get costs/i);
    expect(submitButton).toBeInTheDocument();

    const legalDisclosure = screen.getByText(/Legal disclosure/i);
    expect(legalDisclosure).toBeInTheDocument();
  });

  it('shows an error when a required field is missing', async () => {
    render(<InquiryForm {...mockInquiryForm} />);
    fireEvent.click(screen.getByText(/Get costs/i));

    expect(await screen.findByText('Please make a selection')).toBeVisible();
    expect(
      await screen.findByText('Please enter your full name')
    ).toBeVisible();
    expect(
      await screen.findByText('Please enter a valid U.S. phone number')
    ).toBeVisible();
    expect(
      await screen.findByText('Please enter a valid email address')
    ).toBeVisible();
  });

  it('fails to submit when field does not match the required format', async () => {
    const { container } = render(<InquiryForm {...mockInquiryForm} />);

    setFieldByName(container, 'email', 'testcaring.com');
    fireEvent.click(screen.getByText(/Get costs/i));

    expect(
      await screen.findByText('Please enter a valid email address')
    ).toBeVisible();
  });

  it('fails to submit when phone number does not have 10 digits', async () => {
    const { container } = render(<InquiryForm {...mockInquiryForm} />);

    setFieldByName(container, 'phoneNumber', '123456789');
    fireEvent.click(screen.getByText(/Get costs/i));

    expect(
      await screen.findByText(
        'Invalid phone number, please enter a valid U.S. phone number'
      )
    ).toBeVisible();
  });

  describe('Phone number formatting', () => {
    it('formats the phone number when autofill inserts an unformatted value', async () => {
      const { container } = render(<InquiryForm {...mockInquiryForm} />);
      const input = container.querySelector(
        `[name="phoneNumber"]`
      ) as HTMLInputElement;

      setFieldByName(container, 'phoneNumber', '1231231234');
      expect(input.value).toEqual('(*************');
    });

    it('removes the country prefix when paste or autofill inserts a number with US country code', async () => {
      const { container } = render(<InquiryForm {...mockInquiryForm} />);
      const input = container.querySelector(
        `[name="phoneNumber"]`
      ) as HTMLInputElement;

      setFieldByName(container, 'phoneNumber', '+****************');
      expect(input.value).toEqual('(*************');
    });

    it('does not remove the country prefix when paste or autofill inserts a number with non-US country code', async () => {
      const { container } = render(<InquiryForm {...mockInquiryForm} />);
      const input = container.querySelector(
        `[name="phoneNumber"]`
      ) as HTMLInputElement;

      setFieldByName(container, 'phoneNumber', '+55 (11) 9999-8888');
      expect(input.value).toEqual('(*************');
    });

    it('does not change the default input behavior when a paste event inserts some numbers on a partially filled input', async () => {
      // Restore the original implementation of requestAnimationFrame
      (window.requestAnimationFrame as jest.Mock).mockRestore();
      const user = userEvent.setup();
      const { container } = render(<InquiryForm {...mockInquiryForm} />);
      const input = container.querySelector(
        `[name="phoneNumber"]`
      ) as HTMLInputElement;

      await user.click(input);
      await user.paste('222');
      await user.paste('333');

      await waitFor(() => {
        expect(input.value).toEqual('(222) 333-____');
      });
    });
  });

  describe('"Paying with Medicaid only" option', () => {
    it('renders the option when rollup type is Senior Living and `showPayingWithMedicaid` is true', () => {
      render(
        <InquiryForm
          {...mockInquiryForm}
          rollUpType={RollUpType.SENIOR_LIVING}
        />
      );

      expect(screen.queryByText(/Paying with Medicaid/i)).toBeInTheDocument();
    });
    it('does not render the option when rollup type is Senior Living and `showPayingWithMedicaid` is false', () => {
      render(
        <InquiryForm
          {...mockInquiryFormWithoutMedicaidCheckbox}
          rollUpType={RollUpType.SENIOR_LIVING}
        />
      );

      expect(
        screen.queryByText(/Paying with Medicaid/i)
      ).not.toBeInTheDocument();
    });

    it('does not render the option when rollup type is Senior Care and `showPayingWithMedicaid` is true', () => {
      render(
        <InquiryForm {...mockInquiryForm} rollUpType={RollUpType.SENIOR_CARE} />
      );

      expect(
        screen.queryByText(/Paying with Medicaid/i)
      ).not.toBeInTheDocument();
    });
    it('does not render the option when rollup type is Senior Care and `showPayingWithMedicaid` is false', () => {
      render(
        <InquiryForm
          {...mockInquiryFormWithoutMedicaidCheckbox}
          rollUpType={RollUpType.SENIOR_CARE}
        />
      );

      expect(
        screen.queryByText(/Paying with Medicaid/i)
      ).not.toBeInTheDocument();
    });
    it('renders the option when rollup type is empty but the care type has a Senior Living rollup type and `showPayingWithMedicaid` is true', () => {
      render(
        <InquiryForm
          {...mockInquiryForm}
          rollUpType={undefined}
          defaultCareType="care-homes"
        />
      );

      expect(screen.queryByText(/Paying with Medicaid/i)).toBeInTheDocument();
    });
    it('does not render the option when rollup type is empty but the care type has a Senior Living rollup type and `showPayingWithMedicaid` is false', () => {
      render(
        <InquiryForm
          {...mockInquiryFormWithoutMedicaidCheckbox}
          rollUpType={undefined}
          defaultCareType="care-homes"
        />
      );

      expect(
        screen.queryByText(/Paying with Medicaid/i)
      ).not.toBeInTheDocument();
    });
  });

  describe('when submitting', () => {
    const fetchSpy = jest.spyOn(window, 'fetch');

    const fillUpAndSubmitForm = async ({
      container,
      response
    }: {
      container: any;
      response?: Record<string, unknown>;
    }) => {
      fetchSpy.mockResolvedValue({
        json: () => Promise.resolve({ success: true, ...response })
      } as Response);

      setFieldByName(container, 'whoAreYouLookingFor', 'Parent(s)');
      setFieldByName(container, 'email', '<EMAIL>');
      setFieldByName(container, 'fullName', 'John Doe');
      setFieldByName(container, 'phoneNumber', '(*************');

      await act(async () => {
        fireEvent.click(screen.getByText(/Get costs/i));
      });
      await waitFor(() =>
        expect(fetchSpy).toHaveBeenNthCalledWith(
          2, // first call is to ipfy
          '/api/inquiry',
          expect.any(Object)
        )
      );
    };
    describe('test for tracking values', () => {
      it('submit with anon and trackingNotes', async () => {
        const { container } = render(
          <PageContext.Provider value={defaultPageProps}>
            <InquiryForm {...mockInquiryForm} overrideDefaultType />
          </PageContext.Provider>
        );

        await fillUpAndSubmitForm({ container });

        const body = JSON.parse(
          fetchSpy.mock.calls[1][1]?.body?.toString() || '{}'
        );
        expect(body.anonId).toBe('mockedId');
        expect(body.trackingNotes).toBe(
          'segment_anonid:mockedId|page_session_id:mockedPageSessionId'
        );
      });
    });
    describe('when overrideDefaultType is turned on', () => {
      it('submits the care type from context', async () => {
        const { container } = render(
          <PageContext.Provider value={defaultPageProps}>
            <InquiryForm {...mockInquiryForm} overrideDefaultType />
          </PageContext.Provider>
        );

        await fillUpAndSubmitForm({ container });

        const body = JSON.parse(
          fetchSpy.mock.calls[1][1]?.body?.toString() || '{}'
        );
        expect(body.typeOfCare).toBe('nursing_homes');
      });

      it('submits the default type when overrideDefaultType is turned on but no type in the context', async () => {
        const noCareTypeContext = {
          ...defaultPageProps,
          context: { params: { careType: undefined } }
        } as PageContextType;
        const { container } = render(
          <PageContext.Provider value={noCareTypeContext}>
            <InquiryForm {...mockInquiryForm} overrideDefaultType />
          </PageContext.Provider>
        );

        await fillUpAndSubmitForm({ container });

        const body = JSON.parse(
          fetchSpy.mock.calls[1][1]?.body?.toString() || '{}'
        );
        expect(body.typeOfCare).toBe('assisted_living');
      });
    });

    it('submits the form without providerSlug if not set', async () => {
      window.location.assign('http://test.caring.com');
      const { container } = render(
        <PageContext.Provider value={defaultPageProps}>
          <InquiryForm
            {...mockInquiryForm}
            thankYouMessage=""
            overrideDefaultType
          />
        </PageContext.Provider>
      );

      await fillUpAndSubmitForm({ container });

      const baseUrl = process.env.NEXT_PUBLIC_CARING_THANK_YOU_PAGE_URL;
      expect(decodeURI(window.location.href)).toBe(
        `${baseUrl}/?phone=(*************`
      );
    });

    it('tracks the Inquiry Submitted event with the values sent to the API', async () => {
      const sessionData: SessionContextType = {
        pageSessionId: 'mockedPageSessionId',
        sessionId: 'mockedSessionId'
      };

      const trackMock = jest.fn();
      window.tracking = {
        ready: jest.fn(),
        track: trackMock
      };

      const { container } = render(
        <SessionContext.Provider value={sessionData}>
          <PageContext.Provider value={defaultPageProps}>
            <InquiryForm
              {...mockInquiryForm}
              thankYouMessage=""
              overrideDefaultType
            />
          </PageContext.Provider>
        </SessionContext.Provider>
      );

      await fillUpAndSubmitForm({ container });

      expect(trackMock).toHaveBeenCalledWith(
        segmentEvents.INQUIRY_SUBMITTED,
        expect.objectContaining({
          form: expect.objectContaining({
            name: expect.any(String),
            display: Display.VERTICAL,
            type: CTAAction.REQUEST_INFO,
            values: expect.arrayContaining([
              { name: 'whoAreYouLookingFor', value: 'Parent(s)' },
              { name: 'email', value: '<EMAIL>' },
              { name: 'fullName', value: 'John Doe' },
              { name: 'phoneNumber', value: '(*************' }
            ]),
            page_session_id: 'mockedPageSessionId',
            session_id: 'mockedSessionId'
          }),
          page_session_id: 'mockedPageSessionId',
          session_id: 'mockedSessionId'
        })
      );
    });

    it('shows up the contact modal when provider came in the response', async () => {
      const { container } = render(
        <ProviderContext.Provider
          value={{ provider: { id: 'providerId' } } as ProviderProps}
        >
          <PageContext.Provider value={defaultPageProps}>
            <InquiryForm
              {...mockInquiryForm}
              thankYouMessage=""
              overrideDefaultType
            />
          </PageContext.Provider>
        </ProviderContext.Provider>
      );

      await fillUpAndSubmitForm({
        container,
        response: {
          provider: { id: 'providerId' }
        }
      });

      expect(
        screen.getByText('Pricing unavailable from this provider')
      ).toBeInTheDocument();
    });

    it('should include utm and sem parameters and landing page URL in the API payload', async () => {
      const { container } = render(
        <PageContext.Provider value={defaultPageProps}>
          <InquiryForm {...mockInquiryForm} overrideDefaultType />
        </PageContext.Provider>
      );

      await fillUpAndSubmitForm({ container });

      expect(getUTMParameters).toHaveBeenCalled();
      expect(getSEMParameters).toHaveBeenCalled();

      const body = JSON.parse(
        fetchSpy.mock.calls[1][1]?.body?.toString() || '{}'
      );

      expect(body.utm_source).toBe('testSource');
      expect(body.utm_campaign).toBe('testCampaign');
      expect(body.utm_content).toBe('testContent');
      expect(body.utm_medium).toBe('testMedium');
      expect(body.utm_term).toBe('testTerm');
      expect(body.msclkid).toBe('testMsclkid');
      expect(body.gclid).toBe('testGclid');
      expect(body.landingPageUrl).toBe(
        'https://www.caring.com/assisted-living/california/los-angeles?utm_source=google'
      );
    });

    it('should not include landingPageUrl when it does not exist', async () => {
      const mockGetLandingPageUrl =
        require('@utils/utmParameters').getLandingPageUrl;
      mockGetLandingPageUrl.mockReturnValueOnce(undefined);

      const { container } = render(
        <PageContext.Provider value={defaultPageProps}>
          <InquiryForm {...mockInquiryForm} overrideDefaultType />
        </PageContext.Provider>
      );

      await fillUpAndSubmitForm({ container });

      const body = JSON.parse(
        fetchSpy.mock.calls[1][1]?.body?.toString() || '{}'
      );

      expect(body.landingPageUrl).toBeUndefined();
    });
  });
});

const defaultPageProps: PageContextType = {
  page: {
    title: 'Nursing Homes in Alabama',
    description: 'Nursing Homes in Alabama',
    keywords: 'nursing homes, alabame',
    canonical: 'http://www.caring.com/senior-living/nursing-homes/alabama',
    main: {
      '@nodes': []
    },
    sidebar: {
      '@nodes': []
    },
    metaDescription: '',
    metaTitle: '',
    metaKeywords: '',
    '@name': '',
    '@path': '',
    '@id': '',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:template': '',
    'mgnl:created': '',
    '@nodes': []
  },
  context: {
    params: {
      careType: 'nursing-homes'
    }
  },
  templateAnnotations: null
};
