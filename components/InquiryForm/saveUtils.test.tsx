import CookieStorage from '@utils/cookieStorage';

import { getSavedValues, saveInquiryForm } from './saveUtils';

jest.mock('@utils/cookieStorage', () => ({
  enabled: true,
  set: jest.fn(),
  get: jest.fn()
}));

describe('saveUtils', () => {
  it('should call set with the proper values', () => {
    const testValues = { name: '<PERSON>', email: '<EMAIL>' };
    saveInquiryForm(testValues);
    expect(CookieStorage.set).toHaveBeenCalledWith(
      'inquiry-form-auto-save',
      JSON.stringify(testValues),
      expect.any(Number)
    );
  });

  it('should call get and receive expected values', () => {
    const testValues = { name: 'Jane', email: '<EMAIL>' };
    CookieStorage.get.mockReturnValueOnce(JSON.stringify(testValues));
    const retrievedValues = getSavedValues();
    expect(retrievedValues).toEqual(testValues);
  });

  it('should handle null case', () => {
    CookieStorage.get.mockReturnValueOnce(null);
    const retrievedValues = getSavedValues();
    expect(retrievedValues).toEqual({});
  });
});
