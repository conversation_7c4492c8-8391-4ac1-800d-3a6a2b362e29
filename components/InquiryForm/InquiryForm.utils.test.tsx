import { FormType } from '@components/Analytics/events/FormStepSubmission';

import { handleFormBlur, handleFormFocus } from './InquiryForm.utils';

describe('InquiryForm Utils', () => {
  describe('handleFormFocus', () => {
    it('should set formFocused to true and call formFieldFocused when formFocused is false and target name is defined', () => {
      const setFormFocused = jest.fn();
      const formFieldFocused = jest.fn();
      const formTemplateId = 'templateId';
      const formType = FormType.INQUIRY;

      const event = {
        target: {
          name: 'inputName'
        }
      } as unknown as React.MouseEvent<HTMLFormElement, MouseEvent>;

      handleFormFocus(
        event,
        false,
        setFormFocused,
        formFieldFocused,
        formTemplateId,
        formType
      );

      expect(setFormFocused).toHaveBeenCalledWith(true);
      expect(formFieldFocused).toHaveBeenCalledWith({
        formDetails: {
          formTemplateId: formTemplateId,
          formType: formType
        },
        focusData: { formType: formType, focusField: 'inputName' }
      });
    });

    it('should not set formFocused to true and not call formFieldFocused when formFocused is true', () => {
      const setFormFocused = jest.fn();
      const formFieldFocused = jest.fn();
      const formTemplateId = 'templateId';
      const formType = FormType.INQUIRY;

      const event = {
        target: {
          name: 'inputName'
        }
      } as unknown as React.MouseEvent<HTMLFormElement, MouseEvent>;

      handleFormFocus(
        event,
        true,
        setFormFocused,
        formFieldFocused,
        formTemplateId,
        formType
      );

      expect(setFormFocused).not.toHaveBeenCalled();
      expect(formFieldFocused).not.toHaveBeenCalled();
    });

    it('should not set formFocused to true and not call formFieldFocused when target name is undefined', () => {
      const setFormFocused = jest.fn();
      const formFieldFocused = jest.fn();
      const formTemplateId = 'templateId';
      const formType = FormType.INQUIRY;

      const event = {
        target: {}
      } as React.MouseEvent<HTMLFormElement, MouseEvent>;

      handleFormFocus(
        event,
        false,
        setFormFocused,
        formFieldFocused,
        formTemplateId,
        formType
      );

      expect(setFormFocused).not.toHaveBeenCalled();
      expect(formFieldFocused).not.toHaveBeenCalled();
    });

    it('should not set formFocused to true and not call formFieldFocused when target name is an empty string', () => {
      const setFormFocused = jest.fn();
      const formFieldFocused = jest.fn();
      const formTemplateId = 'templateId';
      const formType = FormType.INQUIRY;

      const event = {
        target: {
          name: ''
        }
      } as unknown as React.MouseEvent<HTMLFormElement, MouseEvent>;

      handleFormFocus(
        event,
        false,
        setFormFocused,
        formFieldFocused,
        formTemplateId,
        formType
      );

      expect(setFormFocused).not.toHaveBeenCalled();
      expect(formFieldFocused).not.toHaveBeenCalled();
    });
  });

  describe('handleFormBlur', () => {
    it('should set formFocused to false when formFocused is true and relatedTarget form is different from the inquiryForm ref', () => {
      const setFormFocused = jest.fn();
      const inquiryForm = {
        current: document.createElement('form')
      };

      const event = {
        relatedTarget: { form: document.createElement('form') }
      } as unknown as React.FocusEvent<HTMLFormElement, Element>;

      handleFormBlur(event, true, inquiryForm, setFormFocused);

      expect(setFormFocused).toHaveBeenCalledWith(false);
    });

    it('should not set formFocused to false when relatedTarget form is the same as the inquiryForm ref', () => {
      const setFormFocused = jest.fn();
      const inquiryForm = {
        current: document.createElement('form')
      };
      const event = {
        relatedTarget: { form: inquiryForm.current }
      } as unknown as React.FocusEvent<HTMLFormElement, Element>;

      handleFormBlur(event, true, inquiryForm, setFormFocused);

      expect(setFormFocused).not.toHaveBeenCalled();
    });
  });
});
