import {
  act,
  fireEvent,
  render,
  screen,
  waitFor
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  autocompleteInputZipResults,
  autocompleteResults
} from '@utils/test-utils/mocks/autocompleteInputResults';

import { fetchGeoLocation } from '~/utils/fetchGeoLocation';

import AutocompleteInput, {
  autocompleteSuggestionMapBoxAPI,
  parseAutocompleteResults
} from './AutocompleteInput';

jest.mock('~/utils/fetchGeoLocation');

(fetchGeoLocation as jest.Mock).mockResolvedValue({
  city: 'TestCity',
  state: 'TestState',
  latitude: '12.34',
  longitude: '56.78'
});

describe('AutocompleteInput', () => {
  it('renders input element', async () => {
    let renderResult;
    await act(async () => {
      renderResult = render(<AutocompleteInput />);
    });
    const inputElement = renderResult.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();
  });

  it('renders input element with default value', async () => {
    let renderResult;
    await act(async () => {
      renderResult = render(<AutocompleteInput defaultValue="Initial Value" />);
    });
    const inputElement = renderResult.getByRole('textbox') as HTMLInputElement;
    expect(inputElement.value).toBe('Initial Value');
  });

  it('updates input value on change', async () => {
    let renderResult;
    await act(async () => {
      renderResult = render(<AutocompleteInput />);
    });
    const inputElement = renderResult.getByRole('textbox') as HTMLInputElement;

    await act(async () => {
      fireEvent.change(inputElement, { target: { value: 'test' } });
    });

    expect(inputElement.value).toBe('test');
  });

  it('calls setKeyword when input value changes', async () => {
    const setKeyword = jest.fn();
    let renderResult;

    await act(async () => {
      renderResult = render(<AutocompleteInput setKeyword={setKeyword} />);
    });

    const inputElement = renderResult.getByRole('textbox');

    await act(async () => {
      fireEvent.change(inputElement, { target: { value: 'test' } });
    });

    expect(setKeyword).toHaveBeenCalledWith('test');
  });

  describe('autocompleteSuggestionMapBoxAPI', () => {
    beforeEach(() => {
      fetchMock.mockResponseOnce(JSON.stringify({}));
    });

    it('calls the mapbox API with the correct URL', () => {
      const callbackMock = jest.fn();
      const inputParams = 'test';
      const expectedUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
        inputParams
      )}.json?access_token=${
        process.env.NEXT_PUBLIC_MAPBOX_API_KEY
      }&country=US&limit=6&types=place,postcode,locality`;

      autocompleteSuggestionMapBoxAPI(inputParams, callbackMock);

      expect(fetchMock).toHaveBeenCalledWith(expectedUrl);
    });
  });

  describe('parseAutocompleteResults', () => {
    it('returns formatted results for autocomplete', () => {
      const expectedResults = [
        { label: '87106, Albuquerque, NM', value: '87106' },
        { label: '87121, Albuquerque, NM', value: '87121' },
        { label: '87110, Albuquerque, NM', value: '87110' },
        { label: '87114, Albuquerque, NM', value: '87114' },
        { label: '87120, Albuquerque, NM', value: '87120' },
        { label: '87109, Albuquerque, NM', value: '87109' }
      ];

      const formattedResults = parseAutocompleteResults(
        autocompleteInputZipResults
      );

      expect(formattedResults).toEqual(expectedResults);
    });

    it('returns formatted results without zip code', () => {
      const expectedResults = [
        { label: 'Albuquerque, NM', value: 'Albuquerque, NM' },
        { label: 'Alburnett, IA', value: 'Alburnett, IA' },
        { label: 'Alburgh, VT', value: 'Alburgh, VT' },
        { label: 'Alburtis, PA', value: 'Alburtis, PA' },
        { label: 'Alburg Center, VT', value: 'Alburg Center, VT' },
        { label: 'Alburg Springs, VT', value: 'Alburg Springs, VT' }
      ];

      const formattedResults = parseAutocompleteResults(autocompleteResults);

      expect(formattedResults).toEqual(expectedResults);
    });
  });

  describe('handle focus', () => {
    beforeEach(() => {
      fetchMock.mockResponse(
        JSON.stringify({
          features: [
            {
              place_name: 'Charlotte, North Carolina, United States'
            },
            {
              place_name: 'Charlottesville, Virginia, United States'
            },
            {
              place_name: 'Charlotte, Michigan, United States'
            },
            {
              place_name: 'Charlotte Hall, Maryland, United States'
            },
            {
              place_name: 'Charlotte, Tennessee, United States'
            },
            {
              place_name: 'Charlotte Harbor, Florida, United States'
            }
          ]
        })
      );
    });

    it('should select an item when pressing enter', async () => {
      const mockSelection = jest.fn();

      await act(async () => {
        render(<AutocompleteInput onSelection={mockSelection} />);
      });

      const inputElement = screen.getByRole('textbox') as HTMLInputElement;

      await act(async () => {
        await userEvent.type(inputElement, 'Charl');
      });

      await waitFor(() => {
        expect(screen.getByText('Charlotte, NC')).toBeInTheDocument();
      });

      await act(async () => {
        await userEvent.type(inputElement, '{arrowdown}');
      });

      await act(async () => {
        await userEvent.type(inputElement, '{enter}');
      });

      await waitFor(() => {
        expect(inputElement.value).toBe('Charlotte, NC');
        expect(mockSelection).toHaveBeenCalledWith('Charlotte, NC');
      });
    });

    test("it doesn't trap focus when pressing enter the second time", async () => {
      const handleSubmit = jest.fn((e) => e.preventDefault());

      await act(async () => {
        render(
          <form onSubmit={handleSubmit}>
            <AutocompleteInput />
          </form>
        );
      });

      const inputElement = screen.getByRole('textbox') as HTMLInputElement;

      await act(async () => {
        await userEvent.type(inputElement, 'Charl');
      });

      await waitFor(() => {
        expect(screen.getByText('Charlotte, NC')).toBeInTheDocument();
      });

      await act(async () => {
        await userEvent.type(inputElement, '{enter}');
      });

      await waitFor(() => {
        expect(inputElement.value).toBe('Charlotte, NC');
      });

      await act(async () => {
        await userEvent.type(inputElement, '{enter}');
      });

      await waitFor(() => {
        expect(handleSubmit).toHaveBeenCalledTimes(1);
      });
    });
  });
});
