import { fireEvent, render, screen } from '@testing-library/react';

import AutocompleteList from './AutocompleteList';

describe('AutocompleteList', () => {
  const autocompleteList = [
    { label: 'Item 1', value: 'Item 1' },
    { label: 'Item 2', value: 'Item 2' },
    { label: 'Item 3', value: 'Item 3' }
  ];
  const setInputValue = jest.fn();
  const setKeyword = jest.fn();
  const setIsInputFocused = jest.fn();
  const setCurrentFocus = jest.fn();
  const onSelection = jest.fn();

  beforeEach(() => {
    render(
      <AutocompleteList
        autocompleteList={autocompleteList}
        currentFocus={-1}
        listRef={{} as React.MutableRefObject<HTMLDivElement>}
        onSelection={onSelection}
        setInputValue={setInputValue}
        setKeyword={setKeyword}
        setIsInputFocused={setIsInputFocused}
        setCurrentFocus={setCurrentFocus}
      />
    );
  });

  afterEach(() => {
    setInputValue.mockClear();
    setKeyword.mockClear();
    setIsInputFocused.mockClear();
    setCurrentFocus.mockClear();
    onSelection.mockClear();
  });

  test('renders the autocomplete list', () => {
    const listItems = screen.getAllByText(/Item/);
    expect(listItems.length).toBe(autocompleteList.length);
  });

  test('calls setInputValue, onSelection, and setKeyword when an item is clicked', () => {
    const listItem = screen.getByText('Item 1');
    fireEvent.mouseDown(listItem);
    expect(setInputValue).toHaveBeenCalledWith('Item 1');
    expect(setKeyword).toHaveBeenCalledWith('Item 1');
    expect(onSelection).toHaveBeenCalledWith('Item 1');
  });

  test('calls setIsInputFocused with false when an item is clicked', () => {
    const listItem = screen.getByText('Item 1');
    fireEvent.mouseDown(listItem);
    expect(setIsInputFocused).toHaveBeenCalledWith(false);
  });

  test('resets setCurrentFocus when the mouse enters the list', () => {
    const listItem = screen.getByText('Item 2');
    fireEvent.mouseEnter(listItem);
    expect(setCurrentFocus).toHaveBeenCalledWith(-1);
  });
});
