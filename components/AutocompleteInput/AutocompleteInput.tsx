import { InputProps } from '@chakra-ui/input';
import { BoxProps } from '@chakra-ui/layout';
import { Box, Input } from '@chakra-ui/react';
import useDebounce from '@hooks/useDebounce';
import { fetchGeoLocation } from '@utils/fetchGeoLocation';
import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';

import SiteContext from '~/contexts/SiteContext';
import { getStateAbbreviation } from '~/utils/UsStates';

import AutocompleteList from './AutocompleteList';
const KEY_ARROW_UP = 'ArrowUp';
const KEY_ARROW_DOWN = 'ArrowDown';
const KEY_ENTER = 'Enter';

export interface AutocompleteInputProps extends InputProps {
  defaultValue?: string;
  buttonOutsideInput?: boolean;
  listContainerProps?: BoxProps;
  name?: string;
  onSelection?: (maybeKeyword?: string) => void;
  setKeyword?: Dispatch<SetStateAction<string>> | ((value: string) => void);
  setLatLng?: Dispatch<SetStateAction<string>> | ((value: string) => void);
}

export interface AutocompleteFormattedLabel {
  label: string;
  value: string;
}
interface MapboxFeature {
  id: string;
  type: string;
  place_type?: string[] | null;
  relevance: number;
  properties: { mapbox_id: string };
  text: string;
  place_name: string;
  bbox?: number[] | null;
  center?: number[] | null;
  geometry: {
    type: string;
    coordinates?: number[] | null;
  };
  context?:
    | {
        id: string;
        mapbox_id: string;
        wikidata: string;
        text: string;
        short_code?: string | null;
      }[]
    | null;
}

const CHARACTER_LIMIT = 2;

export const autocompleteSuggestionMapBoxAPI = (inputParams, callback) => {
  const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
    inputParams
  )}.json?access_token=${
    process.env.NEXT_PUBLIC_MAPBOX_API_KEY
  }&country=US&limit=6&types=place,postcode,locality`;
  fetch(url)
    .then((response) => response.json())
    .then((data) => callback(data.features as MapboxFeature[]))
    .catch((error) => {
      console.error('Mapbox error:', error);
    });
};

export const parseAutocompleteResults = (
  autocompleteResults: MapboxFeature[]
) => {
  return autocompleteResults.map((item) => {
    const isZip = item.place_type?.includes('postcode');
    const [city, stateAndZip] = item.place_name.split(', ');
    const lastSpaceIndex = stateAndZip.lastIndexOf(' ');

    const state = isZip ? stateAndZip.slice(0, lastSpaceIndex) : stateAndZip;
    const zip = isZip ? stateAndZip.slice(lastSpaceIndex + 1) : null;
    const data = {
      label: `${zip ? `${zip}, ` : ''}${city}, ${getStateAbbreviation(state)}`,
      value: `${zip ? `${zip}` : `${city}, ${getStateAbbreviation(state)}`}`
    };
    return data;
  });
};

function AutocompleteInput({
  defaultValue = '',
  buttonOutsideInput,
  listContainerProps,
  name,
  onSelection,
  setKeyword,
  setLatLng,
  ...rest
}: AutocompleteInputProps) {
  const [inputValue, setInputValue] = useState(defaultValue);
  const [geoData, setGeoData] = useState<{ city?: string; state?: string }>({});
  const debouncedInputValue = useDebounce(inputValue, 500);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [autocompleteResults, setAutocompleteResults] = useState<
    MapboxFeature[]
  >([]);
  const [autocompleteList, setAutocompleteList] = useState<
    AutocompleteFormattedLabel[]
  >([]);
  const [currentFocus, setCurrentFocus] = useState(-1);
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path || '';
  const listRef = useRef() as React.MutableRefObject<HTMLDivElement>;
  const isFocusedAndHasResults = isInputFocused && autocompleteList.length > 0;

  const setInputValues = useCallback(
    (input: string) => {
      setInputValue(input);
      setKeyword?.(input);
    },
    [setKeyword]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues(e.target.value);
    setIsInputFocused(true);
  };

  const handleAutocompleteResults = useCallback(
    (features: MapboxFeature[]) => {
      setAutocompleteResults(features);
      if (features[0]?.center && setLatLng) {
        const [lng, lat] = features[0]?.center;
        setLatLng(`${lat},${lng}`);
      }
    },
    [setLatLng]
  );

  const fetchAutocompleteResults = useCallback(
    (value: string) => {
      if (value.length > CHARACTER_LIMIT) {
        autocompleteSuggestionMapBoxAPI(value, handleAutocompleteResults);
      } else {
        setAutocompleteResults([]);
        setAutocompleteList([]);
      }
    },
    [handleAutocompleteResults]
  );

  useEffect(() => {
    fetchAutocompleteResults(debouncedInputValue);
  }, [debouncedInputValue, fetchAutocompleteResults]);

  const city = useMemo(() => geoData.city, [geoData.city]);
  const state = useMemo(() => geoData.state, [geoData.state]);

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchGeoLocation();
      setGeoData(data);
    };
    fetchData();
  }, [domain]);

  useEffect(() => {
    if (!inputValue && !isInputFocused && city && state) {
      setInputValues(`${city}, ${state}`);
    }
  }, [inputValue, isInputFocused, city, state, setInputValues]);

  useEffect(() => {
    if (autocompleteResults?.length > 0) {
      setAutocompleteList(parseAutocompleteResults(autocompleteResults));
    } else {
      setAutocompleteList([]);
    }
  }, [autocompleteResults]);

  const updateFocus = useCallback((focused: boolean) => {
    setIsInputFocused(focused);
  }, []);

  const handleBlur = useCallback(() => {
    if (isFocusedAndHasResults) {
      setInputValues(
        autocompleteList[currentFocus]?.value ?? autocompleteList[0]?.value
      );
    }
    updateFocus(false);
    setAutocompleteList([]);
    setCurrentFocus(-1);
  }, [
    isFocusedAndHasResults,
    autocompleteList,
    currentFocus,
    setInputValues,
    updateFocus
  ]);

  const handleArrowUp = () => {
    if (currentFocus > 0) setCurrentFocus(currentFocus - 1);
  };

  const handleArrowDown = () => {
    if (currentFocus + 1 < autocompleteList.length) {
      setCurrentFocus(currentFocus + 1);
    }
  };

  const handleEnter = (event: React.KeyboardEvent) => {
    if (!isFocusedAndHasResults) {
      onSelection?.(inputValue);
      return;
    }

    event.preventDefault();
    const value =
      autocompleteList[currentFocus]?.value ?? autocompleteList[0]?.value;

    if (value) {
      setInputValues(value);
      setIsInputFocused(false);
      setAutocompleteList([]);
      setCurrentFocus(-1); // Reset the focus index
      onSelection?.(value);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case KEY_ARROW_UP:
        event.preventDefault();
        handleArrowUp();
        break;
      case KEY_ARROW_DOWN:
        event.preventDefault();
        handleArrowDown();
        break;
      case KEY_ENTER:
        handleEnter(event);
        break;
      default:
        break;
    }
  };

  const handleClick = useCallback(() => {
    setIsInputFocused(true);
    if (inputValue.length > CHARACTER_LIMIT) {
      fetchAutocompleteResults(inputValue);
    }
  }, [inputValue, fetchAutocompleteResults]);

  return (
    <Box position="relative" width="100%">
      <Input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onClick={handleClick}
        onFocus={handleClick}
        onBlur={handleBlur}
        borderBottomRadius={isFocusedAndHasResults ? '0' : 'md'}
        {...rest}
      />
      {isFocusedAndHasResults && (
        <AutocompleteList
          autocompleteList={autocompleteList}
          currentFocus={currentFocus}
          listRef={listRef}
          onSelection={onSelection}
          setInputValue={setInputValue}
          setKeyword={setKeyword}
          setIsInputFocused={setIsInputFocused}
          setCurrentFocus={setCurrentFocus}
          {...listContainerProps}
        />
      )}
    </Box>
  );
}
export default AutocompleteInput;
