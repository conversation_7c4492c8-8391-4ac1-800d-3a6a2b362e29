import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';

const ClearFiltersButton = ({ onClick }) => (
  <Button
    type="button"
    size="sm"
    colorScheme="accent"
    variant="outline"
    onClick={onClick}
    elementAction={ElementActions.FILTERS}
    elementName={ElementNames.GENERIC_BUTTON}
    elementType={ElementTypes.BUTTON}
  >
    Clear filters
  </Button>
);

export default ClearFiltersButton;
