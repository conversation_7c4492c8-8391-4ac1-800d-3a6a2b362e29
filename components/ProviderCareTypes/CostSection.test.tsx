import '@testing-library/jest-dom';

import { render, screen } from '@utils/test-utils';

import { Metadata } from '~/types/Magnolia';

import CostSection from './CostSection';

describe('<CostSection />', () => {
  describe("when there's no cost", () => {
    it('does not render anything', () => {
      render(<CostSection minimumCost={null} metadata={{} as Metadata} />);
      expect(screen.queryByTestId('service-price')).not.toBeInTheDocument();
    });
  });

  describe("when there's a cost and 'obfuscated' flag is false", () => {
    it('renders the formatted cost not blurred', () => {
      render(<CostSection minimumCost={1234.56} metadata={{} as Metadata} />);

      const formattedCost = screen.queryByTestId('formatted-cost');
      const costCSS =
        formattedCost?.ownerDocument.defaultView?.getComputedStyle(
          formattedCost
        );

      expect(formattedCost).toBeInTheDocument();
      expect(formattedCost?.textContent).toEqual('$1,234.56');
      expect(costCSS?.getPropertyValue('filter')).not.toMatch(
        'var(--chakra-blur)'
      );
    });

    it('does not render the CTA button', () => {
      render(<CostSection minimumCost={1234.56} metadata={{} as Metadata} />);
      expect(screen.queryByTestId('unlock-price-cta')).not.toBeInTheDocument();
    });
  });

  describe("when there's a cost and 'obfuscated' flag is true", () => {
    it('renders the formatted cost blurred', () => {
      render(
        <CostSection
          minimumCost={1234.56}
          obfuscated
          obfuscatedCostCta={{ text: 'CTA Button', isInquiry: true }}
          metadata={{} as Metadata}
        />
      );
      const formattedCost = screen.queryByTestId('formatted-cost');
      const costCSS =
        formattedCost?.ownerDocument.defaultView?.getComputedStyle(
          formattedCost
        );

      expect(formattedCost).toBeInTheDocument();
      expect(costCSS?.getPropertyValue('filter')).toMatch('var(--chakra-blur)');
      expect(costCSS?.getPropertyValue('--chakra-blur')).toEqual('blur(10px)');
    });

    it('renders the CTA button', () => {
      render(
        <CostSection
          minimumCost={1234.56}
          obfuscated
          obfuscatedCostCta={{ text: 'CTA Button', isInquiry: true }}
          metadata={{} as Metadata}
        />
      );
      expect(screen.queryByTestId('unlock-price-cta')).toBeInTheDocument();
    });
  });
});
