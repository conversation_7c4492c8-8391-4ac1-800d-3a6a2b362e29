import '@testing-library/jest-dom';

import { mockProvider } from '@mocks/use-provider-fallback-mock';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { formatNumberLocale } from '@utils/number';
import { render, screen, waitFor } from '@utils/test-utils';

import axe from '~/axe-helper';
import { ProviderContextWrapper } from '~/contexts/Provider';

import ProviderCareTypes from './ProviderCareTypes';

const queryClient = new QueryClient();

function renderProviderCareTypesWithProvider(provider, props = {}) {
  return render(
    <QueryClientProvider client={queryClient}>
      <ProviderContextWrapper provider={provider}>
        <ProviderCareTypes {...props} />
      </ProviderContextWrapper>
    </QueryClientProvider>
  );
}

describe('<ProviderCareTypes />', () => {
  it('renders a title', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider, {
      title: 'Section title'
    });

    const heading = await waitFor(() =>
      screen.getByRole('heading', {
        name: 'Section title'
      })
    );
    expect(heading).toBeInTheDocument();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders a summary', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider, {
      summary: 'Section summary'
    });

    const text = screen.getByText('Section summary');
    expect(text).toBeInTheDocument();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders services', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider);
    mockProvider.services.forEach((service) => {
      const SERVICE_INFO_COUNT = 1;

      const serviceTitle = screen.getAllByText(service.category.name);
      expect(serviceTitle.length).toEqual(SERVICE_INFO_COUNT);

      const serviceDescription = screen.getAllByText(
        service.category.description
      );
      expect(serviceDescription.length).toEqual(SERVICE_INFO_COUNT);
    });

    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders services with images', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider, {
      withImages: true
    });

    const images = mockProvider.services.filter(
      (service) => service.category.imageURL
    );
    const rendered = screen.getAllByTestId('service-image');
    expect(rendered.length).toBe(images.length);
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders services without images', () => {
    renderProviderCareTypesWithProvider(mockProvider, { withImages: false });

    const rendered = screen.queryAllByTestId('service-image');
    expect(rendered.length).toBe(0);
  });

  it('renders services with descriptions', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider, {
      withDescription: true
    });

    const descriptions = mockProvider.services.filter(
      (service) => service.category.description
    );
    const rendered = screen.getAllByTestId('service-description');
    expect(rendered.length).toBe(descriptions.length);
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders services without descriptions', () => {
    renderProviderCareTypesWithProvider(mockProvider, {
      withDescription: false
    });

    const rendered = screen.queryAllByTestId('service-description');
    expect(rendered.length).toBe(0);
  });

  it('renders services with prices', async () => {
    const { container } = renderProviderCareTypesWithProvider(mockProvider, {
      withPricing: true
    });
    mockProvider.services.forEach((service) => {
      if (service.costs?.startingPriceCents) {
        const parsedPrice = formatNumberLocale(
          service.costs.startingPriceCents / 100
        );
        const price = screen.getByText(`$${parsedPrice}`);
        expect(price).toBeInTheDocument();
      }
    });

    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders services without prices', () => {
    renderProviderCareTypesWithProvider(mockProvider, { withPricing: false });

    const rendered = screen.queryAllByTestId('service-price');
    expect(rendered.length).toBe(0);
  });
});
