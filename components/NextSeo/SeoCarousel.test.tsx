import { render } from '@testing-library/react';
import React from 'react';

import SeoCarousel from './SeoCarousel';

jest.mock('next/head', () => {
  return {
    __esModule: true,
    default: ({ children }: { children: Array<React.ReactElement> }) => {
      return children;
    }
  };
});

describe('SeoCarousel', () => {
  it('renders the JSON-LD script with correct data', () => {
    const props = {
      numberOfItems: 5,
      name: 'Sample Carousel',
      data: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Item 1',
          url: 'https://example.com/item1',
          image: {
            '@type': 'ImageObject',
            name: 'Item 1',
            url: 'https://example.com/item1.jpg'
          }
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Item 2',
          url: 'https://example.com/item2',
          image: {
            '@type': 'ImageObject',
            name: 'Item 2',
            url: 'https://example.com/item2.jpg'
          }
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: 'Item 3',
          url: 'https://example.com/item3',
          image: {
            '@type': 'ImageObject',
            name: 'Item 3',
            url: 'https://example.com/item3.jpg'
          }
        },
        {
          '@type': 'ListItem',
          position: 4,
          name: 'Item 4',
          url: 'https://example.com/item4',
          image: {
            '@type': 'ImageObject',
            name: 'Item 4',
            url: 'https://example.com/item4.jpg'
          }
        },
        {
          '@type': 'ListItem',
          position: 5,
          name: 'Item 5',
          url: 'https://example.com/item5',
          image: {
            '@type': 'ImageObject',
            name: 'Item 5',
            url: 'https://example.com/item5.jpg'
          }
        }
      ]
    };

    render(<SeoCarousel {...props} />);

    const script = document.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    const jsonContent = JSON.parse(script?.innerHTML || '');

    expect(jsonContent).toEqual({
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      numberOfItems: props.numberOfItems,
      name: props.name,
      itemListElement: props.data
    });
  });
});
