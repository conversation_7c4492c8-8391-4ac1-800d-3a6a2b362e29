import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import {
  providerEmail,
  reviewData,
  reviewResponseData,
  token
} from '@utils/test-utils';
import React from 'react';

import ReviewResponse from './ReviewResponse';

// Mock postReviewResponse function
jest.mock('@services/review/api', () => ({
  postReviewResponse: jest.fn()
}));

// Mock @chakra-ui/react
jest.mock('@chakra-ui/react', () => ({
  ...jest.requireActual('@chakra-ui/react'),
  useToast: jest.fn()
}));

const toastMock = jest.fn();
jest.requireMock('@chakra-ui/react').useToast.mockReturnValue(toastMock);

const mockMetadata = {
  '@index': 0,
  '@name': '0',
  '@path': '/caring.com/reviews-notifications/review-response-post/main/0',
  '@id': '0af85cf7-ba31-4226-843e-bb7d6374228a',
  '@nodeType': 'mgnl:component',
  'mgnl:template': 'spa-lm:components/reviewResponse',
  'mgnl:created': '2023-08-15T21:56:53.110+02:00',
  'mgnl:lastModified': '2023-10-04T16:39:26.113+02:00',
  '@nodes': []
};

const mockAnalytics = {
  track: jest.fn()
};
window.tracking = mockAnalytics;

describe('ReviewResponse component', () => {
  it('renders the component and submits the form successfully', async () => {
    // Mock postReviewResponse to return success
    const mockPostReviewResponse = jest.requireMock(
      '@services/review/api'
    ).postReviewResponse;
    mockPostReviewResponse.mockResolvedValue({ success: true });

    render(
      <ReviewResponse
        data={{
          review: reviewData,
          token
        }}
        successUrl="success"
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Wait for the component to render
    await waitFor(() => {
      screen.getAllByText('Respond to a review');
      screen.getByText('Review and Response Guidelines');
    });

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Your name'), {
      target: { value: reviewResponseData.authorName }
    });
    fireEvent.change(screen.getByLabelText('Your title'), {
      target: { value: reviewResponseData.authorTitle }
    });
    fireEvent.change(
      screen.getByLabelText(/Your public response to this review/),
      {
        target: { value: reviewResponseData.content }
      }
    );
    fireEvent.change(screen.getByLabelText('Your email'), {
      target: { value: reviewResponseData.authorEmail }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Submit Response' }));

    // Wait for the form submission
    await waitFor(() => {
      expect(mockPostReviewResponse).toHaveBeenCalledWith(
        token,
        reviewResponseData
      );
    });
  });

  it('renders the component and displays error toast on form submission failure', async () => {
    // Mock postReviewResponse to return failure
    const mockPostReviewResponse = jest.requireMock(
      '@services/review/api'
    ).postReviewResponse;
    mockPostReviewResponse.mockResolvedValue({
      success: false,
      error: 'Submission failed'
    });

    render(
      <ReviewResponse
        data={{
          review: reviewData,
          token
        }}
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Your name'), {
      target: { value: reviewResponseData.authorName }
    });
    fireEvent.change(screen.getByLabelText('Your title'), {
      target: { value: reviewResponseData.authorTitle }
    });
    fireEvent.change(
      screen.getByLabelText(/Your public response to this review/),
      {
        target: { value: reviewResponseData.content }
      }
    );
    fireEvent.change(screen.getByLabelText('Your email'), {
      target: { value: reviewResponseData.authorEmail }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Submit Response' }));

    // Wait for the form submission
    await waitFor(() => {
      expect(mockPostReviewResponse).toHaveBeenCalledWith(
        token,
        reviewResponseData
      );
    });

    // Verify that the toast function was called with the expected values
    expect(toastMock).toHaveBeenCalledWith({
      title: 'Error',
      description: 'Submission failed',
      status: 'error',
      duration: null,
      isClosable: true,
      position: 'top-right'
    });
  });

  it('renders the component without review data', async () => {
    render(
      <ReviewResponse
        data={{
          review: null,
          token
        }}
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Wait for the component to render
    await waitFor(() => {
      screen.getByText('Respond to a review');
      screen.getByText('Review and Response Guidelines');
    });
  });

  it('should fill the email field with provider email', () => {
    render(
      <ReviewResponse
        data={{
          review: reviewData,
          token,
          providerEmail
        }}
        successUrl="/success"
        guidelinesUrl="/guidelines"
        metadata={mockMetadata}
      />
    );

    // Check if the email field is filled with the provider's email
    expect(screen.getByLabelText('Your email')).toHaveValue(providerEmail);
  });

  it('should not fill the email field if provider email is empty or null', () => {
    render(
      <ReviewResponse
        data={{
          review: reviewData,
          token,
          providerEmail: null
        }}
        successUrl="/success"
        guidelinesUrl="/guidelines"
        metadata={mockMetadata}
      />
    );

    expect(screen.getByLabelText('Your email')).toHaveValue('');
  });
});
