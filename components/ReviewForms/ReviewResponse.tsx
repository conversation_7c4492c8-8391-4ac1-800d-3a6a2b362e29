'use client';

import {
  <PERSON>,
  Card,
  CardBody,
  Container,
  Divider,
  Heading,
  HStack,
  Link,
  Text,
  useToast
} from '@chakra-ui/react';
import {
  stepContent,
  stepSubmission
} from '@components/Analytics/events/eventContracts';
import useFormStepSubmission, {
  FormType
} from '@components/Analytics/events/FormStepSubmission';
import useFormSubmission from '@components/Analytics/events/FormSubmission';
import { zodResolver } from '@hookform/resolvers/zod';
import { postReviewResponse } from '@services/review/api';
import { safeFormat } from '@utils/dates';
import parse from 'html-react-parser';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';
import { z as zod } from 'zod';

import InfoSection from './InfoSection';
import ReviewResponseForm from './ReviewForm';
import {
  formFieldResonsePrompts,
  formFieldTypes,
  ReviewFormPageProps
} from './ReviewsForm.utils';

export function LeftColumnContent({ title, desc }) {
  return (
    <Box p={5} minW="350px" maxW="320px" height="100%">
      <Heading fontSize="md" lineHeight="1">
        {title}
      </Heading>
      <Text as="section" fontSize="sm" mt={4}>
        {desc}
      </Text>
    </Box>
  );
}

// Review Response Form Schema
export const ReviewResponseSchema = zod.object({
  authorName: zod
    .string()
    .min(2, { message: 'Name must be at least 2 characters long' }),
  authorTitle: zod
    .string()
    .min(4, { message: 'Title must be at least 4 characters long' }),
  content: zod
    .string()
    .min(4, { message: 'Response must be at least 4 characters long' }),
  authorEmail: zod.string().email('Invalid email')
});

export type ReviewResponseSchemaType = zod.infer<typeof ReviewResponseSchema>;

const ReviewResponse: React.FC<ReviewFormPageProps> = ({
  data,
  metadata,
  successUrl,
  guidelinesUrl,
  formattedDomain,
  infoSectionContent
}) => {
  const toast = useToast();
  const { review, token } = data;
  const formId = 'reviewResponseForm';
  const createDate = review?.createdAt
    ? safeFormat(review.createdAt, 'MM/dd/yy')
    : '';

  const formStepSubmission = useFormStepSubmission();
  const formSubmission = useFormSubmission();

  const { register, handleSubmit, formState, reset } =
    useForm<ReviewResponseSchemaType>({
      mode: 'onBlur',
      resolver: zodResolver(ReviewResponseSchema),
      defaultValues: {
        authorName: '',
        authorTitle: '',
        content: '',
        authorEmail: data?.providerEmail ?? ''
      }
    });

  const formFieldsNamespace = [
    successUrl ?? '',
    guidelinesUrl ?? '',
    infoSectionContent ?? ''
  ].join(' ');

  const handleFormEvents = useCallback(
    (data, formInstanceId: string) => {
      const formFieldsPrompts = formFieldResonsePrompts(formattedDomain);
      const formTemplateId = uuidv5(formFieldsNamespace, metadata['@id']);
      const formStepInstanceId = uuidv4();
      const stepContent: stepContent[] = [];
      const stepSubmission: stepSubmission[] = [];
      const stepIndex = 1;
      const stepId = uuidv5(String(stepIndex), metadata['@id']);
      if (Object.keys(data).length > 0) {
        Object.entries(data).map((prompt, i) => {
          const field = prompt[0];
          const value = prompt[1] as string;

          stepContent.push({
            prompt_id: uuidv5(field, metadata['@id']),
            prompt_type: formFieldTypes[field] ?? '',
            prompt_instance_id: uuidv4(),
            prompt_index: i + 1,
            prompt_value: formFieldsPrompts[field] ?? '',
            response_array: [
              {
                response_value: value,
                response_id: uuidv4()
              }
            ]
          });
        });
        stepSubmission.push({
          step_id: stepId,
          step_instance_id: formStepInstanceId,
          step_index: stepIndex
        });
      }

      formStepSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        form_type: FormType.REVIEW,
        step_id: stepId,
        step_instance_id: formStepInstanceId,
        step_index: 1,
        step_content: stepContent
      });
      formSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        step_submissions: stepSubmission,
        form_type: FormType.REVIEW
      });
    },
    [
      metadata,
      formFieldsNamespace,
      formStepSubmission,
      formSubmission,
      formattedDomain
    ]
  );

  const onSubmit = handleSubmit(async (data) => {
    const formInstanceId = uuidv4();
    if (!token) {
      return toast({
        title: 'Invalid review',
        description:
          'Please make sure you have a valid review before submitting.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top-right'
      });
    }
    try {
      const response = await postReviewResponse(token, {
        ...data
      });
      if (response.success) {
        if (successUrl) {
          window.location.assign(successUrl);
        } else {
          toast({
            title: 'Success',
            description: 'Your response has been submitted successfully!',
            status: 'success',
            duration: 3000,
            isClosable: true,
            position: 'top-right'
          });
          reset();
        }
      } else {
        toast({
          title: 'Error',
          description: response.error,
          status: 'error',
          duration: null,
          isClosable: true,
          position: 'top-right'
        });
      }
      handleFormEvents({ ...data, response: response.error }, formInstanceId);
    } catch (error) {
      console.error('error', error);
      toast({
        title: 'Error',
        description:
          'An error occurred while creating the response. Please try again later.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top-right'
      });
    }
  });

  return (
    <Container maxWidth="container.xl" marginBottom="20px">
      <Heading size="xl">Respond to a review</Heading>
      <Link
        href={guidelinesUrl}
        target="_blank"
        fontSize="sm"
        textDecoration="underline"
      >
        Review and Response Guidelines
      </Link>
      {review && (
        <HStack
          mt={6}
          width="full"
          marginTop={10}
          alignItems="flex-start"
          flexDir={{ base: 'column', md: 'row' }}
        >
          <LeftColumnContent
            title="Published review"
            desc={`Posted by ${review?.authorName} on ${createDate}`}
          />
          <Box p={5} width="full">
            <Card width="full">
              <CardBody>
                <Text as="h4" fontSize="xl" fontWeight="bold" mb="1">
                  {review?.title}
                </Text>
                <Text>{parse(review?.text)}</Text>
              </CardBody>
            </Card>
          </Box>
        </HStack>
      )}
      <Divider />
      <ReviewResponseForm
        formId={formId}
        register={register}
        onSubmit={onSubmit}
        formState={formState}
        submitButtonLabel="Submit Response"
        responseSectionTitle="Response"
        responseSectionDescription="Your response will be visible by everyone"
        responseSectionFormTitle={`Your public response to this review (to be posted to ${formattedDomain})`}
        disclaimer={`I acknowledge that I am authorized to submit this official response, which may be published on ${formattedDomain} below the review.`}
        infoSectionTitle="Your info"
        infoSectionDescription={
          <InfoSection infoSectionContent={infoSectionContent} />
        }
      />
    </Container>
  );
};

export default ReviewResponse;
