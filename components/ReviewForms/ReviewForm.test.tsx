import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import ReviewForm from './ReviewForm';

describe('ReviewForm', () => {
  const mockOnSubmit = jest.fn();

  const formState = {
    errors: {
      content: { type: 'manual', message: 'Text is required' },
      authorName: { type: 'manual', message: 'Author name is required' },
      authorTitle: { type: 'manual', message: 'Author title is required' },
      authorEmail: { type: 'manual', message: 'Invalid email' }
    },
    isDirty: false,
    isLoading: false,
    isSubmitted: false,
    isSubmitSuccessful: false,
    submitCount: 0,
    isValid: false,
    isSubmitting: false,
    isValidating: false,
    dirtyFields: {},
    touchedFields: {}
  };

  const register = jest.fn();

  it('renders form with inputs and error messages', () => {
    render(
      <ReviewForm
        onSubmit={mockOnSubmit}
        formId="test-form"
        formState={formState}
        register={register}
        disclaimer="Disclaimer test"
        responseSectionTitle="Response"
        responseSectionFormTitle="Test response section title"
        responseSectionDescription="Test description"
        infoSectionTitle="Your info"
        infoSectionDescription="Info description"
      />
    );

    expect(screen.getByTestId('review-response-form')).toBeInTheDocument();

    expect(screen.getByLabelText('Your name')).toBeInTheDocument();
    expect(screen.getByLabelText('Your title')).toBeInTheDocument();
    expect(screen.getByLabelText('Your email')).toBeInTheDocument();

    expect(screen.getByText('Text is required')).toBeInTheDocument();
    expect(screen.getByText('Author name is required')).toBeInTheDocument();
    expect(screen.getByText('Author title is required')).toBeInTheDocument();
    expect(screen.getByText('Invalid email')).toBeInTheDocument();
  });

  it('calls onSubmit when form is submitted', () => {
    const mockOnSubmit = jest.fn();

    const { getByTestId } = render(
      <ReviewForm
        onSubmit={mockOnSubmit}
        formId="test-form"
        formState={formState}
        register={register}
        responseSectionTitle="Response"
        responseSectionDescription="Test description"
        infoSectionTitle="Your info"
        infoSectionDescription="Info description"
        responseSectionFormTitle="Test response section title"
      />
    );

    const form = getByTestId('review-response-form');
    fireEvent.submit(form);
    expect(mockOnSubmit).toHaveBeenCalledTimes(1);
  });

  it('displays submitButtonLabel on submit button', () => {
    render(
      <ReviewForm
        onSubmit={mockOnSubmit}
        formId="test-form"
        formState={formState}
        register={register}
        submitButtonLabel="Custom Submit Label"
        disclaimer="Disclaimer test"
        responseSectionTitle="Response"
        responseSectionDescription="Test description"
        infoSectionTitle="Your info"
        infoSectionDescription="Info description"
        responseSectionFormTitle="Test response section title"
      />
    );

    expect(
      screen.getByRole('button', { name: 'Custom Submit Label' })
    ).toBeInTheDocument();
  });
});
