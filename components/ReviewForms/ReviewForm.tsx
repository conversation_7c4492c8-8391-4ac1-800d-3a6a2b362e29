import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Divider,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Text,
  Textarea,
  VStack
} from '@chakra-ui/react';
import useFormFieldFocused from '@components/Analytics/events/FormFieldFocused';
import { FormType } from '@components/Analytics/events/FormStepSubmission';
import {
  handleFormBlur,
  handleFormFocus
} from '@components/InquiryForm/InquiryForm.utils';
import React from 'react';
import { useRef, useState } from 'react';
import { FormState, UseFormRegister } from 'react-hook-form';

import { LeftColumnContent } from './ReviewResponse';
import { reviewFormCommonFields } from './ReviewsForm.utils';

interface ReviewResponseFields {
  content: string;
  authorName: string;
  authorTitle: string;
  authorEmail: string;
}

export interface ReviewFormProps {
  formId: string;
  disclaimer?: string;
  submitButtonLabel?: string;
  buttonColorScheme?: string;
  infoSectionTitle: React.ReactNode;
  responseSectionTitle: React.ReactNode;
  infoSectionDescription: React.ReactNode;
  responseSectionDescription: React.ReactNode;
  responseSectionFormTitle: React.ReactNode;
  formState: FormState<ReviewResponseFields>;
  register: UseFormRegister<ReviewResponseFields>;
  onSubmit: () => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({
  onSubmit,
  formId,
  register,
  formState,
  disclaimer,
  infoSectionTitle,
  responseSectionTitle,
  infoSectionDescription,
  responseSectionDescription,
  responseSectionFormTitle,
  buttonColorScheme = 'primary',
  submitButtonLabel = 'Submit'
}) => {
  const [formFocused, setFormFocused] = useState(false);
  const formFieldFocused = useFormFieldFocused();
  const reviewForm = useRef<HTMLFormElement>(null);
  const { errors } = formState;

  return (
    <form
      id={formId}
      onSubmit={onSubmit}
      data-testid="review-response-form"
      onClick={(e) =>
        handleFormFocus(
          e,
          formFocused,
          setFormFocused,
          formFieldFocused,
          formId,
          FormType.REVIEW
        )
      }
      onBlur={(e) => handleFormBlur(e, formFocused, reviewForm, setFormFocused)}
      ref={reviewForm}
    >
      <>
        {/* Response */}
        <HStack
          mt={6}
          width="full"
          marginTop={10}
          alignItems={'start'}
          flexDir={{ base: 'column', md: 'row' }}
        >
          <LeftColumnContent
            title={responseSectionTitle}
            desc={responseSectionDescription}
          />
          <Box p={5} width="full">
            <Card width="full">
              <CardBody>
                <FormControl isInvalid={Boolean(errors.content)}>
                  <FormLabel my={2}>{responseSectionFormTitle}</FormLabel>
                  <Textarea
                    {...register('content', { required: true })}
                    rows={8}
                  />
                  <FormErrorMessage>
                    {String(errors?.content?.message)}
                  </FormErrorMessage>
                </FormControl>
              </CardBody>
            </Card>
          </Box>
        </HStack>
        <Divider />
        <HStack
          mt={6}
          width="full"
          marginTop={10}
          alignItems={'start'}
          flexDir={{ base: 'column', md: 'row' }}
        >
          <LeftColumnContent
            title={infoSectionTitle}
            desc={infoSectionDescription}
          />
          <Box p={5} width="full">
            <Card>
              <CardBody>
                {/* Name */}
                <FormControl isInvalid={Boolean(errors.authorName)}>
                  <FormLabel my={2}>
                    {reviewFormCommonFields.authorName}
                  </FormLabel>
                  <Input {...register('authorName', { required: true })} />
                  <FormErrorMessage>
                    {String(errors?.authorName?.message)}
                  </FormErrorMessage>
                </FormControl>
                {/* Title */}
                <FormControl isInvalid={Boolean(errors.authorTitle)}>
                  <FormLabel my={2}>
                    {reviewFormCommonFields.authorTitle}
                  </FormLabel>
                  <Input {...register('authorTitle', { required: true })} />
                  <FormErrorMessage>
                    {String(errors?.authorTitle?.message)}
                  </FormErrorMessage>
                </FormControl>
                {/* E-mail */}
                <FormControl isInvalid={Boolean(errors.authorEmail)}>
                  <FormLabel my={2}>
                    {reviewFormCommonFields.authorEmail}
                  </FormLabel>
                  <Input {...register('authorEmail', { required: true })} />
                  <FormErrorMessage>
                    {String(errors?.authorEmail?.message)}
                  </FormErrorMessage>
                </FormControl>
              </CardBody>
            </Card>
          </Box>
        </HStack>
        {/* Submit Button and disclaimer  */}
        <VStack px={5} alignItems="flex-end">
          {disclaimer && <Text my={5}>{disclaimer}</Text>}
          <Button
            px={10}
            size="md"
            form={formId}
            type="submit"
            colorScheme={buttonColorScheme}
          >
            {submitButtonLabel}
          </Button>
        </VStack>
      </>
    </form>
  );
};

export default ReviewForm;
