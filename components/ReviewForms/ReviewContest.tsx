'use client';

import {
  <PERSON>,
  Card,
  CardBody,
  Container,
  Divider,
  Heading,
  HStack,
  Link,
  Text,
  useToast
} from '@chakra-ui/react';
import {
  stepContent,
  stepSubmission
} from '@components/Analytics/events/eventContracts';
import useFormStepSubmission, {
  FormType
} from '@components/Analytics/events/FormStepSubmission';
import useFormSubmission from '@components/Analytics/events/FormSubmission';
import { zodResolver } from '@hookform/resolvers/zod';
import { postReviewContest } from '@services/review/api';
import { safeFormat } from '@utils/dates';
import parse from 'html-react-parser';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

import InfoSection from './InfoSection';
import ReviewForm from './ReviewForm';
import {
  LeftColumnContent,
  ReviewResponseSchema,
  ReviewResponseSchemaType
} from './ReviewResponse';
import {
  formFieldsContestPrompts,
  formFieldTypes,
  ReviewFormPageProps
} from './ReviewsForm.utils';

const ReviewContest: React.FC<ReviewFormPageProps> = ({
  data,
  successUrl,
  guidelinesUrl,
  infoSectionContent,
  metadata
}) => {
  const toast = useToast();
  const { review, token } = data;
  const formId = 'reviewContestForm';
  const createDate = review?.createdAt
    ? safeFormat(review.createdAt, 'MM/dd/yy')
    : '';

  const formStepSubmission = useFormStepSubmission();
  const formSubmission = useFormSubmission();

  const { register, handleSubmit, formState, reset } =
    useForm<ReviewResponseSchemaType>({
      mode: 'onBlur',
      resolver: zodResolver(ReviewResponseSchema),
      defaultValues: {
        authorName: '',
        authorTitle: '',
        content: '',
        authorEmail: data?.providerEmail ?? ''
      }
    });

  const formFieldsNamespace = [
    successUrl ?? '',
    guidelinesUrl ?? '',
    infoSectionContent ?? ''
  ].join(' ');

  const handleFormEvents = useCallback(
    (data, formInstanceId: string) => {
      const formTemplateId = uuidv5(formFieldsNamespace, metadata['@id']);
      const formStepInstanceId = uuidv4();
      const stepContent: stepContent[] = [];
      const stepSubmission: stepSubmission[] = [];
      const stepIndex = 1;
      const stepId = uuidv5(String(stepIndex), metadata['@id']);
      if (Object.keys(data).length > 0) {
        Object.entries(data).map((prompt, i) => {
          const field = prompt[0];
          const value = prompt[1] as string;

          stepContent.push({
            prompt_id: uuidv5(field, metadata['@id']),
            prompt_type: formFieldTypes[field] ?? '',
            prompt_instance_id: uuidv4(),
            prompt_index: i + 1,
            prompt_value: formFieldsContestPrompts[field] ?? '',
            response_array: [
              {
                response_value: value,
                response_id: uuidv4()
              }
            ]
          });
        });
        stepSubmission.push({
          step_id: stepId,
          step_instance_id: formStepInstanceId,
          step_index: stepIndex
        });
      }

      formStepSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        form_type: FormType.REVIEW,
        step_id: stepId,
        step_instance_id: formStepInstanceId,
        step_index: 1,
        step_content: stepContent
      });
      formSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        step_submissions: stepSubmission,
        form_type: FormType.REVIEW
      });
    },
    [metadata, formFieldsNamespace, formStepSubmission, formSubmission]
  );

  const onSubmit = handleSubmit(async (data) => {
    const formInstanceId = uuidv4();

    if (!token) {
      return toast({
        title: 'Invalid Review',
        description:
          'Please make sure you have a valid review before submitting.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top-right'
      });
    }
    try {
      const response = await postReviewContest(token, { ...data });
      if (response.success) {
        if (successUrl) {
          window.location.href = successUrl;
        } else {
          toast({
            title: 'Success',
            description: 'Your contestation has been submitted successfully!',
            status: 'success',
            duration: 3000,
            isClosable: true,
            position: 'top-right'
          });
          reset();
        }
      } else {
        toast({
          title: 'Error',
          description: response.error,
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top-right'
        });
      }
      handleFormEvents({ ...data, response: response.error }, formInstanceId);
    } catch (error) {
      console.error('error', error);
      toast({
        title: 'Error',
        description:
          'An error occurred while creating the contestation. Please try again later.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top-right'
      });
    }
  });

  return (
    <Container maxW="container.xl" marginBottom="20px">
      <Heading size="xl">Request removal</Heading>
      <Link
        href={guidelinesUrl}
        target="_blank"
        fontSize="sm"
        textDecoration="underline"
      >
        Review and Response Guidelines
      </Link>
      {review && (
        <HStack
          mt={6}
          width="full"
          marginTop={10}
          alignItems="flex-start"
          flexDir={{ base: 'column', md: 'row' }}
        >
          <LeftColumnContent
            title="Published review"
            desc={`Posted by ${review?.authorName} on ${createDate}`}
          />
          <Box p={5} width="full">
            <Card width="full">
              <CardBody>
                <Text fontSize="xl" fontWeight="bold" as="h4" mb="1">
                  {review?.title}
                </Text>
                <Text>{parse(review?.text)}</Text>
              </CardBody>
            </Card>
          </Box>
        </HStack>
      )}
      <Divider />
      <ReviewForm
        formId={formId}
        register={register}
        onSubmit={onSubmit}
        formState={formState}
        buttonColorScheme="red"
        submitButtonLabel="Request Removal"
        responseSectionTitle={formFieldsContestPrompts.content}
        responseSectionDescription={
          <Text>
            Please state factual errors within the review, and / or provide
            reasons you think the review doesn&apos;t meet the{' '}
            <Link
              href={guidelinesUrl}
              target="_blank"
              textDecoration="underline"
            >
              Review Guidelines
            </Link>
            .
          </Text>
        }
        responseSectionFormTitle="Reasons to remove"
        infoSectionTitle="Your info"
        infoSectionDescription={
          <InfoSection infoSectionContent={infoSectionContent} />
        }
      />
    </Container>
  );
};

export default ReviewContest;
