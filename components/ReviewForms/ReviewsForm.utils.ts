import { SingleReviewResponse } from '@services/review';

import { Metadata } from '~/types/Magnolia';

export interface ReviewFormPageProps {
  data: {
    review?: SingleReviewResponse;
    token?: string;
    providerEmail?: string;
  };
  successUrl?: string;
  guidelinesUrl: string;
  formattedDomain: string;
  infoSectionContent?: string;
  metadata: Metadata;
}

export const formFieldTypes = {
  authorName: 'text',
  authorTitle: 'text',
  content: 'text',
  authorEmail: 'text'
};

export const reviewFormCommonFields = {
  authorName: 'Your name',
  authorTitle: 'Your title',
  authorEmail: 'Your email'
};

export const formFieldsContestPrompts = {
  ...reviewFormCommonFields,
  content: `Your reasons for contesting this review`
};

export const formFieldResonsePrompts = (formattedDomain) => ({
  ...reviewFormCommonFields,
  content: `Your public response to this review (to be posted to ${formattedDomain})`
});
