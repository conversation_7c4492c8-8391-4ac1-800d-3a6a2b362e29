import { getReviewByToken, SingleReviewResponse } from '@services/review/api';
import { GetServerSidePropsContext } from 'next';

import { ReviewFormProps } from './ReviewForm';

export const getServerSideComponentProps = async (
  _: ReviewFormProps,
  context: GetServerSidePropsContext
): Promise<{
  review: SingleReviewResponse | null;
  token: string | null;
  providerEmail: string | undefined;
}> => {
  const { token, provider_email } = context.query;

  if (!token || typeof token !== 'string') {
    return {
      token: null,
      review: null,
      providerEmail: provider_email as string
    };
  }
  const response = await getReviewByToken(token);

  if (response) {
    return {
      review: response,
      token: token as string,
      providerEmail: provider_email as string
    };
  } else {
    return { token, review: null, providerEmail: provider_email as string };
  }
};
