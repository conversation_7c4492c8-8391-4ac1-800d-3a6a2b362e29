import { render } from '@testing-library/react';
import React from 'react';

import { SuccessToastDescription } from './ReviewSubmissionsNew';

describe('SuccessToastDescription', () => {
  it('renders with the default message', () => {
    const { getByText } = render(
      <SuccessToastDescription domain="Caring.com" />
    );
    const messageElement = getByText(
      /Your review is being processed. If approved it will appear in 3 business days./i
    );
    expect(messageElement).toBeInTheDocument();
  });
  it('renders with the provided message', () => {
    const message = 'Test message.';
    const { getByText } = render(
      <SuccessToastDescription domain="Caring.com" message={message} />
    );
    const messageElement = getByText(/Test message/i);
    expect(messageElement).toBeInTheDocument();
  });
});
