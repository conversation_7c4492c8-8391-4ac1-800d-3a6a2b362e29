import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import {
  providerEmail,
  reviewData,
  reviewResponseData,
  token
} from '@utils/test-utils';
import React from 'react';

import ReviewContest from './ReviewContest';

// Mock postReviewContest function
jest.mock('@services/review/api', () => ({
  postReviewContest: jest.fn()
}));

// Mock @chakra-ui/react
jest.mock('@chakra-ui/react', () => ({
  ...jest.requireActual('@chakra-ui/react'),
  useToast: jest.fn()
}));

const mockMetadata = {
  '@index': 0,
  '@name': '0',
  '@path': '/caring.com/reviews-notifications/review-response-contest/main/0',
  '@id': 'da8aac60-c5cf-47bd-8ee4-cadc8de30626',
  '@nodeType': 'mgnl:component',
  'mgnl:template': 'spa-lm:components/reviewContest',
  'mgnl:created': '2023-08-18T04:01:55.917+02:00',
  'mgnl:lastModified': '2023-10-04T16:40:35.441+02:00',
  '@nodes': []
};

const mockLocation = {
  pathname: '',
  hash: '',
  search: '',
  assign: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  toString: jest.fn()
};

describe('ReviewContest component', () => {
  beforeEach(() => {
    const mockAnalytics = {
      ready: jest.fn(),
      track: jest.fn(),
      user: jest.fn()
    };
    window.tracking = mockAnalytics;

    delete window.location;
    window.location = mockLocation;
  });

  it('renders the component and submits the form successfully', async () => {
    // Mock postReviewContest to return success
    const mockPostReviewContest = jest.requireMock(
      '@services/review/api'
    ).postReviewContest;
    mockPostReviewContest.mockResolvedValue({ success: true });

    render(
      <ReviewContest
        data={{
          review: reviewData,
          token,
          providerEmail
        }}
        successUrl="/success"
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Wait for the component to render
    await waitFor(() => {
      screen.getAllByText('Request Removal');
      screen.getByText('Review and Response Guidelines');
    });

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Your name'), {
      target: { value: reviewResponseData.authorName }
    });
    fireEvent.change(screen.getByLabelText('Your title'), {
      target: { value: reviewResponseData.authorTitle }
    });
    fireEvent.change(screen.getByLabelText('Reasons to remove'), {
      target: { value: reviewResponseData.content }
    });
    fireEvent.change(screen.getByLabelText('Your email'), {
      target: { value: reviewResponseData.authorEmail }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Request Removal' }));

    // Wait for the form submission
    await waitFor(() => {
      expect(mockPostReviewContest).toHaveBeenCalledWith(
        token,
        reviewResponseData
      );
    });
  });

  it('renders the component and displays error toast on form submission failure', async () => {
    // Mock postReviewContest to return failure
    const mockPostReviewContest = jest.requireMock(
      '@services/review/api'
    ).postReviewContest;
    mockPostReviewContest.mockResolvedValue({
      success: false,
      error: 'Submission failed'
    });

    // Mock the toast function
    const toastMock = jest.fn();
    const useToastMock = jest.requireMock('@chakra-ui/react').useToast;
    useToastMock.mockReturnValue(toastMock);

    render(
      <ReviewContest
        data={{
          review: reviewData,
          token
        }}
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Your name'), {
      target: { value: reviewResponseData.authorName }
    });
    fireEvent.change(screen.getByLabelText('Your title'), {
      target: { value: reviewResponseData.authorTitle }
    });
    fireEvent.change(screen.getByLabelText('Reasons to remove'), {
      target: { value: reviewResponseData.content }
    });
    fireEvent.change(screen.getByLabelText('Your email'), {
      target: { value: reviewResponseData.authorEmail }
    });
    fireEvent.click(screen.getByRole('button', { name: 'Request Removal' }));

    // Wait for the form submission
    await waitFor(() => {
      expect(mockPostReviewContest).toHaveBeenCalledWith(
        token,
        reviewResponseData
      );
    });

    // Verify that the toast function was called with the expected values
    expect(toastMock).toHaveBeenCalledWith({
      title: 'Error',
      description: 'Submission failed',
      status: 'error',
      duration: 3000,
      isClosable: true,
      position: 'top-right'
    });
  });

  it('renders the component without review data', async () => {
    render(
      <ReviewContest
        data={{
          review: null,
          token
        }}
        guidelineUrl="sampleGuidelineUrl"
        metadata={mockMetadata}
      />
    );

    // Wait for the component to render
    await waitFor(() => {
      screen.getAllByText('Request Removal');
      screen.getByText('Review and Response Guidelines');
    });
  });

  it('should fill the email field with provider email', () => {
    render(
      <ReviewContest
        data={{
          review: reviewData,
          token,
          providerEmail
        }}
        successUrl="/success"
        guidelinesUrl="/guidelines"
        metadata={mockMetadata}
      />
    );

    // Check if the email field is filled with the provider's email
    expect(screen.getByLabelText('Your email')).toHaveValue(providerEmail);
  });

  it('should not fill the email field if provider email is empty or null', () => {
    render(
      <ReviewContest
        data={{
          review: reviewData,
          token,
          providerEmail: null
        }}
        successUrl="/success"
        guidelinesUrl="/guidelines"
        metadata={mockMetadata}
      />
    );

    expect(screen.getByLabelText('Your email')).toHaveValue('');
  });
});
