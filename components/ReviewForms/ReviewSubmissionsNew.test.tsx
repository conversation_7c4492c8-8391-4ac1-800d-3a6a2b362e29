import fetch from 'jest-fetch-mock';

import {
  EMAIL_FIELD,
  fillInInput,
  fireEvent,
  RELATIONSHIP_FIELD,
  render,
  REVIEW_TEXT_FIELD,
  screen,
  SCREEN_NAME_FIELD,
  SERVICE_FIELD,
  setDesktopScreen,
  submitForm,
  waitForReviewApiToBeCalled
} from '~/utils/test-utils';

import ReviewSubmissionsNew from './ReviewSubmissionsNew';

const mockMetadata = {
  '@index': 0,
  '@name': '0',
  '@path': '/caring.com/reviews-submissions/new/main/0',
  '@id': 'c52d844c-7a61-427c-8fb3-982085bb010e',
  '@nodeType': 'mgnl:component',
  'mgnl:lastModified': '2023-11-30T04:53:24.161+01:00',
  'mgnl:template': 'spa-lm:components/reviewSubmissionsNew',
  'mgnl:created': '2023-11-30T04:53:24.161+01:00',
  '@nodes': []
};

const mockAnalytics = {
  track: jest.fn()
};
window.tracking = mockAnalytics;

describe('Review Submission New', () => {
  beforeEach(() => {
    setDesktopScreen();
    fetch.resetMocks();

    // Mock form submission to avoid "invalid json response body" error messages
    fetchMock.mockIf(/\/api\/reviews\/create-review$/, '{}');
  });

  it('should render review submissions new', async () => {
    render(
      <ReviewSubmissionsNew
        reviewGuidelinesURL="test.com"
        metadata={mockMetadata}
      />
    );

    expect(
      screen.getByRole('heading', {
        name: /share your thoughts/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByText(/how would you rate this provider\?/i)
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', {
        name: /what should care seekers or potential residents know\?/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', { name: /screen name/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', { name: /email address/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('combobox', {
        name: /relationship to provider/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('combobox', {
        name: /care type or service being reviewed/i
      })
    ).toBeInTheDocument();
    expect(screen.getByText(/food \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/activities \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/staff \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/facility \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/value \(optional\)/i)).toBeInTheDocument();
  });

  it('should render ReviewGuidelines', async () => {
    render(
      <ReviewSubmissionsNew
        reviewGuidelinesURL="/path/to/review_guidelines"
        metadata={mockMetadata}
      />
    );

    expect(screen.getByText('Review Guidelines')).toBeVisible();
    expect(screen.getByText('View All Guidelines')).toBeVisible();
    expect(screen.getByText('View All Guidelines')).toHaveAttribute(
      'href',
      expect.stringContaining('/path/to/review_guidelines')
    );
  });

  it('should validate required fields', async () => {
    render(
      <ReviewSubmissionsNew
        reviewGuidelinesURL="/path/to/review_guidelines"
        metadata={mockMetadata}
      />
    );

    submitForm();

    expect(await screen.findByText(/rate is required/i)).toBeInTheDocument();
    expect(screen.getByText(/review text is required/i)).toBeInTheDocument();
    expect(
      screen.getByText(/relationship to provider is required/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/care type or service is required/i)
    ).toBeInTheDocument();
  });

  it('should submit a review with the form values when provider is unknown', async () => {
    render(
      <ReviewSubmissionsNew
        reviewGuidelinesURL="/path/to/review_guidelines"
        metadata={mockMetadata}
      />
    );

    fillInInput('Provider', 'Test Provider');
    fillInInput('City', 'Chicago');
    fillInInput('State', 'IL', 'combobox');
    fillInInput(REVIEW_TEXT_FIELD, 'Test Review Content');
    fillInInput(SCREEN_NAME_FIELD, 'Test Name');
    fillInInput(EMAIL_FIELD, '<EMAIL>');
    fillInInput(RELATIONSHIP_FIELD, 'I visited this facility', 'combobox');
    fillInInput(SERVICE_FIELD, 'assisted_living_facilities', 'combobox');
    fireEvent.click(screen.getAllByTestId('star-1')[0]);

    submitForm();

    await waitForReviewApiToBeCalled();

    const content = fetch.mock.lastCall?.[1];
    const body = JSON.parse(content?.body?.toString() || '');
    expect(body).toMatchObject({
      body: 'Test Review Content',
      authorName: 'Test Name',
      authorEmail: '<EMAIL>',
      title: 'I visited this facility',
      ratingOverall: 1,
      ratingStaff: null,
      ratingActivities: null,
      ratingFood: null,
      ratingFacilities: null,
      ratingValue: null,
      // New providers need other fields to be sent
      locationId: null,
      resourceType: 'assisted_living_facilities',
      facilityName: 'Test Provider'
    });
  });

  it('should resets the careType if the provider name changes', async () => {
    render(
      <ReviewSubmissionsNew
        reviewGuidelinesURL="/path/to/review_guidelines"
        metadata={mockMetadata}
      />
    );

    fillInInput('Provider', 'Test Provider');
    fillInInput('City', 'Chicago');
    fillInInput('State', 'IL', 'combobox');
    fillInInput(REVIEW_TEXT_FIELD, 'Test Review Content');
    fillInInput(SCREEN_NAME_FIELD, 'Test Name');
    fillInInput(EMAIL_FIELD, '<EMAIL>');
    fillInInput(RELATIONSHIP_FIELD, 'I visited this facility', 'combobox');
    fillInInput(SERVICE_FIELD, 'assisted_living_facilities', 'combobox');
    fireEvent.click(screen.getAllByTestId('star-1')[0]);

    fillInInput('Provider', 'New Provider');

    expect(
      screen.getByRole('combobox', {
        name: SERVICE_FIELD
      })
    ).toHaveValue('');
  });
});
