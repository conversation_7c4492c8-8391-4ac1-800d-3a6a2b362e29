import { Link } from '@chakra-ui/layout';
import { Section } from '@components/Sections';
import { modifyTrailingSlash } from '@utils/modifyTrailingSlash';

import { HeadingElements } from '~/@types/heading';
import { Provider } from '~/contexts/Provider';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';

import NearbyGrid from './NearbyGrid';

export type NearbyProvidersProps = {
  heading?: string;
  headingElement?: HeadingElements;
  content?: string;
  enableTrailingSlash?: boolean;
  providers: Provider[];
};

const NearbyProviders = ({
  heading,
  headingElement = 'h3',
  content,
  enableTrailingSlash = false,
  providers
}: NearbyProvidersProps) => {
  const { getProviderDetailsPath } = useTenantFunctions();

  if (providers?.length === 0) {
    return null;
  }

  return (
    <>
      <Section
        headingElement={headingElement}
        title={heading}
        richText={content}
      />
      <NearbyGrid>
        {providers.map((result, i) => {
          const path = modifyTrailingSlash(
            enableTrailingSlash,
            getProviderDetailsPath(result)
          );
          return (
            <Link key={`${i}-${path}`} color="primary.700" href={path}>
              {result.name}
            </Link>
          );
        })}
      </NearbyGrid>
    </>
  );
};

export default NearbyProviders;
