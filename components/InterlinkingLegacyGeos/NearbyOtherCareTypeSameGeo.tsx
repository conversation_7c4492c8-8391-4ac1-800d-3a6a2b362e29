import { Link } from '@chakra-ui/layout';
import { Section } from '@components/Sections';
import { careTypeRollups } from '@utils/careTypeRollups';
import { modifyTrailingSlash } from '@utils/modifyTrailingSlash';
import { labelToSlug } from '@utils/strings';

import { HeadingElements } from '~/@types/heading';

import NearbyGrid from './NearbyGrid';
import { Link as LinkProps } from './types';

export type NearbyOtherCareTypeSameGeoProps = {
  heading?: string;
  headingElement?: HeadingElements;
  content?: string;
  links: Array<LinkProps>;
  catalogLinks?: string;
  enableTrailingSlash?: boolean;
  useCatalogForNearby?: boolean;
  initialData?: any[];
  city?: string;
  county?: string;
  state?: string;
};

// CORE-1362 Clean up dead code on Website due to links generated on backend
const formatLink = ({ link, city, county, state, enableTrailingSlash }) => {
  const careType = link.localResourceTypeRegion?.localResourceType?.urlName;
  const rollup = careTypeRollups[careType];
  const linkText = link.linkText;
  const path = modifyTrailingSlash(
    enableTrailingSlash,
    `/${rollup}/${careType === rollup ? '' : careType + '/'}${labelToSlug(
      state
    )}/${labelToSlug(city ?? county)}`
  );
  return { text: linkText, href: link.url ?? path };
};

const NearbyOtherCareTypeSameGeo = ({
  links,
  heading,
  headingElement = 'h3',
  content,
  enableTrailingSlash = false,
  city,
  county,
  state
}: NearbyOtherCareTypeSameGeoProps) => {
  const availableLinks = links.map((link) => {
    return formatLink({
      link,
      city,
      county,
      state,
      enableTrailingSlash
    });
  });

  if (availableLinks?.length === 0) {
    return null;
  }

  return (
    <>
      <Section
        headingElement={headingElement}
        title={heading}
        richText={content}
      />
      <NearbyGrid>
        {availableLinks?.map((link, i) => {
          return (
            <Link
              key={`${i}-${link.href}`}
              color="primary.700"
              href={link.href}
            >
              {link.text}
            </Link>
          );
        })}
      </NearbyGrid>
    </>
  );
};

export default NearbyOtherCareTypeSameGeo;
