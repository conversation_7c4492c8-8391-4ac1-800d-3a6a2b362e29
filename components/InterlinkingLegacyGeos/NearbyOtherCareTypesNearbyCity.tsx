import { Link } from '@chakra-ui/layout';
import { Section } from '@components/Sections';
import { careTypeRollups } from '@utils/careTypeRollups';
import { modifyTrailingSlash } from '@utils/modifyTrailingSlash';
import { labelToSlug } from '@utils/strings';

import NearbyGrid from './NearbyGrid';
import { NearByLinkProps } from './types';

// CORE-1362 Clean up dead code on Website due to links generated on backend
const formatLink = ({ link, enableTrailingSlash }) => {
  const careType = link.localResourceTypeRegion?.localResourceType?.urlName;
  const region = link.localResourceTypeRegion?.region.urlName;
  const state = link.localResourceTypeRegion?.state.urlName;
  const rollup = careTypeRollups[careType];
  const linkText = link.linkText;
  const path = modifyTrailingSlash(
    enableTrailingSlash,
    `/${rollup}/${careType === rollup ? '' : careType + '/'}${labelToSlug(
      state
    )}/${labelToSlug(region)}`
  );
  return { text: linkText, href: link.url ?? path };
};

const NearbyOtherCareTypesNearbyCity = ({
  heading,
  headingElement = 'h3',
  content,
  links,
  enableTrailingSlash = false
}: NearByLinkProps) => {
  const availableLinks = links.map((link) => {
    return formatLink({
      link: link,
      enableTrailingSlash
    });
  });

  if (availableLinks?.length === 0) {
    return null;
  }

  return (
    <>
      <Section
        headingElement={headingElement}
        title={heading}
        richText={content}
      />
      <NearbyGrid>
        {availableLinks?.map((link, i) => {
          return (
            <Link
              key={`${i}-${link.href}`}
              color="primary.700"
              href={link.href}
            >
              {link.text}
            </Link>
          );
        })}
      </NearbyGrid>
    </>
  );
};

export default NearbyOtherCareTypesNearbyCity;
