import { fetchNearbyProviders } from '@hooks/provider';
import { getOtherCareTypesBySameLocale } from '@services/interlinking/api';
import isObject from 'lodash/isObject';
import startCase from 'lodash/startCase';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { FetchResponse } from './InterlinkingLegacyGeos';
import { NearbyCareProps } from './types';
export const getServerSideComponentProps = async (
  {
    numberOfItemsToDisplay = '6',
    careType,
    latitude,
    longitude,
    state,
    county,
    city,
    otherCareTypesSameCity
  }: NearbyCareProps,
  context: GetServerSidePropsContext
): Promise<FetchResponse> => {
  const site = findSiteForContext(context);
  const alteredCareType = isObject(careType) ? careType.name : careType;

  if (site.path == 'caring.com') {
    if (otherCareTypesSameCity?.enable) {
      const results = await getOtherCareTypesBySameLocale({
        context,
        state,
        county,
        city,
        careType: alteredCareType,
        limit: 6
      });
      const careTypes = results.map((item) => ({
        linkText: startCase(item.careType),
        url: item.urlPath
      }));
      return {
        nearbyProviders: {},
        nearbyTypesSameCity: careTypes || []
      };
    }
    return { nearbyProviders: {} };
  }

  const lat = parseFloat(latitude ?? '') || 0;
  const lng = parseFloat(longitude ?? '') || 0;
  const nearbyProviders = await fetchNearbyProviders({
    careType: alteredCareType,
    hitsPerPage: Number(numberOfItemsToDisplay),
    page: 0,
    latitude: lat,
    longitude: lng,
    domain: site.path
  });

  return {
    nearbyProviders
  };
};
