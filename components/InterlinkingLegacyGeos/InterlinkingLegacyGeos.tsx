'use client';

import { Stack } from '@chakra-ui/layout';
import EmptyComponent from '@components/EmptyComponent';
import Container from '@components/LayoutStructure/Container';
import { ProvidersNearMeQueryResponse } from '@hooks/provider';
import { toCapitalizedWords } from '@utils/strings';
import { getStateAbbreviation } from '@utils/UsStates';
import isObject from 'lodash/isObject';
import orderBy from 'lodash/orderBy';
import { useContext } from 'react';

import CatalogContext from '~/contexts/CatalogContext';
import { Catalog } from '~/types/LocaleCatalog';

import NearbyCities from './NearbyCities';
import NearbyCounties from './NearbyCounties';
import NearbyOtherCareTypeSameGeo from './NearbyOtherCareTypeSameGeo';
import NearbyOtherCareTypesNearbyCity from './NearbyOtherCareTypesNearbyCity';
import NearbyProviders from './NearbyProviders';
import { NearbyCareProps } from './types';

export type FetchResponse = {
  nearbyProviders: ProvidersNearMeQueryResponse | {};
  nearbyTypesSameCity?: Catalog[];
};

const InterlinkingLegacyGeos: React.FC<NearbyCareProps> = ({
  communities,
  nearbyCitiesSameType,
  nearbyCountiesSameType,
  otherCareTypesSameCity,
  otherCareTypesNearbyCity,
  state,
  city,
  county,
  careType,
  data: { nearbyProviders, nearbyTypesSameCity = [] }
}) => {
  const alteredCareType = isObject(careType) ? careType.name : careType;
  const catalogContext = useContext(CatalogContext);
  const { legacyNearbyData } = catalogContext || {};
  const {
    nearbyTypesNearbyCity,
    sameTypeNearbyCities,
    sameTypeNearbyCounties
  } = legacyNearbyData || {};

  // Nearby communities
  const nearbyCommunities =
    orderBy(nearbyProviders?.results, ['bayesian_average'], ['desc']) || [];
  const nearbyCommunitiesHeading = `${toCapitalizedWords(
    alteredCareType
  )} near ${toCapitalizedWords(city || county)}, ${getStateAbbreviation(
    state
  )}`;
  const showNearbyCommunities =
    communities?.enable && nearbyCommunities.length > 0;

  // Nearby cities
  const nearbyCitiesHeading = `Other options in ${toCapitalizedWords(
    city
  )}, ${getStateAbbreviation(state)}`;
  const showNearbyCities =
    (nearbyCitiesSameType?.enable &&
      sameTypeNearbyCities &&
      sameTypeNearbyCities.length > 0) ||
    nearbyCitiesSameType?.useCatalogForNearby;

  // Nearby counties
  const nearbyCountiesHeading = `Other options in ${toCapitalizedWords(
    county
  )}, ${getStateAbbreviation(state)}`;
  const showNearbyCounties =
    nearbyCountiesSameType?.enable &&
    sameTypeNearbyCounties &&
    sameTypeNearbyCounties.length > 0;

  // Nearby other care types
  const nearbyTypesSameCityHeading = `Other options in ${toCapitalizedWords(
    city || county
  )}, ${getStateAbbreviation(state)}`;
  const showOtherCareTypesSameCity =
    (otherCareTypesSameCity?.enable &&
      nearbyTypesSameCity &&
      nearbyTypesSameCity.length > 0) ||
    otherCareTypesSameCity?.useCatalogForNearby;

  // Nearby other care types
  const nearbyTypesNearbyCityHeading = `More options near ${toCapitalizedWords(
    city || county
  )}, ${getStateAbbreviation(state)}`;
  const showOtherCareTypesNearbyCity =
    otherCareTypesNearbyCity?.enable &&
    nearbyTypesNearbyCity &&
    nearbyTypesNearbyCity.length > 0;

  if (
    !showNearbyCommunities &&
    !showNearbyCities &&
    !showNearbyCounties &&
    !showOtherCareTypesSameCity &&
    !showOtherCareTypesNearbyCity
  ) {
    return <EmptyComponent />;
  }

  return (
    <Container as="section" display="flex" flexDirection="column" gap="4">
      <Stack spacing="0" gap={4}>
        {showNearbyCommunities && (
          <NearbyProviders
            content={communities.content}
            heading={communities.heading || nearbyCommunitiesHeading}
            headingElement={communities.headingElement}
            providers={nearbyCommunities}
            enableTrailingSlash={communities.enableTrailingSlash}
          />
        )}

        {showNearbyCities && (
          <NearbyCities
            links={sameTypeNearbyCities || []}
            content={nearbyCitiesSameType.content}
            heading={nearbyCitiesSameType.heading || nearbyCitiesHeading}
            headingElement={nearbyCitiesSameType.headingElement}
            enableTrailingSlash={nearbyCitiesSameType.enableTrailingSlash}
          />
        )}

        {showNearbyCounties && (
          <NearbyCounties
            links={sameTypeNearbyCounties}
            content={nearbyCountiesSameType.content}
            heading={nearbyCountiesSameType.heading || nearbyCountiesHeading}
            headingElement={nearbyCountiesSameType.headingElement}
            enableTrailingSlash={nearbyCountiesSameType.enableTrailingSlash}
          />
        )}
        {showOtherCareTypesSameCity && (
          <NearbyOtherCareTypeSameGeo
            links={nearbyTypesSameCity || []}
            content={otherCareTypesSameCity.content}
            heading={
              otherCareTypesSameCity.heading || nearbyTypesSameCityHeading
            }
            headingElement={otherCareTypesSameCity.headingElement}
            enableTrailingSlash={otherCareTypesSameCity.enableTrailingSlash}
            city={city}
            county={county}
            state={state}
          />
        )}
        {showOtherCareTypesNearbyCity && (
          <NearbyOtherCareTypesNearbyCity
            careType={alteredCareType}
            links={nearbyTypesNearbyCity}
            content={otherCareTypesNearbyCity.content}
            heading={
              otherCareTypesNearbyCity.heading || nearbyTypesNearbyCityHeading
            }
            headingElement={otherCareTypesNearbyCity.headingElement}
            enableTrailingSlash={otherCareTypesNearbyCity.enableTrailingSlash}
          />
        )}
      </Stack>
    </Container>
  );
};

export default InterlinkingLegacyGeos;
