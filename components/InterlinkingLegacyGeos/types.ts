import { ProvidersNearMeQueryResponse } from '@hooks/provider';

import { HeadingElements } from '~/@types/heading';
import { Metadata } from '~/types/Magnolia';

interface LocalResourceTypeRegion {
  region?: {
    urlName: string;
  };
  state?: {
    urlName: string;
  };
  localResourceType?: {
    urlName: string;
  };
}

export interface Link {
  linkText: string;
  url?: string;

  /**
   * @deprecated CORE-932: Remove when GeosV2 endpoint becomes the default
   */
  localResourceTypeRegion?: LocalResourceTypeRegion;
}

export interface CatalogLink {
  text: string;
  href: string;
}

export type NearByLinkProps = {
  heading?: string;
  headingElement?: HeadingElements;
  content?: string;
  links: Array<Link>;
  catalogLinks?: string;
  careType?: string;
  enableTrailingSlash?: boolean;
  useCatalogForNearby?: boolean;
};

export interface Section extends Metadata {
  headingElement: HeadingElements;
  heading: string;
  content: string;
}

export interface Sections extends Metadata {
  [key: string]: string | Array<string> | Section | undefined;
}

export interface NearbyProps extends Metadata {
  enable?: boolean;
  heading?: string;
  headingElement?: HeadingElements;
  content?: string;
  enableTrailingSlash?: boolean;
  useCatalogForNearby?: boolean;
  catalogLinks?: string;
}
export interface NearbyCareProps {
  sections: Sections;
  communities?: NearbyProps;
  nearbyCitiesSameType?: NearbyProps;
  nearbyCountiesSameType?: NearbyProps;
  otherCareTypesSameCity?: NearbyProps;
  otherCareTypesNearbyCity?: NearbyProps;
  numberOfItemsToDisplay?: string;
  state: string;
  city: string;
  county: string;
  latitude?: string;
  longitude?: string;
  careType: string | { name: string };
  data: {
    nearbyProviders: ProvidersNearMeQueryResponse;
    nearbyCities: any[];
    nearbyOtherCareTypeSameCity: any[];
    nearbyTypesSameCity?: any[];
  };
}
