import { getOtherCareTypesBySameLocale } from '@services/interlinking/api';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { getServerSideComponentProps } from './ServerComponent';
import { NearbyCareProps } from './types';

const spyFetchNearbyProviders = jest.fn();
jest.mock('@hooks/provider', () => ({
  fetchNearbyProviders: jest.fn()
}));

jest.mock('@services/interlinking/api', () => ({
  getOtherCareTypesBySameLocale: jest.fn()
}));

jest.mock('~/contexts/SiteContext', () => ({
  findSiteForContext: jest.fn()
}));

describe('getServerSideComponentProps', () => {
  it('does not call algolia when domain is caring.com', async () => {
    const props = {} as any as NearbyCareProps;
    const context = {} as any as GetServerSidePropsContext;

    (findSiteForContext as jest.Mock).mockReturnValue({ path: 'caring.com' });

    await getServerSideComponentProps(props, context);
    expect(spyFetchNearbyProviders).not.toHaveBeenCalled();
  });

  describe('otherCareTypesSameCity feature', () => {
    const mockContext = {
      req: { headers: { host: 'caring.com' } },
      resolvedUrl: '/test'
    } as any;

    beforeEach(() => {
      jest.clearAllMocks();
      (findSiteForContext as jest.Mock).mockReturnValue({ path: 'caring.com' });
    });

    it('should fetch other care types when otherCareTypesSameCity.enable is true', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' },
        { careType: 'memory-care', urlPath: '/path/to/memory-care' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        careType: 'nursing-homes',
        state: 'california',
        city: 'los-angeles',
        county: '',
        sections: {},
        data: {
          nearbyProviders: {},
          nearbyCities: [],
          nearbyOtherCareTypeSameCity: []
        },
        otherCareTypesSameCity: { enable: true }
      } as any;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'california',
        city: 'los-angeles',
        county: '',
        careType: 'nursing-homes',
        limit: 6
      });

      expect(result).toEqual(
        expect.objectContaining({
          nearbyTypesSameCity: [
            { linkText: 'Assisted Living', url: '/path/to/assisted-living' },
            { linkText: 'Memory Care', url: '/path/to/memory-care' }
          ]
        })
      );
    });

    it('should not fetch other care types when otherCareTypesSameCity.enable is false', async () => {
      const props = {
        careType: 'nursing-homes',
        state: 'california',
        city: 'los-angeles',
        county: '',
        sections: {},
        data: {
          nearbyProviders: {},
          nearbyCities: [],
          nearbyOtherCareTypeSameCity: []
        },
        otherCareTypesSameCity: { enable: false }
      } as any;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).not.toHaveBeenCalled();
      expect(result).not.toHaveProperty('nearbyTypesSameCity');
    });

    it('should handle object careType properly when fetching other care types', async () => {
      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue([]);

      const props = {
        careType: { name: 'nursing-homes', display: 'Nursing Homes' },
        state: 'california',
        city: 'los-angeles',
        county: '',
        sections: {},
        data: {
          nearbyProviders: {},
          nearbyCities: [],
          nearbyOtherCareTypeSameCity: []
        },
        otherCareTypesSameCity: { enable: true }
      } as any;

      await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith(
        expect.objectContaining({
          careType: 'nursing-homes'
        })
      );
    });

    it('should handle empty results from getOtherCareTypesBySameLocale', async () => {
      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue([]);

      const props = {
        careType: 'nursing-homes',
        state: 'california',
        city: 'los-angeles',
        county: '',
        sections: {},
        data: {
          nearbyProviders: {},
          nearbyCities: [],
          nearbyOtherCareTypeSameCity: []
        },
        otherCareTypesSameCity: { enable: true }
      } as any;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(result).toEqual(
        expect.objectContaining({
          nearbyTypesSameCity: []
        })
      );
    });
  });
});
