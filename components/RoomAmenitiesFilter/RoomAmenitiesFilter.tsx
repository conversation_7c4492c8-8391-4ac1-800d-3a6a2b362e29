import CheckboxInput from '@components/CheckboxInput';

interface RoomAmenitiesFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const roomAmenitiesFilterItems = [
  {
    value: 'balcony-patio',
    label: 'Balcony/Patio'
  },
  {
    value: 'cable',
    label: 'Cable'
  },
  {
    value: 'kitchen-kitchenette',
    label: 'Kitchen/Kitchenette'
  }
];

function RoomAmenitiesFilter({ onChange, value }: RoomAmenitiesFilterProps) {
  return (
    <CheckboxInput
      name="roomAmenities"
      onChange={onChange}
      items={roomAmenitiesFilterItems}
      value={value}
    />
  );
}

export default RoomAmenitiesFilter;
