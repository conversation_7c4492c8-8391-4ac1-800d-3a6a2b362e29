import { Box, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import StoryImage from '@components/Image/StoryImage';
import Container from '@components/LayoutStructure/Container';

import { MagnoliaImage } from '~/types/Magnolia';

interface Props {
  image: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  title?: string;
  description?: string;
  buttonText?: string;
  downloadUrl?: string;
  isImageLink?: boolean;
}

const DownloadPdfBlock = ({
  title,
  description,
  image,
  buttonText,
  downloadUrl,
  isImageLink
}: Props) => {
  const renderImage = () => {
    return (
      <Box
        minWidth={{ base: '294px', sm: '235px' }}
        maxWidth={{ base: '294px', sm: '235px' }}
      >
        <StoryImage
          switchable={image}
          displayAsBackground={false}
          desktopHeight="265px"
          mobileHeight="237px"
          backgroundSize="contain"
          withContainer={false}
          containerMarginBottom="0px"
        />
      </Box>
    );
  };

  return (
    <Container>
      <Box>
        {title && (
          <Text
            as="h2"
            fontSize={'xl'}
            lineHeight={'6'}
            fontWeight={700}
            mb={3}
            color="primary.900"
          >
            {title}
          </Text>
        )}
        {description && (
          <Text fontSize={'md'} fontWeight={400} mb={4}>
            {description}
          </Text>
        )}
      </Box>
      {image && (
        <Box
          px={16}
          py={5}
          border="1px solid #ced4da"
          borderRadius={'5px'}
          width={'fit-content'}
        >
          {isImageLink ? (
            <a href={downloadUrl}>{renderImage()}</a>
          ) : (
            renderImage()
          )}
        </Box>
      )}
      {(buttonText || downloadUrl) && (
        <Button
          as={'a'}
          href={downloadUrl}
          variant={'outline'}
          color={'accent.400'}
          borderColor={'accent.400'}
          fontWeight={700}
          mt={5}
          width={{ base: 'full', sm: 'auto' }}
          elementAction={ElementActions.INTERNAL_LINK}
          elementName={ElementNames.PRODUCT_REVIEW_CARD}
          elementType={ElementTypes.BUTTON}
          destinationUrl={downloadUrl}
        >
          {buttonText}
        </Button>
      )}
    </Container>
  );
};

export default DownloadPdfBlock;
