import { render } from '@testing-library/react';

import Accordion from './Accordion';

describe('Accordion', () => {
  test('renders without crashing', () => {
    render(
      <Accordion
        openIndexes={[0, 2]}
        items={[
          { label: 'Item 1', children: 'Content 1' },
          { label: 'Item 2', children: <div>Content 2</div> },
          { label: 'Item 3', children: <span>Content 3</span> }
        ]}
      />
    );
  });
});
