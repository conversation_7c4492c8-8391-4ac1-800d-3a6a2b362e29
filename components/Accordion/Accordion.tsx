import {
  Accordion as <PERSON><PERSON><PERSON><PERSON>rdion,
  <PERSON>ccordionButton,
  AccordionButtonProps,
  AccordionIcon,
  AccordionItem as ChakraAccordionItem,
  AccordionPanel,
  Flex,
  Icon as ChakraIcon,
  Text,
  Tooltip as ChakraTooltip,
  useDisclosure
} from '@chakra-ui/react';
import { MouseEventHandler } from 'react';
import { MdOutlineHelpOutline } from 'react-icons/md';

interface TooltipProps {
  text: string;
}

export interface AccordionItemProps {
  label: string;
  children: React.ReactNode;
  infoText?: string;
  accordionButtonProps?: AccordionButtonProps;
}

interface AccordionProps {
  openIndexes?: number[];
  items: AccordionItemProps[];
  accordionButtonProps?: AccordionButtonProps;
}

const Tooltip: React.FC<TooltipProps> = ({ text }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const onClick: MouseEventHandler = (e) => {
    e.stopPropagation();
    const action = isOpen ? onClose : onOpen;
    action();
  };

  return (
    <ChakraTooltip
      hasArrow
      label={text}
      bg="primary.600"
      color="white"
      fontSize="sm"
      px={6}
      py={2}
      textAlign="center"
      borderRadius={4}
      isOpen={isOpen}
      display="inline-block"
    >
      <Flex>
        <ChakraIcon
          as={MdOutlineHelpOutline}
          onMouseEnter={onOpen}
          onMouseLeave={onClose}
          onClick={onClick}
          w="18px"
          h="18px"
          role="presentation"
        />
      </Flex>
    </ChakraTooltip>
  );
};

const AccordionItem = ({
  label,
  children,
  infoText,
  accordionButtonProps
}: AccordionItemProps) => {
  return (
    <ChakraAccordionItem>
      <Text>
        <AccordionButton {...accordionButtonProps}>
          {infoText ? (
            <Flex display="inline-flex" flex="1" alignItems="center" gap={1.5}>
              <Text as="span" textAlign="left" fontWeight="bold">
                {label}
              </Text>
              <Tooltip text={infoText} />
            </Flex>
          ) : (
            <Text as="span" flex="1" textAlign="left" fontWeight="bold">
              {label}
            </Text>
          )}
          <AccordionIcon />
        </AccordionButton>
      </Text>
      <AccordionPanel my={3}>{children}</AccordionPanel>
    </ChakraAccordionItem>
  );
};

const Accordion = ({
  openIndexes,
  items,
  accordionButtonProps
}: AccordionProps) => {
  return (
    <ChakraAccordion defaultIndex={openIndexes} allowMultiple>
      {items.map((item) => (
        <AccordionItem
          key={item.label}
          label={item.label}
          infoText={item.infoText}
          accordionButtonProps={accordionButtonProps}
        >
          {item.children}
        </AccordionItem>
      ))}
    </ChakraAccordion>
  );
};

export default Accordion;
