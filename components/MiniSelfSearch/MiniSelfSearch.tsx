'use client';

import { <PERSON><PERSON> } from '@chakra-ui/button';
import { InputGroup, InputRightElement } from '@chakra-ui/input';
import { Box, Heading } from '@chakra-ui/react';
import useSearchStepSubmission from '@components/Analytics/events/SearchStepSubmission';
import useSearchSubmission from '@components/Analytics/events/SearchSubmission';
import { CareType } from '@components/FacetedSearch/types';
import {
  default as Container,
  default as FullWidthContainer
} from '@components/LayoutStructure/FullWidthContainer';
import {
  STRING_TO_ICON_COMPONENT,
  StringToIconKeys
} from '@components/RenderIcon';
import useSearch from '@hooks/useSearch';
import { fetchGeoLocation } from '@utils/fetchGeoLocation';
import isObject from 'lodash/isObject';
import { useContext, useEffect, useMemo, useState } from 'react';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import SiteContext from '~/contexts/SiteContext';
import { Metadata } from '~/types/Magnolia';

import {
  StyledMiniSearchAutocompleteInput,
  StyledMiniSelfSearchInput
} from './MiniSelfSearchStyles';

export interface CallToActionProps {
  text?: string;
  placeholder?: string;
  textColor?: string;
  bgColor?: string;
  maxWidth?: string;
  maxHeight?: string;
  fullWidth?: boolean;
  buttonOutsideInput?: boolean;
  icon?: StringToIconKeys;
  miniSelfSearchId: string;
  metadata?: Metadata;
  title?: string;
  headingElement?: HeadingElements;
  desktopHeadingSize?: HeadingSizes;
  headingSize?: HeadingSizes;
  titleAlignment?: 'left' | 'center' | 'right';
  inputAlignment?:
    | 'left'
    | 'center'
    | 'right'
    | 'flex-start'
    | 'flex-end'
    | 'start'
    | 'end';
  enablePredictiveSearch?: boolean;
  careType?: CareType;
}

const MiniSelfSearch: React.FC<CallToActionProps> = (props) => {
  const {
    maxWidth,
    text,
    textColor,
    bgColor,
    icon,
    buttonOutsideInput = false,
    maxHeight,
    placeholder = 'Enter City, State or Zip',
    miniSelfSearchId,
    title,
    headingElement,
    desktopHeadingSize,
    headingSize,
    titleAlignment,
    inputAlignment = 'center',
    enablePredictiveSearch = false,
    careType
  } = props;
  const { buildSearchUrl } = useSearch();
  const [keyword, setKeyword] = useState('');
  const [geoData, setGeoData] = useState<{
    city?: string;
    state?: string;
    latitude?: string;
    longitude?: string;
  }>({});
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path || '';

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchGeoLocation();
      setGeoData(data);
    };
    fetchData();
  }, [domain]);

  const city = useMemo(() => geoData.city, [geoData.city]);
  const state = useMemo(() => geoData.state, [geoData.state]);

  useEffect(() => {
    if (city && state) {
      setKeyword(`${city}, ${state}`);
    }
  }, [city, state]);

  const getIcon = (iconName?: StringToIconKeys) => {
    if (!iconName) return '';
    return { leftIcon: STRING_TO_ICON_COMPONENT[iconName] };
  };
  const StyledInput = useMemo(
    () => StyledMiniSelfSearchInput(domain),
    [domain]
  );

  const StyledAutocompleteInput = useMemo(
    () => StyledMiniSearchAutocompleteInput(domain),
    [domain]
  );

  const searchStepSubmission = useSearchStepSubmission();
  const searchSubmission = useSearchSubmission();
  const handleSearchEvents = () => {
    const stepInstanceId = uuidv4();
    const searchTemplateId = miniSelfSearchId ?? props?.metadata?.['@id'];
    const stepId = uuidv5(placeholder, searchTemplateId);
    const searchInstanceId = uuidv4();

    searchStepSubmission({
      search_template_id: searchTemplateId,
      search_instance_id: searchInstanceId,
      step_id: stepId,
      step_instance_id: stepInstanceId,
      step_index: 1,
      step_content: [
        {
          prompt_id: uuidv5(placeholder, searchTemplateId),
          prompt_type: 'text',
          prompt_instance_id: uuidv4(),
          prompt_index: 1,
          prompt_value: placeholder,
          response_array: [
            {
              response_value: keyword,
              response_id: uuidv4()
            }
          ]
        }
      ]
    });

    searchSubmission({
      search_template_id: searchTemplateId,
      search_instance_id: searchInstanceId,
      step_submissions: [
        {
          step_id: stepId,
          step_instance_id: stepInstanceId,
          step_index: 1
        }
      ]
    });
  };

  const onSearch = (maybeKeyword?: string) => {
    const careTypeProp = isObject(careType) ? careType.name : careType;
    const searchPageUrl = buildSearchUrl({
      careType: careTypeProp,
      keyword: maybeKeyword ?? keyword
    });
    handleSearchEvents();
    location.assign(searchPageUrl);
  };

  return (
    <>
      {title && (
        <Heading
          as={headingElement}
          size={{
            base: headingSize,
            md: desktopHeadingSize ? desktopHeadingSize : headingSize
          }}
          textAlign={titleAlignment}
          style={{ scrollMarginTop: 48 }}
          pb={5}
        >
          {title}
        </Heading>
      )}
      <Box display="flex" width="100%" justifyContent={inputAlignment}>
        <InputGroup
          display="flex"
          gap={2}
          alignContent="start"
          maxWidth={maxWidth}
          py={2}
          zIndex={2}
        >
          {enablePredictiveSearch && (
            <StyledAutocompleteInput
              setKeyword={setKeyword}
              pr={buttonOutsideInput ? 0 : '33%'}
              pl={{ base: 1, lg: 3 }}
              fontSize={{ base: 'sm', md: 'md', lg: 'xl' }}
              placeholder={placeholder}
              aria-label="search-keyword"
              height="4rem"
              maxHeight={buttonOutsideInput ? maxHeight : undefined}
              onSelection={onSearch}
            />
          )}
          {!enablePredictiveSearch && (
            <StyledInput
              value={keyword || ''}
              onChange={(e) => {
                setKeyword(e.target.value);
              }}
              pr={buttonOutsideInput ? 0 : '33%'}
              pl={{ base: 1, lg: 3 }}
              autoComplete="shipping address-line1"
              fontSize={{ base: 'sm', md: 'md', lg: 'xl' }}
              placeholder={placeholder}
              aria-label="search-keyword"
              height="4rem"
              maxHeight={buttonOutsideInput ? maxHeight : undefined}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  onSearch();
                }
              }}
            />
          )}
          {!buttonOutsideInput ? (
            <InputRightElement
              height="100%"
              width="33%"
              display="flex"
              justifyContent="end"
            >
              <Button
                as="a"
                mr={{ base: 1, md: 3 }}
                data-testid="mini-self-search-button"
                height="3rem"
                colorScheme={bgColor}
                variant="solid"
                color={textColor}
                fontSize={{ base: 'sm', lg: 'md' }}
                fontWeight={700}
                onClick={() => onSearch()}
                {...getIcon(icon)}
              >
                {text}
              </Button>
            </InputRightElement>
          ) : (
            <Button
              as="a"
              data-testid="mini-self-search-button-outside"
              height={buttonOutsideInput ? maxHeight : '100%'}
              width="fit-content"
              colorScheme={bgColor}
              variant="solid"
              color={textColor}
              fontSize={{ base: 'sm', lg: 'md' }}
              fontWeight={700}
              px="3rem"
              onClick={() => onSearch()}
              {...getIcon(icon)}
            >
              {text}
            </Button>
          )}
        </InputGroup>
      </Box>
    </>
  );
};

const MiniSelfSearchWrapper: React.FC<CallToActionProps> = ({
  fullWidth,
  ...otherProps
}) => {
  return fullWidth ? (
    <FullWidthContainer>
      <MiniSelfSearch {...otherProps} />
    </FullWidthContainer>
  ) : (
    <Container>
      <MiniSelfSearch {...otherProps} />
    </Container>
  );
};

export default MiniSelfSearchWrapper;
