import userEvent from '@testing-library/user-event';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';
import { act, useContext } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { fetchGeoLocation } from '~/utils/fetchGeoLocation';
import { fireEvent, render, screen, waitFor } from '~/utils/test-utils';

import MiniSelfSearch from './MiniSelfSearch';

const props = {
  miniSelfSearchId: uuidv4(),
  maxWidth: 'lg',
  text: 'Search'
};

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn()
}));

jest.mock('~/utils/fetchGeoLocation');

describe('MiniSelfSearch', () => {
  const originalWindowLocation = window.location;
  const originalWindowTracking = window.tracking;

  const mockAnalytics = {
    track: jest.fn()
  };
  const mockLocation = { assign: jest.fn() };

  beforeEach(() => {
    window.location = mockLocation as unknown as Location;
    window.tracking = mockAnalytics;

    const pageProps = {
      page: {
        canonical: 'https://test.com'
      },
      site: {
        name: 'Seniorhomes.com',
        domains: [
          'seniorhomes.com',
          'dxp.seniorhomes.com',
          'seniorhomes.localhost',
          'seniorhomes.dev.dxp.seniorhomes.com',
          'seniorhomes.stg.dxp.seniorhomes.com',
          'seniorhomes.prod.dxp.seniorhomes.com',
          'author.seniorhomes.dev.dxp.seniorhomes.com',
          'author.seniorhomes.stg.dxp.seniorhomes.com',
          'author.seniorhomes.prod.dxp.seniorhomes.com'
        ],
        path: 'seniorhomes.com',
        segmentWriteKey: 'RxyAwA9TTuvsIVwniogU9vmNkOYNT4Uo',
        segmentCdnURL: 'https://segment-cdn.caring.com',
        partnerToken: '56b2659'
      },
      context: {
        params: {
          careTypeOrState: 'search'
        }
      }
    };

    useContext.mockReturnValue(pageProps);

    (fetchGeoLocation as jest.Mock).mockResolvedValue({
      city: 'TestCity',
      state: 'TestState',
      latitude: '12.34',
      longitude: '56.78'
    });

    fetchMock.mockResponse(
      JSON.stringify({
        features: [
          {
            place_name: 'Charlotte, North Carolina, United States'
          },
          {
            place_name: 'Charlottesville, Virginia, United States'
          },
          {
            place_name: 'Charlotte, Michigan, United States'
          },
          {
            place_name: 'Charlotte Hall, Maryland, United States'
          },
          {
            place_name: 'Charlotte, Tennessee, United States'
          },
          {
            place_name: 'Charlotte Harbor, Florida, United States'
          }
        ]
      })
    );
  });

  afterEach(() => {
    mockAnalytics.track.mockClear();
    mockLocation.assign.mockClear();
    fetchMock.resetMocks();
  });

  afterAll(() => {
    window.location = originalWindowLocation;
    window.analytics = originalWindowTracking;
  });

  it('renders an input element', async () => {
    let renderResult;
    await act(async () => {
      renderResult = render(<MiniSelfSearch {...props} />);
    });

    const inputElement = renderResult.getByLabelText('search-keyword');
    expect(inputElement).toBeInTheDocument();
  });

  it('renders the cta button outside the search input', async () => {
    let renderResult;
    await act(async () => {
      renderResult = render(<MiniSelfSearch {...props} buttonOutsideInput />);
    });

    const buttonInside = renderResult.queryByTestId('mini-self-search-button');
    const buttonOutside = renderResult.queryByTestId(
      'mini-self-search-button-outside'
    );
    expect(buttonInside).not.toBeInTheDocument();
    expect(buttonOutside).toBeInTheDocument();
  });

  it('tracks analytics on submission', async () => {
    window.tracking = mockAnalytics;

    let renderResult;
    await act(async () => {
      renderResult = render(<MiniSelfSearch {...props} />);
    });

    const inputElement = renderResult.getByLabelText('search-keyword');

    await act(async () => {
      fireEvent.change(inputElement, { target: { value: 'San Francisco' } });
    });

    const buttonElement = renderResult.getByTestId('mini-self-search-button');

    await act(async () => {
      buttonElement.click();
    });

    expect(mockAnalytics.track).toHaveBeenCalledWith('Search Step Submission', {
      page_session_id: undefined,
      session_id: undefined,
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID,
      search_step_submission_json:
        '{"search_step_submission":[{"search_template_id":"00000000-0000-0000-0000-000000000000","search_instance_id":"00000000-0000-0000-0000-000000000000","step_id":"00000000-0000-0000-0000-000000000000","step_instance_id":"00000000-0000-0000-0000-000000000000","step_index":1,"step_content":[{"prompt_id":"00000000-0000-0000-0000-000000000000","prompt_type":"text","prompt_instance_id":"00000000-0000-0000-0000-000000000000","prompt_index":1,"prompt_value":"Enter City, State or Zip","response_array":[{"response_value":"San Francisco","response_id":"00000000-0000-0000-0000-000000000000"}]}]}]}',
      form_type: ''
    });
  });

  it('redirects to search results page when search button is clicked', async () => {
    const mockLocationAssign = jest.fn();
    window.location = { assign: mockLocationAssign } as unknown as Location;

    let renderResult;
    await act(async () => {
      renderResult = render(<MiniSelfSearch {...props} />);
    });

    const inputElement = renderResult.getByLabelText('search-keyword');

    await act(async () => {
      fireEvent.change(inputElement, { target: { value: 'San Francisco' } });
    });

    const buttonElement = renderResult.getByTestId('mini-self-search-button');

    await act(async () => {
      buttonElement.click();
    });

    expect(mockLocationAssign).toHaveBeenCalledWith(
      '/search/?keyword=San+Francisco'
    );
  });

  it('redirects to search results page when enter is pressed', async () => {
    const mockLocationAssign = jest.fn();
    window.location = { assign: mockLocationAssign } as unknown as Location;

    await act(async () => {
      render(<MiniSelfSearch {...props} enablePredictiveSearch />);
    });

    const inputElement = screen.getByRole('textbox');

    await act(async () => {
      await userEvent.type(inputElement, 'Charl');
    });

    await waitFor(() => {
      expect(screen.getByText('Charlotte, NC')).toBeInTheDocument();
    });

    await act(async () => {
      await userEvent.type(inputElement, '{enter}');
    });

    expect(mockLocationAssign).toHaveBeenCalledWith(
      '/search/?keyword=Charlotte%2C+NC'
    );
  });

  it('redirects to search results page when an item is clicked', async () => {
    const mockLocationAssign = jest.fn();

    window.location = { assign: mockLocationAssign } as unknown as Location;

    render(<MiniSelfSearch {...props} enablePredictiveSearch />);

    const inputElement = screen.getByRole('textbox');

    await userEvent.type(inputElement, 'Charl');

    await waitFor(() => {
      expect(screen.getByText('Charlotte, NC')).toBeInTheDocument();
    });

    const listItem = screen.getByText('Charlotte, NC');
    fireEvent.mouseDown(listItem);

    expect(mockLocationAssign).toHaveBeenCalledWith(
      '/search/?keyword=Charlotte%2C+NC'
    );
  });

  it('redirects to search results page when an item is focused and enter is pressed', async () => {
    const mockLocationAssign = jest.fn();

    window.location = { assign: mockLocationAssign } as unknown as Location;

    render(<MiniSelfSearch {...props} enablePredictiveSearch />);

    const inputElement = screen.getByRole('textbox');

    await userEvent.type(inputElement, 'Charl');

    await waitFor(() => {
      expect(screen.getByText('Charlotte, NC')).toBeInTheDocument();
    });

    await userEvent.type(inputElement, '{arrowdown}');
    await userEvent.type(inputElement, '{arrowdown}');
    await userEvent.type(inputElement, '{arrowdown}');
    await userEvent.type(inputElement, '{enter}');

    expect(mockLocationAssign).toHaveBeenCalledWith(
      '/search/?keyword=Charlotte%2C+MI'
    );
  });
});
