import CheckboxInput from '@components/CheckboxInput';

interface PersonalCareFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const personalCareFilterItems = [
  {
    value: 'bathing',
    label: 'Bathing Assistance'
  },
  {
    value: 'dressing',
    label: 'Dressing Assistance'
  },
  {
    value: 'meal-prep',
    label: 'Meal Prep'
  },
  {
    value: 'personal-care',
    label: 'Personal Care Assistance'
  },
  {
    value: 'toilet',
    label: 'Toilet Assistance'
  }
];

function PersonalCareFilter({ onChange, value }: PersonalCareFilterProps) {
  return (
    <CheckboxInput
      name="personalCare"
      onChange={onChange}
      items={personalCareFilterItems}
      value={value}
    />
  );
}

export default PersonalCareFilter;
