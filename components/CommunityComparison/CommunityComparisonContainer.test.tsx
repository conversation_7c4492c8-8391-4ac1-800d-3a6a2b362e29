import { mockModMonProvider } from '@mocks/modmon.mock';
import { render, screen } from '@testing-library/react';
import { mockResizeObserver } from 'jsdom-testing-mocks';
import { act } from 'react';

import { Provider } from '~/contexts/Provider';
import { TenantFunctionsContext } from '~/contexts/TenantFunctionsContext';

import CommunityComparisonContainer, {
  MEDIUM_SCREEN_WIDTH,
  SMALL_SCREEN_WIDTH
} from './CommunityComparisonContainer';
const resizeObserverMock = mockResizeObserver();

describe('CommunityComparisonContainer', () => {
  const mockGetProviderDetailsPath = jest.fn();
  const defaultWrapper = ({ children }) => (
    <TenantFunctionsContext.Provider
      value={{
        getProviderDetailsPath: mockGetProviderDetailsPath,
        getProviderDescription: jest.fn(),
        getBreadcrumbLinks: jest.fn(),
        getCareTypePath: jest.fn(),
        getCareTypePathForSitemap: jest.fn(),
        getCareTypeParamsForSitemap: jest.fn(),
        generateContentForStateSitemap: jest.fn(),
        getTrailingSlashURL: jest.fn(),
        getReviewPath: jest.fn(),
        getCountyForSearch: jest.fn()
      }}
    >
      {children}
    </TenantFunctionsContext.Provider>
  );

  const originalWindowInnerWidth = window.innerWidth;
  afterEach(() => {
    window.innerWidth = originalWindowInnerWidth;
  });

  describe('image handling', () => {
    it('uses images when available', () => {
      const { container } = render(
        <CommunityComparisonContainer providers={[mockModMonProvider]} />,
        { wrapper: defaultWrapper }
      );

      const renderedImages = container.querySelectorAll('img');
      expect(renderedImages[0]).toHaveAttribute(
        'src',
        'https://d13iq96prksfh0.cloudfront.net/cdn/photos/215515/275x275'
      );
    });

    it('falls back to photos when images is empty', () => {
      const providerWithoutImages = {
        ...mockModMonProvider,
        images: []
      };

      const { container } = render(
        <CommunityComparisonContainer
          providers={[providerWithoutImages as Provider]}
        />,
        { wrapper: defaultWrapper }
      );

      const renderedImages = container.querySelectorAll('img');
      expect(renderedImages[0]).toHaveAttribute(
        'src',
        'https://d13iq96prksfh0.cloudfront.net/cdn/photos/242074/275x275'
      );
    });

    it('uses fallback image when both images and photos are undefined', () => {
      const providerWithoutMedia = {
        ...mockModMonProvider,
        images: undefined,
        photos: undefined
      };

      const { container } = render(
        <CommunityComparisonContainer
          providers={[providerWithoutMedia as Provider]}
        />,
        { wrapper: defaultWrapper }
      );

      const renderedImages = container.querySelectorAll('img');
      expect(renderedImages.length).toBe(1);
    });

    it('handles null values in photos array', () => {
      const providerWithNullPhotos = {
        ...mockModMonProvider,
        images: [],
        photos: [
          { url: 'https://d13iq96prksfh0.cloudfront.net/cdn/photos/193204' },
          null,
          { url: 'https://d13iq96prksfh0.cloudfront.net/cdn/photos/193207' }
        ]
      };

      const { container } = render(
        <CommunityComparisonContainer
          providers={[providerWithNullPhotos as Provider]}
        />,
        { wrapper: defaultWrapper }
      );

      const renderedImages = container.querySelectorAll('img');
      expect(renderedImages[0]).toHaveAttribute(
        'src',
        'https://d13iq96prksfh0.cloudfront.net/cdn/photos/193204/275x275'
      );
    });

    it('shows 3 slides with large container', () => {
      const { container } = render(
        <CommunityComparisonContainer
          providers={[
            mockModMonProvider,
            { ...mockModMonProvider, id: '123', legacyId: '456' },
            { ...mockModMonProvider, id: '789', legacyId: '101' }
          ]}
        />,
        { wrapper: defaultWrapper }
      );

      const slides = container.querySelectorAll('.slick-slide');
      expect(slides.length).toBe(3);
      expect(slides[0]).toHaveAttribute('aria-hidden', 'false');
      expect(slides[1]).toHaveAttribute('aria-hidden', 'false');
      expect(slides[2]).toHaveAttribute('aria-hidden', 'false');
    });

    it('shows 2 slides with medium container', () => {
      const { container } = render(
        <CommunityComparisonContainer
          providers={[
            mockModMonProvider,
            { ...mockModMonProvider, id: '123', legacyId: '456' },
            { ...mockModMonProvider, id: '789', legacyId: '101' }
          ]}
        />,
        { wrapper: defaultWrapper }
      );

      const slideContainer = screen.getByTestId(
        'community-comparison-container'
      );
      resizeObserverMock.mockElementSize(slideContainer, {
        contentBoxSize: { inlineSize: MEDIUM_SCREEN_WIDTH - 1 }
      });
      act(() => {
        resizeObserverMock.resize();
      });

      const slides = container.querySelectorAll('.slick-slide');
      expect(slides.length).toBe(3);
      expect(slides[0]).toHaveAttribute('aria-hidden', 'false');
      expect(slides[1]).toHaveAttribute('aria-hidden', 'false');
      expect(slides[2]).toHaveAttribute('aria-hidden', 'true');
    });

    it('shows 1 slide with small container', () => {
      const { container } = render(
        <CommunityComparisonContainer
          providers={[
            mockModMonProvider,
            { ...mockModMonProvider, id: '123', legacyId: '456' },
            { ...mockModMonProvider, id: '789', legacyId: '101' }
          ]}
        />,
        { wrapper: defaultWrapper }
      );

      const slideContainer = screen.getByTestId(
        'community-comparison-container'
      );
      resizeObserverMock.mockElementSize(slideContainer, {
        contentBoxSize: { inlineSize: SMALL_SCREEN_WIDTH - 1 }
      });
      act(() => {
        resizeObserverMock.resize();
      });

      const slides = container.querySelectorAll('.slick-slide');
      expect(slides.length).toBe(3);
      expect(slides[0]).toHaveAttribute('aria-hidden', 'false');
      expect(slides[1]).toHaveAttribute('aria-hidden', 'true');
      expect(slides[2]).toHaveAttribute('aria-hidden', 'true');
    });
  });

  it('calculates a max height for cards', () => {
    const { container } = render(
      <CommunityComparisonContainer
        providers={[
          mockModMonProvider,
          { ...mockModMonProvider, id: '123', legacyId: '456' },
          {
            ...mockModMonProvider,
            id: '789',
            name: 'Some Really Long Name That Will Definitely Push The Card Height Way More Than The Other Card Because We Need To Test Height Disparity',
            legacyId: '101'
          }
        ]}
      />,
      { wrapper: defaultWrapper }
    );

    const card = screen.getByTestId('community-comparison-card-456');
    expect(window.getComputedStyle(card).height).toEqual('100%');
  });
});
