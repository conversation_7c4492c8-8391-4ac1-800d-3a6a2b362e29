'use client';

import { Stack } from '@chakra-ui/layout';
import { NearbyProps } from '@components/NearbyCare/types';
import { Section } from '@components/Sections';
import { ProvidersNearMeQueryResponse } from '@hooks/provider';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import orderBy from 'lodash/orderBy';
import dynamic from 'next/dynamic';
import React, { useContext, useEffect } from 'react';

import ProviderContext from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Domains } from '~/types/Domains';
import { providerIsEnhancedAndNotSuppressed } from '~/utils/providers';

import CommunityComparisonContainer from './CommunityComparisonContainer';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

export type FetchResponse = {
  nearbyProviders: ProvidersNearMeQueryResponse | {};
};

export interface CommunityComparisonProps {
  communities: NearbyProps;
  latitude?: string;
  longitude?: string;
  data: {
    nearbyProviders: ProvidersNearMeQueryResponse;
  };
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  displayBadges?: boolean;
  displayViewCommunityButton?: boolean;
  viewCommunityButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  inquiryId?: string;
  viewCommunityButtonColorScheme?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  deviceVisibility?: DeviceVisibility;
}

const CommunityComparison: React.FC<CommunityComparisonProps> = ({
  communities,
  inquiryId = '',
  displayBadges,
  providerTitleColor,
  providerTitleColorRange,
  viewCommunityButtonText,
  displayRequestInfoButton,
  data: { nearbyProviders },
  displayViewCommunityButton,
  ratingStarsColor = 'accent',
  ratingStarsColorRange = '500',
  viewCommunityButtonColorScheme,
  deviceVisibility
}) => {
  const { site } = useContext(SiteContext);
  const isCaringWebSite = site?.path === Domains.CaringDomains.LIVE;
  const provider = useContext(ProviderContext)?.provider;
  const isHidden = useResponsiveDisplay(deviceVisibility);

  useEffect(() => {
    const id = window.location.hash.substring(1);
    if (id.startsWith('provider-comparison-')) {
      document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  if (isHidden || !provider) {
    return <></>;
  }

  const isEnhanced = providerIsEnhancedAndNotSuppressed(provider);
  const isBasicListing = isCaringWebSite && !isEnhanced;
  const displayNearbyProviderListing = !isCaringWebSite || isBasicListing;

  const nearbyCommunities =
    orderBy(
      [provider, ...nearbyProviders?.results],
      ['bayesian_average'],
      ['desc']
    ) || [];

  if (nearbyProviders?.results?.length === 0 || !displayNearbyProviderListing)
    return <></>;

  return (
    <Container py="16" id={`provider-comparison-${provider.id}`}>
      <Stack spacing="8" align="stretch">
        <Section
          headingElement={communities.headingElement}
          richText={communities.content}
          title={communities.heading}
        />
        {nearbyCommunities.length > 0 && (
          <CommunityComparisonContainer
            modalId={inquiryId}
            providers={nearbyCommunities}
            displayBadges={displayBadges}
            ratingStarsColor={ratingStarsColor}
            providerTitleColor={providerTitleColor}
            ratingStarsColorRange={ratingStarsColorRange}
            viewCommunityButtonText={viewCommunityButtonText}
            providerTitleColorRange={providerTitleColorRange}
            displayRequestInfoButton={displayRequestInfoButton}
            enableTrailingSlash={communities.enableTrailingSlash}
            displayViewCommunityButton={displayViewCommunityButton}
            viewCommunityButtonColorScheme={viewCommunityButtonColorScheme}
          />
        )}
      </Stack>
    </Container>
  );
};

export default CommunityComparison;
