'use client';

import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';

import { Box } from '@chakra-ui/react';
import styled from '@emotion/styled';
import React, { useEffect, useRef, useState } from 'react';
import { MdArrowBackIos, MdArrowForwardIos } from 'react-icons/md';
import Slider, { Settings } from 'react-slick';

import { Provider } from '~/contexts/Provider';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import {
  calculateProviderMinPrice,
  contractIsSubscription,
  providerIsEnhancedAndNotSuppressed
} from '~/utils/providers';

import CommunityComparisonCard from './CommunityComparisonCard';

interface ArrowProps {
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  currentSlide?: number;
  slideCount?: number;
}

export interface CommunityComparisonContainerProps {
  providers?: Provider[];
  enableTrailingSlash?: boolean;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  displayBadges?: boolean;
  displayViewCommunityButton?: boolean;
  displayCurrentProvider?: boolean;
  viewCommunityButtonText?: string;
  displayRequestInfoButton?: boolean;
  modalId?: string;
  viewCommunityButtonColorScheme?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  queryId?: string;
  listId?: string;
  customSliderSettings?: Settings;
}

const DotsWrapper = styled(Box)`
  .slick-slider {
    @media (max-width: 1024px) {
      margin: 15px;
    }
  }
  .slick-dots li {
    position: relative;
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0;
    padding: 0;
    cursor: pointer;
    opacity: 0.5;
    &.slick-active {
      opacity: 1;
    }
  }
`;

const PrevArrow = ({ className, style, onClick, currentSlide }: ArrowProps) => {
  return (
    <Box
      h="48px"
      w="48px"
      left={{ base: '-45px', xl: '-60px' }}
      borderRadius="100%"
      className={className}
      _before={{ display: 'none' }}
      style={{
        ...style,
        zIndex: '100',
        backgroundColor: 'white',
        boxShadow: '0px 13px 26px -6px rgba(0, 0, 0, 0.25)',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      display={{
        base:
          currentSlide && currentSlide > 0
            ? 'flex !important'
            : 'none !important'
      }}
      onClick={onClick}
    >
      <MdArrowBackIos
        fontSize="14px"
        color="black"
        width="14px"
        height="14px"
      />
    </Box>
  );
};

const NextArrow = ({
  className,
  style,
  onClick,
  currentSlide,
  slideCount
}: ArrowProps) => {
  return (
    <Box
      h="48px"
      w="48px"
      right={{ base: '-45px', xl: '-60px' }}
      borderRadius="100%"
      className={className}
      _before={{ display: 'none' }}
      style={{
        ...style,
        zIndex: '100',
        backgroundColor: 'white',
        boxShadow: '0px 13px 26px -6px rgba(0, 0, 0, 0.25)',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      display={{
        base:
          currentSlide && currentSlide + 1 === slideCount
            ? 'none !important'
            : 'flex !important',
        md:
          currentSlide && currentSlide + 2 === slideCount
            ? 'none !important'
            : 'flex !important',
        lg:
          currentSlide && currentSlide + 3 === slideCount
            ? 'none !important'
            : 'flex !important'
      }}
      onClick={onClick}
    >
      <MdArrowForwardIos
        fontSize="14px"
        color="black"
        width="14px"
        height="14px"
      />
    </Box>
  );
};

export const SMALL_SCREEN_WIDTH = 768;
export const MEDIUM_SCREEN_WIDTH = 991;

const CommunityComparisonContainer = ({
  providers,
  modalId = '',
  queryId = '',
  listId = '',
  ratingStarsColor,
  providerTitleColor,
  displayBadges = false,
  ratingStarsColorRange,
  providerTitleColorRange,
  viewCommunityButtonText,
  customSliderSettings = {},
  displayViewCommunityButton,
  enableTrailingSlash = false,
  displayCurrentProvider = true,
  viewCommunityButtonColorScheme,
  displayRequestInfoButton = true
}: CommunityComparisonContainerProps): JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [slidesToShow, setSlidesToShow] = useState(3);

  useEffect(() => {
    // TODO: CORE-1615 - Create Properly Responsive Slider
    // https://caring.atlassian.net/jira/software/c/projects/CORE/boards/144/backlog?selectedIssue=CORE-1615
    // The following code is a temporary solution to make the slider responsive, but ideally we'd
    // be using CSS media queries to handle this. react-slick Slider supports responsiveness but in a limited way.
    // We need the slider to be responsive to the container size, and not the page size.

    const observer = new ResizeObserver((entries) => {
      const containerWidth = entries[0].contentRect.width;

      if (containerWidth < SMALL_SCREEN_WIDTH) {
        setSlidesToShow(1);
      } else if (containerWidth < MEDIUM_SCREEN_WIDTH) {
        setSlidesToShow(2);
      } else {
        setSlidesToShow(3);
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [containerRef]);

  const { getProviderDetailsPath } = useTenantFunctions();

  const [maxCardHeight, setMaxCardHeight] = useState('100%');

  const settings = {
    dots: true,
    appendDots: (dots: React.ReactNode) => (
      <ul style={{ bottom: '-40px' }}> {dots} </ul>
    ),
    customPaging: () => (
      <Box _before={{ color: 'gray.600', content: '"•"' }}></Box>
    ),
    infinite: false,
    slidesToShow: slidesToShow,
    slidesToScroll: slidesToShow,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    ...customSliderSettings
  };

  useEffect(() => {
    // This calculation to enforce the card heights is not working properly anymore.
    // The goal of this block is to make all cards the same height.
    // If there is one card, it sets to 100%.
    // If there are multiple, it figures out which has the most height and sets all to it.
    // The problem is that now that we are being responsive on container width and not page width.
    // We need to allow the resize observer to redraw the cards and THEN determine
    // what the resulting heights are, but that is a hacky thing to do.
    // This should all go away with CORE-1615 noted above.
    if (!containerRef.current || !window) {
      return;
    }

    const containerWidth = containerRef.current?.getBoundingClientRect().width;

    // Calculate the maximum height of CommunityComparisonCard components
    const cardElements = document.querySelectorAll('.slick-slide > div');
    let maxHeight = 0;

    cardElements.forEach((element) => {
      const height = element.clientHeight;
      maxHeight = Math.max(maxHeight, height);
    });

    // Set the maximum height for all cards
    setMaxCardHeight(
      `${containerWidth > SMALL_SCREEN_WIDTH ? maxHeight : '100%'}`
    );
  }, [providers]);

  return (
    <DotsWrapper
      data-testid="community-comparison-container"
      ref={containerRef}
    >
      <Slider {...settings}>
        {providers?.map((result, index) => {
          const currentProvider = index === 0;
          const price =
            result?.startPriceInCents ?? calculateProviderMinPrice({ result });
          const url = getProviderDetailsPath(result);
          const address = `${result.address?.city}, ${result.address?.state} ${
            result.address?.zipCode || ''
          }`;
          const images = result?.images?.length
            ? result.images.filter(Boolean)
            : result?.photos
                ?.filter(Boolean)
                ?.map((photo) => photo?.url)
                ?.filter(Boolean) ?? [];
          const title = result.name;
          const id = result.legacyId ?? result.id;
          const accommodations = result?.accommodations
            ?.filter((accommodation) => accommodation.title)
            .map((item) => item.title?.toLocaleLowerCase());

          const isSubscription = contractIsSubscription(result);
          const isEnhanced = providerIsEnhancedAndNotSuppressed(result);

          return (
            <CommunityComparisonCard
              key={id}
              id={id}
              url={url}
              title={title}
              price={price}
              images={images}
              listId={listId}
              provider={result}
              address={address}
              queryId={queryId}
              modalId={modalId}
              height={maxCardHeight}
              displayBadges={displayBadges}
              accommodations={accommodations}
              currentProvider={currentProvider}
              ratingStarsColor={ratingStarsColor}
              providerTitleColor={providerTitleColor}
              ratingStarsColorRange={ratingStarsColorRange}
              displayCurrentProvider={displayCurrentProvider}
              providerTitleColorRange={providerTitleColorRange}
              viewCommunityButtonText={viewCommunityButtonText}
              displayRequestInfoButton={displayRequestInfoButton}
              displayViewCommunityButton={displayViewCommunityButton}
              viewCommunityButtonColorScheme={viewCommunityButtonColorScheme}
              showVerifiedBadge={
                result.isVerified ?? (isSubscription || isEnhanced)
              }
            />
          );
        })}
      </Slider>
    </DotsWrapper>
  );
};

export default CommunityComparisonContainer;
