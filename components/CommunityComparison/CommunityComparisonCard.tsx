import {
  Box,
  Card,
  Divider,
  Heading,
  HStack,
  Icon,
  Link,
  Stack,
  Text,
  VStack
} from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { CaringStars } from '@components/CaringStars';
import FeaturedAmenities from '@components/ProviderCard/FeaturedAmenities';
import { Rating } from '@components/Search/SearchResult';
import SearchResultImage from '@components/Search/SearchResultImage';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import {
  getFeaturedAmenities,
  getFeaturedAmenitiesByIds
} from '@utils/amenities';
import { getColor } from '@utils/getColor';
import { formatNumberLocale, formatRating } from '@utils/number';
import parse from 'html-react-parser';
import { useCallback } from 'react';
import { MdVerified } from 'react-icons/md';
import CookieStorage from 'utils/cookieStorage';

import { useModalControls } from '~/contexts/ModalContext';
import { Provider } from '~/contexts/Provider';

export interface CommunityComparisonCardProps {
  provider: Provider;
  id: string;
  url?: string;
  title: string;
  price: number;
  images: Array<string>;
  listId: string;
  address: string;
  queryId: string;
  height?: string;
  modalId?: string;
  displayBadges: boolean;
  currentProvider: boolean;
  enhancedListing?: boolean;
  accommodations?: Array<string | undefined>;
  displayRequestInfoButton?: boolean;
  displayViewCommunityButton?: boolean;
  displayCurrentProvider?: boolean;
  viewCommunityButtonText?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  viewCommunityButtonColorScheme?: string;
  showVerifiedBadge?: boolean;
}

const hasCaringStars = (provider: Provider): boolean => {
  return Boolean(
    provider.isCaringStar ||
      provider.caringStars?.length ||
      provider.awards?.some((award) => award?.name === 'Caring Stars')
  );
};

const CommunityComparisonCard = ({
  id,
  url,
  title,
  price,
  images,
  listId,
  address,
  queryId,
  provider,
  modalId = '',
  height = '100%',
  accommodations,
  currentProvider,
  providerTitleColor,
  displayBadges = true,
  providerTitleColorRange,
  displayCurrentProvider = true,
  ratingStarsColor = 'tertiary',
  ratingStarsColorRange = '500',
  displayRequestInfoButton = true,
  displayViewCommunityButton = true,
  viewCommunityButtonText = 'View Community',
  viewCommunityButtonColorScheme = 'secondary',
  showVerifiedBadge = false
}: CommunityComparisonCardProps): JSX.Element => {
  const elementClicked = useElementClicked();
  const showPrice = useInquiryFormSubmitted();

  const { show: showInquiryForm } = useModalControls(modalId);

  const openInquiryForm = useCallback(
    ({ url, id }) => {
      const selectedProvider = { url, id };

      if (CookieStorage.enabled) {
        CookieStorage.set('provider', JSON.stringify(selectedProvider));
      }

      showInquiryForm();
    },
    [showInquiryForm]
  );

  return (
    <Card
      data-testid={`community-comparison-card-${id}`}
      key={id}
      h={height}
      padding={0}
      margin="5px"
      background="white"
      borderRadius="md"
      boxShadow={
        currentProvider && displayCurrentProvider
          ? '0px 10px 15px -3px #0000001A'
          : 'none'
      }
    >
      <VStack gap={0} h={height}>
        {displayCurrentProvider && (
          <Text
            gap="10px"
            width="full"
            height="36px"
            color="white"
            display="flex"
            fontWeight={700}
            alignItems="center"
            justifyContent="center"
            borderRadius="12px 12px 0 0"
            backgroundColor={currentProvider ? 'primary.600' : 'transparent'}
          >
            {currentProvider && 'Currently Viewing'}
          </Text>
        )}

        <VStack
          p="10px"
          bg="white"
          rounded="md"
          width="full"
          height="100%"
          alignItems={'left'}
          justifyContent="space-between"
          minHeight={{ base: 'max-content', sm: 'xs', md: '360px' }}
        >
          <VStack alignItems="left">
            <Box rounded="md">
              <Box
                height="155"
                rounded="md"
                bg="gray.200"
                overflow="hidden"
                position="relative"
              >
                <Link
                  href={currentProvider ? '' : url}
                  _hover={{ textDecoration: 'none' }}
                  onClick={(e) => {
                    if (currentProvider) {
                      e.preventDefault();
                      window.scroll(0, 0);
                    }
                    elementClicked({
                      element: {
                        type: ElementTypes.LINK,
                        action: ElementActions.INTERNAL_LINK,
                        name: ElementNames.COMPARISON_PROVIDER_IMAGE,
                        text: '',
                        color: '',
                        textColor: ''
                      },
                      destinationUrl: '/bestseniorliving/'
                    });
                  }}
                >
                  <SearchResultImage images={images} title={title} />
                </Link>
              </Box>
            </Box>
            <HStack
              pb={2}
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <Link
                href={currentProvider ? '' : url}
                _hover={{ textDecoration: 'none' }}
                onClick={(e) => {
                  if (currentProvider) {
                    e.preventDefault();
                    window.scroll(0, 0);
                  }
                  elementClicked({
                    element: {
                      type: ElementTypes.LINK,
                      action: ElementActions.INTERNAL_LINK,
                      name: ElementNames.COMPARISON_PROVIDER_TITLE,
                      text: title,
                      color: '',
                      textColor: getColor(
                        providerTitleColor,
                        providerTitleColorRange
                      )
                    },
                    destinationUrl: '/bestseniorliving/'
                  });
                }}
              >
                <Heading
                  className="texto-icono-centrados"
                  size="md"
                  as={'span'}
                  color={getColor(providerTitleColor, providerTitleColorRange)}
                  display="inline-block"
                  width="100%"
                >
                  {title}
                  {showVerifiedBadge && (
                    <Icon
                      as={MdVerified}
                      w="24px"
                      h="24px"
                      role="presentation"
                      color="primary.700"
                      verticalAlign="top"
                      ml="5px"
                      float={{ base: 'right', md: 'none' }}
                      data-testid="community-comparison-verified-badge"
                    />
                  )}
                </Heading>
              </Link>
              {displayBadges && hasCaringStars(provider) && (
                <Link
                  width={55}
                  target="_blank"
                  href="/bestseniorliving/"
                  _hover={{ textDecoration: 'none' }}
                  onClick={() => {
                    elementClicked({
                      element: {
                        type: ElementTypes.LINK,
                        action: ElementActions.INTERNAL_LINK,
                        name: ElementNames.CARING_STARS_BADGE,
                        text: '',
                        color: '',
                        textColor: ''
                      },
                      destinationUrl: '/bestseniorliving/'
                    });
                  }}
                >
                  <CaringStars displayYears={false} />
                </Link>
              )}
            </HStack>
            <Text size="xs" color="gray.700" m={0}>
              {parse(address)}
            </Text>
            {displayRequestInfoButton && (
              <Box gap={10} w="full">
                <Button
                  my={2}
                  w="full"
                  size="md"
                  minH={10}
                  colorScheme="secondary"
                  data-testid="compare-request-info-btn"
                  onClick={() => openInquiryForm({ url, id: provider.id })}
                  elementType={ElementTypes.BUTTON}
                  elementAction={ElementActions.OPEN_MODAL}
                  elementName={ElementNames.INFO_REQUEST_SECTION}
                  destinationUrl={url}
                  query={{
                    locationId: id ?? '',
                    queryId: queryId,
                    listId: listId
                  }}
                >
                  {showPrice ? 'Get Information' : 'Get Pricing'}
                </Button>
              </Box>
            )}
          </VStack>
          <Divider borderColor="gray.300" height="2px" gap="14px" />
          <Stack direction="row" alignItems="center">
            <Link
              w="full"
              _hover={{ textDecoration: 'none' }}
              href={`${url}#all_reviews_section`}
              onClick={() => {
                elementClicked({
                  element: {
                    type: ElementTypes.LINK,
                    action: ElementActions.INTERNAL_LINK,
                    name: ElementNames.COMPARISON_PROVIDER_RATING,
                    text: '',
                    color: '',
                    textColor: ''
                  },
                  destinationUrl: `${url}#all_reviews_section`
                });
              }}
            >
              <Rating
                iconSize={8}
                countFontSize="30px"
                reviewsFontSize="md"
                reviewsTextDecoration="underline"
                reviewCount={provider.reviewCount}
                ratingStarsColor={ratingStarsColor}
                rating={formatRating(provider.averageRating)}
                ratingStarsColorRange={ratingStarsColorRange}
              />
            </Link>
          </Stack>
          <Divider borderColor="gray.300" height="2px" gap="14px" />
          <VStack alignItems="flex-start">
            <Heading as="p" size="sm" fontWeight={400}>
              Bedroom Type
            </Heading>
            <Text
              as="p"
              minH="24px"
              size="sm"
              fontWeight={700}
              textTransform="capitalize"
            >
              {accommodations &&
                accommodations?.length > 0 &&
                accommodations?.join(', ')}
            </Text>
          </VStack>
          <Divider borderColor="gray.300" height="2px" gap="14px" />
          <VStack alignItems="flex-start">
            <Heading as="p" size="sm" fontWeight={400}>
              Starting Price
            </Heading>
            {price > 0 ? (
              <Text as="p" fontWeight={700} size={{ base: 'sm', md: 'md' }}>
                $
                <Text
                  as="span"
                  fontWeight={700}
                  textTransform="capitalize"
                  size={{ base: 'sm', md: 'md' }}
                  filter={showPrice ? '' : 'blur(10px)'}
                >
                  {formatNumberLocale(price / 100)}/mo
                </Text>
              </Text>
            ) : (
              <Text color="gray.600" filter={showPrice ? '' : 'blur(10px)'}>
                Pricing not available
              </Text>
            )}
          </VStack>
          <Divider borderColor="gray.300" height="2px" gap="14px" />
          <VStack alignItems="flex-start">
            <Heading as="p" size="sm" fontWeight={400}>
              Featured Amenities
            </Heading>
            <Box h="24px" mt={2}>
              <FeaturedAmenities
                amenities={
                  provider?.amenityIds
                    ? getFeaturedAmenitiesByIds(provider.amenityIds)
                    : getFeaturedAmenities(provider.amenities || {})
                }
                hasJumpLink={currentProvider ? true : false}
              />
            </Box>
          </VStack>
          <Divider borderColor="gray.300" height="2px" gap="14px" />
          <VStack alignItems="flex-start">
            <Heading as="p" size="sm" fontWeight={400}>
              Care Types
            </Heading>
            <Text as="p" size="sm" fontWeight={700} textTransform="capitalize">
              {provider?.careTypes?.join(', ') ??
                provider?.services
                  ?.map((item) => item.category.name)
                  .join(', ')}
            </Text>
          </VStack>
          {displayViewCommunityButton && (
            <Button
              as="a"
              size="md"
              minH={10}
              href={url}
              variant="outline"
              onClick={() => {
                elementClicked({
                  element: {
                    type: ElementTypes.LINK,
                    action: ElementActions.INTERNAL_LINK,
                    name: ElementNames.PROVIDER_CARD,
                    text: title,
                    color: 'white',
                    textColor:
                      getColor(providerTitleColor, providerTitleColorRange) ||
                      ''
                  },
                  destinationUrl: url,
                  query: {
                    locationId: id,
                    queryId: queryId,
                    listId: listId
                  }
                });
              }}
              elementType={ElementTypes.BUTTON}
              elementAction={ElementActions.OPEN_MODAL}
              elementName={ElementNames.INFO_REQUEST_SECTION}
              destinationUrl={url}
              colorScheme={viewCommunityButtonColorScheme}
              query={{
                locationId: id ?? '',
                queryId: queryId,
                listId: listId
              }}
            >
              {viewCommunityButtonText}
            </Button>
          )}
        </VStack>
      </VStack>
    </Card>
  );
};

export default CommunityComparisonCard;
