import { render, screen, waitFor } from '@testing-library/react';
import { fireEvent } from '@utils/test-utils';
import { mockRouter } from '@utils/test-utils/mocks/router';
import { NextRouter } from 'next/router';

import { Provider } from '~/contexts/Provider';

import {
  CommunityComparisonBanner,
  ComparisonErrorDialog
} from './GeosCommunityComparison';

// Mock @chakra-ui/react
jest.mock('@chakra-ui/react', () => ({
  ...jest.requireActual('@chakra-ui/react'),
  useBreakpointValue: jest.fn()
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} alt="" />;
  }
}));

const communityComparisonBannerPropsMock = {
  displayBadges: true,
  ratingStarsColor: 'tertiary',
  ratingStarsColorRange: '500',
  providerTitleColor: 'black',
  providerTitleColorRange: '500',
  displayRequestInfoButton: true,
  handleCompare: jest.fn(),
  onCancel: jest.fn(),
  displayViewCommunityButton: true,
  viewCommunityButtonColorScheme: 'secondary',
  openCompareModal: false,
  router: mockRouter() as NextRouter
};

describe('Geo Comparison Error Modal', () => {
  test('renders modal when isOpen is true', () => {
    render(<ComparisonErrorDialog isOpen onClose={() => {}} />);
    expect(screen.getByText(/Too many selections/i)).toBeInTheDocument();
  });

  test('does not render modal when isOpen is false', () => {
    render(<ComparisonErrorDialog isOpen={false} onClose={() => {}} />);
    expect(screen.queryByText(/Too many selections/i)).not.toBeInTheDocument();
  });

  test('calls onClose prop on button click', () => {
    const mockOnClose = jest.fn();
    render(<ComparisonErrorDialog isOpen={true} onClose={mockOnClose} />);
    const button = screen.getByRole('button', { name: /Continue/i });
    fireEvent.click(button);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
});

const mockItems = [
  {
    id: '1',
    name: 'Community 1',
    address: { city: 'New York', state: 'NY' },
    images: [
      'https://dsjrrk2ui1p8q.cloudfront.net/cdn/photos/147197/original.png'
    ]
  },
  {
    id: '2',
    name: 'Community 2',
    address: { city: 'Los Angeles', state: 'CA' },
    images: [
      'https://dsjrrk2ui1p8q.cloudfront.net/cdn/photos/147197/original.png'
    ]
  }
] as Provider[];

const mockAnalytics = {
  track: jest.fn()
};
window.tracking = mockAnalytics;

describe('Geo Comparison Banner', () => {
  test('renders banner with 2 items', () => {
    render(
      <CommunityComparisonBanner
        providers={mockItems}
        {...communityComparisonBannerPropsMock}
      />
    );
    const compareButton = screen.getByTestId('compareDesktop');
    expect(compareButton).not.toBeDisabled();
    expect(screen.getByText(/Compare 2 of 4/i)).toBeInTheDocument();
    const communityNames = screen.getAllByText(/Community/i);
    expect(communityNames.length).toBe(2);
  });

  test('renders banner with 1 item', () => {
    render(
      <CommunityComparisonBanner
        providers={[mockItems[0]]}
        {...communityComparisonBannerPropsMock}
      />
    );
    const compareButton = screen.getByTestId('compareDesktop');
    expect(compareButton).toBeDisabled();
    expect(
      screen.getByText(/Add another community to compare/i)
    ).toBeInTheDocument();
    const communityNames = screen.getAllByText(/Community/i);
    expect(communityNames.length).toBe(2);
  });

  test('calls close button click', () => {
    const useBreakpointValueMock =
      jest.requireMock('@chakra-ui/react').useBreakpointValue;
    useBreakpointValueMock.mockReturnValue(true);
    render(
      <CommunityComparisonBanner
        providers={mockItems}
        {...communityComparisonBannerPropsMock}
      />
    );

    const removeButton = screen.getByTestId('remove1');
    expect(removeButton).toBeInTheDocument();
    fireEvent.click(removeButton);
  });

  test('toggles accordion on button click (mobile)', async () => {
    const useBreakpointValueMock =
      jest.requireMock('@chakra-ui/react').useBreakpointValue;
    useBreakpointValueMock.mockReturnValue(false);
    render(
      <CommunityComparisonBanner
        providers={mockItems}
        {...communityComparisonBannerPropsMock}
      />
    );

    const accordionButton = screen.getByTestId('accordionButton');
    expect(accordionButton).toBeVisible();
    fireEvent.click(accordionButton);
    await waitFor(() => {
      const accordion = screen.getByRole('region');
      expect(accordion).toBeVisible();
    });
  });

  test('calls compare button and display compare modal', () => {
    const useBreakpointValueMock =
      jest.requireMock('@chakra-ui/react').useBreakpointValue;
    useBreakpointValueMock.mockReturnValue(true);

    const mockLocation = {
      pathname: '',
      hash: '',
      search: '',
      assign: jest.fn(),
      reload: jest.fn(),
      replace: jest.fn(),
      toString: jest.fn()
    };

    delete window.location;
    window.location = mockLocation;

    render(
      <CommunityComparisonBanner
        providers={mockItems}
        {...communityComparisonBannerPropsMock}
      />
    );

    const compareButton = screen.getByTestId('compareDesktop');
    expect(compareButton).toBeInTheDocument();
    expect(compareButton).not.toBeDisabled();
    fireEvent.click(compareButton);
  });
});
