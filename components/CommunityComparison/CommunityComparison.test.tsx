import { Box } from '@chakra-ui/react';
import { useResponsiveDisplay } from '@hooks/useResponsiveDisplay';
import { render, screen } from '@testing-library/react';
import { providerIsEnhancedAndNotSuppressed } from '@utils/providers';
import { ComponentProps, useContext } from 'react';

import CommunityComparison from './CommunityComparison';

jest.mock('./CommunityComparisonContainer', () =>
  jest.fn(() => <Box>Community Comparison Container</Box>)
);

jest.mock('~/utils/providers', () => ({
  providerIsEnhancedAndNotSuppressed: jest.fn().mockReturnValue(false)
}));

jest.mock('@hooks/useResponsiveDisplay', () => ({
  ...jest.requireActual('@hooks/useResponsiveDisplay'),
  useResponsiveDisplay: jest.fn().mockReturnValue(false)
}));

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn().mockReturnValue({
    site: { path: 'caring.com' },
    provider: { id: 'providerId' }
  })
}));

describe('components::CommunityComparison', () => {
  const renderComponent = (
    props: Partial<ComponentProps<typeof CommunityComparison>> = {}
  ) =>
    render(
      <CommunityComparison
        deviceVisibility="desktop"
        communities={{ title: 'Community Comparison Title' }}
        data={{ nearbyProviders: { results: [{}] } }}
        {...props}
      />
    );

  test('renders the container', () => {
    renderComponent();
    expect(
      screen.getByText('Community Comparison Container')
    ).toBeInTheDocument();
  });

  describe('does not render', () => {
    test('when useResponsiveDisplay hides it', () => {
      (useResponsiveDisplay as jest.Mock).mockReturnValueOnce(true);

      renderComponent();
      expect(
        screen.queryByText('Community Comparison Container')
      ).not.toBeInTheDocument();
    });

    test('when no provider', () => {
      (useContext as jest.Mock).mockReturnValue({});

      renderComponent();
      expect(
        screen.queryByText('Community Comparison Container')
      ).not.toBeInTheDocument();
    });

    test('with nearbyProviders without services', () => {
      renderComponent({ nearbyProviders: { servides: [] } });
      expect(
        screen.queryByText('Community Comparison Container')
      ).not.toBeInTheDocument();
    });

    test('when site is caring and provider is enhanced', () => {
      (useContext as jest.Mock).mockReturnValue({
        site: { path: 'caring.com' },
        provider: { id: 'providerId-2' }
      });
      (providerIsEnhancedAndNotSuppressed as jest.Mock).mockReturnValue(true);

      renderComponent();
      expect(
        screen.queryByText('Community Comparison Container')
      ).not.toBeInTheDocument();
    });
  });

  describe('scroll to content', () => {
    test("is possible when hash includes provider's id", () => {
      const scrollIntoView = jest.fn();
      jest
        .spyOn(window.location, 'hash', 'get')
        .mockReturnValue('#provider-comparison-1234');
      jest
        .spyOn(document, 'getElementById')
        .mockReturnValue({ scrollIntoView } as unknown as HTMLElement);

      renderComponent();
      expect(scrollIntoView).toBeCalledTimes(1);
    });

    test("should not be trigger without provider's id in the hash", () => {
      const scrollIntoView = jest.fn();
      jest.spyOn(window.location, 'hash', 'get').mockReturnValue('');
      jest
        .spyOn(document, 'getElementById')
        .mockReturnValue({ scrollIntoView } as unknown as HTMLElement);

      renderComponent();
      expect(scrollIntoView).not.toBeCalled();
    });
  });
});
