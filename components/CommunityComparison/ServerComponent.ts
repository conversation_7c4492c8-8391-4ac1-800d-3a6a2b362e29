import { NearbyCareProps } from '@components/NearbyCare/types';
import { fetchNearbyProviders } from '@hooks/provider';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { FetchResponse } from './CommunityComparison';

export const getServerSideComponentProps = async (
  { careType, latitude, longitude }: NearbyCareProps,
  context: GetServerSidePropsContext
): Promise<FetchResponse> => {
  const site = findSiteForContext(context);
  const lat = parseFloat(latitude ?? '') || 0;
  const lng = parseFloat(longitude ?? '') || 0;
  const alteredCareType: string = isObject(careType) ? careType.name : careType;
  const result = await fetchNearbyProviders({
    careType: alteredCareType,
    hitsPerPage: 5,
    page: 0,
    latitude: lat,
    longitude: lng,
    domain: site.path,
    enhancedListing: true
  });

  return { nearbyProviders: result };
};
