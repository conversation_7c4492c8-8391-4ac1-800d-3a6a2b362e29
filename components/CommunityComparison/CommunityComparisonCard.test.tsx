import { fireEvent, render } from '@testing-library/react';
import React from 'react';

import { useModalControls } from '~/contexts/ModalContext';
import { Provider } from '~/contexts/Provider';

import CommunityComparisonCard from './CommunityComparisonCard';

jest.mock('~/contexts/ModalContext');

describe('CommunityComparisonCard', () => {
  const mockShowInquiryForm = jest.fn();

  const defaultProps = {
    id: '1',
    images: [],
    title: 'Provider Name',
    price: 1000,
    listId: 'list-1',
    address: 'Provider Address',
    queryId: 'query-1',
    provider: {} as Provider,
    modalId: 'modal',
    displayRequestInfoButton: true,
    displayBadges: false,
    currentProvider: true,
    displayViewCommunityButton: true,
    displayCurrentProvider: true
  };

  beforeEach(() => {
    (useModalControls as jest.Mock).mockReturnValue({
      show: mockShowInquiryForm
    });
  });

  it('should call showInquiryForm when displayRequestInfoButton is clicked', () => {
    window.tracking = {
      track: jest.fn()
    };
    const { getByTestId } = render(
      <CommunityComparisonCard {...defaultProps} />
    );
    if (defaultProps.displayRequestInfoButton) {
      const requestInfoButton = getByTestId('compare-request-info-btn');
      fireEvent.click(requestInfoButton);
      expect(mockShowInquiryForm).toHaveBeenCalled();
    } else {
      expect(() => getByTestId('compare-request-info-btn')).toThrow();
    }
  });

  describe('hasCaringStars', () => {
    const baseProvider = {
      averageRating: 0,
      description: '',
      id: 'test-id',
      name: 'Test Provider',
      phoneNumber: '',
      reviewCount: 0,
      slug: 'test-slug'
    };

    it('should return true when provider has caringStars', () => {
      const props = {
        ...defaultProps,
        displayBadges: true,
        provider: {
          ...baseProvider,
          caringStars: [{ year: 2025, name: 'Caring Star' }],
          awards: []
        } as Provider
      };

      const { getByAltText } = render(<CommunityComparisonCard {...props} />);
      expect(getByAltText('Top rated on Caring.com')).toBeInTheDocument();
    });

    it('should return true when provider has Caring Stars award', () => {
      const props = {
        ...defaultProps,
        displayBadges: true,
        provider: {
          ...baseProvider,
          caringStars: [],
          awards: [{ year: 2025, name: 'Caring Stars' }]
        } as Provider
      };

      const { getByAltText } = render(<CommunityComparisonCard {...props} />);
      expect(getByAltText('Top rated on Caring.com')).toBeInTheDocument();
    });

    it('should return false when provider has no caringStars or Caring Stars awards', () => {
      const props = {
        ...defaultProps,
        displayBadges: true,
        provider: {
          ...baseProvider,
          caringStars: [],
          awards: [{ year: 2025, name: 'Home Care Pulse Certified' }]
        } as Provider
      };

      const { queryByAltText } = render(<CommunityComparisonCard {...props} />);
      expect(queryByAltText('Top rated on Caring.com')).not.toBeInTheDocument();
    });

    it('should return false when provider has undefined awards and caringStars', () => {
      const props = {
        ...defaultProps,
        displayBadges: true,
        provider: {
          ...baseProvider,
          caringStars: undefined,
          awards: undefined
        } as Provider
      };

      const { queryByAltText } = render(<CommunityComparisonCard {...props} />);
      expect(queryByAltText('Top rated on Caring.com')).not.toBeInTheDocument();
    });
  });

  describe('showVerifiedBadge', () => {
    const baseProvider = {
      averageRating: 0,
      description: '',
      id: 'test-id',
      name: 'Test Provider',
      phoneNumber: '',
      reviewCount: 0,
      slug: 'test-slug'
    };

    it('should display verified badge when showVerifiedBadge is true', () => {
      const props = {
        ...defaultProps,
        showVerifiedBadge: true,
        provider: baseProvider as Provider
      };

      const { getByTestId } = render(<CommunityComparisonCard {...props} />);
      expect(
        getByTestId('community-comparison-verified-badge')
      ).toBeInTheDocument();
    });

    it('should not display verified badge when showVerifiedBadge is false', () => {
      const props = {
        ...defaultProps,
        showVerifiedBadge: false,
        provider: baseProvider as Provider
      };

      const { queryByTestId } = render(<CommunityComparisonCard {...props} />);
      expect(
        queryByTestId('community-comparison-verified-badge')
      ).not.toBeInTheDocument();
    });

    it('should not display verified badge when showVerifiedBadge is undefined (default)', () => {
      const props = {
        ...defaultProps,
        provider: baseProvider as Provider
      };

      const { queryByTestId } = render(<CommunityComparisonCard {...props} />);
      expect(
        queryByTestId('community-comparison-verified-badge')
      ).not.toBeInTheDocument();
    });
  });
});
