import { fireEvent, render, screen, waitFor } from '~/utils/test-utils';

import CaringStarsMultiYearBadge from './CaringStarsMultiYearBadge';

describe('CaringStarsMultiYearBadge', () => {
  const analyticsMock = {
    track: jest.fn()
  };
  beforeEach(() => {
    window.tracking = analyticsMock;
  });
  const years = [
    { year: 2021, name: 'Caring Star' },
    { year: 2020, name: 'Caring Star' },
    { year: 2019, name: 'Caring Star' }
  ];
  const moreYears = years.concat([
    { year: 2018, name: 'Caring Star' },
    { year: 2016, name: 'Caring Star' }
  ]);

  it('renders the component', () => {
    render(<CaringStarsMultiYearBadge years={years} />);
    const image = screen.getByAltText('Top rated on Caring.com');
    const text = screen.getByText('2019, 2020, 2021');
    expect(image).toBeInTheDocument();
    expect(text).toBeInTheDocument();
  });

  test('renders a link to "/bestseniorliving/"', () => {
    render(<CaringStarsMultiYearBadge years={years} />);
    expect(screen.getByRole('link')).toHaveAttribute(
      'href',
      `/bestseniorliving/`
    );
  });

  it('displays the 3 most recent years, and a "View More" button', () => {
    render(<CaringStarsMultiYearBadge years={moreYears} />);
    const text = screen.getByText('2019, 2020, 2021');
    const button = screen.getByText('View More');
    const hiddenYear = screen.queryByText(/2018/i);
    const hiddenYear2 = screen.queryByText(/2016/i);

    expect(text).toBeInTheDocument();
    expect(button).toBeInTheDocument();
    expect(hiddenYear).not.toBeInTheDocument();
    expect(hiddenYear2).not.toBeInTheDocument();
  });

  it('displays all years when "View More" button is clicked', async () => {
    render(<CaringStarsMultiYearBadge years={moreYears} />);
    const button = screen.getByText('View More');
    fireEvent.click(button);
    await waitFor(() => {
      expect(
        screen.getByText('2016, 2018, 2019, 2020, 2021')
      ).toBeInTheDocument();
      expect(screen.getByText('View Less')).toBeInTheDocument();
    });
  });
});
