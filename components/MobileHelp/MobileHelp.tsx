import { Box, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import StoryImage from '@components/Image/StoryImage';
import Container from '@components/LayoutStructure/Container';

import { MagnoliaImage } from '~/types/Magnolia';

interface Props {
  productImage: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  title?: string;
  buttonText?: string;
  url?: string;
  withContainer?: boolean;
}

const MobileHelp = ({
  productImage,
  title,
  buttonText,
  url,
  withContainer = true
}: Props) => {
  const card = () => {
    return (
      <Box
        border="1px solid"
        borderColor="gray.200"
        borderRadius={'md'}
        padding={4}
        display="flex"
        flexDirection={{ base: 'column', sm: 'row' }}
        justifyContent="space-evenly"
        alignItems="center"
        gap={{ base: 8, sm: 12 }}
      >
        {productImage && (
          <Box width={{ base: '294px', sm: '235px' }}>
            <StoryImage
              switchable={productImage}
              displayAsBackground={false}
              desktopHeight="265px"
              mobileHeight="237px"
              backgroundSize="contain"
              withContainer={false}
              containerMarginBottom="0px"
            />
          </Box>
        )}
        <Box display="flex" flexDirection="column" alignItems="center">
          {title && (
            <Text
              as="h2"
              fontSize={'xl'}
              lineHeight={'6'}
              fontWeight={700}
              mb={2}
              color="primary.900"
              textAlign="center"
            >
              {title}
            </Text>
          )}
          {(buttonText || url) && (
            <Button
              as={'a'}
              href={url}
              mt={8}
              bgColor={'secondary.500'}
              color="white"
              fontWeight={700}
              elementAction={ElementActions.INTERNAL_LINK}
              elementName={ElementNames.PRODUCT_REVIEW_CARD}
              elementType={ElementTypes.BUTTON}
              destinationUrl={url}
            >
              {buttonText}
            </Button>
          )}
        </Box>
      </Box>
    );
  };

  return withContainer ? <Container>{card()}</Container> : card();
};

export default MobileHelp;
