interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  style?: React.CSSProperties | undefined;
  onClick?: React.MouseEventHandler<any> | undefined;
}

const CloudFrontImage = ({
  src,
  alt,
  width = 900,
  height = 600,
  style,
  onClick,
  ...rest
}: ImageProps): React.ReactElement => {
  if (!src) {
    return <></>;
  }

  const lastIndex = src.lastIndexOf('/');
  const imagePath = src.slice(0, lastIndex);
  const filename = src.slice(lastIndex);

  let newSrc = src + '/' + width + 'x' + height;

  if (filename.includes('.')) {
    newSrc =
      imagePath + '/' + width + 'x' + height + '.' + src.split('.').pop() ?? '';
  }

  /* eslint-disable @next/next/no-img-element */
  return (
    <img
      src={newSrc}
      alt={alt}
      width={width}
      height={height}
      style={style ? style : undefined}
      onClick={onClick ? onClick : () => {}}
      {...rest}
    />
  );
};

export default CloudFrontImage;
