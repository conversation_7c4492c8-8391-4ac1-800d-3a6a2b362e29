import { render, screen } from '@utils/test-utils';
import { IMAGE_FIXTURE_CLOUDINARY_SOURCE } from '@utils/test-utils';

import CloudinaryImage from './CloudFrontImage';

/* eslint-disable @next/next/no-img-element */
describe('CloudinaryImage', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should not render component when invalid source is provided', async () => {
    const { container } = render(
      /* eslint-disable @next/next/no-img-element */
      <CloudinaryImage
        src={''}
        alt={IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDINARY_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDINARY_SOURCE.height}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render a Image', async () => {
    const { container } = render(
      /* eslint-disable @next/next/no-img-element */
      <CloudinaryImage
        src={IMAGE_FIXTURE_CLOUDINARY_SOURCE.src}
        alt={IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDINARY_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDINARY_SOURCE.height}
      />
    );

    const img = screen.getByAltText(IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt);
  });
});
