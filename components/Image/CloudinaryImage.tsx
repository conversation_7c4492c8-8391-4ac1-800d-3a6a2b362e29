interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean | undefined;
  style?: React.CSSProperties | undefined;
  onClick?: React.MouseEventHandler<any> | undefined;
}

const CloudinaryImage = ({
  src,
  alt,
  width = 900,
  height = 600,
  fill,
  style,
  onClick,
  ...rest
}: ImageProps): React.ReactElement => {
  if (!src) {
    return <></>;
  }

  const pathParts = src.split('/');
  const uploadIndex = pathParts.indexOf('upload');
  let newSrc = '';

  if (uploadIndex !== -1) {
    const transformation = `c_fill,w_${width},h_${height},q_70`;
    pathParts.splice(uploadIndex + 1, 0, transformation);
    newSrc = pathParts.join('/');
  }
  /* eslint-disable @next/next/no-img-element */
  return (
    <img
      src={newSrc.length > 0 ? newSrc : src}
      alt={alt}
      width={width}
      height={height}
      style={style ? style : undefined}
      onClick={onClick ? onClick : () => {}}
      {...rest}
    />
  );
};

export default CloudinaryImage;
