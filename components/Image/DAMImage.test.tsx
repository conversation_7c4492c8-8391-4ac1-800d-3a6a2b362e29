import { DAM_IMAGE_FIXTURE, render, screen } from '@utils/test-utils';
import cloneDeep from 'lodash/cloneDeep';

import { MagnoliaImage } from '~/types/Magnolia';

import DAMImage from './DAMImage';

describe('DAMImage', () => {
  it('should render component when source is provided', async () => {
    const { container } = render(<DAMImage src={DAM_IMAGE_FIXTURE} />);
    expect(screen.getByAltText(DAM_IMAGE_FIXTURE.metadata.caption));
    expect(container.firstChild).toHaveAttribute('src');
    expect(container.firstChild).toHaveAttribute('alt');
  });

  it('should not render when a valid source is not provided', async () => {
    const { container } = render(<DAMImage src={{} as MagnoliaImage} />);
    expect(container.firstChild).not.toHaveAttribute('src');
    expect(container.firstChild).not.toHaveAttribute('alt');
  });

  it('should not render when a valid path is not valid', async () => {
    const source = cloneDeep(DAM_IMAGE_FIXTURE);
    source['@path'] = undefined as any;
    const { container } = render(<DAMImage src={source as MagnoliaImage} />);
    expect(container.firstChild).toBeEmptyDOMElement();
    expect(container.firstChild).not.toHaveAttribute('src');
    expect(container.firstChild).not.toHaveAttribute('alt');
  });
});
