import { render, screen } from '@utils/test-utils';
export * from '@testing-library/react';
import {
  IMAGE_FIXTURE_CLOUDFRONT_SOURCE,
  IMAGE_FIXTURE_CLOUDINARY_SOURCE,
  IMAGE_FIXTURE_OTHER_SOURCE
} from '@utils/test-utils';

import WhichImage from './WhichImage';
import { WhichImageType } from './WhichImage';

/* eslint-disable @next/next/no-img-element */
describe('WhichImage', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should not render component when invalid source is provided', async () => {
    const { container } = render(
      <WhichImage
        path={''}
        title={IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDINARY_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDINARY_SOURCE.height}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('Should render a next/image tag', async () => {
    // This will change once we allow for Cloudinary images
    const { container } = render(
      <WhichImage
        path={IMAGE_FIXTURE_CLOUDINARY_SOURCE.src}
        title={IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDINARY_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDINARY_SOURCE.height}
      />
    );

    const img = screen.getByAltText(IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_CLOUDINARY_SOURCE.alt);
    expect(img.getAttribute('data-type')).toBe(WhichImageType.CLOUDINARYIMAGE);
  });

  it('Should render a img tag with a CloudFrontImage path', async () => {
    const { container } = render(
      <WhichImage
        path={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.src}
        title={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.height}
      />
    );
    const img = screen.getByAltText(IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt);

    expect(img.getAttribute('data-type')).toBe(WhichImageType.CLOUDFRONTIMAGE);
  });

  it('Should render a image src with NextImage', async () => {
    const { container } = render(
      <WhichImage
        path={IMAGE_FIXTURE_OTHER_SOURCE.src}
        title={IMAGE_FIXTURE_OTHER_SOURCE.alt}
        width={IMAGE_FIXTURE_OTHER_SOURCE.width}
        height={IMAGE_FIXTURE_OTHER_SOURCE.height}
      />
    );
    const img = screen.getByAltText(IMAGE_FIXTURE_OTHER_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_OTHER_SOURCE.alt);

    expect(img.getAttribute('data-type')).toBe(WhichImageType.NEXTIMAGE);
  });

  it('Should render a static image with NextImage', async () => {
    const path = {
      blurDataURL:
        '/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fplaceholder_2.f408af6d.jpg&w=8&q=70',
      blurHeight: 5,
      blurWidth: 8,
      height: IMAGE_FIXTURE_OTHER_SOURCE.height,
      src: '/_next/static/media/placeholder_2.f408af6d.jpg',
      width: IMAGE_FIXTURE_OTHER_SOURCE.width
    };
    const { container } = render(
      <WhichImage path={path} title={IMAGE_FIXTURE_OTHER_SOURCE.alt} />
    );
    const img = screen.getByAltText(IMAGE_FIXTURE_OTHER_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_OTHER_SOURCE.alt);
    expect(img.getAttribute('data-type')).toBe(WhichImageType.NEXTIMAGE);
    expect(img.getAttribute('width')).toBe(
      IMAGE_FIXTURE_OTHER_SOURCE.width.toString()
    );
  });
});
