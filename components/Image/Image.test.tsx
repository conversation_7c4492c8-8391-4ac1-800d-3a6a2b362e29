/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
import { render, screen } from '@utils/test-utils';
import {
  IMAGE_FIXTURE_DAM_CHOOSER,
  IMAGE_FIXTURE_EXTERNAL_SOURCE,
  IMAGE_FIXTURE_INVALID_SOURCE
} from '@utils/test-utils';
import cloneDeep from 'lodash/cloneDeep';

import Image from './Image';

describe('Image', () => {
  beforeEach(() => {
    jest.mock('next/image', () => ({
      __esModule: true,
      default: (props: any) => {
        return <img {...props} />;
      }
    }));

    // Avoid logging error messages on tests that are supposed to not render component
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should not render component when invalid source is provided', async () => {
    const { container } = render(
      <Image
        switchable={IMAGE_FIXTURE_INVALID_SOURCE.switchable}
        breakpoints={IMAGE_FIXTURE_INVALID_SOURCE.breakpoints}
        preloadImage={IMAGE_FIXTURE_INVALID_SOURCE.preloadImage}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render component if has not valid image params', async () => {
    const fixtureCopy = cloneDeep(IMAGE_FIXTURE_INVALID_SOURCE);
    fixtureCopy.switchable.image = null as any;
    fixtureCopy.switchable.imageUrl = null as any;
    fixtureCopy.switchable.imageAlt = '' as any;

    const { container } = render(
      <Image
        switchable={fixtureCopy.switchable}
        breakpoints={fixtureCopy.breakpoints}
        preloadImage={fixtureCopy.preloadImage}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render if invalid host is found ', async () => {
    const fixtureCopy = cloneDeep(IMAGE_FIXTURE_EXTERNAL_SOURCE);
    fixtureCopy.switchable.imageUrl = '/test';
    const { container } = render(
      <Image
        switchable={fixtureCopy.switchable}
        breakpoints={fixtureCopy.breakpoints}
        preloadImage={fixtureCopy.preloadImage}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render a DAMImage', async () => {
    const { container } = render(
      <Image
        switchable={IMAGE_FIXTURE_DAM_CHOOSER.switchable}
        breakpoints={IMAGE_FIXTURE_DAM_CHOOSER.breakpoints}
        preloadImage={IMAGE_FIXTURE_DAM_CHOOSER.preloadImage}
      />
    );

    expect(
      screen.getByAltText(
        IMAGE_FIXTURE_DAM_CHOOSER.switchable.image.metadata.caption
      )
    );
    expect(container.firstChild).not.toBeEmptyDOMElement();
  });

  it('should render an External source image', () => {
    render(
      <Image
        switchable={IMAGE_FIXTURE_EXTERNAL_SOURCE.switchable}
        breakpoints={IMAGE_FIXTURE_EXTERNAL_SOURCE.breakpoints}
        preloadImage={IMAGE_FIXTURE_EXTERNAL_SOURCE.preloadImage}
      />
    );

    const img = screen.getByAltText(
      IMAGE_FIXTURE_EXTERNAL_SOURCE.switchable.imageAlt
    );
    expect(img.getAttribute('alt')).toBe(
      IMAGE_FIXTURE_EXTERNAL_SOURCE.switchable.imageAlt
    );
  });

  it('should render an anchor if url provided', () => {
    render(
      <Image
        switchable={IMAGE_FIXTURE_DAM_CHOOSER.switchable}
        breakpoints={IMAGE_FIXTURE_DAM_CHOOSER.breakpoints}
        preloadImage={IMAGE_FIXTURE_DAM_CHOOSER.preloadImage}
        linkProperties={{
          url: '+144444444',
          rel: ['external'],
          target: '_blank',
          type: 'tel'
        }}
      />
    );

    const img = screen.getByAltText(
      IMAGE_FIXTURE_DAM_CHOOSER.switchable.image.metadata.caption
    );
    const parentElement = img.parentElement;
    expect(parentElement?.tagName).toBe('A');
    expect(parentElement?.getAttribute('href')).toBe('tel:+144444444');
    expect(parentElement?.getAttribute('rel')).toBe('external');
    expect(parentElement?.getAttribute('target')).toBe('_blank');
  });

  it('should render a div if url not provided', () => {
    render(
      <Image
        switchable={IMAGE_FIXTURE_DAM_CHOOSER.switchable}
        breakpoints={IMAGE_FIXTURE_DAM_CHOOSER.breakpoints}
        preloadImage={IMAGE_FIXTURE_DAM_CHOOSER.preloadImage}
        linkProperties={{
          rel: ['external'],
          target: '_blank',
          type: 'tel'
        }}
      />
    );

    const img = screen.getByAltText(
      IMAGE_FIXTURE_DAM_CHOOSER.switchable.image.metadata.caption
    );
    const parentElement = img.parentElement;
    expect(parentElement?.tagName).toBe('DIV');
  });
});
