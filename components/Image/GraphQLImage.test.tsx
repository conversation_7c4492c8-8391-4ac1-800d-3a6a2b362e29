import { render } from '@testing-library/react';

import GraphQLImage from './GraphQLImage';

describe('GraphQLImage', () => {
  const env = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...env };
  });

  afterEach(() => {
    process.env = env;
  });

  it('should render the image with the correct src', () => {
    process.env.NEXT_PUBLIC_MGNL_HOST = 'https://prod.dxp.caring.com';
    const image = {
      link: '/dam/jcr:e679f409-ec55-4ade-8a74-a96cd006ae2c/caring-desktop.png',
      caption: ''
    };
    const expectedSrc =
      'https://prod.dxp.caring.com/dam/jcr:e679f409-ec55-4ade-8a74-a96cd006ae2c/caring-desktop.png';
    const { container } = render(
      <GraphQLImage src={image} alt="" height={40} width={40} unoptimized />
    );
    expect(container.firstChild).toHaveAttribute('src', expectedSrc);
  });

  it('should render on the author preview page', () => {
    process.env.NEXT_PUBLIC_MGNL_HOST = 'https://prod.dxp.caring.com/author';
    const image = {
      link: '/author/dam/jcr:e679f409-ec55-4ade-8a74-a96cd006ae2c/caring-desktop.png',
      caption: ''
    };
    const expectedSrc =
      'https://prod.dxp.caring.com/author/dam/jcr:e679f409-ec55-4ade-8a74-a96cd006ae2c/caring-desktop.png';
    const { container } = render(
      <GraphQLImage src={image} alt="" height={40} width={40} unoptimized />
    );
    expect(container.firstChild).toHaveAttribute('src', expectedSrc);
  });
});
