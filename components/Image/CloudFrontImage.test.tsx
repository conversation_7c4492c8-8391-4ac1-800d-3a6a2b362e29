import { render, screen } from '@utils/test-utils';
import { IMAGE_FIXTURE_CLOUDFRONT_SOURCE } from '@utils/test-utils';

import CloudFrontImage from './CloudFrontImage';

/* eslint-disable @next/next/no-img-element */
describe('CloudFrontImage', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should not render component when invalid source is provided', async () => {
    const { container } = render(
      <CloudFrontImage
        src={''}
        alt={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.height}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render a Image', async () => {
    const { container } = render(
      <CloudFrontImage
        src={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.src}
        alt={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt}
        width={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.width}
        height={IMAGE_FIXTURE_CLOUDFRONT_SOURCE.height}
      />
    );

    const img = screen.getByAltText(IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt);
    expect(img.getAttribute('alt')).toBe(IMAGE_FIXTURE_CLOUDFRONT_SOURCE.alt);
  });
});
