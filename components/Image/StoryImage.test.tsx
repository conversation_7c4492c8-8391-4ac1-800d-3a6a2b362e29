import { render } from '@utils/test-utils';

import StoryImage from './StoryImage';

describe('StoryImage', () => {
  beforeEach(() => {
    process.env.NEXT_PUBLIC_MGNL_HOST = 'https://prod.dxp.caring.com';
  });

  describe('URL encoding for external sources', () => {
    const getUrlFromNextImageSrc = (src: string) => {
      const match = src.match(/url=([^&]+)/);
      if (!match) return '';
      return decodeURIComponent(decodeURIComponent(match[1]));
    };

    const normalizedUrl = (url: string) => {
      return url
        .replace(/%20/g, ' ')
        .replace(/%E2%80%94/g, '—')
        .replace(/%27/g, "'");
    };

    it('should properly encode URLs with special characters', () => {
      const complexUrl =
        "https://prod.dxp.caring.com/author/dam/test/Medical Conditions That Can Mimic Dementia — But Aren't.jpg?v=123";
      const { container } = render(
        <StoryImage
          switchable={{
            field: 'externalSource',
            imageUrl: complexUrl,
            imageAlt: 'Test image'
          }}
          displayAsBackground={false}
        />
      );

      const img = container.querySelector('img');
      const nextImageSrc = img?.getAttribute('src') || '';
      expect(nextImageSrc).toMatch(/^\/_next\/image\?/);

      const encodedUrl = getUrlFromNextImageSrc(nextImageSrc);
      expect(normalizedUrl(encodedUrl)).toBe(normalizedUrl(complexUrl));
    });

    it('should handle URLs without special characters', () => {
      const simpleUrl =
        'https://dlyhjlf6lts50.cloudfront.net/app/uploads/2022/04/hero-Caring_emergency-housing-guide.jpg';
      const { container } = render(
        <StoryImage
          switchable={{
            field: 'externalSource',
            imageUrl: simpleUrl,
            imageAlt: 'Test image'
          }}
          displayAsBackground={false}
        />
      );

      const img = container.querySelector('img');
      const nextImageSrc = img?.getAttribute('src') || '';
      expect(nextImageSrc).toMatch(/^\/_next\/image\?/);

      const encodedUrl = getUrlFromNextImageSrc(nextImageSrc);
      expect(normalizedUrl(encodedUrl)).toBe(normalizedUrl(simpleUrl));
    });

    it('should preserve query parameters', () => {
      const urlWithQuery = 'https://example.com/image.jpg?width=100&height=200';
      const { container } = render(
        <StoryImage
          switchable={{
            field: 'externalSource',
            imageUrl: urlWithQuery,
            imageAlt: 'Test image'
          }}
          displayAsBackground={false}
        />
      );

      const img = container.querySelector('img');
      const nextImageSrc = img?.getAttribute('src') || '';
      expect(nextImageSrc).toMatch(/^\/_next\/image\?/);

      const encodedUrl = getUrlFromNextImageSrc(nextImageSrc);
      expect(normalizedUrl(encodedUrl)).toBe(normalizedUrl(urlWithQuery));
    });
  });
});
