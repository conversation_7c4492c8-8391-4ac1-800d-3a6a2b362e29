import { render, screen } from '@testing-library/react';

import axe from '~/axe-helper';

import HighlightBlock from './HighlightBlock';

const mockData = {
  subText: 'Some small title',
  mainText: 'Some text describing how awsome things',
  author: 'Author',
  authorRelationship: 'authorRelationship'
};

describe('HighlightBlock', () => {
  it('renders without violation', async () => {
    const { container } = render(<HighlightBlock {...mockData} />);

    expect(screen.getByText(mockData.subText)).toBeVisible();
    expect(screen.getByText(mockData.mainText)).toBeVisible();
    expect(screen.getByText(mockData.author)).toBeVisible();
    expect(screen.getByText(mockData.authorRelationship)).toBeVisible();
    expect(await axe(container)).toHaveNoViolations();
  });
});
