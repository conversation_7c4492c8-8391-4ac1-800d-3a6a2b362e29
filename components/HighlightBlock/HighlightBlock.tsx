import { Card, CardBody, Text } from '@chakra-ui/react';
import Container from '@components/LayoutStructure/Container';

interface Props {
  subText?: string;
  mainText: string;
  author?: string;
  authorRelationship?: string;
}

const HighlightBlock = ({
  subText,
  mainText,
  author,
  authorRelationship
}: Props) => {
  return (
    <Container>
      <Card background="gray.50" boxShadow="lg" rounded="xl">
        <CardBody
          display="flex"
          flexDirection="column"
          alignItems="center"
          textAlign="center"
          fontSize="md"
          lineHeight="19.2px"
          color="primary.900"
          fontWeight="700"
        >
          {subText && <Text mb={4}>{subText}</Text>}
          <Text fontSize="3xl" lineHeight="36px" color="primary.700" mb={4}>
            {mainText}
          </Text>
          {author && <Text>{author}</Text>}
          {authorRelationship && (
            <Text fontWeight="400">{authorRelationship}</Text>
          )}
        </CardBody>
      </Card>
    </Container>
  );
};
export default HighlightBlock;
