import {
  fireEvent,
  mockCollapsibleSection,
  render,
  screen
} from '@utils/test-utils';

import CollapsibleSection from './CollapsibleSection';

describe('Collapsible Section', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  describe('Collapsible Section', () => {
    it('should render the full content when component is created', () => {
      render(<CollapsibleSection {...mockCollapsibleSection} />);
      expect(screen.getByText('View fewer details')).toBeInTheDocument;
    });

    it('should hide content and change the button label when "view less" button is clicked', async () => {
      window.HTMLElement.prototype.scrollIntoView = jest.fn();

      render(<CollapsibleSection {...mockCollapsibleSection} />);
      fireEvent.click(screen.getByText(/View fewer details/i));

      expect(await screen.findByText('View more details')).toBeVisible();
    });
  });
});
