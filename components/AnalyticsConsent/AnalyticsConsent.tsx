'use client';

import { Box } from '@chakra-ui/layout';
import {
  getCategories,
  getConsent,
  setConsent
} from '@components/Analytics/consent';
import Heading from '@components/Heading';
import HtmlToReact from '@components/HtmlToReact';
import Container from '@components/LayoutStructure/Container';
import { isEmptyText } from '@utils/isEmptyText';
import { useEffect, useState } from 'react';

import { useSessionContext } from '~/contexts/SessionContext';

import styles from './AnalyticsConsent.module.css';

interface Props {
  title: string;
  richTextBeforeForm: string;
  richTextAfterForm: string;
}

const AnalyticsConsentComponent: React.FC<Props> = ({
  title,
  richTextBeforeForm,
  richTextAfterForm
}: Props) => {
  const sessionContext = useSessionContext();
  useEffect(() => {
    setIsAdvertising(!getConsent().Advertising);
  }, []);

  const [isAdvertising, setIsAdvertising] = useState(
    !getCategories().Advertising
  );

  const handleToggleAdvertising = (e) => {
    const advertising = e.target.checked;
    setIsAdvertising(advertising);
    const defaultCategories = getCategories();
    setConsent(sessionContext, {
      Advertising: !advertising,
      Analytics: defaultCategories.Analytics,
      Functional: defaultCategories.Functional,
      DataSharing: defaultCategories.DataSharing
    });
  };

  const copyBefore = isEmptyText(richTextBeforeForm)
    ? null
    : HtmlToReact({ html: richTextBeforeForm });
  const copyAfter = isEmptyText(richTextAfterForm)
    ? null
    : HtmlToReact({ html: richTextAfterForm });

  return (
    <Container>
      {title && (
        <Heading
          headingElement="h1"
          headingSize="xl"
          mobileHeadingSize="xl"
          title={title}
          withContainer={false}
          textAlign="left"
        />
      )}
      <Box className="magnolia-text" fontSize="lg" mt="2">
        <Box className={styles.typography}>{copyBefore}</Box>
        <Box className={styles.analyticsConsentForm}>
          <p className={styles.typographyBold}>
            Opt Out of Targeting Cookies (including Social Media Cookies)
          </p>
          <p>
            <label>
              <input
                type="checkbox"
                checked={isAdvertising}
                onChange={handleToggleAdvertising}
              />
              <span>
                {isAdvertising
                  ? " Done. You've Rejected Cookies"
                  : ' Reject Cookies'}
              </span>
            </label>
          </p>
          <p className={styles.typography}>
            If you clear your cookies, use a different browser, or switch
            devices, you will need to submit a new opt-out request to ensure
            your Cookie preference is applied.
          </p>
        </Box>
        <Box className={styles.typography}>{copyAfter}</Box>
      </Box>
    </Container>
  );
};

export default AnalyticsConsentComponent;
