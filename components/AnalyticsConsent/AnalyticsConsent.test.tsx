import { ChakraProvider, theme } from '@chakra-ui/react';
import * as consentModule from '@components/Analytics/consent';
import * as HtmlToReactModule from '@components/HtmlToReact';
import { render, screen } from '@testing-library/react';
import * as isEmptyTextModule from '@utils/isEmptyText';
import { setMobileScreen } from '@utils/test-utils/test-utils';

import AnalyticsConsentComponent from './AnalyticsConsent';

const mockUseBreakpointValue = jest.fn();
jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: (...args) => mockUseBreakpointValue(...args)
}));

jest.mock('@components/Analytics/consent');
jest.mock('@utils/isEmptyText');
jest.mock('@components/HtmlToReact');
jest.mock('./AnalyticsConsent.module.css', () => ({
  typography: 'typography',
  analyticsConsentForm: 'analyticsConsentForm',
  typographyBold: 'typographyBold'
}));

const customRender = (ui: React.ReactElement) => {
  return render(<ChakraProvider theme={theme}>{ui}</ChakraProvider>);
};

describe('AnalyticsConsentComponent', () => {
  const defaultProps = {
    title: 'Test Title',
    richTextBeforeForm: '<p>Before</p>',
    richTextAfterForm: '<p>After</p>'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (consentModule.getCategories as jest.Mock).mockReturnValue({
      Advertising: false,
      Analytics: true,
      Functional: true,
      DataSharing: false
    });
    (consentModule.getConsent as jest.Mock).mockReturnValue({
      Advertising: false
    });
    (consentModule.setConsent as jest.Mock).mockImplementation(() => {});
    (isEmptyTextModule.isEmptyText as jest.Mock).mockImplementation(
      (text) => !text
    );
    (HtmlToReactModule.default as jest.Mock).mockImplementation(({ html }) => (
      <div>{html}</div>
    ));
    setMobileScreen();
    mockUseBreakpointValue.mockReturnValue(true);
  });

  it('renders the title', () => {
    customRender(
      <AnalyticsConsentComponent
        title="Test Title"
        richTextBeforeForm="<p>Before</p>"
        richTextAfterForm="<p>After</p>"
      />
    );
    expect(
      screen.getByRole('heading', { name: /Test Title/i })
    ).toBeInTheDocument();
  });

  it('renders rich text before and after form', () => {
    customRender(<AnalyticsConsentComponent {...defaultProps} />);
    expect(screen.getAllByText('<p>Before</p>')[0]).toBeInTheDocument();
    expect(screen.getAllByText('<p>After</p>')[0]).toBeInTheDocument();
  });

  it('checkbox reflects Advertising category from getCategories', () => {
    (consentModule.getCategories as jest.Mock).mockReturnValueOnce({
      Advertising: false
    });
    customRender(<AnalyticsConsentComponent {...defaultProps} />);
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeChecked();
  });

  it('shows correct label text for checked and unchecked', () => {
    (consentModule.getCategories as jest.Mock).mockReturnValueOnce({
      Advertising: false
    });
    customRender(<AnalyticsConsentComponent {...defaultProps} />);
    expect(
      screen.getByText(/Done\. You\'ve Rejected Cookies/i)
    ).toBeInTheDocument();

    (consentModule.getCategories as jest.Mock).mockReturnValueOnce({
      Advertising: true
    });
  });

  it('renders explanatory text', () => {
    customRender(<AnalyticsConsentComponent {...defaultProps} />);
    expect(
      screen.getByText(/If you clear your cookies, use a different browser/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /Opt Out of Targeting Cookies \(including Social Media Cookies\)/i
      )
    ).toBeInTheDocument();
  });
});
