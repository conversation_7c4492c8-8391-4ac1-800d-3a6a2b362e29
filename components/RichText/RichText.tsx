import { Box } from '@chakra-ui/layout';
import HtmlToReact from '@components/HtmlToReact';
import { isEmptyText } from '@utils/isEmptyText';
import dynamic from 'next/dynamic';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
interface RichTextProps {
  richText?: string;
}

const RichText: React.FC<RichTextProps> = ({ richText }) => {
  const textIsEmpty = isEmptyText(richText);
  const parsedRichText =
    richText && !textIsEmpty ? HtmlToReact({ html: richText }) : null;
  if (parsedRichText === null) return null;

  return (
    <Container>
      <Box className="magnolia-text">{parsedRichText}</Box>
    </Container>
  );
};

export default RichText;
