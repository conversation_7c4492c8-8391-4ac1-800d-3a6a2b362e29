import CheckboxInput from '@components/CheckboxInput';

interface LanguagesFilterProps {
  languages?: string[];
  onChange(name: string, value: string[]): void;
  value: string[];
}

const defaultLanguages = [
  'Spanish',
  'French',
  'German',
  'Cantonese',
  'Farsi',
  'Hebrew',
  'Hindi',
  'Italian',
  'Korean',
  'Mandarin',
  'Russian',
  'Japanese',
  'Tagalog',
  'Vietnamese'
];

export const languageFilterItems = defaultLanguages.map((language) => ({
  value: language,
  label: language
}));

function LanguagesFilter({ onChange, value }: LanguagesFilterProps) {
  return (
    <CheckboxInput
      name="languages"
      onChange={onChange}
      maxItemsBeforeCollapse={3}
      items={languageFilterItems}
      value={value}
    />
  );
}

export default LanguagesFilter;
