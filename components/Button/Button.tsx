'use client';

import {
  But<PERSON> as <PERSON>kraButton,
  ButtonProps as ChakraButtonProps
} from '@chakra-ui/button';
import { forwardRef } from '@chakra-ui/system';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { StringToIconKeys } from '@components/RenderIcon';
import { getIcon } from '@utils/buttons';
import { filterNonReactAttributes } from '@utils/filterNonReactAttributes';

export interface ButtonProps
  extends Omit<ChakraButtonProps, 'leftIcon' | 'rightIcon'> {
  leftIcon?: StringToIconKeys | React.ReactElement;
  rightIcon?: StringToIconKeys | React.ReactElement;
  elementName?: ElementNames;
  elementType?: ElementTypes;
  elementAction: ElementActions;
  destinationUrl?: string;
  query?: {
    locationId: string;
    queryId: string;
    listId: string;
  };
}

const Button = forwardRef<ButtonProps, 'button'>((props, ref) => {
  const {
    children,
    leftIcon,
    rightIcon,
    elementName = ElementNames.GENERIC_BUTTON,
    elementType = ElementTypes.BUTTON,
    elementAction,
    destinationUrl = '',
    query,
    ...rest
  } = props;
  const elementClicked = useElementClicked();
  const leftIconComponent =
    typeof leftIcon === 'string' ? getIcon(leftIcon) : leftIcon;
  const rightIconComponent =
    typeof rightIcon === 'string' ? getIcon(rightIcon) : rightIcon;
  const color = rest.colorScheme;
  const textColor = rest.textColor || 'white';
  const filteredProps = filterNonReactAttributes(rest);
  return (
    <ChakraButton
      ref={ref}
      leftIcon={leftIconComponent}
      rightIcon={rightIconComponent}
      {...filteredProps}
      onClick={(event) => {
        requestAnimationFrame(() => {
          if (rest.onClick) {
            rest.onClick(event);
          }
        });

        elementClicked({
          element: {
            type: elementType,
            action: elementAction,
            name: elementName,
            text: children?.toString() ?? '',
            color: color ?? '',
            textColor: typeof textColor === 'string' ? textColor : ''
          },
          destinationUrl: destinationUrl,
          query: query
        });
      }}
    >
      {children}
    </ChakraButton>
  );
});

export default Button;
