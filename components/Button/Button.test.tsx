import { render, screen } from '@testing-library/react';
import React from 'react';

import Button, { ButtonProps } from './Button';

describe('Button', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  const clickFn = jest.fn();
  const mockBtn: ButtonProps = {
    children: 'Example Btn',
    colorScheme: 'primary',
    variant: 'outline',
    onClick: clickFn
  };
  it('renders button with text and solid state', () => {
    render(<Button {...mockBtn} state="solid" />);
    const buttonElement = screen.getByText(/Example Btn/i);
    expect(buttonElement).toBeInTheDocument();
    buttonElement.click();
    expect(clickFn).toHaveBeenCalled();
  });

  it('renders button with ghost state', () => {
    render(<Button {...mockBtn} state="ghost" />);
    const buttonElement = screen.getByText(/Example Btn/i);
    expect(buttonElement).toBeInTheDocument();
  });

  it('renders button with outline state', () => {
    render(<Button {...mockBtn} state="outline" />);
    const buttonElement = screen.getByText(/Example Btn/i);
    expect(buttonElement).toBeInTheDocument();
  });

  it('renders button with icon', () => {
    const { container } = render(
      <Button leftIcon="MdPhone" onClick={() => {}}>
        Call
      </Button>
    );
    const icons = container.getElementsByClassName('chakra-button__icon');
    expect(icons.length).toBe(1);
    const svg = icons[0].getElementsByTagName('svg');
    expect(svg[0]).toBeInTheDocument();
  });
});
