import { render } from '@testing-library/react';
import { act } from 'react';

import { DevHubSpotChatEntry } from './DevHubSpotChatEntry';

jest.mock('next/script', () => {
  return function MockScript({ id }: { id: string }) {
    return <div data-testid={id} />;
  };
});

describe('DevHubSpotChatEntry', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeAll(() => {
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('renders script in development environment', async () => {
    // @ts-expect-error
    process.env.NODE_ENV = 'development';
    let rendered;
    await act(async () => {
      rendered = render(<DevHubSpotChatEntry />);
    });
    expect(rendered.getByTestId('hs-script-loader')).toBeInTheDocument();
  });

  it('does not render script in production environment', async () => {
    // @ts-expect-error
    process.env.NODE_ENV = 'production';
    let rendered;
    await act(async () => {
      rendered = render(<DevHubSpotChatEntry />);
    });
    expect(rendered.container).toBeEmptyDOMElement();
  });
});
