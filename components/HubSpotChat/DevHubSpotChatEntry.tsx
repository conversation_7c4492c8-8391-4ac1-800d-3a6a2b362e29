'use client';

import Script from 'next/script';

/* 
  This component is used to load the HubSpot chat widget on dev environments.
  In deployed environments, we will load the HubSpot tracking script via Segment

  Clean-up ticket to re-evaluate this after enabling the script in staging: https://caring.atlassian.net/browse/CORE-1683
*/

export const DevHubSpotChatEntry = () => {
  const isDev = process.env.NODE_ENV === 'development';

  if (!isDev) {
    return null;
  }

  return (
    <Script
      id="hs-script-loader"
      type="text/javascript"
      // B2B HubSpot Chat Inject Link
      src="//js.hs-scripts.com/47780629.js"
      strategy="lazyOnload"
      async
      defer
    />
  );
};
