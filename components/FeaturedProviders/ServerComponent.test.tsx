import { mockProviderV2 } from '@utils/test-utils/mocks/provider';
import fetch from 'jest-fetch-mock';
import { GetServerSidePropsContext } from 'next';

import { SiteDefinition } from '~/contexts/SiteContext';

import { FeaturedProvidersProps } from './FeaturedProviders';
import { getServerSideComponentProps } from './ServerComponent';
describe('FeaturedProviders', () => {
  beforeEach(() => {
    fetch.resetMocks();
  });

  const context = {
    req: {
      headers: {
        host: 'host'
      }
    }
  } as GetServerSidePropsContext;

  const siteDefinition = {
    name: 'Caring.com',
    domains: ['caring.com'],
    path: 'caring.com',
    segmentWriteKey: process.env.NEXT_PUBLIC_SEGMENT_CARING_WRITE_KEY ?? '',
    segmentCdnURL: process.env.NEXT_PUBLIC_SEGMENT_CARING_CDN ?? '',
    partnerToken: process.env.NEXT_PUBLIC_CARING_PARTNER_TOKEN ?? '',
    publicFolder: 'caring_public',
    storyPaths: []
  } as SiteDefinition;

  const providers = {
    providers: {
      '@nodes': ['providers0', 'providers1', 'providers2', 'providers3'],
      providers0: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      },
      providers1: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      },
      providers2: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      },
      providers3: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      }
    }
  } as FeaturedProvidersProps;

  it('provider should contain the following attributes', async () => {
    fetch.mockResponse(JSON.stringify(mockProviderV2));

    jest.mock('~/contexts/SiteContext', () => ({
      findSiteForContext: jest.fn().mockReturnValue(siteDefinition)
    }));

    const result = await getServerSideComponentProps(providers, context);

    const provider = result.providerResults[0];

    expect(provider.id).toEqual('test-id');
    expect(provider.legacyId).toEqual('test-legacy-id');
    expect(provider.slug).toEqual('test-slug');
    expect(provider.name).toEqual('Test Provider');
    expect(provider.enhancedListing).toEqual(true);
    expect(provider.reviewCount).toEqual(0);
    expect(provider.photos[0].url).toEqual('/test-image-1.jpg');
    expect(provider.averageRating).toEqual(0);
    expect(provider.address.street).toEqual('test-street');
    expect(provider.address.city).toEqual('test-city');
    expect(provider.address.state).toEqual('test-state');
    expect(provider.address.zipCode).toEqual('test-zip-code');
  });

  it('should not contain the following attributes', async () => {
    fetch.mockResponse(JSON.stringify(mockProviderV2));

    jest.mock('~/contexts/SiteContext', () => ({
      findSiteForContext: jest.fn().mockReturnValue(siteDefinition)
    }));

    const result = await getServerSideComponentProps(providers, context);

    const provider = result.providerResults[0];

    expect(provider.address.number).toBeUndefined();
    expect(provider.address.district).toBeUndefined();
    expect(provider.address.county).toBeUndefined();
    expect(provider.address.country).toBeUndefined();
    expect(provider.address.latitude).toBeUndefined();
    expect(provider.address.longitude).toBeUndefined();
    expect(provider.address.formattedAddress).toBeUndefined();
    expect(provider.canonicalSlug).toBeUndefined();
    expect(provider.number).toBeUndefined();
    expect(provider.district).toBeUndefined();
    expect(provider.county).toBeUndefined();
    expect(provider.country).toBeUndefined();
    expect(provider.latitude).toBeUndefined();
    expect(provider.longitude).toBeUndefined();
    expect(provider.formattedAddress).toBeUndefined();
    expect(provider.accommodations).toBeUndefined();
    expect(provider.amenities).toBeUndefined();
    expect(provider.amenitiesLegacy).toBeUndefined();
    expect(provider.careType).toBeUndefined();
    expect(provider.careTypesDescription).toBeUndefined();
    expect(provider.category).toBeUndefined();
    expect(provider.description).toBeUndefined();
    expect(provider.faqs).toBeUndefined();
    expect(provider.hasActivePromotions).toBeUndefined();
    expect(provider.hasImages).toBeUndefined();
    expect(provider.hidePricing).toBeUndefined();
    expect(provider.homeCarePulse).toBeUndefined();
    expect(provider.caringStars).toBeUndefined();
    expect(provider.lastReviewSnippet).toBeUndefined();
    expect(provider.meta).toBeUndefined();
    expect(provider.tags).toBeUndefined();
    expect(provider.parametrizedRatings).toBeUndefined();
    expect(provider.maximumCost).toBeUndefined();
    expect(provider.minimumCost).toBeUndefined();
    expect(provider?.photos[1]?.url).toBeUndefined();
    expect(provider.phoneNumber).toBeUndefined();
    expect(provider.promotions).toBeUndefined();
    expect(provider.description).toBeUndefined();
    expect(provider.reviews).toBeUndefined();
    expect(provider.services).toBeUndefined();
    expect(provider.meta).toBeUndefined();
  });
});
