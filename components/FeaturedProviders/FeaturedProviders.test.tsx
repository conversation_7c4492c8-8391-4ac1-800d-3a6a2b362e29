import { render, screen } from '@utils/test-utils';
import { mockProvider } from '@utils/test-utils/mocks/provider';

import FeaturedProviders from './FeaturedProviders';
import { FetchResponse } from './FeaturedProviders';
describe('FeaturedProviders', () => {
  const mockData = {
    providerResults: [
      {
        ...mockProvider,
        id: 'provider0',
        name: 'Provider 0'
      },
      {
        ...mockProvider,
        id: 'provider1',
        name: 'Provider 1'
      },
      {
        ...mockProvider,
        id: 'provider2',
        name: 'Provider 2'
      }
    ]
  } as FetchResponse;
  const providerProps = {
    providers: {
      '@nodes': ['providers0', 'providers1', 'providers2'],
      providers0: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      },
      providers1: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      },
      providers2: {
        slug: '/senior-living/nevada/reno/atria-summit-ridge-89523'
      }
    }
  };
  it('renders the title and text', () => {
    render(
      <FeaturedProviders
        title="Featured Providers"
        headingElement="h2"
        text="Some text"
        data={mockData}
        {...providerProps}
      />
    );

    expect(screen.getByText('Featured Providers')).toBeInTheDocument();
    expect(screen.getByText('Some text')).toBeInTheDocument();
  });

  it('renders the provider results', () => {
    render(
      <FeaturedProviders
        title="Featured Providers"
        headingElement="h2"
        text="Some text"
        data={mockData}
        {...providerProps}
      />
    );
    expect(screen.getByText('Provider 0')).toBeInTheDocument();
    expect(screen.getByText('Provider 1')).toBeInTheDocument();
    expect(screen.getByText('Provider 2')).toBeInTheDocument();
  });

  it('expect FeaturedProviders component to not be in document', () => {
    const emptyResponse = {
      providerResults: []
    } as FetchResponse;
    render(
      <FeaturedProviders
        title="Featured Providers"
        headingElement="h2"
        text="Some text"
        data={emptyResponse}
        {...providerProps}
      />
    );
    expect(screen.queryByText('Featured Providers')).toBeNull();
    expect(screen.queryByText('Some text')).toBeNull();
  });
});
