import { getProviderByUrl } from '@services/providers/api';
import { findSiteForContext } from '@utils/domains';
import { removePropertyRecursive } from '@utils/parser/removePropertyRecursive';
import { GetServerSidePropsContext } from 'next';

import { calculateProviderMinPrice } from '~/utils/providers';

import { FeaturedProvider, FeaturedProvidersProps } from './FeaturedProviders';

const FILTER_PROPERTIES = [
  'number',
  'district',
  'county',
  'country',
  'latitude',
  'longitude',
  'formattedAddress',
  'canonicalSlug',
  'accommodations',
  'amenities',
  'amenitiesLegacy',
  'careType',
  'category',
  'description',
  'faqs',
  'hasImages',
  'hidePricing',
  'homeCarePulse',
  'caringStars',
  'lastReviewSnippet',
  'meta',
  'maximumCost',
  'minimumCost',
  'otherWebsiteDetails',
  'parametrizedRatings',
  'phoneNumber',
  'residentCapacity',
  'reviews',
  'services',
  'promotions',
  'starsBySection',
  'tags',
  'careTypesDescription',
  'hasActivePromotions',
  'websiteURL',
  'webSiteDetailsVariants'
];

export const getServerSideComponentProps = async (
  { providers }: FeaturedProvidersProps,
  context: GetServerSidePropsContext
): Promise<any> => {
  const site = findSiteForContext(context);

  const results = await Promise.allSettled(
    providers['@nodes'].map(async (provider) => {
      return getProviderByUrl(providers[provider].slug, site.path);
    })
  );

  const providerResults = results
    .map((provider) => {
      const retProvider =
        provider.status === 'fulfilled' && provider?.value?.statusCode !== 404
          ? provider.value
          : null;

      if (!retProvider) {
        return;
      }
      // Calculate the min price for the provider
      // This is used to display the min price in the provider card
      // This also removes the need to calculate the min price on the component side
      retProvider['minPrice'] = calculateProviderMinPrice({
        result: retProvider
      });
      if (retProvider?.photos) {
        // We only want to show one photo for each provider
        // reducing the number of photos to 1 for each provider also reduces the size of the response to the client.
        retProvider.photos = retProvider.photos.slice(0, 1);
        // Remove the id from the photo object as it is not needed on the client side
        delete retProvider.photos[0].id;
      }
      return retProvider as FeaturedProvider;
    })
    .filter((provider) => provider !== null);

  removePropertyRecursive(providerResults, FILTER_PROPERTIES);

  return {
    providerResults
  };
};
