'use client';

import { Flex } from '@chakra-ui/layout';
import Container from '@components/LayoutStructure/Container';
import { Section } from '@components/Sections';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { Color } from '@utils/getColor';

import { HeadingElements } from '~/@types/heading';
import { Award } from '~/contexts/Provider';
import { buildProviderUrlWithRollup } from '~/contexts/TenantFunctions/utils';

import SearchResult from '../Search/SearchResult';

export interface FeaturedProvider {
  id: string;
  localEntityId?: string;
  slug: string;
  enhancedListing?: boolean;
  averageRating: number;
  name: string;
  photos?: {
    url: string;
    id?: string;
  }[];
  address?: {
    street?: string;
    city: string;
    state: string;
    zipCode?: string;
  };
  reviewCount: number;
  awards?: Award[] | null;
  city?: {
    name: string;
    urlName: string;
  };
  state?: {
    name: string;
    code?: string;
    urlName: string;
  };
  rollupType?: {
    singularSeoName: string;
    urlName: string;
  };
  rollup?: {
    singularSeoName: string;
    urlName: string;
  };
  minPrice?: number;
}

export type FetchResponse = {
  providerResults: FeaturedProvider[];
};

export interface FeaturedProvidersProps {
  title: string;
  headingElement: HeadingElements;
  text: string;
  textAlignment?: 'left' | 'center' | 'right';
  textColor?: Color;
  data: FetchResponse;
  providers: {
    '@nodes': string[];
    providers0: { slug: string };
    providers1?: { slug: string };
    providers2?: { slug: string };
    providers3?: { slug: string };
    providers4?: { slug: string };
    providers5?: { slug: string };
    providers6?: { slug: string };
  };
  enableTrailingSlash?: boolean;
  providerTitleColor?: Color;
  displayBadges?: boolean;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  learnMoreButton?: string;
  inquiryId?: string;
  requestInfoButtonColorScheme?: string;
  learnMoreButtonColorScheme?: string;
  ratingStarsColor?: Color;
  dontOpenInNewTab?: boolean;
}

const FeaturedProviders = ({
  title,
  headingElement,
  text,
  textAlignment,
  textColor = { color: 'black', range: '500' },
  data,
  providerTitleColor = { color: 'primary', range: '800' },
  displayBadges = false,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton = false,
  requestInfoButtonText,
  learnMoreButton,
  inquiryId,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor = { color: 'primary', range: '800' },
  dontOpenInNewTab = false
}: FeaturedProvidersProps): JSX.Element => {
  const showPrice = useInquiryFormSubmitted();

  if (data.providerResults.length === 0) {
    return <></>;
  }

  const renderSearchResult = (result: FeaturedProvider) => {
    const path = buildProviderUrlWithRollup({
      rollupType: result?.rollupType?.urlName,
      state: result?.state?.urlName ?? '',
      city: result?.city?.urlName ?? '',
      slug: result.slug
    });

    const images = result?.photos?.map((photo) => photo.url);
    const formattedAddress = `${result.address?.street}, <br/> ${result.address?.city}, ${result.address?.state} ${result.address?.zipCode}`;
    return (
      <SearchResult
        key={result.id}
        id={result.id}
        legacyId={result.id}
        title={result.name}
        address={formattedAddress}
        images={images ?? []}
        averageRating={result.averageRating}
        reviewCount={result.reviewCount}
        price={result.minPrice || 0}
        showPrice={showPrice}
        element={'p'}
        path={path}
        caringStars={result?.awards || []}
        providerTitleColor={providerTitleColor.color}
        providerTitleColorRange={providerTitleColor.range}
        displayBadges={displayBadges}
        displayLearnMoreButton={displayLearnMoreButton}
        learnMoreButtonText={learnMoreButtonText}
        displayRequestInfoButton={displayRequestInfoButton}
        requestInfoButtonText={requestInfoButtonText}
        readMoreButton={learnMoreButton}
        modalId={inquiryId}
        requestInfoButtonColorScheme={requestInfoButtonColorScheme}
        learnMoreButtonColorScheme={learnMoreButtonColorScheme}
        ratingStarsColor={ratingStarsColor.color}
        ratingStarsColorRange={ratingStarsColor.range}
        showVerifiedBadge={result.enhancedListing}
        width={'100%'}
        dontOpenInNewTab={dontOpenInNewTab}
      />
    );
  };

  return (
    <Container>
      <Section
        headingElement={headingElement}
        richText={text}
        title={title}
        textAlignment={textAlignment}
        richTextColor={textColor.color}
        richTextColorRange={textColor.range}
        titleAlignment={textAlignment}
        titleColor={textColor.color}
        titleColorRange={textColor.range}
      />
      <Flex mt={8} gap={8} flexWrap="wrap" justifyContent="space-evenly">
        {data.providerResults?.map(renderSearchResult)}
      </Flex>
    </Container>
  );
};

export default FeaturedProviders;
