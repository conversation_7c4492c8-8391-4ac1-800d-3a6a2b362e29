import CheckboxInput from '@components/CheckboxInput';

interface HealthServiceFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const healthServicesFilterItems = [
  {
    value: 'medication',
    label: 'Medication Management'
  },
  {
    value: 'nurses',
    label: 'Nurses On-site'
  },
  {
    value: 'physical-therapy',
    label: 'Physical Therapy'
  }
];

function HealthServiceFilter({ onChange, value }: HealthServiceFilterProps) {
  return (
    <CheckboxInput
      name="healthServices"
      onChange={onChange}
      items={healthServicesFilterItems}
      value={value}
    />
  );
}

export default HealthServiceFilter;
