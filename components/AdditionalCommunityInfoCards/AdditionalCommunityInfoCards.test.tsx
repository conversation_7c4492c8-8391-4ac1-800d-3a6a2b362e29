import { useBreakpointValue } from '@chakra-ui/react';
import { render, screen } from '@testing-library/react';

import AdditionalCommunityInfoCardsComponent, {
  shouldRenderInfoCards
} from './AdditionalCommunityInfoCards';

jest.mock('@chakra-ui/react', () => ({
  ...jest.requireActual('@chakra-ui/react'),
  useBreakpointValue: jest.fn(),
  Icon: () => null,
  VStack: ({ children }) => <div>{children}</div>
}));

const mockItems = {
  '@nodes': ['items0', 'items1'],
  items0: {
    icon: 'PiLayoutLight',
    title: 'Floor Plans',
    iconColor: 'gray',
    iconColorRange: '800',
    cta: {
      text: 'View Details',
      bgColor: 'green',
      state: 'outline',
      url: 'https://modular-monolith-file-uploads.s3.amazonaws.com/provider-portal/floor-plans/2025-01-30-71e86638-49f4-4831-901f-5912155442cc-floorplan_0_GENERAL.pdf',
      bgColorRange: '900'
    },
    '@nodes': ['cta']
  },
  items1: {
    icon: 'GiForkKnifeSpoon',
    title: 'Sample Menu',
    iconColor: 'gray',
    iconColorRange: '800',
    cta: {
      text: 'View Details',
      bgColor: 'green',
      state: 'outline',
      url: 'https://modular-monolith-file-uploads.s3.amazonaws.com/provider-portal/location-menus/2025-01-09-ff1001c3-2a51-43e0-aa00-bc09dab8969b-menu_3.pdf',
      bgColorRange: '600'
    },
    '@nodes': ['cta']
  }
};

describe('shouldRenderInfoCards', () => {
  it('returns false when nodes array is empty', () => {
    expect(shouldRenderInfoCards([])).toBeFalsy();
  });

  it('returns true when nodes are valid', () => {
    expect(shouldRenderInfoCards(['node1'])).toBeTruthy();
  });
});

describe('AdditionalCommunityInfoCardsComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useBreakpointValue as jest.Mock).mockImplementation(() => false);

    Object.defineProperty(window, 'location', {
      value: {
        hash: ''
      },
      writable: true
    });
  });

  it('renders nothing when items are empty', () => {
    const { container } = render(
      <AdditionalCommunityInfoCardsComponent items={{ '@nodes': [] }} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders the component with default title when no title provided', () => {
    render(<AdditionalCommunityInfoCardsComponent items={mockItems} />);
    expect(
      screen.getByText('Additional Community Information')
    ).toBeInTheDocument();
  });

  it('renders the component with custom title', () => {
    const customTitle = 'Custom Title';
    render(
      <AdditionalCommunityInfoCardsComponent
        items={mockItems}
        title={customTitle}
      />
    );
    expect(screen.getByText(customTitle)).toBeInTheDocument();
  });

  it('renders all cards with correct content', () => {
    render(<AdditionalCommunityInfoCardsComponent items={mockItems} />);

    expect(screen.getByText('Floor Plans')).toBeInTheDocument();
    expect(screen.getByText('Sample Menu')).toBeInTheDocument();
    expect(screen.getAllByText('View Details')).toHaveLength(2);
  });

  it('filters out cards without CTA URLs', () => {
    const itemsWithEmptyUrl = {
      '@nodes': ['items0', 'items1'],
      items0: {
        ...mockItems.items0,
        cta: { ...mockItems.items0.cta, url: '' }
      },
      items1: mockItems.items1
    };

    render(<AdditionalCommunityInfoCardsComponent items={itemsWithEmptyUrl} />);

    expect(screen.queryByText('Floor Plans')).not.toBeInTheDocument();
    expect(screen.getByText('Sample Menu')).toBeInTheDocument();
  });

  it('scrolls to the correct element when hash is present', () => {
    const scrollIntoViewMock = jest.fn();
    Element.prototype.scrollIntoView = scrollIntoViewMock;

    Object.defineProperty(window, 'location', {
      value: {
        hash: '#provider-additional-info-box'
      },
      writable: true
    });

    render(<AdditionalCommunityInfoCardsComponent items={mockItems} />);

    expect(scrollIntoViewMock).toHaveBeenCalledWith({ behavior: 'smooth' });
  });
});
