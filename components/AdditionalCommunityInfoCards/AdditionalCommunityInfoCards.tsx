'use client';

import { Box, Flex, Text } from '@chakra-ui/layout';
import { Icon, VStack } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames
} from '@components/Analytics/events/ElementClicked';
import CTA from '@components/CTA';
import Heading from '@components/Heading';
import { StringToIconKeys } from '@components/RenderIcon';
import { STRING_TO_ICON_CLASS } from '@components/RenderIcon';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { Fragment } from 'react';
import { useEffect } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

export interface ItemsWithIcon extends Metadata {
  title: string;
  icon: StringToIconKeys;
  iconColor: StringToIconKeys;
  iconColorRange: StringToIconKeys;
}

interface Items extends Metadata {
  [key: string]: string | string[] | ItemsWithIcon | undefined | null | any;
}

interface AdditionalCommunityInfoCardsProps {
  templateId?: string;
  title?: string;
  headingElement?: HeadingElements;
  headingSize?: HeadingSizes;
  items?: Items;
}

export const shouldRenderInfoCards = (nodes: any[]) => {
  if (nodes.length === 0) {
    return false;
  }
  return true;
};

const AdditionalCommunityInfoCardsComponent: React.FC<
  AdditionalCommunityInfoCardsProps
> = ({ templateId, headingElement, headingSize, items, ...props }) => {
  useEffect(() => {
    const id =
      typeof window !== 'undefined' ? window.location.hash.substring(1) : '';
    if (id?.startsWith('provider-additional-info-box')) {
      document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  const nodes = (items?.['@nodes'] || [])
    .filter((node) => Boolean(items?.[node]?.cta?.url?.trim()))
    .map((node) => items?.[node]);
  const shouldRenderContent = shouldRenderInfoCards(nodes);

  if (!shouldRenderContent) {
    return null;
  }

  return (
    <Container p={8}>
      <Box id={'provider-additional-info-box'}></Box>
      <Heading
        headingElement={headingElement || 'h3'}
        headingSize={headingSize || 'lg'}
        title={props.title || 'Additional Community Information'}
        withContainer={false}
        paddingBottom="8"
      />
      <Box width="full">
        <Flex
          gap="2"
          width="full"
          flexWrap="wrap"
          justifyContent="flex-start"
          direction={{ base: 'column', lg: 'row' }}
        >
          {nodes.map((el, i) => (
            <Fragment key={i}>
              <VStack
                flex={{
                  base: '1',
                  lg: `0 0 calc(${100 / Math.min(nodes.length, 3)}% - 8px)`
                }}
                width="full"
                border="1px"
                borderColor="gray.400"
                borderRadius="6px"
                p={2.5}
                spacing={2}
                height="full"
                boxShadow="0px 2px 4px -1px rgba(0, 0, 0, 0.06), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)"
                transition="all ease 0.5s"
                _hover={{
                  transform: 'scale(1.02)',
                  boxShadow: '0px 0px 25px 0px rgba(0, 0, 0, 0.35)'
                }}
              >
                <Text
                  fontSize={16}
                  as="span"
                  fontWeight="700"
                  color="gray.700"
                  role="heading"
                  aria-label={el.title}
                  aria-level={4}
                >
                  {el.title}
                </Text>
                {el.icon && (
                  <Icon
                    w={50}
                    h={50}
                    color={`${el.iconColor}.${el.iconColorRange}`}
                    as={STRING_TO_ICON_CLASS[el.icon]}
                    aria-hidden="true"
                    role="img"
                    aria-label={`${el.title} icon`}
                  />
                )}
                <CTA
                  text={el?.cta?.text || 'View Details'}
                  state={el?.cta?.state}
                  bgColor={el?.cta?.bgColor}
                  actionBehavior={{
                    field: 'openLink',
                    url: el?.cta?.url,
                    behavior: '_blank',
                    rel: ['noopener', 'noreferrer']
                  }}
                  buttonWidth={{ lg: 'auto' }}
                  buttonProps={{
                    elementAction: ElementActions.EXTERNAL_LINK,
                    destinationUrl: el?.cta?.url
                  }}
                  trackingName={ElementNames.GENERIC_BUTTON}
                  metadata={el?.metadata}
                  useWidthOnMobile={true}
                  mb={2}
                />
              </VStack>
            </Fragment>
          ))}
        </Flex>
      </Box>
    </Container>
  );
};

export default AdditionalCommunityInfoCardsComponent;
