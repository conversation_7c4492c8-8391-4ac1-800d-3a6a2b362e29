import {
  formStep,
  FormStepSubmission
} from '@components/Analytics/events/eventContracts';
import { FormType } from '@components/Analytics/events/FormStepSubmission';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import * as GrowthBookReact from '@growthbook/growthbook-react';
import {
  act,
  fireEvent,
  getByLabelText,
  getByTestId,
  getByText,
  Matcher,
  render,
  screen,
  waitFor
} from '@utils/test-utils';
import { ReactElement } from 'react';

import segmentEvents from '~/config/segment-events';
import { SessionContext, SessionContextType } from '~/contexts/SessionContext';

import { MultiStepForm, MultiStepFormProps } from './MultiStepForm';
import { StepConfig } from './types';
import { BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE } from './variants/BudgetQuestionRadioConfig';

jest.mock('@growthbook/growthbook-react', () => ({
  useFeatureIsOn: jest.fn().mockReturnValue(false),
  GrowthBookProvider: ({ children }) => children,
  GrowthBook: jest.fn().mockImplementation(() => ({}))
}));

const mockUseFeatureIsOn = GrowthBookReact.useFeatureIsOn as jest.Mock;

let v4Counter = 0;
jest.mock('uuid', () => ({
  v4: () => {
    const newValue = v4Counter.toString();
    v4Counter++;
    return newValue;
  },
  v5: (value: string, namespace: string) => `v5(${value}, ${namespace})`
}));

describe('MultiStepForm', () => {
  beforeEach(() => {
    mockUseFeatureIsOn.mockReset();
    mockUseFeatureIsOn.mockReturnValue(false);
  });

  const errorOnSubmit = 'Please try again.';
  const errorOnOtherSteps =
    'Please review the other steps, there is an error on at least one of them.';
  const baseMultiStepFormProps: Omit<MultiStepFormProps, 'data'> = {
    errorMessages: {
      errorOnSubmit,
      validationError: errorOnOtherSteps
    },
    formType: {
      field: 'inquiry',
      type: CTAAction.REQUEST_INFO,
      display: Display.VERTICAL
    },
    metadata: {
      '@id': 'multi-step-form-id'
    },
    thankYou: {
      path: '/custom-thank-you-page'
    }
  };

  const textFormatting: StepConfig['headline']['textFormatting'] = {
    field: 'default'
  };
  const backgroundColor = {
    color: 'background',
    range: '50'
  };
  const entriesColor = {
    color: 'background',
    range: '400'
  };
  const errorColor = {
    color: 'error',
    range: '500'
  };

  const step1: StepConfig = {
    headline: {
      text: "Let's begin",
      textFormatting
    },
    subHeadline: {
      text: 'Get free pricing information',
      textFormatting
    },
    image: {
      switchable: {
        field: 'damChooser'
      }
    },
    form: {
      helpBackgroundColor: {
        color: 'background',
        range: '100'
      },
      headline:
        'Please provide some information about the place you are looking for assisted living',
      cta: {
        label: 'Next',
        formatting: textFormatting
      },
      backgroundColor,
      entriesColor,
      errorColor,
      fields: [
        {
          type: 'input',
          inputType: 'generic',
          label: 'Zip code or city:',
          name: 'zip_code_or_city',
          isRequired: true,
          isRequiredErrorMessage: 'Please provide a valid location'
        }
      ]
    }
  };

  const step2: StepConfig = {
    headline: {
      text: 'Almost there!',
      textFormatting
    },
    subHeadline: {
      text: 'This is the last step',
      textFormatting
    },
    form: {
      headline: 'Please provide some information about you',
      cta: {
        label: 'Save',
        formatting: textFormatting
      },
      backgroundColor,
      entriesColor,
      errorColor,
      helpBackgroundColor: {
        color: 'background',
        range: '100'
      },
      fields: [
        {
          type: 'input',
          inputType: 'generic',
          label: 'Full Name:',
          name: 'full_name',
          isRequired: false
        },
        {
          type: 'input',
          inputType: 'email',
          label: 'Email:',
          name: 'email',
          isRequired: true,
          isRequiredErrorMessage: 'Please provide a valid email'
        },
        {
          type: 'input',
          inputType: 'generic',
          label: 'Should not be submitted:',
          name: 'do_not_submit_this_entry',
          defaultValue: 'Do not submit me',
          doNotSubmit: true,
          isRequired: false
        }
      ]
    }
  };

  const sessionData: SessionContextType = {
    pageSessionId: 'mockedPageSessionId',
    sessionId: 'mockedSessionId'
  };

  const trackMock = jest.fn<void, [segmentEvents, Record<string, any>]>();

  const getApiMockedCall = () => getApiMockedCalls()[0];
  const getApiMockedCalls = () =>
    fetchMock.mock.calls.filter((call) =>
      call[0]?.toString().startsWith('/api/multi-step-form')
    );

  const getEventTrackingCalls = (eventName: segmentEvents) =>
    trackMock.mock.calls.filter((call) => call[0]?.toString() === eventName);

  const growthbook = new GrowthBookReact.GrowthBook({
    enabled: true,
    features: {
      SEM_BUDGET_QUESTION_RADIO: {
        defaultValue: false
      }
    }
  });

  const renderAndWaitForFetchesToFinish = async (component: ReactElement) => {
    const result = render(
      <SessionContext.Provider value={sessionData}>
        <GrowthBookReact.GrowthBookProvider growthbook={growthbook}>
          {component}
        </GrowthBookReact.GrowthBookProvider>
      </SessionContext.Provider>
    );

    // Avoid warnings that an update on the component "was not wrapped in act(...)":
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    return result;
  };

  // Because field validation only happens on blur, we need to manually trigger this event
  // when testing field change
  const changeFieldValue = ({
    label,
    value,
    container
  }: {
    label: Matcher;
    value: string;
    container?: HTMLElement;
  }) => {
    const field = container
      ? getByLabelText(container, label)
      : screen.getByLabelText(label);
    fireEvent.change(field, { target: { value } });
    fireEvent.blur(field);
  };

  beforeEach(() => {
    v4Counter = 0;
    window.tracking = {
      ready: jest.fn(),
      track: trackMock
    };
    trackMock.mockReset();
    fetchMock.resetMocks();
    fetchMock.mockIf(
      /https:\/\/api.ipify.org/,
      JSON.stringify({ ip: '*******' })
    );
  });

  describe('when component is rendered', () => {
    beforeEach(async () => {
      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, step2],
            hiddenFields: [
              {
                name: 'local_resource_type',
                defaultValue: 'Assisted Living'
              },
              {
                name: 'do_not_submit_this_hidden_value',
                defaultValue: 'Do not submit me',
                doNotSubmit: true
              }
            ],
            pagination: {}
          }}
        />
      );
    });

    it('shows only step 1', async () => {
      expect(screen.getByTestId('step-1')).toBeVisible();
      expect(screen.getByTestId('step-2')).not.toBeVisible();
    });

    describe('and user submits the form without filling up the required fields', () => {
      it('shows the error messages for required fields', async () => {
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(
            screen.getByText('Please provide a valid location')
          ).toBeVisible();
        });
      });
    });

    describe('and user submits the form after filling up the required fields', () => {
      it('shows the next step and hides the current one', async () => {
        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-1')).not.toBeVisible();
          expect(screen.getByTestId('step-2')).toBeVisible();
        });
      });

      it('tracks a Step Submission event with the values of that step', async () => {
        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });

        const stepSubmittedCalls = getEventTrackingCalls(
          segmentEvents.FORM_STEP_SUBMITTED
        );

        expect(stepSubmittedCalls).toHaveLength(1);
        expect(stepSubmittedCalls[0][1]).toMatchObject({
          form_type: FormType.INQUIRY,
          page_session_id: 'mockedPageSessionId',
          session_id: 'mockedSessionId',
          // Details of this value are checked below
          form_step_submission_json: expect.any(String)
        });

        const eventJson = JSON.parse(
          stepSubmittedCalls[0][1]?.form_step_submission_json || '{}'
        );

        expect(eventJson).toEqual({
          form_step_submission: [
            expect.objectContaining({
              form_template_id: expect.any(String),
              form_instance_id: expect.any(String),
              form_type: 'inquiry',
              step_id: expect.any(String),
              step_instance_id: expect.any(String),
              step_index: 0,
              step_content: [
                expect.objectContaining({
                  prompt_id: expect.any(String),
                  prompt_type: 'input',
                  prompt_instance_id: expect.any(String),
                  prompt_index: 1,
                  prompt_value: 'Zip code or city:',
                  response_array: [
                    {
                      response_value: '10014',
                      response_id: expect.any(String)
                    }
                  ]
                })
              ]
            })
          ]
        });
      });
    });

    describe('and user fills up all the required fields on all steps and submits the form', () => {
      beforeEach(async () => {
        // Step 1
        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });

        // Step 2
        changeFieldValue({ label: 'Full Name:', value: 'John Doe' });
        changeFieldValue({ label: 'Email:*', value: '<EMAIL>' });
        changeFieldValue({
          label: 'Should not be submitted:',
          value: 'This should not be sent to the API'
        });
      });

      it('submits all form values not marked as "doNotSubmit" to the Multi-Step Form API', async () => {
        fireEvent.click(screen.getByText(step2.form.cta.label));
        await waitFor(() => {
          expect(getApiMockedCall()).toBeDefined();
        });

        const [url, requestDetails] = getApiMockedCall() || [];
        const body = JSON.parse(requestDetails?.body?.toString() || '');

        expect(url).toEqual('/api/multi-step-form');
        expect(requestDetails?.method).toEqual('POST');
        expect(body.do_not_submit_this_entry).toBeUndefined();
        expect(body).toMatchObject({
          zip_code_or_city: '10014',
          full_name: 'John Doe',
          email: '<EMAIL>'
        });
      });

      it('submits all hidden fields not marked as "doNotSubmit"', async () => {
        fireEvent.click(screen.getByText(step2.form.cta.label));
        await waitFor(() => {
          expect(getApiMockedCall()).toBeDefined();
        });

        const [_url, requestDetails] = getApiMockedCall() || [];
        const body = JSON.parse(requestDetails?.body?.toString() || '');

        expect(body.do_not_submit_this_hidden_value).toBeUndefined();
        expect(body).toMatchObject({
          local_resource_type: 'Assisted Living'
        });
      });

      it('tracks a Step Submission event with the values of that step', async () => {
        fireEvent.click(screen.getByText(step2.form.cta.label));
        await waitFor(() => {
          expect(getApiMockedCall()).toBeDefined();
        });

        const stepSubmittedCalls = getEventTrackingCalls(
          segmentEvents.FORM_STEP_SUBMITTED
        );

        expect(stepSubmittedCalls).toHaveLength(2);
        expect(stepSubmittedCalls[1][1]).toMatchObject({
          form_type: FormType.INQUIRY,
          page_session_id: 'mockedPageSessionId',
          session_id: 'mockedSessionId',
          // Details of this value are checked below
          form_step_submission_json: expect.any(String)
        });

        const eventJson = JSON.parse(
          stepSubmittedCalls[1][1]?.form_step_submission_json || '{}'
        );

        expect(eventJson).toEqual({
          form_step_submission: [
            expect.objectContaining({
              form_type: 'inquiry',
              step_index: 1,
              step_content: [
                expect.objectContaining({
                  prompt_type: 'input',
                  prompt_index: 1,
                  prompt_value: 'Full Name:',
                  response_array: [
                    expect.objectContaining({
                      response_value: 'John Doe'
                    })
                  ]
                }),
                expect.objectContaining({
                  prompt_type: 'input',
                  prompt_index: 2,
                  prompt_value: 'Email:',
                  response_array: [
                    expect.objectContaining({
                      response_value: '<EMAIL>'
                    })
                  ]
                }),
                // 3rd field ('Should not be submitted:') was not submitted to the API,
                // but should be tracked anyway
                expect.objectContaining({
                  prompt_type: 'input',
                  prompt_index: 3,
                  prompt_value: 'Should not be submitted:',
                  response_array: [
                    expect.objectContaining({
                      response_value: 'This should not be sent to the API'
                    })
                  ]
                })
              ]
            })
          ]
        });
      });

      it('tracks a Form Submission event with the ids of each step', async () => {
        fireEvent.click(screen.getByText(step2.form.cta.label));
        await waitFor(() => {
          expect(getApiMockedCall()).toBeDefined();
        });

        const stepSubmittedCalls = getEventTrackingCalls(
          segmentEvents.FORM_STEP_SUBMITTED
        );
        const formSubmittedCalls = getEventTrackingCalls(
          segmentEvents.FORM_SUBMITTED
        );

        const inquirySubmission = getEventTrackingCalls(
          segmentEvents.INQUIRY_SUBMITTED
        );
        expect(formSubmittedCalls).toHaveLength(1);
        // The event is tracked before the redirect, so we can check it here
        expect(inquirySubmission).toHaveLength(1);
        expect(formSubmittedCalls[0][1]).toMatchObject({
          form_type: FormType.INQUIRY,
          page_session_id: 'mockedPageSessionId',
          session_id: 'mockedSessionId',
          // Details of this value are checked below
          form_submission_json: expect.any(String)
        });

        const eventJson = JSON.parse(
          formSubmittedCalls[0][1]?.form_submission_json || '{}'
        );
        const lastStepSubmitted = JSON.parse(
          stepSubmittedCalls[1][1]?.form_step_submission_json || '{}'
        ).form_step_submission[0];

        expect(eventJson).toEqual({
          form_submission: [
            expect.objectContaining({
              form_type: 'inquiry',
              // These props are the same between all the steps, we can check with any of them
              form_template_id: lastStepSubmitted.form_template_id,
              form_instance_id: lastStepSubmitted.form_instance_id,
              step_submissions: [
                expect.objectContaining({
                  step_index: 0,
                  // These props will be validated on a later test (see `describe("event tracking ids")`)
                  step_id: expect.any(String),
                  step_instance_id: expect.any(String)
                }),
                expect.objectContaining({
                  step_index: 1,
                  step_id: expect.any(String),
                  step_instance_id: expect.any(String)
                })
              ]
            })
          ]
        });
      });

      describe('and form submission is successful', () => {
        beforeEach(async () => {
          window.location.assign('http://test.caring.com');
          fetchMock.mockIf(
            /\/api\/multi-step-form/,
            JSON.stringify({ status: 200 })
          );

          fireEvent.click(screen.getByText(step2.form.cta.label));
          await waitFor(() => {
            expect(getApiMockedCall()).toBeDefined();
          });
        });

        it('redirects to the Thank You page', () => {
          expect(window.location.href).toMatch(
            new RegExp('^http://test.caring.com/custom-thank-you-page')
          );
        });

        it('includes submitted and non-submitted values as query param', () => {
          const url = new URL(window.location.href);
          const params = Object.fromEntries(url.searchParams);
          expect(params).toMatchObject({
            zip_code_or_city: '10014',
            full_name: 'John Doe',
            email: '<EMAIL>',
            local_resource_type: 'Assisted Living',
            do_not_submit_this_entry: 'This should not be sent to the API'
          });
        });

        it('tracks an Inquiry Submission event with all the submitted values', () => {
          // The event is tracked before the redirect, so we can check it here
          expect(trackMock).toHaveBeenCalledWith(
            segmentEvents.INQUIRY_SUBMITTED,
            expect.objectContaining({
              form: expect.objectContaining({
                name: 'multi-step-senior-living-vertical-request-info',
                display: Display.VERTICAL,
                type: CTAAction.REQUEST_INFO,
                values: expect.arrayContaining([
                  { name: 'zip_code_or_city', value: '10014' },
                  { name: 'full_name', value: 'John Doe' },
                  { name: 'email', value: '<EMAIL>' },
                  { name: 'local_resource_type', value: 'Assisted Living' }
                ]),
                page_session_id: 'mockedPageSessionId',
                session_id: 'mockedSessionId'
              }),
              page_session_id: 'mockedPageSessionId',
              session_id: 'mockedSessionId'
            })
          );
        });
      });

      describe('and form submission returns an error', () => {
        beforeEach(async () => {
          // We'll need to make sure CTA was disabled and re-enabled, so instead of immediately returning
          // the response, we need to return it only after the button was disabled
          fetchMock.mockResponseOnce(
            () =>
              new Promise((resolve) => {
                waitFor(() => {
                  const lastSubmitButton = screen.getAllByRole('button').pop();
                  expect(lastSubmitButton).toHaveAttribute('disabled');
                }).then(() => {
                  resolve({ status: 500 });
                });
              })
          );

          fireEvent.click(screen.getByText(step2.form.cta.label));
          await waitFor(() => {
            expect(getApiMockedCall()).toBeDefined();
          });
        });

        it('does not redirect page and shows the error message instead', async () => {
          await waitFor(() => {
            expect(screen.getAllByText(errorOnSubmit)[0]).toBeInTheDocument();
          });
        });

        it('re-enable the CTA', async () => {
          await waitFor(() => {
            const lastSubmitButton = screen.getAllByRole('button').pop();
            expect(lastSubmitButton).not.toHaveAttribute('disabled');
          });
        });
      });
    });
  });

  describe('when headings and sub-headings texts use values from form entries', () => {
    const dynamicStep2: StepConfig = {
      ...step2,
      headline: {
        ...step2.headline,
        text: 'Headline with param from form ({form.zip_code_or_city})'
      },
      subHeadline: {
        ...step2.subHeadline,
        text: 'Sub-headline with param from form ({form.zip_code_or_city})'
      },
      form: {
        ...step2.form,
        headline: 'Form headline with param from form ({form.zip_code_or_city})'
      }
    };

    beforeEach(async () => {
      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, dynamicStep2],
            hiddenFields: [],
            pagination: {}
          }}
        />
      );
    });

    it('removes those values from headlines and sub-headlines while the fields are not filled up', () => {
      expect(
        screen.getByText('Headline with param from form ()')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Sub-headline with param from form ()')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Form headline with param from form ()')
      ).toBeInTheDocument();
    });

    it('formats headlines and sub-headlines using information from the form values when they were already filled up', async () => {
      // Fill up 1st step
      changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
      fireEvent.click(screen.getByText(step1.form.cta.label));

      await waitFor(() => {
        expect(screen.getByTestId('step-1')).not.toBeVisible();
        expect(screen.getByTestId('step-2')).toBeVisible();
      });

      expect(
        screen.getByText('Headline with param from form (10014)')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Sub-headline with param from form (10014)')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Form headline with param from form (10014)')
      ).toBeInTheDocument();
    });
  });

  describe('when hidden fields uses values from the URL and from the form', () => {
    beforeEach(async () => {
      window.location.assign('http://test.caring.com?utm_id=123');

      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1],
            hiddenFields: [
              {
                name: 'value_from_url',
                defaultValue: '{url.utm_id}'
              },
              {
                name: 'value_from_form',
                defaultValue: '{form.zip_code_or_city}'
              },
              {
                name: 'unknown_value_from_url',
                defaultValue: '{url.unknown_query_param}'
              },
              {
                name: 'unknown_value_from_form',
                defaultValue: '{form.unknown_field}'
              }
            ],
            pagination: {}
          }}
        />
      );

      changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
      fireEvent.click(screen.getByText(step1.form.cta.label));
      await waitFor(() => {
        expect(getApiMockedCall()).toBeDefined();
      });
    });

    it('submits the parsed hidden fields that exist', () => {
      const body = getApiMockedCall()?.[1]?.body?.toString() || '{}';
      expect(JSON.parse(body)).toMatchObject({
        value_from_url: '123',
        value_from_form: '10014'
      });
    });

    it('submits unknown hidden fields as empty strings', () => {
      const body = getApiMockedCall()?.[1]?.body?.toString() || '{}';
      expect(JSON.parse(body)).toMatchObject({
        unknown_value_from_url: '',
        unknown_value_from_form: ''
      });
    });
  });

  describe('when hidden fields use some of the client variables', () => {
    beforeEach(async () => {
      // For {client.browser}
      Object.defineProperty(navigator, 'userAgent', { value: 'Test Browser' });

      // For {client.anonymousId} and {client.segmentTracking}
      window.tracking = {
        ready: (callback: Function) => callback(),
        user: jest.fn(() => ({
          anonymousId: jest.fn().mockReturnValue('mockedAnonymousId')
        })),
        track: jest.fn()
      };

      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1],
            hiddenFields: [
              {
                name: 'ip_address',
                defaultValue: '{client.ip}'
              },
              {
                name: 'browser',
                defaultValue: '{client.browser}'
              },
              {
                name: 'form_instance_id',
                defaultValue: '{client.formInstanceId}'
              },
              {
                name: 'anonymous_id',
                defaultValue: '{client.anonId}'
              },
              {
                name: 'notes',
                defaultValue: '{client.trackingNotes}'
              }
            ],
            pagination: {}
          }}
        />
      );

      changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
      fireEvent.click(screen.getByText(step1.form.cta.label));
      await waitFor(() => {
        expect(getApiMockedCall()).toBeDefined();
      });
    });

    it('submits the hidden fields with IP and browser values', () => {
      const body = getApiMockedCall()?.[1]?.body?.toString() || '{}';
      expect(JSON.parse(body)).toMatchObject({
        ip_address: '*******',
        browser: 'Test Browser',
        form_instance_id: expect.any(String),
        anonymous_id: 'mockedAnonymousId',
        notes:
          'segment_anonid:mockedAnonymousId|page_session_id:mockedPageSessionId'
      });
    });
  });

  describe('event tracking ids', () => {
    const eventsOfForm1: {
      step1?: formStep;
      step2?: formStep;
    } = {};
    const eventsOfForm2: {
      step1?: formStep;
      step2?: formStep;
    } = {};

    const renderAndSubmitForms = async () => {
      const multipleForms = await renderAndWaitForFetchesToFinish(
        <>
          <div data-testid="form1">
            <MultiStepForm
              {...baseMultiStepFormProps}
              data={{
                steps: [step1, step2],
                hiddenFields: [],
                pagination: {}
              }}
            />
          </div>
          <div data-testid="form2">
            <MultiStepForm
              {...baseMultiStepFormProps}
              data={{
                steps: [step1, step2],
                hiddenFields: [],
                pagination: {}
              }}
            />
          </div>
        </>
      );

      const form1 = multipleForms.getByTestId('form1');
      const form2 = multipleForms.getByTestId('form2');

      await submitForm(form1);
      await submitForm(form2);
      await waitFor(() => {
        expect(getApiMockedCalls()).toHaveLength(2);
      });
    };

    const submitForm = async (container: HTMLElement) => {
      // Step 1
      changeFieldValue({
        label: 'Zip code or city:*',
        value: '10014',
        container
      });
      fireEvent.click(getByText(container, step1.form.cta.label));

      await waitFor(() => {
        expect(getByTestId(container, 'step-2')).toBeVisible();
      });

      // Step 2
      changeFieldValue({
        label: 'Full Name:',
        value: 'John Doe',
        container
      });
      changeFieldValue({
        label: 'Email:*',
        value: '<EMAIL>',
        container
      });
      fireEvent.click(getByText(container, step2.form.cta.label));
    };

    const parseEventData = (event: FormStepSubmission): formStep => {
      return (
        JSON.parse(event.form_step_submission_json || '{}')
          .form_step_submission[0] || {}
      );
    };

    beforeEach(async () => {
      await renderAndSubmitForms();

      // Extract event data to be easier to test their values later
      const events = getEventTrackingCalls(
        segmentEvents.FORM_STEP_SUBMITTED
      ) as Array<[string, FormStepSubmission]>;

      eventsOfForm1.step1 = parseEventData(events[0][1]);
      eventsOfForm1.step2 = parseEventData(events[1][1]);
      eventsOfForm2.step1 = parseEventData(events[2][1]);
      eventsOfForm2.step2 = parseEventData(events[3][1]);
    });

    it('uses the same "form_template_id" for all steps and all forms', () => {
      const baseline = eventsOfForm1.step1?.form_template_id;
      expect(eventsOfForm1.step2?.form_template_id).toEqual(baseline);
      expect(eventsOfForm2.step1?.form_template_id).toEqual(baseline);
      expect(eventsOfForm2.step2?.form_template_id).toEqual(baseline);
    });

    it('uses the same "form_instance_id" for all steps but different ones between forms', () => {
      // Same between steps
      expect(eventsOfForm1.step1?.form_instance_id).toEqual(
        eventsOfForm1.step2?.form_instance_id
      );
      expect(eventsOfForm2.step1?.form_instance_id).toEqual(
        eventsOfForm2.step2?.form_instance_id
      );

      // Different between forms
      expect(eventsOfForm1.step1?.form_instance_id).not.toEqual(
        eventsOfForm2.step1?.form_instance_id
      );
    });

    it('uses different values for "step_id" and "step_content.prompt_id" for each step but keep them the same between forms', () => {
      const getValueOfStep = (step?: formStep) => ({
        step_id: step?.step_id,
        step_content: step?.step_content.map(
          (stepContent) => stepContent.prompt_id
        )
      });

      const step1ofForm1 = getValueOfStep(eventsOfForm1.step1);
      const step2ofForm1 = getValueOfStep(eventsOfForm1.step2);
      const step1ofForm2 = getValueOfStep(eventsOfForm2.step1);
      const step2ofForm2 = getValueOfStep(eventsOfForm2.step2);

      // Same between forms
      expect(step1ofForm1?.step_id).toEqual(step1ofForm2?.step_id);
      expect(step2ofForm2?.step_id).toEqual(step2ofForm2?.step_id);
      expect(step1ofForm1?.step_content).toEqual(step1ofForm2?.step_content);
      expect(step2ofForm2?.step_content).toEqual(step2ofForm2?.step_content);

      // Different between steps
      expect(step1ofForm1?.step_id).not.toEqual(step2ofForm1?.step_id);
      expect(step1ofForm2?.step_id).not.toEqual(step2ofForm2?.step_id);
      expect(step1ofForm1?.step_content).not.toEqual(
        step2ofForm1?.step_content
      );
      expect(step1ofForm2?.step_content).not.toEqual(
        step2ofForm2?.step_content
      );
    });

    it('always uses different values for "step_instance_id", "step_content.prompt_instance_id", and "step_content.response_array.response_id"', () => {
      const getValueOfStep = (step?: formStep) => ({
        step_instance_id: step?.step_instance_id,
        step_content: {
          prompt_instance_id: step?.step_content.map(
            (stepContent) => stepContent.prompt_instance_id
          ),
          response_array: step?.step_content.map(
            (stepContent) => stepContent.response_array[0].response_id
          )
        }
      });

      const step1ofForm1 = getValueOfStep(eventsOfForm1.step1);
      const step2ofForm1 = getValueOfStep(eventsOfForm1.step2);
      const step1ofForm2 = getValueOfStep(eventsOfForm2.step1);
      const step2ofForm2 = getValueOfStep(eventsOfForm2.step2);

      // step_instance_id
      expect(step1ofForm1?.step_instance_id).not.toEqual(
        step1ofForm2?.step_instance_id
      );
      expect(step2ofForm1?.step_instance_id).not.toEqual(
        step2ofForm2?.step_instance_id
      );

      // step_content.prompt_instance_id
      expect(step1ofForm1?.step_content.prompt_instance_id).not.toEqual(
        step1ofForm2?.step_content.prompt_instance_id
      );
      expect(step2ofForm1?.step_content.prompt_instance_id).not.toEqual(
        step2ofForm2?.step_content.prompt_instance_id
      );

      // step_content.response_array
      expect(step1ofForm1?.step_content.response_array).not.toEqual(
        step1ofForm2?.step_content.response_array
      );
      expect(step2ofForm1?.step_content.response_array).not.toEqual(
        step2ofForm2?.step_content.response_array
      );
    });
  });

  describe('when tracking the Step Submission event for a form entry with no label', () => {
    beforeEach(async () => {
      const step: StepConfig = {
        ...step1,
        form: {
          ...step1.form,
          headline: 'Enter your zip code',
          fields: [
            {
              type: 'input',
              inputType: 'generic',
              label: '',
              name: 'zipCode',
              placeholder: '00000',
              isRequired: false
            }
          ]
        }
      };

      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step],
            hiddenFields: [],
            pagination: {}
          }}
        />
      );

      const zipCode = screen.getByPlaceholderText('00000');
      fireEvent.change(zipCode, { target: { value: '11111' } });
      fireEvent.click(screen.getByText(step1.form.cta.label));

      await waitFor(() => {
        expect(getApiMockedCall()).toBeDefined();
      });
    });

    it('uses the form headline as prompt value', () => {
      const stepSubmittedCalls = getEventTrackingCalls(
        segmentEvents.FORM_STEP_SUBMITTED
      );
      const eventJson = JSON.parse(
        stepSubmittedCalls[0][1]?.form_step_submission_json || '{}'
      );

      expect(eventJson.form_step_submission[0].step_content).toEqual([
        expect.objectContaining({
          prompt_index: 1,
          prompt_value: 'Enter your zip code',
          response_array: [
            expect.objectContaining({
              response_value: '11111'
            })
          ]
        })
      ]);
    });
  });

  describe('when form type is "other"', () => {
    beforeEach(async () => {
      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1],
            hiddenFields: [],
            pagination: {}
          }}
          formType={{ field: 'other' }}
        />
      );

      changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
      fireEvent.click(screen.getByText(step1.form.cta.label));
      await waitFor(() => {
        expect(getApiMockedCall()).toBeDefined();
      });
    });

    it('does not track any event', () => {
      expect(trackMock).not.toHaveBeenCalled();
    });
  });

  describe('form navigation between steps', () => {
    // While `jest-location-mock` does not handle window.history, we need to
    // manually set the location to simulate the back/forward buttons of the
    // browser, instead of simply calling window.history.back() / forward().
    // This limitation might be fixed once this PR is merged and released:
    // https://github.com/evelynhathaway/jest-location-mock/pull/179
    const goBack = () => {
      act(() => {
        window.location.assign('/multi-step-form-test#step-1');
        window.dispatchEvent(new Event('hashchange'));
      });
    };
    const goForward = () => {
      act(() => {
        window.location.assign('/multi-step-form-test#step-2');
        window.dispatchEvent(new Event('hashchange'));
      });
    };

    beforeEach(async () => {
      window.location.assign('/multi-step-form-test');

      await renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, step2],
            hiddenFields: [],
            pagination: {}
          }}
        />
      );
    });

    describe('when user is on the 2nd step of the form and they press the back button of the browser', () => {
      beforeEach(async () => {
        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });
        goBack();
      });

      it('goes back to the 1st step', async () => {
        await waitFor(() => {
          expect(screen.getByTestId('step-1')).toBeVisible();
        });
      });

      it('goes back to the 2nd step when user presses the forward button of the browser', async () => {
        await waitFor(() => {
          expect(screen.getByTestId('step-1')).toBeVisible();
        });

        goForward();

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });
      });

      describe('and a required field is cleaned up while using the browser navigation', () => {
        beforeEach(async () => {
          // User already filled up step 1, was on step 2, and went back to step 1:
          await waitFor(() => {
            expect(screen.getByTestId('step-1')).toBeVisible();
          });

          // Now they remove the required value of step 1, and move to the final step by pressing the forward button of the browser:
          changeFieldValue({ label: 'Zip code or city:*', value: '' });

          goForward();
          await waitFor(() => {
            expect(screen.getByTestId('step-2')).toBeVisible();
          });

          // Finally they fill up step 2 and submit the form:
          changeFieldValue({ label: 'Full Name:', value: 'John Doe' });
          changeFieldValue({ label: 'Email:*', value: '<EMAIL>' });
          fireEvent.click(screen.getByText(step2.form.cta.label));

          // An error message will be rendered on the first step
          await waitFor(() => {
            expect(
              screen.getByTestId('error-zip_code_or_city')
            ).toBeInTheDocument();
          });
        });

        it('does not allow user to submit the form', async () => {
          expect(
            getEventTrackingCalls(segmentEvents.FORM_STEP_SUBMITTED)
          ).toHaveLength(1); // 1st step was successfully submitted

          expect(
            getEventTrackingCalls(segmentEvents.FORM_SUBMITTED)
          ).toHaveLength(0);

          expect(getApiMockedCall()).not.toBeDefined();
        });

        it('shows an error message', async () => {
          await waitFor(() => {
            expect(screen.getByText(errorOnOtherSteps)).toBeVisible();
          });
        });
      });
    });

    describe('event tracking', () => {
      beforeEach(async () => {
        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });
        goBack();

        await waitFor(() => {
          expect(screen.getByTestId('step-1')).toBeVisible();
        });

        changeFieldValue({ label: 'Zip code or city:*', value: '99999' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });
      });

      it('tracks a new Step Submission event for that same step when user re-submits it', async () => {
        const stepSubmittedCalls = getEventTrackingCalls(
          segmentEvents.FORM_STEP_SUBMITTED
        );

        expect(stepSubmittedCalls).toHaveLength(2);

        const eventJson = JSON.parse(
          stepSubmittedCalls[1][1]?.form_step_submission_json || '{}'
        );

        expect(eventJson).toEqual({
          form_step_submission: [
            expect.objectContaining({
              step_content: [
                expect.objectContaining({
                  response_array: [
                    expect.objectContaining({
                      response_value: '99999'
                    })
                  ]
                })
              ]
            })
          ]
        });
      });

      describe('event tracking ids', () => {
        const parseEventData = (event: FormStepSubmission): formStep => {
          return (
            JSON.parse(event.form_step_submission_json || '{}')
              .form_step_submission[0] || {}
          );
        };

        const getFormStepSubmittedEvents = () => {
          // Extract event data to be easier to test their values later
          const events = getEventTrackingCalls(
            segmentEvents.FORM_STEP_SUBMITTED
          ) as Array<[string, FormStepSubmission]>;

          const firstTry = parseEventData(events[0][1]);
          const secondTry = parseEventData(events[1][1]);

          return { firstTry, secondTry };
        };

        it('uses the same "form_template_id", "form_instance_id" for both step submissions', () => {
          const { firstTry, secondTry } = getFormStepSubmittedEvents();
          expect(firstTry.form_template_id).toEqual(secondTry.form_template_id);
          expect(firstTry.form_instance_id).toEqual(secondTry.form_instance_id);
        });

        it('uses the same "step_id" and "step_content.prompt_id" for both step submissions', () => {
          const getValueOfStep = (step?: formStep) => ({
            step_id: step?.step_id,
            step_content: step?.step_content.map(
              (stepContent) => stepContent.prompt_id
            )
          });

          const { firstTry, secondTry } = getFormStepSubmittedEvents();
          const stepDataOfFirstTry = getValueOfStep(firstTry);
          const stepDataOfSecondTry = getValueOfStep(secondTry);

          expect(stepDataOfFirstTry.step_id).toEqual(
            stepDataOfSecondTry.step_id
          );
          expect(stepDataOfFirstTry.step_content).toEqual(
            stepDataOfSecondTry.step_content
          );
        });

        it('uses different values for "step_instance_id", "step_content.prompt_instance_id", and "step_content.response_array.response_id"', () => {
          const getValueOfStep = (step?: formStep) => ({
            step_instance_id: step?.step_instance_id,
            step_content: {
              prompt_instance_id: step?.step_content.map(
                (stepContent) => stepContent.prompt_instance_id
              ),
              response_array: step?.step_content.map(
                (stepContent) => stepContent.response_array[0].response_id
              )
            }
          });

          const { firstTry, secondTry } = getFormStepSubmittedEvents();
          const stepDataOfFirstTry = getValueOfStep(firstTry);
          const stepDataOfSecondTry = getValueOfStep(secondTry);

          expect(stepDataOfFirstTry.step_instance_id).not.toEqual(
            stepDataOfSecondTry.step_instance_id
          );

          expect(
            stepDataOfFirstTry.step_content.prompt_instance_id
          ).not.toEqual(stepDataOfSecondTry.step_content.prompt_instance_id);

          expect(stepDataOfFirstTry.step_content.response_array).not.toEqual(
            stepDataOfSecondTry.step_content.response_array
          );
        });
      });
    });
  });

  describe('SEM Budget Form Feature', () => {
    const renderFormWithSteps = async () => {
      return renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, step2],
            hiddenFields: [],
            pagination: {}
          }}
        />
      );
    };

    beforeEach(() => {
      mockUseFeatureIsOn.mockReset();
    });

    describe('when feature flag is OFF', () => {
      beforeEach(() => {
        mockUseFeatureIsOn.mockReturnValue(false);
      });

      it('renders original form steps without modifications', async () => {
        await renderFormWithSteps();

        expect(screen.getByTestId('step-1')).toBeVisible();
        expect(screen.getByText(step1.form.headline!)).toBeInTheDocument();

        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
          expect(screen.getByText(step2.form.headline!)).toBeInTheDocument();
        });
      });
    });

    describe('when feature flag is ON', () => {
      // Create step2 with radio button override applied (simulating server-side processing)
      const step2WithRadioOverride: StepConfig = {
        ...step2,
        form: {
          ...step2.form,
          ...BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form,
          fields: BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form
            .fields as StepConfig['form']['fields']
        } as any
      };

      const renderFormWithRadioExperiment = async () => {
        return renderAndWaitForFetchesToFinish(
          <MultiStepForm
            {...baseMultiStepFormProps}
            data={{
              steps: [step1, step2WithRadioOverride],
              hiddenFields: [],
              pagination: {}
            }}
          />
        );
      };

      beforeEach(() => {
        mockUseFeatureIsOn.mockReturnValue(false);
      });

      it('renders first step without modifications', async () => {
        await renderFormWithRadioExperiment();

        expect(screen.getByTestId('step-1')).toBeVisible();
        expect(screen.getByText(step1.form.headline!)).toBeInTheDocument();
      });

      it('allows selecting timeline and who you are looking for', async () => {
        await renderFormWithRadioExperiment();

        await act(async () => {
          changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
          fireEvent.click(screen.getByText(step1.form.cta.label));
        });

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
        });

        const radioGroups = screen.getAllByRole('radiogroup');
        expect(radioGroups).toHaveLength(3);

        expect(screen.getByTestId('radio-option-Urgent')).toBeInTheDocument();

        await act(async () => {
          fireEvent.click(screen.getByTestId('radio-option-30 Days'));
        });

        expect(screen.getByTestId('radio-option-Myself')).toBeInTheDocument();

        await act(async () => {
          fireEvent.click(screen.getByTestId('radio-option-Myself'));
        });
      });
    });
  });

  describe('Budget Question Radio Experiment', () => {
    const renderFormWithSteps = async () => {
      return renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, step2],
            hiddenFields: [],
            pagination: {}
          }}
        />
      );
    };

    beforeEach(() => {
      mockUseFeatureIsOn.mockReset();
    });

    describe('when feature flag is OFF', () => {
      beforeEach(() => {
        mockUseFeatureIsOn.mockReturnValue(false);
      });

      it('renders original form steps without modifications', async () => {
        await renderFormWithSteps();

        expect(screen.getByTestId('step-1')).toBeVisible();
        expect(screen.getByText(step1.form.headline!)).toBeInTheDocument();

        changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
        fireEvent.click(screen.getByText(step1.form.cta.label));

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();
          expect(screen.getByText(step2.form.headline!)).toBeInTheDocument();
        });
      });
    });

    describe('when feature flag is ON', () => {
      const step2WithRadioOverride: StepConfig = {
        ...step2,
        form: {
          ...step2.form,
          ...BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form,
          fields: BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form
            .fields as StepConfig['form']['fields']
        } as any
      };

      const renderFormWithRadioExperiment = async () => {
        return renderAndWaitForFetchesToFinish(
          <MultiStepForm
            {...baseMultiStepFormProps}
            data={{
              steps: [step1, step2WithRadioOverride],
              hiddenFields: [],
              pagination: {}
            }}
          />
        );
      };

      beforeEach(() => {
        mockUseFeatureIsOn.mockReturnValue(false);
      });

      it('renders radio buttons instead of select dropdowns', async () => {
        await renderFormWithRadioExperiment();

        await act(async () => {
          changeFieldValue({ label: 'Zip code or city:*', value: '10014' });
          fireEvent.click(screen.getByText(step1.form.cta.label));
        });

        await waitFor(() => {
          expect(screen.getByTestId('step-2')).toBeVisible();

          const radioGroups = screen.getAllByRole('radiogroup');
          expect(radioGroups).toHaveLength(3);
          expect(screen.getByTestId('radio-option-Urgent')).toBeInTheDocument();
          expect(
            screen.getByTestId('radio-option-30 Days')
          ).toBeInTheDocument();

          expect(screen.queryByRole('combobox')).not.toBeInTheDocument();
        });
      });
    });
  });

  describe('Hero Image', () => {
    const renderFormWithSteps = async (
      shouldShowHeroBehindFormLayout?: boolean
    ) => {
      return renderAndWaitForFetchesToFinish(
        <MultiStepForm
          {...baseMultiStepFormProps}
          data={{
            steps: [step1, step2],
            hiddenFields: [],
            pagination: {},
            shouldShowHeroBehindFormLayout
          }}
        />
      );
    };

    it('renders image below the form when the flag is off', async () => {
      await renderFormWithSteps();
      expect(screen.getByTestId('hero-image-0')).toBeVisible();
      expect(screen.queryAllByTestId('hero-image-behind-form-0')).toHaveLength(
        0
      );
    });

    it('renders image behind the form when flag is on', async () => {
      await renderFormWithSteps(true);
      expect(screen.getByTestId('hero-image-behind-form-0')).toBeVisible();
      expect(screen.queryAllByTestId('hero-image-0')).toHaveLength(0);
    });
  });
});
