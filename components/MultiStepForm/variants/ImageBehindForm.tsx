import { Box } from '@chakra-ui/react';
import StepImage from '@components/Image/Image';

import { MultiStepFormData } from '../MultiStepForm';

interface ImageProps {
  config: MultiStepFormData;
  currentStep: number;
}

const ImageBehindForm = ({ config, currentStep }: ImageProps) => {
  return (
    <>
      {config.steps.map(
        (step, index) =>
          step.image && (
            <Box
              key={index}
              display={index === currentStep ? 'block' : 'none'}
              as="div"
              data-testid={`hero-image-behind-form-${index}`}
              position="absolute"
              width="100%"
              height="458px"
              opacity={0.5}
            >
              <StepImage {...step.image} fill />
            </Box>
          )
      )}
    </>
  );
};

export default ImageBehindForm;
