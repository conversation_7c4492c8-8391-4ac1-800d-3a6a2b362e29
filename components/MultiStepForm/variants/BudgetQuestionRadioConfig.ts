export const BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE = {
  form: {
    headline:
      'Select your timeline, budget, and who you are looking for to compare options. ',
    cta: {
      label: 'Continue',
      formatting: {
        field: 'default'
      }
    },
    display: 'vertical',
    backgroundColor: {
      range: '50',
      color: 'white'
    },
    helpBackgroundColor: {
      range: '100',
      color: 'background'
    },
    entriesColor: {
      color: 'background',
      range: '500'
    },
    errorColor: {
      range: '500',
      color: 'error'
    },
    fields: [
      {
        type: 'radio-button',
        name: 'timeline',
        label: 'Timeline',
        doNotSubmit: true,
        isRequired: false,
        options: [
          {
            label: 'Urgent',
            value: 'Urgent'
          },
          {
            label: '30 Days',
            value: '30 Days'
          },
          {
            label: '60 Days',
            value: '60 Days'
          },
          {
            label: 'Longer',
            value: 'Longer'
          }
        ]
      },
      {
        type: 'radio-button',
        name: 'budget',
        label: 'Monthly Budget',
        doNotSubmit: true,
        isRequired: false,
        options: [
          {
            label: 'Unsure',
            value: 'Unsure'
          },
          {
            label: 'Luxury',
            value: 'Luxury'
          },

          {
            label: 'Standard',
            value: 'Standard'
          },
          {
            label: 'Economy',
            value: 'Economy'
          }
        ]
      },
      {
        type: 'radio-button',
        name: 'inquiry_for',
        label: 'Who are you looking for?',
        doNotSubmit: false,
        isRequired: true,
        isRequiredErrorMessage: 'This field is required',
        options: [
          {
            label: 'Myself',
            value: 'Myself'
          },
          {
            label: 'Parent(s)',
            value: 'Parent(s)'
          },

          {
            label: 'Spouse',
            value: 'Spouse'
          },
          {
            label: 'Other',
            value: 'Other'
          }
        ]
      }
    ],
    entryList: {
      entryList0: {
        label: 'Timeline',
        name: 'timeline',
        doNotSubmit: true,
        entryType: {
          field: 'radio-button',
          options: {
            options0: {
              label: 'Urgent',
              value: 'Urgent',
              '@nodes': []
            },
            options1: {
              label: '30 Days',
              value: '30 Days',
              '@nodes': []
            },
            options2: {
              label: '60 Days',
              value: '60 Days',
              '@nodes': []
            },
            options3: {
              label: 'Longer',
              value: 'Longer',
              '@nodes': []
            },
            '@nodes': ['options0', 'options1', 'options2', 'options3']
          },
          '@nodes': ['options']
        },
        isRequired: {
          field: 'false',
          '@nodes': []
        },
        '@nodes': ['entryType', 'isRequired']
      },
      entryList1: {
        label: 'Monthly Budget',
        name: 'budget',
        doNotSubmit: true,
        entryType: {
          field: 'radio-button',
          options: {
            options0: {
              label: 'Unsure',
              value: 'Unsure',
              '@nodes': []
            },
            options1: {
              label: 'Luxury',
              value: 'Luxury',
              '@nodes': []
            },
            options2: {
              label: 'Standard',
              value: 'Standard',
              '@nodes': []
            },
            options3: {
              label: 'Economy',
              value: 'Economy',
              '@nodes': []
            },
            '@nodes': ['options0', 'options1', 'options2', 'options3']
          },
          '@nodes': ['options']
        },
        isRequired: {
          field: 'false',
          '@nodes': []
        },
        '@nodes': ['entryType', 'isRequired']
      },
      entryList2: {
        label: 'Who are you looking for?',
        name: 'inquiry_for',
        entryType: {
          field: 'radio-button',
          options: {
            options0: {
              label: 'Myself',
              value: 'Myself',
              '@nodes': []
            },
            options1: {
              label: 'Parent(s)',
              value: 'Parent(s)',
              '@nodes': []
            },
            options2: {
              label: 'Spouse',
              value: 'Spouse',
              '@nodes': []
            },
            options3: {
              label: 'Other',
              value: 'Other',
              '@nodes': []
            },

            '@nodes': ['options0', 'options1', 'options2', 'options3']
          },
          '@nodes': ['options']
        },
        isRequired: {
          isRequiredErrorMessage: 'This field is required',
          field: 'true',
          '@nodes': []
        },
        '@nodes': ['entryType', 'isRequired']
      },
      '@nodes': ['entryList0', 'entryList1', 'entryList2']
    }
  }
};
