import { ThemeTypings } from '@chakra-ui/react';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import { RollUpType } from '@components/Navigation';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import { MagnoliaImage, MagnoliaNode, Metadata } from '~/types/Magnolia';
import { Color } from '~/utils/getColor';

export interface MagnoliaMultiStepFormSubmissionProps
  extends Omit<Metadata, '@nodes'> {
  '@id': string;
  'mgnl:template': string;
  postUrl?: string;
  postFormat?: 'json' | 'form';
  includeCaringPartnerToken?: {
    field: 'none' | 'systemToken' | 'customToken';
    customToken?: string;
  };
}

interface InquiryFormEventTrackingProps {
  field: 'inquiry';
  type: CTAAction;
  display: Display;
  rollUp?: RollUpType;
}
interface OtherFormEventTrackingProps {
  field: 'other';
}
export interface MultiStepFormEventTrackingProps {
  formType?: InquiryFormEventTrackingProps | OtherFormEventTrackingProps;
}

export interface MagnoliaMultiStepForm
  extends MagnoliaMultiStepFormSubmissionProps,
    MultiStepFormEventTrackingProps,
    MagnoliaNode {
  steps?: MagnoliaNode & {
    [key: string]: MagnoliaStep;
  };
  hiddenEntries?: MagnoliaNode & {
    [key: string]: HiddenField;
  };
  thankYou: {
    path?: string;
  };
  errorMessages?: {
    errorOnSubmit?: string;
    validationError?: string;
  };
  image: MagnoliaStepImage;
  formBackgroundColor: Color;
  helpBackgroundColor: Color;
  formEntriesColor: Color;
  errorColor: Color;
}

export interface MagnoliaStep extends MagnoliaNode {
  headline?: MagnoliaTextWithSizeAndStyle;
  subHeadline?: MagnoliaTextWithSizeAndStyle;
  image: MagnoliaStepImage;
  form: MagnoliaFormConfig;
}

export interface MagnoliaTextWithSizeAndStyle {
  text?: string;
  textFormatting: {
    field: 'custom' | 'default';
    // Styles for 'custom' option
    color?: {
      color?: string;
      colorRange?: string;
    };
    element?: HeadingElements | 'p';
    sizeOnDesktop?: HeadingSizes;
    sizeOnMobile?: HeadingSizes;
    styles?: {
      bold: boolean;
      italic: boolean;
    };
  };
}

export interface HiddenField {
  name: string;
  defaultValue: string;
  doNotSubmit?: boolean;
}

export interface MagnoliaStepImage {
  imageBorderRadius?: string;
  switchable: {
    field: 'none' | 'componentDefault' | 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
  };
}

export type StepConfig = Omit<MagnoliaStep, 'image' | 'entryList'> & {
  image?: MagnoliaStepImage;
  form: MagnoliaFormConfig & {
    backgroundColor: Color;
    helpBackgroundColor: Color;
    entriesColor: Color;
    errorColor: Color;
    fields: Array<FieldConfig>;
    display?: Display;
  };
};

export interface MagnoliaFormConfig {
  // We don't need the form headline to have configurable styles,
  // otherwise this type would be a MagnoliaTextWithSizeAndStyle
  headline?: string;
  legalDisclosure?: string;
  helpTextHeader?: string;
  helpText?: string;
  infoText?: string;
  cta: {
    label: string;
    formatting: {
      field: 'custom' | 'default';
      color?: ThemeTypings['colorSchemes'];
      size?: 'xs' | 'sm' | 'md' | 'lg';
      variant?: 'solid' | 'outline' | 'ghost';
    };
  };
  entryList?: MagnoliaNode & {
    [key: string]: MagnoliaFieldConfig;
  };
}

export interface FieldErrorDetails {
  message?: string;
}

interface BaseFieldConfig {
  name: string;
  label?: string;
  defaultValue?: string;
  doNotSubmit?: boolean;
  isRequired: boolean;
  isRequiredErrorMessage?: string;
  infoText?: string;
}

export interface InputFieldConfig extends BaseFieldConfig {
  type: 'input';
  inputType: 'generic' | 'phone' | 'email';
  typeErrorMessage?: string;
  typeMask?: string;
  placeholder?: string;
}
export interface SelectFieldConfig extends BaseFieldConfig {
  type: 'select';
  options: SelectOption[];
}
export interface SelectOption {
  label: string;
  value?: string;
}

export interface CheckboxFieldConfig extends BaseFieldConfig {
  type: 'checkbox';
}

export interface RadioButtonFieldConfig extends BaseFieldConfig {
  type: 'radio-button';
  options: SelectOption[];
}

export type FieldConfig =
  | InputFieldConfig
  | SelectFieldConfig
  | CheckboxFieldConfig
  | RadioButtonFieldConfig;

interface MagnoliaBaseFieldConfig extends MagnoliaNode {
  name: string;
  label?: string;
  defaultValue?: string;
  doNotSubmit?: boolean;
  isRequired: {
    field: 'true' | 'false';
    isRequiredErrorMessage?: string;
  };
}
export interface MagnoliaInputFieldConfig extends MagnoliaBaseFieldConfig {
  entryType: {
    field: 'input';
    inputType: {
      field: 'generic' | 'phone' | 'email';
      typeErrorMessage?: string;
      typeMask?: {
        field: 'true' | 'false';
        mask?: string;
      };
    };
    placeholder?: InputFieldConfig['placeholder'];
  };
}
export interface MagnoliaSelectFieldConfig extends MagnoliaBaseFieldConfig {
  entryType: {
    field: 'select';
    options: MagnoliaNode & {
      [key: string]: SelectOption;
    };
  };
}

export interface MagnoliaCheckboxFieldConfig extends MagnoliaBaseFieldConfig {
  entryType: {
    field: 'checkbox';
  };
}

export interface MagnoliaRadioButtonFieldConfig
  extends MagnoliaBaseFieldConfig {
  entryType: {
    field: 'radio-button';
    options: MagnoliaNode & {
      [key: string]: SelectOption;
    };
  };
}

export type MagnoliaFieldConfig =
  | MagnoliaInputFieldConfig
  | MagnoliaSelectFieldConfig
  | MagnoliaCheckboxFieldConfig
  | MagnoliaRadioButtonFieldConfig;
