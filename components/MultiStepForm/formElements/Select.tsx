import {
  FormControl,
  FormErrorMessage,
  FormLabel
} from '@chakra-ui/form-control';
import { Box } from '@chakra-ui/layout';
import { Select as ChakraSelect } from '@chakra-ui/react';
import Tooltip from '@components/Tooltip/Tooltip';

import { SelectFieldConfig } from '../types';
import { FormFieldCommonProps } from './FormField';

type Props = SelectFieldConfig & FormFieldCommonProps;

const Select: React.FC<Props> = ({
  error,
  errorColorKey,
  entriesColorKey,
  label,
  name,
  options,
  isRequired,
  register,
  infoText
}) => {
  return (
    <FormControl
      fontSize="md"
      isRequired={isRequired}
      isInvalid={Boolean(error)}
    >
      <Box display="flex" alignItems="center" gap={2} my={2}>
        <FormLabel margin={0}>{label}</FormLabel>
        {infoText && <Tooltip text={infoText} />}
      </Box>
      <ChakraSelect
        {...register(name)}
        borderColor={entriesColorKey}
        errorBorderColor={errorColorKey}
      >
        {(options || []).map(({ value, label }, index) => (
          <option
            key={`${index}-${value}`}
            value={value || ''}
            data-testid="select-option"
          >
            {label}
          </option>
        ))}
      </ChakraSelect>
      <FormErrorMessage textColor={errorColorKey}>
        {String(error?.message)}
      </FormErrorMessage>
    </FormControl>
  );
};

export default Select;
