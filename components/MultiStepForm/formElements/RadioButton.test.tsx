import { fireEvent, render, screen } from '@testing-library/react';

import RadioButton from './RadioButton';

describe('MultiStepForm RadioButton', () => {
  const register = jest.fn().mockReturnValue({
    onChange: jest.fn(),
    ref: jest.fn()
  });
  const setValue = jest.fn();
  const trigger = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the label and the radio options, and register the field', () => {
    render(
      <RadioButton
        type="radio-button"
        name="test_radio"
        label="Test Radio:"
        isRequired={false}
        register={register}
        setValue={setValue}
        trigger={trigger}
        options={[
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
      />
    );

    const label = screen.getByText('Test Radio:');
    expect(label).toBeInTheDocument();

    const option1 = screen.getByText('Option 1');
    const option2 = screen.getByText('Option 2');
    expect(option1).toBeInTheDocument();
    expect(option2).toBeInTheDocument();

    expect(register).toHaveBeenCalledWith('test_radio');
  });

  it('should call setValue and trigger when a radio option is selected', () => {
    render(
      <RadioButton
        type="radio-button"
        name="test_radio"
        label="Test Radio:"
        isRequired={false}
        register={register}
        setValue={setValue}
        trigger={trigger}
        options={[
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
      />
    );

    const option1Radio = screen.getByTestId('radio-option-option1');
    fireEvent.click(option1Radio);

    expect(setValue).toHaveBeenCalledWith('test_radio', 'option1');
    expect(trigger).toHaveBeenCalledWith('test_radio');
  });

  it('should display error message when error is provided', () => {
    render(
      <RadioButton
        type="radio-button"
        name="test_radio"
        label="Test Radio:"
        isRequired={true}
        register={register}
        setValue={setValue}
        trigger={trigger}
        options={[
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        error={{ message: 'This field is required' }}
      />
    );

    const errorMessage = screen.getByTestId('error-test_radio');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage).toHaveTextContent('This field is required');
  });

  it('should set default value when provided', () => {
    render(
      <RadioButton
        type="radio-button"
        name="test_radio"
        label="Test Radio:"
        isRequired={false}
        register={register}
        setValue={setValue}
        trigger={trigger}
        defaultValue="option2"
        options={[
          { value: 'option1', label: 'Option 1' },
          { value: 'option2', label: 'Option 2' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
      />
    );

    // The RadioGroup should have the default value set
    const radioGroup = screen.getByRole('radiogroup');
    expect(radioGroup).toBeInTheDocument();
  });
});
