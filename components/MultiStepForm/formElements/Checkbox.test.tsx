import { fireEvent, render, screen } from '@utils/test-utils';

import Check<PERSON><PERSON>ield from './Checkbox';

describe('MultiStepForm Checkbox', () => {
  const register = jest.fn().mockReturnValue({
    onChange: jest.fn(),
    ref: jest.fn(),
    name: 'test_checkbox'
  });

  it('should render the checkbox with label and register the field', () => {
    render(
      <CheckboxField
        type="checkbox"
        name="test_checkbox"
        label="Test Checkbox:"
        isRequired={false}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const label = screen.getByText('Test Checkbox:');
    expect(label).toBeInTheDocument();

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeInTheDocument();

    // Verify ARIA attributes
    const labelId = checkbox.getAttribute('aria-labelledby');
    expect(labelId).toBeTruthy();
    const labelElement = screen.getByText('Test Checkbox:');
    expect(labelElement).toHaveAttribute('id', labelId);
  });

  it('should render tooltip when infoText is provided', () => {
    const tooltipText = 'This is helpful information';
    render(
      <CheckboxField
        type="checkbox"
        name="test_checkbox"
        label="Test Checkbox:"
        isRequired={false}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
        infoText={tooltipText}
      />
    );

    const infoIcon = screen.getByRole('presentation');
    expect(infoIcon).toBeInTheDocument();

    fireEvent.mouseEnter(infoIcon);

    const tooltip = screen.getByText(tooltipText);
    expect(tooltip).toBeInTheDocument();
  });

  it('should not render tooltip when infoText is not provided', () => {
    render(
      <CheckboxField
        type="checkbox"
        name="test_checkbox"
        label="Test Checkbox:"
        isRequired={false}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const infoIcon = screen.queryByRole('presentation');
    expect(infoIcon).not.toBeInTheDocument();
  });

  it('should handle checkbox state changes', () => {
    const onChange = jest.fn();
    register.mockReturnValue({
      onChange,
      ref: jest.fn(),
      name: 'test_checkbox'
    });

    render(
      <CheckboxField
        type="checkbox"
        name="test_checkbox"
        label="Test Checkbox:"
        isRequired={false}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    expect(onChange).toHaveBeenCalled();
  });

  it('should have correct accessibility attributes', () => {
    render(
      <CheckboxField
        type="checkbox"
        name="test_checkbox"
        label="Test Checkbox:"
        isRequired={true}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const checkbox = screen.getByRole('checkbox');
    const labelId = checkbox.getAttribute('aria-labelledby');
    expect(labelId).toBeTruthy();
    expect(checkbox).toHaveAttribute('aria-required', 'true');

    const formGroup = screen.getByRole('group');
    expect(formGroup).toBeInTheDocument();
  });
});
