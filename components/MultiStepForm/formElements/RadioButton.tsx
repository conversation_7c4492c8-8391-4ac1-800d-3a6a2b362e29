import {
  FormControl,
  FormErrorMessage,
  FormLabel
} from '@chakra-ui/form-control';
import { Box, Grid } from '@chakra-ui/layout';
import { RadioGroup } from '@chakra-ui/react';
import Tooltip from '@components/Tooltip/Tooltip';
import { useId, useState } from 'react';

import { RadioButtonFieldConfig } from '../types';
import { FormFieldCommonProps } from './FormField';

type Props = RadioButtonFieldConfig & FormFieldCommonProps;

const RadioButton: React.FC<Props> = ({
  error,
  errorColorKey,
  entriesColorKey,
  label,
  name,
  options,
  isRequired,
  register,
  setValue,
  trigger,
  infoText,
  defaultValue
}) => {
  const labelId = useId();
  const { onChange, ref, ...registerProps } = register(name);
  const [selectedValue, setSelectedValue] = useState(defaultValue || '');

  const handleChange = (value: string) => {
    setSelectedValue(value);
    setValue(name, value);
    trigger(name);
    onChange({ target: { name, value } });
  };

  return (
    <FormControl
      fontSize="md"
      isRequired={isRequired}
      isInvalid={Boolean(error)}
      pt={2}
      role="group"
    >
      <Box display="flex" alignItems="center" gap={2} mb={4}>
        <FormLabel margin={0} id={labelId}>
          {label}
        </FormLabel>
        {infoText && <Tooltip text={infoText} />}
      </Box>

      <RadioGroup
        value={selectedValue}
        onChange={handleChange}
        aria-labelledby={labelId}
        {...registerProps}
      >
        <Grid templateColumns="repeat(2, 1fr)" gap={4}>
          {(options || []).map(({ value, label: optionLabel }, index) => {
            const isSelected = selectedValue === value;
            return (
              <Box
                key={`${index}-${value}`}
                as="label"
                cursor="pointer"
                borderWidth="1px"
                borderColor={isSelected ? 'blue.500' : entriesColorKey}
                borderRadius="md"
                p={2}
                textAlign="center"
                transition="all 0.2s"
                bg={isSelected ? 'blue.50' : 'white'}
                fontWeight={isSelected ? 'semibold' : 'normal'}
                _hover={{
                  borderColor: isSelected ? 'blue.500' : 'blue.300',
                  bg: isSelected ? 'blue.50' : 'blue.25'
                }}
              >
                <input
                  type="radio"
                  name={name}
                  value={value || ''}
                  checked={isSelected}
                  onChange={() => handleChange(value || '')}
                  data-testid={`radio-option-${value}`}
                  style={{
                    position: 'absolute',
                    opacity: 0,
                    width: 0,
                    height: 0
                  }}
                />
                {optionLabel}
              </Box>
            );
          })}
        </Grid>
      </RadioGroup>

      {error && (
        <FormErrorMessage
          textColor={errorColorKey}
          data-testid={`error-${name}`}
        >
          {String(error?.message)}
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

export default RadioButton;
