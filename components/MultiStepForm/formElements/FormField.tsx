import {
  UseFormRegister,
  UseFormSetValue,
  UseFormTrigger
} from 'react-hook-form';

import { FieldConfig, FieldErrorDetails } from '../types';
import CheckboxField from './Checkbox';
import Input from './Input';
import RadioButton from './RadioButton';
import Select from './Select';

export type FormFieldCommonProps = {
  entriesColorKey: string;
  error?: FieldErrorDetails;
  errorColorKey: string;
  register: UseFormRegister<any>;
  setValue: UseFormSetValue<any>;
  trigger: UseFormTrigger<any>;
};

type Props = FieldConfig & FormFieldCommonProps;

const FormField: React.FC<Props> = (props) => {
  switch (props.type) {
    case 'input':
      return <Input {...props} />;
    case 'select':
      return <Select {...props} />;
    case 'checkbox':
      return <CheckboxField {...props} />;
    case 'radio-button':
      return <RadioButton {...props} />;
    default:
      return null;
  }
};

export default FormField;
