import { FormControl, FormErrorMessage } from '@chakra-ui/form-control';
import { Box } from '@chakra-ui/layout';
import styles from '@components/Checkbox/Checkbox.module.css';
import formStyles from '@components/MultiStepForm/formElements/Checkbox.module.css';
import Tooltip from '@components/Tooltip/Tooltip';
import { useId } from 'react';

import { CheckboxFieldConfig } from '../types';
import { FormFieldCommonProps } from './FormField';

type Props = CheckboxFieldConfig & FormFieldCommonProps;

const CheckboxField: React.FC<Props> = ({
  error,
  errorColorKey,
  entriesColorKey,
  label,
  name,
  isRequired,
  register,
  infoText
}) => {
  const labelId = useId();
  const { onChange, ref, ...registerProps } = register(name, {
    setValueAs: (value) => value === 'true' || value === true || false
  });

  return (
    <FormControl
      fontSize="md"
      isRequired={isRequired}
      isInvalid={Boolean(error)}
      pt={2}
      role="group"
    >
      <Box display="flex" alignItems="center" gap={2}>
        <label
          className={`${styles.checkboxContainer} ${formStyles.container}`}
        >
          <input
            type="checkbox"
            className={styles.checkboxInput}
            aria-labelledby={labelId}
            aria-required={isRequired}
            ref={ref}
            onChange={onChange}
            {...registerProps}
          />
          <span
            className={`${styles.checkmark} ${formStyles.checkboxmark}`}
            aria-hidden="true"
          ></span>
          <span
            id={labelId}
            className={`${styles.checkboxLabel} ${formStyles.label}`}
          >
            {label}
          </span>
        </label>
        {infoText && <Tooltip text={infoText} />}
      </Box>
      {error && (
        <FormErrorMessage
          textColor={errorColorKey}
          data-testid={`error-${name}`}
        >
          {String(error?.message)}
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

export default CheckboxField;
