import { fireEvent, render, screen } from '@utils/test-utils';

import Select from './Select';

describe('MultiStepForm Select', () => {
  const register = jest.fn();

  it('should render the label and the options, and register the field', () => {
    render(
      <Select
        type="select"
        name="test_select"
        label="Test Select:"
        isRequired={false}
        register={register}
        options={[
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const label = screen.getByText('Test Select:');
    expect(label).toBeInTheDocument();

    const options = screen.getAllByTestId('select-option');
    expect(options.length).toEqual(2);
    expect(options[0]).toHaveTextContent('Yes');
    expect(options[1]).toHaveTextContent('No');

    expect(register).toHaveBeenCalledWith('test_select');
  });

  describe('when there is a validation error', () => {
    it('should render the error message', () => {
      const error = 'There is something wrong with this value...';
      render(
        <Select
          type="select"
          name="test_select"
          isRequired={false}
          register={register}
          options={[]}
          error={{ message: error }}
          entriesColorKey="gray.500"
          errorColorKey="red.500"
          setValue={jest.fn()}
          trigger={jest.fn()}
        />
      );

      const errorMessage = screen.getByText(error);
      expect(errorMessage).toBeInTheDocument();
    });
  });

  it('should render tooltip when infoText is provided', () => {
    const tooltipText = 'This is helpful information';
    render(
      <Select
        type="select"
        name="test_select"
        label="Test Select:"
        isRequired={false}
        register={register}
        options={[
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
        infoText={tooltipText}
      />
    );

    const infoIcon = screen.getByRole('presentation');
    expect(infoIcon).toBeInTheDocument();

    fireEvent.mouseEnter(infoIcon);

    const tooltip = screen.getByText(tooltipText);
    expect(tooltip).toBeInTheDocument();
  });

  it('should not render tooltip when infoText is not provided', () => {
    render(
      <Select
        type="select"
        name="test_select"
        label="Test Select:"
        isRequired={false}
        register={register}
        options={[
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const infoIcon = screen.queryByRole('presentation');
    expect(infoIcon).not.toBeInTheDocument();
  });
});
