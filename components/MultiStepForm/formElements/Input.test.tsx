import { fireEvent, render, screen } from '@utils/test-utils';

import Input from './Input';

describe('MultiStepForm Input', () => {
  const register = jest.fn();

  it('should render the label and an input with the provided name, and register the field', () => {
    render(
      <Input
        type="input"
        inputType="generic"
        name="test_input"
        label="Test Input:"
        placeholder="Type something here..."
        isRequired={false}
        register={register}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        setValue={jest.fn()}
        trigger={jest.fn()}
      />
    );

    const label = screen.getByText('Test Input:');
    expect(label).toBeInTheDocument();

    const input = screen.getByPlaceholderText('Type something here...');
    expect(input).toBeInTheDocument();

    expect(register).toHaveBeenCalledWith('test_input');
  });

  describe('when there is a validation error', () => {
    it('should render the error message', () => {
      const error = 'There is something wrong with this value...';
      render(
        <Input
          type="input"
          inputType="generic"
          name="test_input"
          isRequired={false}
          register={register}
          error={{ message: error }}
          entriesColorKey="gray.500"
          errorColorKey="red.500"
          setValue={jest.fn()}
          trigger={jest.fn()}
        />
      );

      const errorMessage = screen.getByText(error);
      expect(errorMessage).toBeInTheDocument();
    });
  });

  it('should render tooltip when infoText is provided', () => {
    const tooltipText = 'This is helpful information';
    render(
      <Input
        type="input"
        inputType="generic"
        name="test_input"
        label="Test Input:"
        isRequired={false}
        register={jest.fn()}
        setValue={jest.fn()}
        trigger={jest.fn()}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
        infoText={tooltipText}
      />
    );

    const infoIcon = screen.getByRole('presentation');
    expect(infoIcon).toBeInTheDocument();

    fireEvent.mouseEnter(infoIcon);

    const tooltip = screen.getByText(tooltipText);
    expect(tooltip).toBeInTheDocument();
  });

  it('should not render tooltip when infoText is not provided', () => {
    render(
      <Input
        type="input"
        inputType="generic"
        name="test_input"
        label="Test Input:"
        isRequired={false}
        register={jest.fn()}
        setValue={jest.fn()}
        trigger={jest.fn()}
        entriesColorKey="gray.500"
        errorColorKey="red.500"
      />
    );

    const infoIcon = screen.queryByRole('presentation');
    expect(infoIcon).not.toBeInTheDocument();
  });
});
