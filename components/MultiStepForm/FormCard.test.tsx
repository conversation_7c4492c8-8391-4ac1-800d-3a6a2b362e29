import { fireEvent, render, screen, waitFor } from '@utils/test-utils';

import FormCard from './FormCard';
import { StepConfig } from './types';

describe('FormCard', () => {
  const mockRegister = jest.fn();
  const mockSetValue = jest.fn();
  const mockTrigger = jest.fn();
  const mockOnSubmit = jest.fn();

  const defaultProps = {
    errors: {},
    form: {
      cta: {
        label: 'Submit',
        formatting: {
          field: 'custom',
          color: 'blue',
          size: 'md',
          variant: 'solid'
        }
      },
      backgroundColor: { color: 'white', range: '500' },
      entriesColor: { color: 'gray', range: '700' },
      errorColor: { color: 'red', range: '500' },
      fields: [
        {
          type: 'input',
          inputType: 'generic',
          name: 'fullName',
          label: 'Full Name',
          placeholder: 'Enter your name',
          isRequired: true,
          isRequiredErrorMessage: undefined
        },
        {
          type: 'input',
          inputType: 'email',
          name: 'email',
          label: 'Email',
          placeholder: 'Enter your email',
          isRequired: true,
          isRequiredErrorMessage: undefined
        }
      ],
      headline: 'Test Form',
      legalDisclosure: '<p>Legal text here</p>'
    } as StepConfig['form'],
    showErrorsOfOtherSteps: false,
    errorMessageOnValidationError: 'Please fix the errors above',
    isSubmitting: false,
    onSubmit: mockOnSubmit,
    register: mockRegister,
    setValue: mockSetValue,
    trigger: mockTrigger
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockTrigger.mockResolvedValue(true);
  });

  it('renders form with all elements', () => {
    render(<FormCard {...defaultProps} />);

    expect(screen.getByText('Test Form')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Legal text here')).toBeInTheDocument();
  });

  it('renders form fields with proper registration', () => {
    render(<FormCard {...defaultProps} />);

    expect(mockRegister).toHaveBeenCalledWith('fullName');
    expect(mockRegister).toHaveBeenCalledWith('email');
  });

  it('shows error message when form has errors', () => {
    const propsWithError = {
      ...defaultProps,
      formError: 'Something went wrong'
    };

    render(<FormCard {...propsWithError} />);
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('shows validation error message when there are errors on other steps', () => {
    const propsWithOtherStepErrors = {
      ...defaultProps,
      showErrorsOfOtherSteps: true,
      errors: {
        otherField: { type: 'required', message: 'Required field' }
      }
    };

    render(<FormCard {...propsWithOtherStepErrors} />);
    expect(screen.getByText('Please fix the errors above')).toBeInTheDocument();
  });

  it('handles form submission with validation', async () => {
    render(<FormCard {...defaultProps} />);

    const form = screen.getByTestId('form-card');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalledWith(['fullName', 'email']);
      expect(mockOnSubmit).toHaveBeenCalled();
    });
  });

  it('does not submit form when validation fails', async () => {
    mockTrigger.mockResolvedValueOnce(false);
    render(<FormCard {...defaultProps} />);

    const form = screen.getByTestId('form-card');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalled();
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  it('renders submit button in loading state', () => {
    const propsWithSubmitting = {
      ...defaultProps,
      isSubmitting: true
    };

    render(<FormCard {...propsWithSubmitting} />);
    expect(screen.getByRole('button')).toHaveAttribute('data-loading');
  });

  it('renders form without legal disclosure when not provided', () => {
    const propsWithoutLegal = {
      ...defaultProps,
      form: {
        ...defaultProps.form,
        legalDisclosure: undefined
      }
    };

    render(<FormCard {...propsWithoutLegal} />);
    expect(screen.queryByText('Legal text here')).not.toBeInTheDocument();
  });

  describe('help text section', () => {
    it('renders help text section when helpText is provided', () => {
      const propsWithHelpText = {
        ...defaultProps,
        form: {
          ...defaultProps.form,
          helpText:
            '<p>This information helps us find the best care options for you.</p>'
        }
      };

      render(<FormCard {...propsWithHelpText} />);

      expect(
        screen.getByText(
          'This information helps us find the best care options for you.'
        )
      ).toBeInTheDocument();
    });

    it('renders custom help text header when helpTextHeader is provided', () => {
      const propsWithCustomHeader = {
        ...defaultProps,
        form: {
          ...defaultProps.form,
          helpText:
            'This information helps us find the best care options for you.',
          helpTextHeader: 'Why we ask for this information:'
        }
      };

      render(<FormCard {...propsWithCustomHeader} />);

      expect(
        screen.getByText('Why we ask for this information:')
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          'This information helps us find the best care options for you.'
        )
      ).toBeInTheDocument();
    });
  });
});
