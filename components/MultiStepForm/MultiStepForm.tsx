'use client';

import { Box, Container, VStack } from '@chakra-ui/react';
import StepImage from '@components/Image/Image';
import { PageContext } from '@components/LayoutStructure/Contexts';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { FEATURE_FLAGS } from '~/constants/features';
import SiteContext from '~/contexts/SiteContext';
import useClientIP from '~/hooks/useClientIP';
import useMultiStepFormEventTracking from '~/hooks/useMultiStepFormEventsTracking';
import useTracking from '~/hooks/useTracking';
import { Metadata } from '~/types/Magnolia';
import { Color } from '~/utils/getColor';

import FormProgressBar from './FormProgressBar';
import {
  getDefaultValues,
  getHiddenValues,
  getPropsToDoNotSubmit,
  getResolver,
  parseValues,
  postMultiStepForm
} from './MultiStepForm.utils';
import Step from './Step';
import { HiddenField, MagnoliaMultiStepForm, StepConfig } from './types';
import ImageBehindForm from './variants/ImageBehindForm';

export interface MultiStepFormData {
  id?: string;
  pagination: {
    active?: boolean;
    color?: Color;
  };
  shouldShowHeroBehindFormLayout?: boolean;
  steps: StepConfig[];
  hiddenFields: HiddenField[];
}

export interface MultiStepFormProps {
  data: MultiStepFormData;
  errorMessages: MagnoliaMultiStepForm['errorMessages'];
  formType: MagnoliaMultiStepForm['formType'];
  metadata: Pick<Metadata, '@id'>;
  thankYou: MagnoliaMultiStepForm['thankYou'];
  className?: string;
}

export const MultiStepForm: React.FC<MultiStepFormProps> = ({
  data,
  errorMessages = {},
  formType,
  metadata,
  thankYou = {
    path: '/thank-you/'
  },
  className
}) => {
  // This hook is here ONLY to track experiment viewed events
  useFeatureIsOn(FEATURE_FLAGS.SEM_HERO_BEHIND_FORM_LAYOUT);

  // Also track the radio experiment for analytics (but don't use for logic)
  useFeatureIsOn(FEATURE_FLAGS.SEM_BUDGET_QUESTION_RADIO);

  const { shouldShowHeroBehindFormLayout } = data;

  const {
    errorOnSubmit = 'Something went wrong. Please try again.',
    validationError = 'Please review the previous steps, there is an error on at least one of them.'
  } = errorMessages;

  const clientIP = useClientIP();
  const { anonId, trackingNotes } = useTracking();
  const siteContext = useContext(SiteContext);
  const pageContext = useContext(PageContext);
  const { formInstanceId, trackStepSubmitted, trackFormSuccessfullySubmitted } =
    useMultiStepFormEventTracking({
      metadata,
      formType,
      stepsConfig: data.steps
    });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formError, setFormError] = useState('');

  const defaultValues = useMemo(
    () => getDefaultValues(data.steps),
    [data.steps]
  );
  const hiddenValues = useMemo(
    () => getHiddenValues(data.hiddenFields),
    [data.hiddenFields]
  );
  const doNotSubmitPropNames = useMemo(
    () => getPropsToDoNotSubmit(data.steps, data.hiddenFields),
    [data.hiddenFields, data.steps]
  );
  const resolver = useMemo(() => getResolver(data.steps), [data.steps]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    watch,
    getValues,
    setValue,
    reset
  } = useForm({
    mode: 'onBlur',
    resolver,
    defaultValues
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const onStepChange = useCallback(
    (newStep: number) => {
      window.location.hash = `step-${newStep + 1}`;
      setCurrentStep(newStep);
    },
    [setCurrentStep]
  );

  // The HTML event used to detect back/forward changes (`hashchange`) does not
  // trigger when the hash changes from/to an empty hash, so we need to make sure
  // the page will always start with a hash

  useEffect(() => {
    const onHashChanged = () => {
      const match = window.location.hash.match(/^#step-(\d+)$/);
      if (match) {
        const newStep = Math.max(0, Number(match[1]) - 1);
        if (newStep < data.steps.length) {
          setCurrentStep(newStep);
        }
      }
    };

    window.addEventListener('hashchange', onHashChanged);
    return () => {
      window.removeEventListener('hashchange', onHashChanged);
    };
  }, [data.steps.length, onStepChange]);

  const onSubmit = handleSubmit(async (data) => {
    setIsSubmitting(true);

    const parsedHiddenValues = parseValues({
      originalValues: hiddenValues,
      values: {
        form: data,
        client: {
          ip: clientIP || '0.0.0.0',
          browser: navigator.userAgent,
          formInstanceId,
          anonId,
          trackingNotes
        }
      }
    });

    const allData = {
      ...parsedHiddenValues,
      ...data
    };

    // JIRA: https://caring.atlassian.net/browse/CORE-1859
    // Ticket is for cleaning up all these deletions, we should
    // be building up what we _do_ want to send instead.
    const filteredData = { ...allData };
    for (let i = 0; i < doNotSubmitPropNames.length; i++) {
      const doNotSubmitProp = doNotSubmitPropNames[i];
      delete filteredData[doNotSubmitProp];
    }

    // JIRA: https://caring.atlassian.net/browse/CORE-1859
    // Remove the flags that are unrelated to inquiry API calls
    delete filteredData.shouldShowHeroBehindFormLayout;

    const domain = siteContext.site?.path || '';
    // [SEM Landing pages] Inquiry submission segment event fails in a safari browser.
    // https://caring.atlassian.net/browse/CORE-1574
    // The issue is that the form submission event is not being sent to Segment in Safari browser.
    // Fixed by moving the call to trackFormSuccessfullySubmitted before the postMultiStepForm call
    // No need to wait for the response of postMultiStepForm to track the form submission event.
    // no tests needed to be updated
    trackFormSuccessfullySubmitted(filteredData);

    const response = await postMultiStepForm({
      ...filteredData,
      formId: metadata['@id'],
      path: pageContext?.page['@path'] ?? '',
      domain
    });

    if (response.success) {
      setFormError('');
      if (thankYou.path) {
        window.location.assign(
          `${thankYou.path}?${new URLSearchParams(allData)}`
        );
      }
    } else {
      setFormError(errorOnSubmit);
      setIsSubmitting(false);
    }
  });

  const images = useMemo(() => {
    if (shouldShowHeroBehindFormLayout) {
      return <ImageBehindForm config={data} currentStep={currentStep} />;
    }

    return data.steps.map(
      (step, index) =>
        step.image && (
          <Box
            key={index}
            display={index === currentStep ? 'block' : 'none'}
            as="div"
            data-testid={`hero-image-${index}`}
          >
            <StepImage {...step.image} />
          </Box>
        )
    );
  }, [data, shouldShowHeroBehindFormLayout, currentStep]);

  return (
    <>
      {shouldShowHeroBehindFormLayout && images}
      <Container maxWidth="container.xl" className={className}>
        <VStack pb={16}>
          {data?.steps?.length > 1 && (
            <FormProgressBar
              currentStep={currentStep}
              totalSteps={data.steps.length}
              setCurrentStep={onStepChange}
              errors={errors}
              trigger={trigger}
              currentStepFields={data.steps[currentStep].form.fields.map(
                (field) => field.name
              )}
            />
          )}
          {data?.steps?.map((step, index) => (
            <Step
              key={index}
              headline={step.headline}
              subHeadline={step.subHeadline}
              active={index === currentStep}
              watchFormValues={watch}
              testId={`step-${index + 1}`}
              errors={errors}
              formError={formError}
              showErrorsOfOtherSteps={index === data.steps.length - 1}
              errorMessageOnValidationError={validationError}
              form={step.form}
              register={register}
              setValue={setValue}
              trigger={trigger}
              isSubmitting={isSubmitting}
              onSubmit={() => {
                if (Object.keys(errors).length > 0) {
                  return;
                }

                trackStepSubmitted(currentStep, getValues);
                if (currentStep === data.steps.length - 1) {
                  onSubmit();
                } else {
                  onStepChange(currentStep + 1);
                }
              }}
            />
          ))}
        </VStack>
      </Container>
      {!shouldShowHeroBehindFormLayout && images}
    </>
  );
};

export default MultiStepForm;
