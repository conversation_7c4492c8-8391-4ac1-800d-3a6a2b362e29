import { FEATURE_FLAGS } from '@constants/features';
import { getFeatureOverrideFromContextServerSide } from '@lib/featureOverride';
import { evaluateGrowthbookFlagServerSide } from '@lib/growthbookServerSideFeatures';
import { getDiceRollUuidFromContext } from '@utils/diceRollUuid';
import { GetServerSidePropsContext } from 'next';
import { ParsedUrlQuery } from 'querystring';

import { getNodes } from '~/utils';
import { Parser } from '~/utils/parser';

import { MultiStepFormData } from './MultiStepForm';
import {
  FieldConfig,
  HiddenField,
  MagnoliaFieldConfig,
  MagnoliaMultiStepForm,
  MagnoliaMultiStepFormSubmissionProps,
  MagnoliaStep,
  MagnoliaStepImage,
  SelectOption,
  StepConfig
} from './types';
import { BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE } from './variants/BudgetQuestionRadioConfig';

export const serverSideOnlyProps: Array<
  keyof MagnoliaMultiStepFormSubmissionProps
> = ['postUrl', 'postFormat', 'includeCaringPartnerToken'];

export const getServerSideComponentProps = async (
  componentConfig: MagnoliaMultiStepForm,
  context: GetServerSidePropsContext
): Promise<MultiStepFormData> => {
  const shouldShowHeroBehindFormLayout = await evaluateGrowthbookFlagServerSide(
    {
      id: getDiceRollUuidFromContext(context),
      pathname: context.req.url,
      featureOverride: getFeatureOverrideFromContextServerSide(context)
    },
    FEATURE_FLAGS.SEM_HERO_BEHIND_FORM_LAYOUT
  );

  const shouldUseBudgetQuestionRadioExperiment =
    await evaluateGrowthbookFlagServerSide(
      {
        id: getDiceRollUuidFromContext(context),
        pathname: context.req.url,
        featureOverride: getFeatureOverrideFromContextServerSide(context)
      },
      FEATURE_FLAGS.SEM_BUDGET_QUESTION_RADIO
    );

  const { steps, hiddenFields } = transformMagnoliaMultiStepForm(
    componentConfig,
    context.query,
    shouldUseBudgetQuestionRadioExperiment
  );

  return {
    id: componentConfig['@id'],
    hiddenFields,
    shouldShowHeroBehindFormLayout,
    steps,
    pagination: {
      active: !!context.preview,
      color: componentConfig.formEntriesColor
    }
  };
};

export const transformMagnoliaMultiStepForm = (
  componentConfig: MagnoliaMultiStepForm,
  queryParams: ParsedUrlQuery,
  shouldUseBudgetQuestionRadioExperiment?: boolean
): { steps: StepConfig[]; hiddenFields: HiddenField[] } => {
  const stepsConfig = componentConfig.steps
    ? getNodes<MagnoliaStep>(componentConfig.steps)
    : [];
  const hiddenFields = componentConfig.hiddenEntries
    ? getNodes<HiddenField>(componentConfig.hiddenEntries)
    : [];

  let steps = stepsConfig.map<StepConfig>((stepConfig) => ({
    headline: stepConfig.headline
      ? {
          ...stepConfig.headline,
          text: parseText(queryParams, stepConfig.headline.text)
        }
      : stepConfig.headline,
    subHeadline: stepConfig.subHeadline
      ? {
          ...stepConfig.subHeadline,
          text: parseText(queryParams, stepConfig.subHeadline.text)
        }
      : stepConfig.subHeadline,
    image:
      stepConfig.image.switchable.field === 'componentDefault'
        ? transformMagnoliaImage(componentConfig?.image)
        : transformMagnoliaImage(stepConfig?.image),
    form: {
      ...stepConfig.form,
      headline: parseText(queryParams, stepConfig?.form?.headline),
      backgroundColor: componentConfig?.formBackgroundColor,
      helpBackgroundColor: componentConfig?.helpBackgroundColor,
      entriesColor: componentConfig?.formEntriesColor,
      errorColor: componentConfig?.errorColor,
      display:
        componentConfig?.formType?.field === 'inquiry'
          ? componentConfig?.formType?.display
          : undefined,
      fields: transformMagnoliaStep(stepConfig).fields
    }
  }));

  // Apply budget question radio experiment override if feature flag is enabled
  if (shouldUseBudgetQuestionRadioExperiment && steps.length > 1) {
    steps = steps.map((step, index) =>
      // Override step 2 with the budget form variant.
      index === 1
        ? {
            ...step,
            form: {
              ...step.form,
              ...BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form,
              fields: BUDGET_QUESTION_RADIO_FORM_VARIANT_OVERRIDE.form
                .fields as StepConfig['form']['fields'],
              backgroundColor: componentConfig?.formBackgroundColor,
              helpBackgroundColor: componentConfig?.helpBackgroundColor,
              entriesColor: componentConfig?.formEntriesColor,
              errorColor: componentConfig?.errorColor,
              display:
                componentConfig?.formType?.field === 'inquiry'
                  ? componentConfig?.formType?.display
                  : undefined
            }
          }
        : step
    ) as StepConfig[];
  }

  return { steps, hiddenFields };
};

const transformMagnoliaImage = (
  image?: MagnoliaStepImage
): MagnoliaStepImage | undefined => {
  switch (image?.switchable.field) {
    case 'none':
      return undefined;
    default:
      return image;
  }
};

const transformMagnoliaStep = (
  stepConfig: MagnoliaStep
): { fields: Array<FieldConfig> } => {
  const formEntries = getNodes<MagnoliaFieldConfig>(stepConfig.form.entryList);
  // @ts-ignore TS cannot handle deconstructing with type-specific props, so it gets
  // confused with `formEntry.entryType.field`, as it could be both "input" and "select".
  // We're safe here because for each value of this prop, the other fields will match
  // the expected props for each type
  const fields = formEntries.map<FieldConfig>((formEntry) => {
    const {
      entryType,
      name,
      label,
      defaultValue,
      isRequired,
      doNotSubmit,
      infoText
    } = formEntry;
    const entryIsRequired = isRequired.field === 'true';
    const entryTypeProps = transformMagnoliaEntryType(entryType);
    return {
      ...entryTypeProps,
      type: entryType?.field,
      name,
      label,
      defaultValue,
      doNotSubmit,
      infoText,
      isRequired: entryIsRequired,
      isRequiredErrorMessage: entryIsRequired
        ? isRequired.isRequiredErrorMessage
        : undefined
    };
  });
  return { fields };
};

const parseText = (queryParams: ParsedUrlQuery, text?: string) => {
  if (!text) return text;
  return Parser({
    source: text,
    values: { url: queryParams }
  });
};

const transformMagnoliaEntryType = (
  entryType: MagnoliaFieldConfig['entryType']
): Partial<FieldConfig> => {
  switch (entryType.field) {
    case 'select':
      const options = getNodes<SelectOption>(entryType.options);
      return { options };
    case 'radio-button':
      const radioOptions = getNodes<SelectOption>(entryType.options);
      return { options: radioOptions };
    case 'input':
      const {
        inputType: { typeErrorMessage, typeMask, field: inputTypeField },
        placeholder
      } = entryType;
      return {
        inputType: inputTypeField,
        typeErrorMessage: typeErrorMessage,
        typeMask: typeMask?.field === 'true' ? typeMask.mask : undefined,
        placeholder: placeholder
      };
    case 'checkbox':
      return {};
    default:
      return {};
  }
};
