import { Text } from '@chakra-ui/react';

import { getTextElementProps } from './MultiStepForm.utils';
import { MagnoliaTextWithSizeAndStyle } from './types';

const TextWithSizeAndStyle: React.FC<MagnoliaTextWithSizeAndStyle> = ({
  text,
  textFormatting
}) => {
  return text ? (
    <Text className="magnolia-text" {...getTextElementProps(textFormatting)}>
      {text}
    </Text>
  ) : null;
};

export default TextWithSizeAndStyle;
