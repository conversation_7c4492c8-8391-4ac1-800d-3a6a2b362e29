import {
  getCtaButtonProps,
  getDefaultValues,
  getHiddenValues,
  getPropsToDoNotSubmit,
  getResolver,
  getTextElementProps
} from './MultiStepForm.utils';
import {
  FieldConfig,
  HiddenField,
  InputFieldConfig,
  MagnoliaFormConfig,
  MagnoliaTextWithSizeAndStyle,
  SelectFieldConfig,
  StepConfig
} from './types';

describe('MultiStepForm Utils', () => {
  const baseInput: InputFieldConfig = {
    type: 'input',
    inputType: 'generic',
    name: 'myInput',
    isRequired: false
  };

  const baseSelect: SelectFieldConfig = {
    type: 'select',
    options: [],
    name: 'mySelect',
    isRequired: false
  };

  const buildStepWithFields = (fields: FieldConfig[]): StepConfig => ({
    headline: {
      text: 'any',
      textFormatting: {
        field: 'default'
      }
    },
    subHeadline: {
      text: '',
      textFormatting: {
        field: 'default'
      }
    },
    form: {
      headline: 'any',
      cta: {
        label: 'any',
        formatting: {
          field: 'default'
        }
      },
      backgroundColor: {
        color: 'background',
        range: '50'
      },
      helpBackgroundColor: {
        color: 'background',
        range: '100'
      },
      entriesColor: {
        color: 'background',
        range: '400'
      },
      errorColor: {
        color: 'error',
        range: '500'
      },
      fields
    }
  });

  describe('getDefaultValues', () => {
    it('returns the default values of all the fields that have one', () => {
      const steps: StepConfig[] = [
        buildStepWithFields([
          {
            ...baseSelect,
            name: 'select_1_1',
            defaultValue: '1.1'
          },
          {
            ...baseInput,
            name: 'input_1_2',
            defaultValue: '1.2'
          },
          {
            ...baseInput,
            name: 'field_without_default_value_1'
          }
        ]),
        buildStepWithFields([
          {
            ...baseSelect,
            name: 'field_without_default_value_2'
          },
          {
            ...baseInput,
            name: 'input_2_2',
            defaultValue: '2.2'
          }
        ])
      ];

      const defaultValues = getDefaultValues(steps);

      expect(Object.keys(defaultValues)).toEqual([
        'select_1_1',
        'input_1_2',
        'input_2_2'
      ]);
      expect(defaultValues).toEqual({
        select_1_1: '1.1',
        input_1_2: '1.2',
        input_2_2: '2.2'
      });
    });
  });

  describe('getHiddenValues', () => {
    it('returns the default values of all the hidden fields', () => {
      const hiddenFields: HiddenField[] = [
        {
          name: 'hidden1',
          defaultValue: '1'
        },
        {
          name: 'hidden2',
          defaultValue: '2'
        },
        {
          name: 'emptyHidden',
          defaultValue: ''
        }
      ];

      const defaultValues = getHiddenValues(hiddenFields);

      expect(Object.keys(defaultValues)).toEqual([
        'hidden1',
        'hidden2',
        'emptyHidden'
      ]);
      expect(defaultValues).toEqual({
        hidden1: '1',
        hidden2: '2',
        emptyHidden: ''
      });
    });
  });

  describe('getPropsToDoNotSubmit', () => {
    it('returns the names of the props marked with "doNotSubmit", both steps entries and hidden entries', () => {
      const steps: StepConfig[] = [
        buildStepWithFields([
          {
            ...baseInput,
            name: 'do_not_submit_1',
            doNotSubmit: true
          },
          {
            ...baseInput,
            name: 'input_1'
          }
        ]),
        buildStepWithFields([
          {
            ...baseInput,
            name: 'input_2'
          },
          {
            ...baseInput,
            name: 'do_not_submit_2',
            doNotSubmit: true
          }
        ])
      ];

      const hiddenFields: HiddenField[] = [
        {
          name: 'hidden_1',
          defaultValue: ''
        },
        {
          name: 'hidden_do_not_submit',
          defaultValue: '',
          doNotSubmit: true
        }
      ];

      const doNotSubmitProps = getPropsToDoNotSubmit(steps, hiddenFields);

      expect(doNotSubmitProps).toEqual([
        'do_not_submit_1',
        'do_not_submit_2',
        'hidden_do_not_submit'
      ]);
    });
  });

  describe('getResolver', () => {
    const context = {};
    const options = {
      fields: {},
      shouldUseNativeValidation: false
    };

    const yesNoSelect: SelectFieldConfig = {
      ...baseSelect,
      options: [
        { label: '- Choose One -' },
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No' }
      ]
    };

    describe('when entry is an optional select entry', () => {
      const optionalSelect: SelectFieldConfig = {
        ...yesNoSelect,
        isRequired: false,
        name: 'optional_select'
      };

      it('returns a resolver that accepts an empty selection', async () => {
        const steps: StepConfig[] = [buildStepWithFields([optionalSelect])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { optional_select: '' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ optional_select: '' });
      });

      it('returns a resolver that accepts any of the option values', async () => {
        const steps: StepConfig[] = [buildStepWithFields([optionalSelect])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { optional_select: 'no' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ optional_select: 'no' });
      });
    });

    describe('when entry is a required select entry', () => {
      const requiredSelect: SelectFieldConfig = {
        ...yesNoSelect,
        isRequired: true,
        isRequiredErrorMessage: 'Please choose one of the options',
        name: 'required_select'
      };

      it('returns a resolver that rejects an empty selection', async () => {
        const steps: StepConfig[] = [buildStepWithFields([requiredSelect])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { required_select: '' },
          context,
          options
        );

        expect(errors).toMatchObject({
          required_select: expect.objectContaining({
            message: 'Please choose one of the options'
          })
        });
        expect(values).toEqual({});
      });

      it('returns a resolver that accepts any of the option values', async () => {
        const steps: StepConfig[] = [buildStepWithFields([requiredSelect])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { required_select: 'yes' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ required_select: 'yes' });
      });
    });

    describe('when entry is an optional input', () => {
      const optionalInput: InputFieldConfig = {
        ...baseInput,
        isRequired: false,
        name: 'optional_input'
      };

      it('returns a resolver that accepts an empty value', async () => {
        const steps: StepConfig[] = [buildStepWithFields([optionalInput])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { optional_input: '' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ optional_input: '' });
      });

      it('returns a resolver that accepts non-empty values', async () => {
        const steps: StepConfig[] = [buildStepWithFields([optionalInput])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { optional_input: 'any value' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ optional_input: 'any value' });
      });

      it('returns a resolver that rejects values longer than 50 chars', async () => {
        const steps: StepConfig[] = [buildStepWithFields([optionalInput])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { optional_input: 'x'.repeat(51) },
          context,
          options
        );

        expect(errors).toEqual({
          optional_input: expect.objectContaining({
            message: 'Value is too long'
          })
        });
        expect(values).toEqual({});
      });
    });

    describe('when entry is a required input', () => {
      const requiredInput: InputFieldConfig = {
        ...baseInput,
        isRequired: true,
        isRequiredErrorMessage: 'Please entry a value',
        name: 'required_input'
      };

      it('returns a resolver that rejects an empty value', async () => {
        const steps: StepConfig[] = [buildStepWithFields([requiredInput])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { required_input: '' },
          context,
          options
        );

        expect(errors).toMatchObject({
          required_input: expect.objectContaining({
            message: 'Please entry a value'
          })
        });
        expect(values).toEqual({});
      });

      it('returns a resolver that accepts non-empty values', async () => {
        const steps: StepConfig[] = [buildStepWithFields([requiredInput])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { required_input: '1' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ required_input: '1' });
      });
    });

    describe('when entry is an email input', () => {
      const emailEntry: InputFieldConfig = {
        ...baseInput,
        typeErrorMessage: 'Please enter a valid email address',
        name: 'emailEntry',
        inputType: 'email'
      };

      it('returns a resolver that rejects invalid email addresses', async () => {
        const steps: StepConfig[] = [buildStepWithFields([emailEntry])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { emailEntry: 'invalid@email' },
          context,
          options
        );

        expect(errors).toMatchObject({
          emailEntry: expect.objectContaining({
            message: 'Please enter a valid email address'
          })
        });
        expect(values).toEqual({});
      });

      it('returns a resolver that accepts valid email addresses', async () => {
        const steps: StepConfig[] = [buildStepWithFields([emailEntry])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { emailEntry: '<EMAIL>' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ emailEntry: '<EMAIL>' });
      });
      it('returns a resolver that accepts empty values when entry is not required', async () => {
        const steps: StepConfig[] = [
          buildStepWithFields([{ ...emailEntry, isRequired: false }])
        ];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { emailEntry: '' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ emailEntry: '' });
      });
    });

    describe('when entry is a phone input', () => {
      const phoneEntry: InputFieldConfig = {
        ...baseInput,
        typeErrorMessage: 'Please enter a valid US phone number',
        name: 'phoneEntry',
        inputType: 'phone'
      };

      it('returns a resolver that rejects invalid US phone numbers', async () => {
        const steps: StepConfig[] = [buildStepWithFields([phoneEntry])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { phoneEntry: '(11) 2222-3333' },
          context,
          options
        );

        expect(errors).toMatchObject({
          phoneEntry: expect.objectContaining({
            message: 'Please enter a valid US phone number'
          })
        });
        expect(values).toEqual({});
      });

      it('returns a resolver that accepts valid US phone numbers', async () => {
        const steps: StepConfig[] = [buildStepWithFields([phoneEntry])];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { phoneEntry: '(*************' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ phoneEntry: '(*************' });
      });

      it('returns a resolver that accepts empty values when entry is not required', async () => {
        const steps: StepConfig[] = [
          buildStepWithFields([{ ...phoneEntry, isRequired: false }])
        ];
        const resolver = getResolver(steps);
        const { values, errors } = await resolver(
          { phoneEntry: '' },
          context,
          options
        );

        expect(errors).toEqual({});
        expect(values).toEqual({ phoneEntry: '' });
      });

      describe('and it has a custom mask', () => {
        const internationalPhoneEntry: InputFieldConfig = {
          ...phoneEntry,
          typeMask: '+9 (*************',
          typeErrorMessage: 'Please enter a valid international phone number'
        };

        it('returns a resolver that rejects invalid phone numbers that do not match the provided regex', async () => {
          const steps: StepConfig[] = [
            buildStepWithFields([internationalPhoneEntry])
          ];
          const resolver = getResolver(steps);
          const { values, errors } = await resolver(
            { phoneEntry: '(*************' },

            context,
            options
          );

          expect(errors).toMatchObject({
            phoneEntry: expect.objectContaining({
              message: 'Please enter a valid international phone number'
            })
          });
          expect(values).toEqual({});
        });

        it('returns a resolver that accepts phone numbers that match the provided regex', async () => {
          const steps: StepConfig[] = [
            buildStepWithFields([internationalPhoneEntry])
          ];
          const resolver = getResolver(steps);
          const { values, errors } = await resolver(
            { phoneEntry: '+1 (*************' },
            context,
            options
          );

          expect(errors).toEqual({});
          expect(values).toEqual({ phoneEntry: '+1 (*************' });
        });
      });
    });

    describe('when there are multiple fields on multiple steps', () => {
      it('returns a resolver for all fields', async () => {
        const steps: StepConfig[] = [
          buildStepWithFields([
            { ...baseInput, isRequired: true, name: 'input_1_1' },
            { ...baseInput, isRequired: true, name: 'input_1_2' }
          ]),
          buildStepWithFields([
            { ...baseInput, isRequired: true, name: 'input_2_1' },
            { ...baseInput, isRequired: true, name: 'input_2_2' }
          ])
        ];

        const resolver = getResolver(steps);

        const validationWithErrors = await resolver(
          {
            input_1_1: 'string 1', // valid
            input_1_2: '', // invalid, empty value
            input_2_1: 3, // invalid, not a string
            input_2_2: 'string 4' // valid
          },
          context,
          options
        );

        expect(validationWithErrors.values).toEqual({});
        expect(Object.keys(validationWithErrors.errors)).toEqual([
          'input_1_2',
          'input_2_1'
        ]);

        const validationWithNoErrors = await resolver(
          {
            input_1_1: 'string 1',
            input_1_2: 'string 2',
            input_2_1: 'string 3',
            input_2_2: 'string 4'
          },
          context,
          options
        );

        expect(validationWithNoErrors.values).toEqual({
          input_1_1: 'string 1',
          input_1_2: 'string 2',
          input_2_1: 'string 3',
          input_2_2: 'string 4'
        });
        expect(validationWithNoErrors.errors).toEqual({});
      });
    });
  });

  describe('getTextElementProps', () => {
    const defaultTextFormatting = { fontSize: { base: 'md', md: 'lg' } };
    const fullyCustomizedText: MagnoliaTextWithSizeAndStyle['textFormatting'] =
      {
        field: 'custom',
        color: {
          color: 'red',
          colorRange: '500'
        },
        element: 'h4',
        sizeOnDesktop: 'sm',
        sizeOnMobile: 'xs',
        styles: {
          bold: true,
          italic: true
        }
      };

    it('returns the default styles when textFormatting is undefined', () => {
      expect(getTextElementProps()).toEqual(defaultTextFormatting);
    });

    // Make sure we ignore previously saved styles when author chooses "default"
    it('returns the default styles when textFormatting type is "default"', () => {
      const textFormatting: MagnoliaTextWithSizeAndStyle['textFormatting'] = {
        ...fullyCustomizedText,
        field: 'default'
      };
      expect(getTextElementProps(textFormatting)).toEqual(
        defaultTextFormatting
      );
    });

    it('returns customized styles when textFormatting type is "custom"', () => {
      expect(getTextElementProps(fullyCustomizedText)).toEqual({
        as: 'h4',
        color: 'red.500',
        fontSize: { base: 'xs', md: 'sm' },
        fontStyle: 'italic',
        fontWeight: 700
      });
    });
  });

  describe('getCtaButtonProps', () => {
    const defaultCtaFormatting = { colorScheme: 'secondary' };
    const fullyCustomizedCta: MagnoliaFormConfig['cta']['formatting'] = {
      field: 'custom',
      color: 'primary',
      size: 'xs',
      variant: 'outline'
    };

    it('returns the default styles when textFormatting is undefined', () => {
      expect(getCtaButtonProps()).toEqual(defaultCtaFormatting);
    });

    // Make sure we ignore previously saved styles when author chooses "default"
    it('returns the default styles when textFormatting type is "default"', () => {
      const ctaFormatting: MagnoliaFormConfig['cta']['formatting'] = {
        ...fullyCustomizedCta,
        field: 'default'
      };
      expect(getCtaButtonProps(ctaFormatting)).toEqual(defaultCtaFormatting);
    });

    it('returns customized styles when textFormatting type is "custom"', () => {
      expect(getCtaButtonProps(fullyCustomizedCta)).toEqual({
        colorScheme: 'primary',
        size: 'xs',
        variant: 'outline'
      });
    });
  });
});
