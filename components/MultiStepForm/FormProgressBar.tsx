import { Box, HStack, IconButton, Text } from '@chakra-ui/react';
import { UseFormTrigger } from 'react-hook-form';
import { MdArrowBackIosNew, MdArrowForwardIos } from 'react-icons/md';

import { Color, getColor } from '~/utils/getColor';

import { FieldErrorDetails } from './types';

interface FormProgressBarProps {
  currentStep: number;
  totalSteps: number;
  color?: Color;
  setCurrentStep: (newStep: number) => void;
  errors: {
    [fieldName: string]: FieldErrorDetails | undefined;
  };
  trigger: UseFormTrigger<any>;
  currentStepFields: string[];
}

const iconButtonStyles = {
  background: 'inherit',
  margin: '0',
  padding: '0',
  minW: '0',
  height: '4',
  _hover: { bg: 'transparent' },
  size: 'md'
};

const FormProgressBar: React.FC<FormProgressBarProps> = ({
  currentStep,
  totalSteps,
  color = {
    color: 'green',
    range: '600'
  },
  setCurrentStep,
  errors,
  trigger,
  currentStepFields
}) => {
  if (totalSteps <= 1) return null;

  const colorKey = getColor(color.color, color.range);
  const progress = ((currentStep + 1) / totalSteps) * 100;

  const validateCurrentStep = async (): Promise<boolean> => {
    const isValid = await trigger(currentStepFields);
    const currentStepErrors =
      Object.keys(errors ?? {}).filter((key) =>
        currentStepFields?.includes?.(key)
      ).length > 0;
    return isValid && !currentStepErrors;
  };

  const handleNextStep = async () => {
    const isValid = await validateCurrentStep();
    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  return (
    <Box width="100%" py="4">
      <HStack width="100%" spacing={2} align="center">
        <IconButton
          {...iconButtonStyles}
          hidden={currentStep === 0}
          onClick={() => setCurrentStep(currentStep - 1)}
          aria-label="Previous step"
          data-testid="prev-step-button"
        >
          <MdArrowBackIosNew color="gray.900" />
        </IconButton>

        <Box
          w="100%"
          h="4px"
          bg="gray.100"
          position="relative"
          overflow="hidden"
          borderRadius="12px"
          data-testid="progress-bar"
        >
          <Box
            position="absolute"
            left={0}
            top={0}
            h="100%"
            bg={colorKey}
            transition="width 0.3s ease-in-out"
            width={`${progress}%`}
            data-testid="progress-indicator"
          />
        </Box>

        <IconButton
          {...iconButtonStyles}
          hidden={currentStep === totalSteps - 1}
          onClick={handleNextStep}
          aria-label="Next step"
          data-testid="next-step-button"
        >
          <MdArrowForwardIos color="gray.900" />
        </IconButton>
      </HStack>

      <Text
        textAlign="start"
        fontSize="sm"
        color="gray.600"
        ml={currentStep === 0 ? '0' : '6'}
      >
        Step {currentStep + 1} of {totalSteps}
      </Text>
    </Box>
  );
};

export default FormProgressBar;
