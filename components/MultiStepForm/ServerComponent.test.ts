import { GetServerSidePropsContext } from 'next';

import { MagnoliaImage } from '~/types/Magnolia';

import {
  getServerSideComponentProps,
  transformMagnoliaMultiStepForm
} from './ServerComponent';
import {
  <PERSON>Field,
  MagnoliaFormConfig,
  MagnoliaInputFieldConfig,
  MagnoliaMultiStepForm,
  MagnoliaSelectFieldConfig,
  MagnoliaStep,
  MagnoliaStepImage,
  MagnoliaTextWithSizeAndStyle
} from './types';

jest.mock('@growthbook/growthbook', () => {
  return {
    GrowthBook: jest.fn().mockImplementation(() => {
      const features: Record<string, boolean> = {};
      return {
        init: jest.fn().mockResolvedValue(undefined),
        setAttributes: jest.fn(),
        isOn: jest.fn((key: string) => features[key] ?? false),
        evalFeature: jest.fn(() => undefined),
        __setFeature: (key: string, val: boolean) => {
          features[key] = val;
        }
      };
    })
  };
});

describe('Multi-Step Form server component', () => {
  const context = {
    req: { cookies: {}, headers: {} },
    res: { getHeader: () => '' }
  } as unknown as GetServerSidePropsContext;

  const emptyTextWithSizeAndStyle: MagnoliaTextWithSizeAndStyle = {
    text: '',
    textFormatting: { field: 'default' }
  };

  const emptyImage: MagnoliaStepImage = {
    switchable: { field: 'none' }
  };

  const emptyForm: MagnoliaFormConfig = {
    cta: {
      label: '',
      formatting: {
        field: 'default'
      }
    }
  };

  const emptyStep: MagnoliaStep = {
    headline: emptyTextWithSizeAndStyle,
    image: emptyImage,
    form: emptyForm,
    '@nodes': []
  };

  const emptyComponentConfig = {
    hiddenEntries: { '@nodes': [] },
    steps: { '@nodes': [] },
    image: emptyImage
  } as unknown as MagnoliaMultiStepForm;

  describe('getServerSideComponentProps', () => {
    describe('showPagination', () => {
      it('shows pagination when on preview mode', async () => {
        const contextWithPreviewMode = {
          preview: true,
          req: {
            cookies: {},
            headers: {}
          },
          res: {
            getHeader: () => ''
          }
        } as unknown as GetServerSidePropsContext;

        const result = await getServerSideComponentProps(
          emptyComponentConfig,
          contextWithPreviewMode
        );
        expect(result.pagination.active).toBeTruthy();
      });

      it('does not show pagination when not on preview mode', async () => {
        const result = await getServerSideComponentProps(
          emptyComponentConfig,
          context
        );
        expect(result.pagination.active).toBeFalsy();
      });
    });

    describe('hiddenFields', () => {
      it('transforms the Magnolia steps into an array of StepConfigs', async () => {
        const hiddenEntries0: HiddenField = {
          name: 'hidden_entry_0',
          defaultValue: '0'
        };

        const hiddenEntries1: HiddenField = {
          name: 'hidden_entry_1',
          defaultValue: '1'
        };

        const componentConfig = {
          ...emptyComponentConfig,
          hiddenEntries: {
            '@nodes': ['hiddenEntries0', 'hiddenEntries1'],
            hiddenEntries0,
            hiddenEntries1
          }
        } as unknown as MagnoliaMultiStepForm;

        const result = await getServerSideComponentProps(
          componentConfig,
          context
        );

        expect(result.hiddenFields).toMatchObject([
          hiddenEntries0,
          hiddenEntries1
        ]);
      });
    });
  });

  describe('transformMagnoliaMultiStepForm', () => {
    describe('steps', () => {
      it('transforms the Magnolia steps into an array of StepConfigs', async () => {
        const steps0: MagnoliaStep = {
          ...emptyStep,
          headline: {
            ...emptyTextWithSizeAndStyle,
            text: 'Step 0 Headline'
          },
          subHeadline: {
            ...emptyTextWithSizeAndStyle,
            text: 'Step 0 Sub-Headline'
          }
        };

        const steps1: MagnoliaStep = {
          ...emptyStep,
          headline: {
            ...emptyTextWithSizeAndStyle,
            text: 'Step 1 Headline'
          }
        };

        const componentConfig = {
          ...emptyComponentConfig,
          steps: {
            '@nodes': ['steps0', 'steps1'],
            steps0,
            steps1
          }
        } as unknown as MagnoliaMultiStepForm;

        const result = transformMagnoliaMultiStepForm(componentConfig, {});

        expect(result.steps).toMatchObject([
          expect.objectContaining({
            headline: expect.objectContaining({
              text: 'Step 0 Headline'
            }),
            subHeadline: expect.objectContaining({
              text: 'Step 0 Sub-Headline'
            })
          }),
          expect.objectContaining({
            headline: expect.objectContaining({
              text: 'Step 1 Headline'
            })
          })
        ]);
      });
    });

    describe('headlines and sub-headlines with values to be parsed', () => {
      const componentConfig = {
        ...emptyComponentConfig,
        steps: {
          '@nodes': ['steps0'],
          steps0: {
            ...emptyStep,
            headline: {
              ...emptyTextWithSizeAndStyle,
              text: 'Headline with URL param: {url.utm_source}'
            },
            subHeadline: {
              ...emptyTextWithSizeAndStyle,
              text: 'Sub-Headline with URL param: {url.utm_source}'
            },
            form: {
              ...emptyForm,
              headline: 'Form headline with URL param: {url.utm_source}'
            }
          }
        }
      } as unknown as MagnoliaMultiStepForm;

      it('replaces values like {url.abc} with the equivalent URL query param', async () => {
        const result = transformMagnoliaMultiStepForm(componentConfig, {
          utm_source: 'email'
        });

        expect(result.steps[0].headline.text).toEqual(
          'Headline with URL param: email'
        );
        expect(result.steps[0].subHeadline.text).toEqual(
          'Sub-Headline with URL param: email'
        );
        expect(result.steps[0].form.headline).toEqual(
          'Form headline with URL param: email'
        );
      });

      // We cannot remove those values yet because there's an extra layer of parsing on the client side,
      // so parser variables need to be available there.
      it('does not remove values like {url.abc} when those params are not present on the URL', async () => {
        const result = transformMagnoliaMultiStepForm(componentConfig, {
          utm_content: 'media'
        });

        expect(result.steps[0].headline.text).toEqual(
          'Headline with URL param: {url.utm_source}'
        );
        expect(result.steps[0].subHeadline.text).toEqual(
          'Sub-Headline with URL param: {url.utm_source}'
        );
        expect(result.steps[0].form.headline).toEqual(
          'Form headline with URL param: {url.utm_source}'
        );
      });
    });

    describe('step images', () => {
      const emptyMagnoliaImage: MagnoliaStepImage = {
        switchable: { field: 'none' }
      };
      const fromComponentMagnoliaImage: MagnoliaStepImage = {
        switchable: {
          field: 'componentDefault'
        }
      };
      const customMagnoliaImage: MagnoliaStepImage = {
        switchable: {
          field: 'damChooser',
          image: { '@id': 'mockedMagnoliaImage' } as MagnoliaImage
        }
      };

      const steps = {
        '@nodes': ['steps0', 'steps1', 'steps2'],
        steps0: {
          ...emptyStep,
          image: emptyMagnoliaImage
        },
        steps1: {
          ...emptyStep,
          image: customMagnoliaImage
        },
        steps2: {
          ...emptyStep,
          image: fromComponentMagnoliaImage
        }
      };

      const STEP_WITH_NO_IMAGE = 0;
      const STEP_WITH_CUSTOM_IMAGE = 1;
      const STEP_WITH_COMPONENT_DEFAULT_IMAGE = 2;

      describe('when component has a default image', () => {
        const componentConfig = {
          ...emptyComponentConfig,
          steps,
          image: customMagnoliaImage
        } as unknown as MagnoliaMultiStepForm;

        it('uses the component default image for all steps with that image choice', async () => {
          const result = transformMagnoliaMultiStepForm(componentConfig, {});

          expect(result.steps[STEP_WITH_CUSTOM_IMAGE].image).toEqual(
            componentConfig.image
          );
        });

        it('uses the step image even if the component has a default image set', async () => {
          const result = transformMagnoliaMultiStepForm(componentConfig, {});

          expect(result.steps[STEP_WITH_CUSTOM_IMAGE].image).toEqual(
            steps.steps1.image
          );
        });

        it('does not use any image for steps with no image', async () => {
          const result = transformMagnoliaMultiStepForm(componentConfig, {});

          expect(result.steps[STEP_WITH_NO_IMAGE].image).toBeUndefined();
        });
      });

      describe('when component does not have a default image', () => {
        const componentConfig = {
          ...emptyComponentConfig,
          steps,
          image: emptyMagnoliaImage
        } as unknown as MagnoliaMultiStepForm;

        it('uses the step image for steps with custom image', async () => {
          const result = transformMagnoliaMultiStepForm(componentConfig, {});

          expect(result.steps[STEP_WITH_CUSTOM_IMAGE].image).toEqual(
            steps.steps1.image
          );
        });

        it('does not use any image for steps with componentDefault or no image at all', async () => {
          const result = transformMagnoliaMultiStepForm(componentConfig, {});

          expect(result.steps[STEP_WITH_NO_IMAGE].image).toBeUndefined();
          expect(
            result.steps[STEP_WITH_COMPONENT_DEFAULT_IMAGE].image
          ).toBeUndefined();
        });
      });
    });

    describe('form entries', () => {
      it('transforms each Magnolia form entry into a FieldConfig', async () => {
        const input: MagnoliaInputFieldConfig = {
          entryType: {
            field: 'input',
            inputType: {
              field: 'generic'
            }
          },
          isRequired: {
            field: 'false'
          },
          name: 'test_input',
          '@nodes': []
        };
        const select: MagnoliaSelectFieldConfig = {
          entryType: {
            field: 'select',
            options: {
              '@nodes': ['options0', 'options1'],
              options0: {
                value: 'option_0',
                label: 'Option 0'
              },
              options1: {
                value: 'option_1',
                label: 'Option 1'
              }
            }
          },
          defaultValue: 'option_0',
          label: 'Select Entry:',
          isRequired: {
            field: 'false'
          },
          name: 'test_select',
          '@nodes': []
        } as unknown as MagnoliaSelectFieldConfig;

        const componentConfig = {
          ...emptyComponentConfig,
          steps: {
            '@nodes': ['steps0'],
            steps0: {
              ...emptyStep,
              form: {
                ...emptyForm,
                entryList: {
                  '@nodes': ['entryList0', 'entryList1'],
                  entryList0: input,
                  entryList1: select
                }
              }
            }
          }
        } as unknown as MagnoliaMultiStepForm;

        const result = transformMagnoliaMultiStepForm(componentConfig, {});

        expect(result.steps.length).toEqual(1);
        expect(result.steps[0].form.fields).toEqual([
          {
            type: 'input',
            inputType: 'generic',
            name: 'test_input',
            isRequired: false,
            isRequiredErrorMessage: undefined,
            defaultValue: undefined,
            label: undefined,
            placeholder: undefined,
            typeErrorMessage: undefined,
            typeMask: undefined
          },
          {
            type: 'select',
            name: 'test_select',
            defaultValue: 'option_0',
            isRequired: false,
            isRequiredErrorMessage: undefined,
            label: 'Select Entry:',
            options: [
              {
                value: 'option_0',
                label: 'Option 0'
              },
              {
                value: 'option_1',
                label: 'Option 1'
              }
            ]
          }
        ]);
      });

      it('transforms all fields of a Magnolia form entry into a full FieldConfig', async () => {
        const fullEntry: MagnoliaInputFieldConfig = {
          entryType: {
            field: 'input',
            inputType: {
              field: 'phone',
              typeErrorMessage: 'Wrong input',
              typeMask: {
                field: 'true',
                mask: '(*************'
              }
            },
            placeholder: '(*************'
          },
          defaultValue: '(*************',
          label: 'Input Entry:',
          isRequired: {
            field: 'true',
            isRequiredErrorMessage: 'Phone is required'
          },
          name: 'test_input',
          '@nodes': []
        };

        const componentConfig = {
          ...emptyComponentConfig,
          steps: {
            '@nodes': ['steps0'],
            steps0: {
              ...emptyStep,
              form: {
                ...emptyForm,
                entryList: {
                  '@nodes': ['entryList0'],
                  entryList0: fullEntry
                }
              }
            }
          }
        } as unknown as MagnoliaMultiStepForm;

        const result = transformMagnoliaMultiStepForm(componentConfig, {});

        expect(result.steps.length).toEqual(1);
        expect(result.steps[0].form.fields.length).toEqual(1);
        expect(result.steps[0].form.fields[0]).toEqual({
          type: 'input',
          defaultValue: '(*************',
          inputType: 'phone',
          isRequired: true,
          isRequiredErrorMessage: 'Phone is required',
          label: 'Input Entry:',
          name: 'test_input',
          placeholder: '(*************',
          typeErrorMessage: 'Wrong input',
          typeMask: '(*************'
        });
      });
    });
  });
});
