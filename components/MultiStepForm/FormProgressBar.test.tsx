import { fireEvent, render, screen, waitFor } from '@utils/test-utils';

import FormProgressBar from './FormProgressBar';

describe('FormProgressBar', () => {
  const mockTrigger = jest.fn();
  const defaultProps = {
    currentStep: 0,
    totalSteps: 5,
    setCurrentStep: jest.fn(),
    errors: {},
    trigger: mockTrigger,
    currentStepFields: ['field1', 'field2']
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockTrigger.mockResolvedValue(true);
  });

  it('should not render progress bar when totalSteps is 1 or less', () => {
    render(<FormProgressBar {...defaultProps} totalSteps={1} />);

    expect(screen.queryByTestId('progress-bar')).not.toBeInTheDocument();
    expect(screen.queryByText('Step 1 of 1')).not.toBeInTheDocument();
  });

  it('should render progress bar and step counter', () => {
    render(<FormProgressBar {...defaultProps} />);

    expect(screen.getByText('Step 1 of 5')).toBeInTheDocument();
    expect(screen.getByTestId('progress-bar')).toBeInTheDocument();
    expect(screen.getByTestId('progress-indicator')).toBeInTheDocument();
  });

  it('should hide previous button on first step', () => {
    render(<FormProgressBar {...defaultProps} currentStep={0} />);

    const prevButton = screen.getByTestId('prev-step-button');
    expect(prevButton).toHaveAttribute('hidden');
  });

  it('should hide next button on last step', () => {
    render(<FormProgressBar {...defaultProps} currentStep={4} />);

    const nextButton = screen.getByTestId('next-step-button');
    expect(nextButton).toHaveAttribute('hidden');
  });

  it('should show both buttons on middle steps', () => {
    render(<FormProgressBar {...defaultProps} currentStep={2} />);

    const prevButton = screen.getByTestId('prev-step-button');
    const nextButton = screen.getByTestId('next-step-button');

    expect(prevButton).not.toHaveAttribute('hidden');
    expect(nextButton).not.toHaveAttribute('hidden');
  });

  it('should call setCurrentStep with previous step when clicking previous button', () => {
    const setCurrentStep = jest.fn();
    render(
      <FormProgressBar
        {...defaultProps}
        currentStep={1}
        setCurrentStep={setCurrentStep}
      />
    );

    const prevButton = screen.getByTestId('prev-step-button');
    fireEvent.click(prevButton);

    expect(setCurrentStep).toHaveBeenCalledWith(0);
  });

  it('should call setCurrentStep with next step when clicking next button', async () => {
    const setCurrentStep = jest.fn();
    render(
      <FormProgressBar
        {...defaultProps}
        currentStep={1}
        setCurrentStep={setCurrentStep}
      />
    );

    const nextButton = screen.getByTestId('next-step-button');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
      expect(setCurrentStep).toHaveBeenCalledWith(2);
    });
  });

  it('should calculate progress correctly', () => {
    render(<FormProgressBar {...defaultProps} currentStep={2} />);

    const progressIndicator = screen.getByTestId('progress-indicator');
    expect(progressIndicator).toHaveStyle({ width: '60%' });
  });

  it('should apply custom color when provided', () => {
    const customColor = {
      color: 'blue',
      range: '500'
    };

    render(<FormProgressBar {...defaultProps} color={customColor} />);

    const progressIndicator = screen.getByTestId('progress-indicator');
    expect(progressIndicator).toHaveStyle({ backgroundColor: 'blue.500' });
  });

  it('should apply default color when no color prop is provided', () => {
    render(<FormProgressBar {...defaultProps} />);

    const progressIndicator = screen.getByTestId('progress-indicator');
    expect(progressIndicator).toHaveStyle({ backgroundColor: 'green.600' });
  });

  describe('next step validation', () => {
    it('should validate fields before moving to next step', async () => {
      const setCurrentStep = jest.fn();
      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).toHaveBeenCalledWith(2);
      });
    });

    it('should not advance to next step if validation fails', async () => {
      mockTrigger.mockResolvedValueOnce(false);
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).not.toHaveBeenCalled();
      });
    });

    it('should not advance to next step if there are existing errors', async () => {
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
          errors={{ field1: { message: 'Required' } }}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).not.toHaveBeenCalled();
      });
    });
  });

  describe('validateCurrentStep', () => {
    it('should return true when fields are valid and there are no errors', async () => {
      const mockTrigger = jest.fn().mockResolvedValue(true);
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
          errors={{}}
          trigger={mockTrigger}
          currentStepFields={['field1', 'field2']}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).toHaveBeenCalledWith(2);
      });
    });

    it('should return false when trigger validation fails', async () => {
      const mockTrigger = jest.fn().mockResolvedValue(false);
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
          errors={{}}
          trigger={mockTrigger}
          currentStepFields={['field1', 'field2']}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).not.toHaveBeenCalled();
      });
    });

    it('should return false when there are errors in current step fields', async () => {
      const mockTrigger = jest.fn().mockResolvedValue(true);
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
          errors={{ field1: { message: 'Required' } }}
          trigger={mockTrigger}
          currentStepFields={['field1', 'field2']}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).not.toHaveBeenCalled();
      });
    });

    it('should ignore errors from other steps', async () => {
      const mockTrigger = jest.fn().mockResolvedValue(true);
      const setCurrentStep = jest.fn();

      render(
        <FormProgressBar
          {...defaultProps}
          currentStep={1}
          setCurrentStep={setCurrentStep}
          errors={{ field3: { message: 'Required' } }}
          trigger={mockTrigger}
          currentStepFields={['field1', 'field2']}
        />
      );

      const nextButton = screen.getByTestId('next-step-button');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockTrigger).toHaveBeenCalledWith(['field1', 'field2']);
        expect(setCurrentStep).toHaveBeenCalledWith(2);
      });
    });
  });
});
