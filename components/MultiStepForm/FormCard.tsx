import { Card, CardBody, CardHeader } from '@chakra-ui/card';
import {
  Box,
  HStack,
  Text,
  useBreakpointValue,
  VStack
} from '@chakra-ui/react';
import {
  ResponsiveQuery,
  responsiveQueryAdapter
} from '@utils/responsiveQueryAdapter';
import { FormEventHandler, useMemo } from 'react';
import { useEffect, useRef, useState } from 'react';
import {
  UseFormRegister,
  UseFormSetValue,
  UseFormTrigger
} from 'react-hook-form';
import { PiQuestionBold } from 'react-icons/pi';

import { getBackgroundColor, getColor } from '~/utils/getColor';

import { FormField, SubmitButton } from './formElements';
import { FieldErrorDetails, StepConfig } from './types';

export type FormCardProps = {
  errors: {
    [fieldName: string]: FieldErrorDetails | undefined;
  };
  form: StepConfig['form'];
  formError?: string;
  showErrorsOfOtherSteps: boolean;
  errorMessageOnValidationError?: string;
  isSubmitting: boolean;
  onSubmit: FormEventHandler<HTMLFormElement>;
  register: UseFormRegister<any>;
  setValue: UseFormSetValue<any>;
  trigger: UseFormTrigger<any>;
};

const FormCard: React.FC<FormCardProps> = ({
  errors,
  form,
  formError,
  showErrorsOfOtherSteps,
  errorMessageOnValidationError,
  isSubmitting,
  onSubmit,
  register,
  setValue,
  trigger
}) => {
  const {
    cta,
    backgroundColor,
    helpBackgroundColor,
    entriesColor,
    errorColor,
    fields,
    headline,
    legalDisclosure,
    helpTextHeader,
    helpText,
    display
  } = form;

  const fitsHorizontalForm = useBreakpointValue({ base: false, md: true });
  const hasASingleFieldWithNoLabel = fields.length === 1 && !fields[0].label;
  const horizontalForm =
    display === 'horizontal' &&
    fitsHorizontalForm &&
    hasASingleFieldWithNoLabel;

  const errorColorKey = getColor(errorColor.color, errorColor.range);
  const backgroundColorKey = getBackgroundColor(
    backgroundColor.color,
    backgroundColor.range
  );

  const helpBgColor = helpBackgroundColor
    ? getBackgroundColor(helpBackgroundColor.color, helpBackgroundColor.range)
    : 'white';
  const entriesColorKey = getColor(entriesColor.color, entriesColor.range);

  const fieldNames = fields.map((field) => field.name);

  const hasErrorsOnOtherSteps = useMemo(() => {
    if (!showErrorsOfOtherSteps) return false;

    const allErrors = Object.keys(errors);
    const errorsOfOtherSteps = allErrors.filter(
      (errorKey) => !fieldNames.includes(errorKey)
    );
    return errorsOfOtherSteps.length > 0;
  }, [errors, fieldNames, showErrorsOfOtherSteps]);

  const handleSubmit: FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    const noErrors = await trigger(fieldNames);
    if (noErrors) {
      onSubmit(e);
    }
  };

  const listOfFields = fields.map((fieldProps, index) => (
    <FormField
      key={index}
      error={errors[fieldProps.name]}
      errorColorKey={errorColorKey}
      entriesColorKey={entriesColorKey}
      register={register}
      setValue={setValue}
      trigger={trigger}
      {...fieldProps}
    />
  ));

  const [containerWidth, setContainerWidth] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(container);
      return () => {
        resizeObserver.unobserve(container);
      };
    }
  }, []);

  const responsiveStyles = (styles: ResponsiveQuery) =>
    responsiveQueryAdapter({
      responsiveQuery: styles,
      width: containerWidth
    });

  return (
    <Card
      align="center"
      bg={backgroundColorKey}
      padding="8"
      rounded="lg"
      shadow="sm"
      boxShadow="md"
      width={responsiveStyles({
        base: 'full',
        md: 'xs',
        lg: 'm',
        xl: 'xl',
        '2xl': '2xl'
      })}
    >
      <CardHeader textAlign="center">
        <Text
          fontSize="xl"
          width={{ base: 'full', md: 'xl' }}
          className="magnolia-text"
        >
          {headline}
        </Text>
      </CardHeader>
      <CardBody width={{ base: 'full', md: 'sm' }}>
        <VStack spacing="10px" align="flex-start">
          {/* noValidate: allow "required" indicator to be displayed,
              but delegate the form validation to react-hook-form */}
          <form
            onSubmit={handleSubmit}
            style={{ width: '100%' }}
            data-testid="form-card"
            noValidate
          >
            {horizontalForm ? (
              <VStack>
                <HStack spacing="10px" width="md" alignItems="flex-start">
                  {...listOfFields}
                  <SubmitButton cta={cta} size="md" />
                </HStack>
              </VStack>
            ) : (
              <>
                <VStack>{...listOfFields}</VStack>
                <SubmitButton
                  isLoading={isSubmitting}
                  cta={cta}
                  width="100%"
                  mt={hasASingleFieldWithNoLabel ? '16px' : 8}
                />
              </>
            )}
          </form>

          {formError && <Text color={errorColorKey}>{formError}</Text>}
          {hasErrorsOnOtherSteps && (
            <Text color={errorColorKey}>{errorMessageOnValidationError}</Text>
          )}
          {helpText && (
            <Box
              width="100%"
              mt="10px"
              p="10px 12px"
              background={helpBgColor}
              borderRadius="6px"
              color="gray.700"
            >
              {helpTextHeader && (
                <Box
                  display="flex"
                  alignItems="center"
                  gap={1}
                  color="black.600"
                  pb="1"
                >
                  <PiQuestionBold size={20} />
                  <Text fontWeight="bold" fontSize={{ base: 'sm', md: 'md' }}>
                    {helpTextHeader}
                  </Text>
                </Box>
              )}
              <Box
                fontSize="sm"
                className="magnolia-text"
                dangerouslySetInnerHTML={{ __html: helpText }}
              />
            </Box>
          )}

          {legalDisclosure && (
            <Box
              pt="6px"
              fontSize="sm"
              fontWeight="400"
              lineHeight="150%"
              className="magnolia-text"
              dangerouslySetInnerHTML={{ __html: legalDisclosure }}
            />
          )}
        </VStack>
      </CardBody>
    </Card>
  );
};

export default FormCard;
