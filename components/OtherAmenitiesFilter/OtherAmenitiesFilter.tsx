import CheckboxInput from '@components/CheckboxInput';

interface OtherAmenitiesFilterFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const otherAmenitiesFilterItems = [
  {
    value: 'swimming-pool',
    label: 'Swimming Pool'
  },
  {
    value: 'entertainment',
    label: 'Entertainment Venues'
  },
  {
    value: 'laundry-line',
    label: 'Laundry/Line Services'
  }
];

function OtherAmenitiesFilterFilter({
  onChange,
  value
}: OtherAmenitiesFilterFilterProps) {
  return (
    <CheckboxInput
      name="otherAmenities"
      onChange={onChange}
      items={otherAmenitiesFilterItems}
      value={value}
    />
  );
}

export default OtherAmenitiesFilterFilter;
