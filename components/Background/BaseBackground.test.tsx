import BaseBackground from '@components/Background/BaseBackground';
import { screen } from '@testing-library/react';
import { render } from '@utils/test-utils';

import { MagnoliaImage } from '~/types/Magnolia';

describe('BaseBackground', () => {
  describe('when type is bgImageForm', () => {
    it('should set the background with overlay', () => {
      render(
        <BaseBackground
          type="bgImageForm"
          image={
            {
              '@id': '123',
              '@path': 'path'
            } as MagnoliaImage
          }
          bgImageOverlay="light"
          bgOverlayOpacity="50"
        >
          <div>test</div>
        </BaseBackground>
      );

      const background = screen.getByTestId('base-background');
      expect(background).toHaveStyle(
        `background-image: url(${process.env.NEXT_PUBLIC_MGNL_HOST}/dam/123path);`
      );
      expect(background).toHaveStyle('background-repeat: no-repeat');
      expect(background).toHaveStyle('background-size: cover');
    });
  });
  describe('when type is bgExternalImageForm', () => {
    it('should set the background with overlay', () => {
      render(
        <BaseBackground
          type="bgExternalImageForm"
          imageUrl="http://test.com"
          bgImageOverlay="dark"
          bgOverlayOpacity="70"
        >
          <div>test</div>
        </BaseBackground>
      );

      const background = screen.getByTestId('base-background');
      expect(background).toHaveStyle(
        'background-image : url(http://test.com);'
      );
      expect(background).toHaveStyle('background-repeat: no-repeat');
      expect(background).toHaveStyle('background-size: cover');
    });
  });

  describe('when type is bgColorForm', () => {
    it("should set the background with 'red'", () => {
      render(
        <BaseBackground type="bgColorForm" bgColor="red">
          <div>test</div>
        </BaseBackground>
      );

      const background = screen.getByTestId('base-background');
      expect(background).toHaveStyle({
        background: 'var(--chakra-colors-red-50)'
      });
    });
  });
});
