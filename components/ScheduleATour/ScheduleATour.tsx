'use client';

import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Box, Flex, Grid, Icon, Text } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import Heading from '@components/Heading';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import {
  ResponsiveQuery,
  responsiveQueryAdapter
} from '@utils/responsiveQueryAdapter';
import dynamic from 'next/dynamic';
import React, { useEffect, useRef, useState } from 'react';
import { useContext } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import { useModalControls } from '~/contexts/ModalContext';
import ProviderContext, { Provider } from '~/contexts/Provider';
import { Metadata } from '~/types/Magnolia';
const InquiryForm = dynamic(() => import('@components/InquiryForm'));
import Container from '@components/LayoutStructure/Container';

const getDayWithSuffix = (date: Date) => {
  const day = date.getDate();
  if (day > 3 && day < 21) return `${day}th`;
  switch (day % 10) {
    case 1:
      return `${day}st`;
    case 2:
      return `${day}nd`;
    case 3:
      return `${day}rd`;
    default:
      return `${day}th`;
  }
};

interface ScheduleATourProps {
  defaultProvider?: Provider;
  templateId?: string;
  title: string;
  headingSize: HeadingSizes;
  headingElement: HeadingElements;
  ctaText: string;
  variant: string;
  ctaColorScheme?: string;
  deviceVisibility: DeviceVisibility;
  metadata?: Metadata;
  tourForm: {
    preTitle: string;
    postTitle: string;
    errorMessage: string;
    ctaText: string;
    ctaColorScheme?: string;
    thankYouMessage?: string;
    thankYouTitle?: string;
    thankYouText?: string;
    legalDislosure?: string;
    textColor?: string;
    textColorRange?: string;
    linkColor?: string;
    linkColorRange?: string;
    nameLabel?: string;
    phoneLabel?: string;
    emailLabel?: string;
  };
}

const ScheduleATour: React.FC<ScheduleATourProps> = ({
  defaultProvider,
  templateId = '',
  title,
  headingSize = 'lg',
  headingElement = 'h2',
  ctaText = 'Schedule Tour',
  variant = 'outline',
  ctaColorScheme = 'primary',
  deviceVisibility,
  tourForm,
  metadata
}) => {
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [tourDate, setTourDate] = useState<Date | null>(null);
  const [tourTime, setTourTime] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const responsiveStyles = (styles: ResponsiveQuery) =>
    responsiveQueryAdapter({
      responsiveQuery: styles,
      width: containerWidth
    });
  const [startDate, setStartDate] = useState(new Date());

  if (!metadata) {
    metadata = {
      '@id': templateId
    };
  }

  const modalId = (metadata && metadata['@id']) ?? '';
  const { show } = useModalControls(modalId);
  const daysToShow = responsiveStyles({ base: 3, md: 7 });
  const provider = useContext(ProviderContext)?.provider ?? defaultProvider;
  const isHidden = useResponsiveDisplay(deviceVisibility);
  const elementClicked = useElementClicked();

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(container);
      return () => {
        resizeObserver.unobserve(container);
      };
    }
  }, []);

  if (isHidden || !provider) {
    return <></>;
  }

  const getNextDays = (start: Date, numDays: number): Date[] => {
    const days: Date[] = [];
    for (let i = 0; i < numDays; i++) {
      const date = new Date(start);
      date.setDate(start.getDate() + i);
      days.push(date);
    }
    return days;
  };

  const handleNext = () => {
    setStartDate((prev) => {
      const next = new Date(prev);
      next.setDate(prev.getDate() + daysToShow);
      return next;
    });
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.NEXT,
        name: ElementNames.SCHEDULE_A_TOUR,
        text: title,
        color: 'primary.500',
        textColor: 'white'
      }
    });
  };

  const handlePrevious = () => {
    setStartDate((prev) => {
      const prevDate = new Date(prev);
      prevDate.setDate(prev.getDate() - daysToShow);
      return prevDate;
    });
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.PREVIOUS,
        name: ElementNames.SCHEDULE_A_TOUR,
        text: title,
        color: 'primary.500',
        textColor: 'white'
      }
    });
  };

  const handleDateClick = (date: Date) => {
    setTourDate(date);
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.TOUR_DATE,
        name: ElementNames.SCHEDULE_A_TOUR,
        text: String(date),
        color: 'white',
        textColor: 'primary.900'
      }
    });
  };

  const handleTimeClick = (time: string) => {
    setTourTime(time);
  };

  const days = getNextDays(startDate, daysToShow);

  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };
  return (
    <Container p={4} ref={containerRef}>
      <Heading
        textColor="primary.900"
        as={headingElement}
        size={headingSize}
        pb={6}
        withContainer={false}
        title={title}
      />
      <Flex justifyContent="space-between" align="center" mb={4}>
        <Icon
          visibility={isSameDay(startDate, new Date()) ? 'hidden' : 'visible'}
          aria-label="Previous week"
          as={ChevronLeftIcon}
          onClick={handlePrevious}
          cursor="pointer"
          width={5}
          height="88px"
        />

        <Grid
          templateColumns={`repeat(${daysToShow}, 1fr)`}
          alignItems="center"
          gap={2}
          width="100%"
          marginX={1}
        >
          {days.map((day, index) => (
            <Box
              key={index}
              textAlign="center"
              onClick={() => handleDateClick(day)}
              cursor="pointer"
              background={
                tourDate?.toDateString() === day.toDateString()
                  ? 'primary.600'
                  : 'transparent'
              }
              textColor={
                tourDate?.toDateString() === day.toDateString()
                  ? 'white'
                  : 'gray.700'
              }
              borderWidth="1px"
              borderColor="gray.200"
              borderRadius="md"
              borderStyle="solid"
              paddingY="8px"
              _hover={{ background: 'primary.600', color: 'white' }}
            >
              <Text fontSize="xs" textTransform="uppercase">
                {day.toLocaleDateString('en-US', { weekday: 'short' })}
              </Text>
              <Text fontSize="3xl" fontWeight="700" lineHeight={'36px'}>
                {day.toLocaleDateString('en-US', { day: 'numeric' })}
              </Text>
              <Text fontSize="xs" textTransform="uppercase">
                {day.toLocaleDateString('en-US', { month: 'short' })}
              </Text>
            </Box>
          ))}
        </Grid>
        <Icon
          aria-label="Next week"
          as={ChevronRightIcon}
          onClick={handleNext}
          cursor="pointer"
          width={5}
          height="88px"
        />
      </Flex>

      <Text textColor="primary.900" textAlign="center" mb={2}>
        Select Tour Time
      </Text>

      <Flex direction="column" align="center" gap={2}>
        <Button
          colorScheme="gray"
          _hover={{ background: 'primary.600', color: 'white' }}
          width="100%"
          maxWidth="248px"
          fontWeight="normal"
          variant="outline"
          elementAction={ElementActions.TOUR_TIME}
          elementName={ElementNames.SCHEDULE_A_TOUR}
          elementType={ElementTypes.BUTTON}
          onClick={() => handleTimeClick('Morning')}
          background={tourTime === 'Morning' ? 'primary.600' : 'transparent'}
          textColor={tourTime === 'Morning' ? 'white' : 'gray.700'}
        >
          Morning
        </Button>
        <Button
          colorScheme="gray"
          _hover={{ background: 'primary.600', color: 'white' }}
          width="100%"
          maxWidth="248px"
          variant="outline"
          fontWeight="normal"
          elementAction={ElementActions.TOUR_TIME}
          elementName={ElementNames.SCHEDULE_A_TOUR}
          elementType={ElementTypes.BUTTON}
          onClick={() => handleTimeClick('Afternoon')}
          background={tourTime === 'Afternoon' ? 'primary.600' : 'transparent'}
          textColor={tourTime === 'Afternoon' ? 'white' : 'gray.700'}
        >
          Afternoon
        </Button>
        <Button
          colorScheme={ctaColorScheme}
          marginTop={4}
          minHeight="48px"
          variant={variant}
          elementAction={ElementActions.OPEN_MODAL}
          elementName={ElementNames.SCHEDULE_A_TOUR}
          elementType={ElementTypes.BUTTON}
          isDisabled={!tourDate || !tourTime} // Disable button if date or time is not selected
          onClick={show}
        >
          {ctaText}
        </Button>
      </Flex>
      {
        <Box>
          <InquiryForm
            {...tourForm}
            title={`${provider.name} in the ${tourTime?.toLowerCase()} on ${
              tourDate
                ? `${tourDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long'
                  })} ${getDayWithSuffix(tourDate)}`
                : ''
            }`}
            formId={modalId}
            ctaAction={CTAAction.SCHEDULE_TOUR}
            tourTime={
              tourDate ? `${tourDate?.toDateString()} - ${tourTime}` : undefined
            }
            showPayingWithMedicaid={false}
            showWhoAreYouLookingFor={false}
            isTourRequest={true}
            thankYouTitle={tourForm.thankYouTitle}
            thankYouText={tourForm.thankYouText}
            display={Display.FIT_CONTENT_MODAL}
            metadata={metadata}
          />
        </Box>
      }
    </Container>
  );
};

export default ScheduleATour;
