import { render, screen } from '~/utils/test-utils';

import MedicareReviewsLink from './MedicareReviewsLink';

describe('MedicareReviewsLink', () => {
  it('should render the medicare care reviews link', () => {
    const props = {
      careType: 'nursing_homes',
      federalProviderNumber: '123456',
      state: 'CA'
    };
    render(<MedicareReviewsLink {...props} />);
    const linkElement = screen.getByText(props.federalProviderNumber);
    expect(linkElement).toBeInTheDocument();
  });

  it('should not render the medicare care reviews link when missing data', () => {
    const props = {
      careType: '',
      federalProviderNumber: '123456',
      state: 'CA'
    };
    const { queryByText } = render(<MedicareReviewsLink {...props} />);
    expect(queryByText(props.federalProviderNumber)).not.toBeInTheDocument();
  });
});
