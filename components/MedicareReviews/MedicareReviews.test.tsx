import { mockProviderV2 } from '@utils/test-utils/mocks/provider';

import ProviderContext from '~/contexts/Provider';
import { mockMedicareReviews, render } from '~/utils/test-utils';

import MedicareReviews from './MedicareReviews';
const ContextWrapper = ({ provider, children }) => {
  return (
    <ProviderContext.Provider
      value={{ setProvider: () => {}, provider: provider }}
    >
      {children}
    </ProviderContext.Provider>
  );
};

describe('MedicareReviews', () => {
  it('should render the component', () => {
    const medicareReviewsProps = {
      ...mockProviderV2,
      services: [
        {
          ...mockProviderV2.services,
          medicares: [mockMedicareReviews]
        }
      ]
    };

    const { getByText } = render(
      <ContextWrapper provider={medicareReviewsProps}>
        <MedicareReviews />
      </ContextWrapper>
    );
    expect(getByText('Medicare')).toBeInTheDocument();
  });
  it('should not render the component when medicare data is not available', () => {
    const { queryByText } = render(
      <ContextWrapper provider={mockProviderV2}>
        <MedicareReviews />
      </ContextWrapper>
    );
    expect(queryByText('Medicare')).not.toBeInTheDocument();
  });

  it('should render a title if provided', () => {
    const medicareReviewsProps = {
      ...mockProviderV2,
      services: [
        {
          ...mockProviderV2.services,
          medicares: [mockMedicareReviews]
        }
      ]
    };

    const { getByText } = render(
      <ContextWrapper provider={medicareReviewsProps}>
        <MedicareReviews title="Test Title" />
      </ContextWrapper>
    );
    expect(getByText('Test Title')).toBeInTheDocument();
  });
});
