import { mockMedicareReviews, render } from '~/utils/test-utils';

import MedicareReviewsBarChart from './MedicareReviewsBarChart';
describe('MedicareReviewsBarChart', () => {
  it('renders ProgressChart for each non-null rating', () => {
    const { getAllByRole } = render(
      <MedicareReviewsBarChart medicareReviews={mockMedicareReviews} />
    );

    const progressCharts = getAllByRole('progressbar');
    expect(progressCharts).toHaveLength(4);
  });

  it('renders a ProgressChart for each non-null rating', () => {
    const newMockMedicareReviews = { ...mockMedicareReviews };
    newMockMedicareReviews.overallRating = null;
    const { getAllByRole } = render(
      <MedicareReviewsBarChart medicareReviews={newMockMedicareReviews} />
    );

    const progressCharts = getAllByRole('progressbar');
    expect(progressCharts).toHaveLength(3);
  });

  it('renders the correct label and value for each ProgressChart', () => {
    const { getByText } = render(
      <MedicareReviewsBarChart medicareReviews={mockMedicareReviews} />
    );

    expect(getByText('4.0')).toBeInTheDocument();
    expect(getByText('3.0')).toBeInTheDocument();
    expect(getByText('5.0')).toBeInTheDocument();
    expect(getByText('2.0')).toBeInTheDocument();
  });

  it('calculates the correct value for each ProgressChart', () => {
    const { getByLabelText } = render(
      <MedicareReviewsBarChart medicareReviews={mockMedicareReviews} />
    );

    expect(getByLabelText('4.0 medicare rating for Health'));
    expect(getByLabelText('3.0 medicare rating for Overall'));
    expect(getByLabelText('5.0 medicare rating for Quality'));
    expect(getByLabelText('2.0 medicare rating for Staff'));
  });
});
