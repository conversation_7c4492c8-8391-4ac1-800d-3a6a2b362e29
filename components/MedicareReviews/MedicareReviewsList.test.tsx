import { mockMedicareReviews, render } from '~/utils/test-utils';

import MedicareReviewsList from './MedicareReviewsList';

describe('MedicareReviewsList', () => {
  it('should render the MedicareReviewsList component', () => {
    const { getByText, queryByText } = render(
      <MedicareReviewsList medicareReviews={mockMedicareReviews} />
    );
    expect(getByText('Ownership Type:')).toBeInTheDocument();
    expect(getByText('Non - Profit Other')).toBeInTheDocument();
    expect(getByText('Rating Date:')).toBeInTheDocument();
    expect(getByText('10/1/2023')).toBeInTheDocument();
    expect(getByText('Timeliness of providing service:')).toBeInTheDocument();
    expect(getByText('96.4%')).toBeInTheDocument();
    expect(
      getByText('Taught patient or caregivers about their drugs:')
    ).toBeInTheDocument();
    expect(getByText('99.5%')).toBeInTheDocument();
    expect(getByText("Checked patient's risk of falling:")).toBeInTheDocument();
    expect(getByText('99.9%')).toBeInTheDocument();
    expect(getByText('Checked patient for depression:')).toBeInTheDocument();
    expect(getByText('99.4%')).toBeInTheDocument();
    expect(getByText('Checked for flu vaccination:')).toBeInTheDocument();
    expect(getByText('77.7%')).toBeInTheDocument();
    expect(getByText('Checked for pneumonia shot:')).toBeInTheDocument();
    expect(getByText('87.1%')).toBeInTheDocument();
    expect(
      getByText(
        'Provided diabetes specific care including getting doctors orders, giving foot care, and teatching patients about foot care:'
      )
    ).toBeInTheDocument();
    expect(getByText('99.7%')).toBeInTheDocument();
    expect(
      getByText('How often patients had less pain moving around:')
    ).toBeInTheDocument();
    expect(getByText('86.0%')).toBeInTheDocument();
    expect(
      getByText(
        'How often home health patients had to be admitted to the hospital:'
      )
    ).toBeInTheDocument();
    expect(getByText('15.1%')).toBeInTheDocument();
    expect(
      getByText(
        'How often patients receiving home health care needed urgent, unplanned care in the ER without being admitted:'
      )
    ).toBeInTheDocument();
    expect(getByText('15.9%')).toBeInTheDocument();
    expect(
      getByText(
        'How often home health patients, who have had a recent hospital stay, had to be re-admitted to the hospital:'
      )
    ).toBeInTheDocument();
    expect(getByText('Same As Expected')).toBeInTheDocument();
    expect(
      getByText(
        'How often home health patients, who have had a recent hospital stay, received care in the hospital emergency room without being re-admitted to the hospital:'
      )
    ).toBeInTheDocument();
    expect(getByText('Worse Than Expected')).toBeInTheDocument();
    expect(
      queryByText(
        'How often home health patients received care in the hospital emergency room without being re-admitted to the hospital:'
      )
    ).not.toBeInTheDocument();
    expect(getByText('Walking or moving around:')).toBeInTheDocument();
    expect(getByText('78.2%')).toBeInTheDocument();
    expect(getByText('Getting in and out of bed:')).toBeInTheDocument();
    expect(getByText('81.4%')).toBeInTheDocument();
    expect(getByText('Bathing:')).toBeInTheDocument();
    expect(getByText('81.5%')).toBeInTheDocument();
    expect(getByText('Breathing:')).toBeInTheDocument();
    expect(getByText('85.6%')).toBeInTheDocument();
    expect(getByText('Wounds/healing after an operation:')).toBeInTheDocument();
    expect(getByText('90.1%')).toBeInTheDocument();
    expect(getByText('Medication compliance:')).toBeInTheDocument();
    expect(getByText('72.3%')).toBeInTheDocument();
  });
});
