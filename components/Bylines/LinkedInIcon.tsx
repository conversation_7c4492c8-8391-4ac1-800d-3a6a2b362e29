import { Link } from '@chakra-ui/react';

export const LinkedInIcon = ({
  href,
  textAlign = 'right'
}: {
  href: string;
  textAlign?:
    | 'center'
    | 'end'
    | 'justify'
    | 'left'
    | 'match-parent'
    | 'right'
    | 'start';
}) => {
  return (
    <Link textAlign={textAlign} href={href} target="_blank" fontSize="xs">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
      >
        <rect width="16" height="16" rx="2" fill="#333333" />
        <path
          d="M3.16667 1.25C2.38426 1.25 1.75 1.88426 1.75 2.66667C1.75 3.44907 2.38426 4.08333 3.16667 4.08333C3.94907 4.08333 4.58333 3.44907 4.58333 2.66667C4.58333 1.88426 3.94907 1.25 3.16667 1.25Z"
          fill="white"
        />
        <path
          d="M1.83333 5.25C1.78731 5.25 1.75 5.28731 1.75 5.33333V14C1.75 14.046 1.78731 14.0833 1.83333 14.0833H4.5C4.54602 14.0833 4.58333 14.046 4.58333 14V5.33333C4.58333 5.28731 4.54602 5.25 4.5 5.25H1.83333Z"
          fill="white"
        />
        <path
          d="M6.16667 5.25C6.12064 5.25 6.08333 5.28731 6.08333 5.33333V14C6.08333 14.046 6.12064 14.0833 6.16667 14.0833H8.83333C8.87936 14.0833 8.91667 14.046 8.91667 14V9.33333C8.91667 9.00181 9.04836 8.68387 9.28278 8.44945C9.5172 8.21503 9.83515 8.08333 10.1667 8.08333C10.4982 8.08333 10.8161 8.21503 11.0506 8.44945C11.285 8.68387 11.4167 9.00181 11.4167 9.33333V14C11.4167 14.046 11.454 14.0833 11.5 14.0833H14.1667C14.2127 14.0833 14.25 14.046 14.25 14V8.2535C14.25 6.63567 12.8429 5.37 11.2332 5.51633C10.7368 5.56146 10.246 5.68487 9.78765 5.8813L8.91667 6.25457V5.33333C8.91667 5.28731 8.87936 5.25 8.83333 5.25H6.16667Z"
          fill="white"
        />
      </svg>
    </Link>
  );
};

export default LinkedInIcon;
