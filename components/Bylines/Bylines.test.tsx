import { render, screen } from '@testing-library/react';

import Bylines from './Bylines';

const mockBylines = {
  dateUpdated: 'December 1, 2023',
  shareUrl: 'shareUrl',
  shareTitle: 'shareTitle',
  sitePath: 'sitePath',
  writtenBy: {
    name: '<PERSON>',
    bio: '<p><PERSON> began his career as a freelance writer</p>',
    linkedIn: 'https://www.linkedin.com/in/daniel-cobb-public/',
    image:
      '/caring-paas-webapp-1.3/dam/jcr:66eed52b-32a4-4430-ada3-143dfafd0c98/Daniel-Headshot-Recent.jpeg',
    staffPosition: 'Managing Editor'
  },
  factCheckedBy: {
    name: '<PERSON>',
    bio: '<p>Lisa</p>',
    linkedIn: 'https://www.linkedin.com/in/lisa/',
    image: '/pathto/image.jpeg',
    staffPosition: 'Managing Editor'
  },
  editedBy: {
    name: '<PERSON>',
    bio: '<p><PERSON></p>',
    linkedIn: 'https://www.linkedin.com/in/rachel/',
    image: '/pathto/image.jpeg',
    staffPosition: 'Managing Editor'
  },
  reviewedBy: {
    name: '<PERSON>',
    bio: '<p><PERSON></p>',
    linkedIn: 'htt ps://www.linkedin.com/in/laura/',
    image: '/pathto/image.jpeg',
    staffPosition: 'Managing Editor'
  }
};

describe('Bylines', () => {
  test('renders Bylines with written by author', () => {
    const foo = render(
      <Bylines
        dateUpdated={mockBylines.dateUpdated}
        shareUrl={mockBylines.shareUrl}
        shareTitle={mockBylines.shareTitle}
        sitePath={mockBylines.sitePath}
        writtenBy={mockBylines.writtenBy}
      />
    );

    expect(
      screen.getByText('Daniel Cobb began his career as a freelance writer')
    ).toBeInTheDocument();

    expect(screen.getByText('Daniel Cobb')).toBeInTheDocument();
  });

  test('renders Bylines with writtenBy and editedBy author', () => {
    const foo = render(
      <Bylines
        dateUpdated={mockBylines.dateUpdated}
        shareUrl={mockBylines.shareUrl}
        shareTitle={mockBylines.shareTitle}
        sitePath={mockBylines.sitePath}
        writtenBy={mockBylines.writtenBy}
        editedBy={mockBylines.editedBy}
      />
    );

    expect(
      screen.getByText('Daniel Cobb began his career as a freelance writer')
    ).toBeInTheDocument();

    expect(screen.getByText('Rachel')).toBeInTheDocument();
  });

  test('renders Bylines with writtenBy, editedBy, reviewedBy, factCheckedBy author', () => {
    const foo = render(
      <Bylines
        dateUpdated={mockBylines.dateUpdated}
        shareUrl={mockBylines.shareUrl}
        shareTitle={mockBylines.shareTitle}
        sitePath={mockBylines.sitePath}
        writtenBy={mockBylines.writtenBy}
        editedBy={mockBylines.editedBy}
        reviewedBy={mockBylines.reviewedBy}
        factCheckedBy={mockBylines.factCheckedBy}
      />
    );

    expect(
      screen.getByText('Daniel Cobb began his career as a freelance writer')
    ).toBeInTheDocument();

    expect(screen.getByText('Rachel')).toBeInTheDocument();
    expect(screen.getByText('Lisa')).toBeInTheDocument();
    expect(screen.getByText('Laura')).toBeInTheDocument();
  });
});
