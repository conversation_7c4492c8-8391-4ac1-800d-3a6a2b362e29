import { Avatar } from '@chakra-ui/avatar';
import { useDisclosure } from '@chakra-ui/hooks';
import { Box, Flex, Text } from '@chakra-ui/layout';
import { HStack, Tooltip } from '@chakra-ui/react';
import { VisuallyHidden } from '@chakra-ui/visually-hidden';
import HtmlToReact from '@components/HtmlToReact';
import { right } from '@popperjs/core';

import { MagnoliaImage } from '~/types/Magnolia';

import { LinkedInIcon } from './LinkedInIcon';

const baseUrl = process.env.NEXT_PUBLIC_MGNL_HOST || null;

export const Author = ({
  contributerType,
  bio,
  name,
  image,
  linkedIn
}: {
  contributerType: string;
  bio: string;
  name: string;
  image?: MagnoliaImage | string;
  linkedIn?: string;
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const parsedBio = HtmlToReact({ html: bio });

  if (!baseUrl) {
    return null;
  }

  if (typeof image != 'string') {
    return null;
  }

  let imageURL = image.startsWith(baseUrl) ? image : baseUrl + image;

  if (baseUrl.endsWith('/author') && image.startsWith('/author/')) {
    imageURL = baseUrl + image.replace('/author', '');
  }

  if (
    baseUrl.endsWith('/caring-paas-webapp-1.3') &&
    image.startsWith('/caring-paas-webapp-1.3/')
  ) {
    imageURL = baseUrl + image.replace('/caring-paas-webapp-1.3', '');
  }

  return (
    <Flex>
      <Box w="50px">
        <Avatar name={name} src={imageURL} />
      </Box>
      <Box w="140px" p="1">
        <Text fontSize="xs" align="left">
          {contributerType}
        </Text>
        <Tooltip label={parsedBio} isOpen={isOpen}>
          <Box>
            <HStack spacing={2}>
              <Text
                fontSize="xs"
                align="left"
                as="u"
                onClick={onOpen}
                onMouseEnter={onOpen}
                onMouseLeave={onClose}
              >
                {name}
              </Text>
              {linkedIn && <LinkedInIcon href={linkedIn} textAlign={right} />}
            </HStack>
            <VisuallyHidden>{parsedBio}</VisuallyHidden>
          </Box>
        </Tooltip>
      </Box>
    </Flex>
  );
};

export default Author;
