import { Box, SimpleGrid, Text } from '@chakra-ui/layout';
import { HStack } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import {
  FacebookIcon,
  FacebookShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  TwitterIcon,
  TwitterShareButton
} from 'next-share';

import { StoryAuthor } from '~/types/Magnolia';

import { Author } from './Author';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

interface BylineProps {
  dateUpdated: string;
  shareUrl: string;
  shareTitle: string;
  sitePath: string;
  factCheckedBy?: StoryAuthor;
  editedBy?: StoryAuthor;
  reviewedBy?: StoryAuthor;
  writtenBy: StoryAuthor;
}

const Bylines: React.FC<BylineProps> = ({
  dateUpdated,
  shareUrl,
  shareTitle,
  sitePath,
  factCheckedBy,
  editedBy,
  reviewedBy,
  writtenBy
}) => {
  const url = `https://www.${sitePath}${shareUrl}/`;

  return (
    <Container as="section">
      {dateUpdated && (
        <Box
          textAlign="left"
          px={{ base: 0, lg: 0 }}
          maxW="container.xl"
          py="0"
          mx="auto"
        >
          <Text
            color="gray.800"
            fontSize={{ base: 'sm', lg: 'lg' }}
            fontWeight="400"
            fontStyle="normal"
            lineHeight={{ base: '150%', lg: 'unset' }}
          >
            Date Updated: {dateUpdated}
          </Text>
        </Box>
      )}

      <HStack spacing={2} py="4">
        <TwitterShareButton url={url} title={shareTitle}>
          <TwitterIcon size={32} round={false} borderRadius={6} />
        </TwitterShareButton>
        <FacebookShareButton url={url} quote={shareTitle}>
          <FacebookIcon size={32} round={false} borderRadius={6} />
        </FacebookShareButton>
        <LinkedinShareButton url={url}>
          <LinkedinIcon size={32} round={false} borderRadius={6} />
        </LinkedinShareButton>
      </HStack>
      <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing="4">
        {writtenBy?.name && (
          <Author
            contributerType="Written by:"
            bio={writtenBy.bio}
            name={writtenBy.name}
            linkedIn={writtenBy.linkedIn}
            image={writtenBy.image}
          />
        )}
        {editedBy?.name && (
          <Author
            contributerType="Edited by:"
            bio={editedBy.bio}
            name={editedBy.name}
            linkedIn={editedBy.linkedIn}
            image={editedBy.image}
          />
        )}
        {reviewedBy?.name && (
          <Author
            contributerType="Reviewed by:"
            bio={reviewedBy.bio}
            name={reviewedBy.name}
            linkedIn={reviewedBy.linkedIn}
            image={reviewedBy.image}
          />
        )}
        {factCheckedBy?.name && (
          <Author
            contributerType="Fact checked by:"
            bio={factCheckedBy.bio}
            name={factCheckedBy.name}
            linkedIn={factCheckedBy.linkedIn}
            image={factCheckedBy.image}
          />
        )}
      </SimpleGrid>
    </Container>
  );
};

export default Bylines;
