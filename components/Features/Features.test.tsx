import { mockFeatures, render, screen } from '@utils/test-utils';

import Features from './Features';

describe('Features', () => {
  test('renders the component with given props', () => {
    const { getByRole } = render(
      <Features
        headingElement={mockFeatures.headingElement}
        title={mockFeatures.title}
        description={mockFeatures.description}
        features={mockFeatures.features}
      />
    );

    expect(getByRole('heading')).toHaveTextContent(mockFeatures.title);
    expect(screen.getByText(mockFeatures.description)).toBeInTheDocument();
    expect(
      screen.getByText(mockFeatures.features?.features0.description)
    ).toBeInTheDocument();
    expect(
      screen.getByText(mockFeatures.features?.features1.description)
    ).toBeInTheDocument();
    expect(
      screen.getByText(mockFeatures.features?.features2.description)
    ).toBeInTheDocument();
    expect(
      screen.getByText(mockFeatures.features?.features3.description)
    ).toBeInTheDocument();
  });
  test('renders the component with a CTA', () => {
    render(
      <Features
        headingElement={mockFeatures.headingElement}
        title={mockFeatures.title}
        description={mockFeatures.description}
        features={mockFeatures.featureWithCta}
      />
    );
    expect(
      screen.getByText(mockFeatures.featureWithCta?.features0.description)
    ).toBeInTheDocument();
    expect(screen.getByText('Learn More')).toBeInTheDocument();
  });
});
