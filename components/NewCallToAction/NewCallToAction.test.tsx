import { NewCallToAction } from '@components/NewCallToAction/index';
import { fireEvent, render, screen } from '@utils/test-utils';

import { Metadata } from '~/types/Magnolia';

const mockShowModal = jest.fn();
jest.mock('~/contexts/ModalContext', () => ({
  useModalDispatch: jest.fn(() => ({
    showModal: mockShowModal
  }))
}));
describe('NewCallToAction', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  describe("when actionBehavior doesn't exist", () => {
    it('should render an inquiry link', () => {
      render(
        <NewCallToAction
          cta1={{
            text: 'Test',
            isInquiry: true,
            inquiryId: '1234'
          }}
          metadata={{} as Metadata}
        />
      );

      const button = screen.getByText('Test');
      expect(button).toBeInTheDocument();
      fireEvent.click(button);
      expect(mockShowModal).toHaveBeenCalled();
    });

    it('should render a url', () => {
      render(
        <NewCallToAction
          cta1={{ text: 'Test', url: 'https://www.caring.com' }}
          metadata={{} as Metadata}
        />
      );

      const button = screen.getByText('Test');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('href', 'https://www.caring.com');
    });
  });

  describe('when actionBehavior and inquiry fields exist', () => {
    it('prioritizes actionBehavior', () => {
      render(
        <NewCallToAction
          cta1={{
            text: 'Test',
            isInquiry: true,
            inquiryId: '1234',
            actionBehavior: {
              field: 'openLink',
              url: 'https://www.caring.com'
            }
          }}
          metadata={{} as Metadata}
        />
      );

      const button = screen.getByText('Test');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('href', 'https://www.caring.com');
    });
  });
});
