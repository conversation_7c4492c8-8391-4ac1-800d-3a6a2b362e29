import { useDisclosure } from '@chakra-ui/hooks';
import { Icon } from '@chakra-ui/icon';
import { Flex, Text } from '@chakra-ui/layout';
import { Tooltip } from '@chakra-ui/tooltip';
import { MdOutlineHelpOutline, MdVerified } from 'react-icons/md';

const Verified = ({
  verifiedText = 'Verified providers have been vetted for quality by the Caring.com team'
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Tooltip
      hasArrow
      label={verifiedText}
      bg="primary.700"
      color="white"
      fontSize="sm"
      px={6}
      py={2}
      textAlign="center"
      borderRadius={4}
      isOpen={isOpen}
      display="inline-block"
    >
      <Flex
        display="inline-flex"
        color="primary.700"
        alignItems="center"
        onMouseEnter={onOpen}
        onMouseLeave={onClose}
        onClick={onOpen}
      >
        <Icon as={MdVerified} w="24px" h="24px" role="presentation" />
        <Text
          className="verified-partner"
          lineHeight="19px"
          fontSize={{ base: 'md', lg: 'md' }}
          fontWeight="bold"
          mx={1}
        >
          Verified Partner
        </Text>
        <Icon
          as={MdOutlineHelpOutline}
          w="18px"
          h="18px"
          marginTop={1}
          marginBottom="auto"
          role="presentation"
        />
      </Flex>
    </Tooltip>
  );
};

export default Verified;
