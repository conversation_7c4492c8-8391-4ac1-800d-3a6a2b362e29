import { Icon } from '@chakra-ui/icon';
import { Flex, Link } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { MdPhone } from 'react-icons/md';

import { formatPhone } from '~/utils/strings';

const ProviderPhoneNumber = ({
  phoneNumber,
  phoneNumberPreText,
  phoneNumberPostText,
  isEnhanced,
  elementClicked
}) => (
  <Link
    onClick={(e) => {
      elementClicked({
        element: {
          type: ElementTypes.TELEPHONE,
          action: ElementActions.PHONE_CALL,
          name: ElementNames.PHONE_CALL_BUTTON,
          text: (e.target as HTMLElement).innerText,
          color: '',
          textColor: 'primary.900'
        },
        destinationUrl: phoneNumber
      });
    }}
    href={`tel:+1${phoneNumber}`}
    textDecoration="none"
    color="primary.900"
  >
    <Flex
      flexWrap="nowrap"
      color="primary.900"
      fontWeight="700"
      alignItems={{ base: 'flex-start', lg: 'center' }}
    >
      <Icon
        as={MdPhone}
        width="20px"
        height="20px"
        marginTop={{ base: '3px', lg: '0px' }}
      />
      {`${
        phoneNumberPreText && !isEnhanced ? phoneNumberPreText : ' '
      } ${formatPhone(String(phoneNumber))} ${
        phoneNumberPostText && !isEnhanced ? `${phoneNumberPostText}` : ''
      }
        `}
    </Flex>
  </Link>
);

export default ProviderPhoneNumber;
