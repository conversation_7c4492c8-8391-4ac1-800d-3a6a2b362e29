import { Box, Icon, Text } from '@chakra-ui/react';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';

const FeaturedAmenity = ({ icon, label }) => (
  <Box
    color="gray.700"
    borderColor="gray.500"
    borderRadius="6px"
    aria-label={label}
    fontSize="12px"
    fontWeight="700"
    display="inline-flex"
    flexWrap="nowrap"
    alignItems="center"
    gap={2}
    height="32px"
    css={containerQueryAdapter({
      properties: [
        {
          property: 'border',
          values: {
            base: 'none',
            sm: '1px solid'
          }
        },
        {
          property: 'padding',
          values: {
            base: '0px 4px',
            sm: '2px 8px'
          }
        }
      ]
    })}
  >
    <Icon
      as={icon}
      boxSize="24px"
      css={containerQueryAdapter({
        properties: [
          {
            property: 'marginRight',
            values: {
              base: '8px',
              sm: '0'
            }
          }
        ]
      })}
    />
    <Text
      whiteSpace="nowrap"
      css={containerQueryAdapter({
        properties: [
          {
            property: 'display',
            values: {
              base: 'none',
              sm: 'block'
            }
          }
        ]
      })}
    >
      {label}
    </Text>
  </Box>
);

export default FeaturedAmenity;
