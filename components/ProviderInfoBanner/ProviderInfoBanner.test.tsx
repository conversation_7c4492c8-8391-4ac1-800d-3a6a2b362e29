import { Chakra<PERSON>rovider } from '@chakra-ui/react';
import { ElementNames } from '@components/Analytics/events/ElementClicked';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { mockModMonProvider } from '@mocks/modmon.mock';
import { render } from '@testing-library/react';
import React from 'react';

import ProviderContext from '~/contexts/Provider';
import { CaringDomains, Domain } from '~/types/Domains';

import ProviderInfoBanner, {
  ProviderInfoBannerProps
} from './ProviderInfoBanner';

jest.mock('@hooks/use-inquiry-form-submitted');
jest.mock('@hooks/useResponsiveDisplay', () => ({
  useResponsiveDisplay: () => false
}));

describe('ProviderInfoBanner - Blur Functionality', () => {
  const mockDomain: Domain = CaringDomains.LIVE;

  const mockInquiryCTA = {
    text: 'Inquire Now',
    textColor: '#ffffff',
    bgColor: '#0070f3',
    state: undefined,
    trackingName: ElementNames.INFO_REQUEST_SECTION,
    actionBehavior: {
      field: 'openInquiry' as const,
      inquiryId: 'inquiry-123'
    },
    metadata: {
      '@id': 'provider-inquiry-cta'
    }
  };

  const mockProviderWithCost = {
    ...mockModMonProvider,
    minimumCost: {
      costCents: 250000,
      currency: 'USD'
    }
  };

  const mockProps: ProviderInfoBannerProps = {
    defaultProvider: mockProviderWithCost,
    defaultDomain: mockDomain,
    headingElement: 'h1',
    headingSize: 'lg',
    phoneNumberPreText: 'Call us at',
    phoneNumber: '************',
    phoneNumberPostText: 'for more information',
    displayReviews: true,
    displayBadges: true,
    displayPricing: true,
    verifiedText: 'Verified',
    ratingColor: 'info',
    ratingColorRange: '500',
    deviceVisibility: 'desktop',
    noHorizontalPadding: false,
    cta: mockInquiryCTA
  };

  const renderComponent = () => {
    const provider = { ...mockProviderWithCost };
    const result = render(
      <ChakraProvider>
        <ProviderContext.Provider value={{ provider, setProvider: jest.fn() }}>
          <ProviderInfoBanner {...mockProps} />
        </ProviderContext.Provider>
      </ChakraProvider>
    );
    return result;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(true);
    const { container } = renderComponent();

    expect(container.firstChild).not.toBeNull();
  });

  it('renders the "Starting Price" text when displayPricing is true', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(true);
    const { getByText } = renderComponent();

    const startingPriceText = getByText('Starting Price');
    expect(startingPriceText).toBeInTheDocument();
  });

  it('renders the price blurred when showPrice logic evaluates to false', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(false);
    const { getByText } = renderComponent();

    const priceElement = getByText(/\$2,500/);
    expect(priceElement).toHaveStyle('filter: blur(10px)');
  });

  it('renders the price clearly when showPrice logic evaluates to true', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(true);
    const { getByText } = renderComponent();

    const priceElement = getByText(/\$2,500/);
    expect(priceElement).not.toHaveStyle('filter: blur(10px)');
  });

  it('hides the geo links when shouldProviderBeNoIndexed returns true', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(true);
    const { queryByText } = renderComponent();

    const geoLink = queryByText('Care Offered:');
    expect(geoLink).not.toBeInTheDocument();
  });
});
