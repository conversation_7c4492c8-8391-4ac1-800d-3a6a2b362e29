import { Flex } from '@chakra-ui/layout';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';

const Divider = ({ hideBorder = false }: { hideBorder?: boolean }) => (
  <Flex
    borderColor="gray.400"
    borderLeftStyle="solid"
    css={containerQueryAdapter({
      properties: [
        {
          property: 'display',
          values: {
            base: hideBorder ? 'none' : 'flex',
            lg: 'flex'
          }
        },
        {
          property: 'padding-right',
          values: {
            base: '0',
            sm: '12px'
          }
        },
        {
          property: 'padding-bottom',
          values: {
            base: '12px',
            sm: '0'
          }
        },
        {
          property: 'border-top-width',
          values: {
            base: '1px',
            sm: '0'
          }
        },
        {
          property: 'border-left-width',
          values: {
            base: '0',
            sm: '1px'
          }
        },
        {
          property: 'height',
          values: {
            base: '0',
            sm: '48px'
          }
        },
        {
          property: 'width',
          values: {
            base: '100%',
            sm: '0'
          }
        }
      ]
    })}
  />
);

export default Divider;
