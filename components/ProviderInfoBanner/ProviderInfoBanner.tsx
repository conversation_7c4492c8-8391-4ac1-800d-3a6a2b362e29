'use client';

import { Icon } from '@chakra-ui/icon';
import { Box, Flex, Link, Text } from '@chakra-ui/layout';
import { Button, Image } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import CTA from '@components/CTA';
import { CTAProps } from '@components/CTA/CTA';
import Heading from '@components/Heading';
import HtmlToReact from '@components/HtmlToReact';
import Container from '@components/LayoutStructure/Container';
import { getProviderAmenitiesFromServices } from '@components/OfferingListing/OfferingListing';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import { getFeaturedAmenities } from '@utils/amenities';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';
import { pluralize } from '@utils/strings';
import { useContext } from 'react';
import {
  MdDirectionsBus,
  MdPets,
  MdStar,
  MdWheelchairPickup,
  MdWifi
} from 'react-icons/md';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import caringStar from '~/assets/badges/caring-star.png';
import ProviderContext, { Provider } from '~/contexts/Provider';
import { Domain } from '~/types/Domains';
import { formatNumberLocale, formatRating } from '~/utils/number';
import {
  contractIsSubscription,
  providerIsClaimed,
  providerIsEnhancedAndNotSuppressed
} from '~/utils/providers';

import Divider from './Divider';
import FeaturedAmenity from './FeaturedAmenity';
import ProviderPhoneNumber from './ProviderPhoneNumber';
import Verified from './Verified';

export interface ProviderInfoBannerProps {
  defaultProvider: Provider;
  defaultDomain?: Domain;
  headingElement: HeadingElements;
  headingSize: HeadingSizes;
  phoneNumberPreText?: string;
  phoneNumber?: string;
  phoneNumberPostText?: string;
  displayReviews?: boolean;
  displayBadges?: boolean;
  displayPricing?: boolean;
  verifiedText?: string;
  ratingColor?: string;
  ratingColorRange?: string;
  deviceVisibility?: DeviceVisibility;
  noHorizontalPadding?: boolean;
  cta: CTAProps;
}

const ProviderInfoBanner = ({
  defaultProvider,
  defaultDomain,
  deviceVisibility,
  headingElement = 'h1',
  headingSize = 'md',
  phoneNumberPreText = '',
  phoneNumberPostText = '',
  phoneNumber = '',
  displayBadges = false,
  displayReviews = false,
  displayPricing = false,
  ratingColor = 'info',
  ratingColorRange = '500',
  verifiedText,
  noHorizontalPadding = false,
  cta
}: ProviderInfoBannerProps): React.ReactElement => {
  const provider = useContext(ProviderContext)?.provider ?? defaultProvider;
  const isHidden = useResponsiveDisplay(deviceVisibility);
  const elementClicked = useElementClicked();
  const shouldShowPrice = useInquiryFormSubmitted();

  if (isHidden || !provider) {
    return <></>;
  }

  const isSubscription = contractIsSubscription(provider);
  const isEnhanced = providerIsEnhancedAndNotSuppressed(provider);
  const amenities = getProviderAmenitiesFromServices(provider);
  const featuredAmenities = getFeaturedAmenities(amenities);
  const hasFeaturedAmenities = Object.values(featuredAmenities).some(
    (value) => value
  );
  const { city, state } = provider as Provider;
  const homeCare: string[] = ['Home Care', 'Home Health Agencies'];
  const hasModifiedTitle =
    provider.tags?.some((tag) => homeCare.includes(tag)) &&
    !isEnhanced &&
    !providerIsClaimed(provider);
  const title = hasModifiedTitle
    ? `${provider.name} ${city?.name || ''}${
        city?.name && state?.code ? ', ' + state?.code : ''
      }`
    : provider.name;
  const pluralizedReviewCount = pluralize(provider?.reviewCount ?? 0, 'review');
  const showClaimListingButton =
    !(isEnhanced || isSubscription) && !!provider.legacyId;
  return (
    <Container
      mb={4}
      noHorizontalPadding={noHorizontalPadding}
      css={`
        container-type: inline-size;
      `}
    >
      <Flex
        height="100%"
        gap={3}
        paddingBottom={4}
        css={containerQueryAdapter({
          properties: [
            {
              property: 'flex-direction',
              values: {
                base: 'column',
                lg: 'row'
              }
            },
            {
              property: 'align-items',
              values: {
                base: 'stretch',
                lg: 'center'
              }
            }
          ]
        })}
      >
        <Flex flex={1} alignItems="center">
          <Box width="100%">
            <Heading
              headingElement={headingElement}
              headingSize={headingSize}
              title={title}
              withContainer={false}
              paddingBottom="6px"
            />

            <Text paddingBottom="6px">
              {provider.address?.formattedAddress}
            </Text>
            <Flex
              whiteSpace="normal"
              gap={3}
              alignItems="center"
              css={containerQueryAdapter({
                properties: [
                  {
                    property: 'flex-wrap',
                    values: {
                      base: 'wrap',
                      sm: 'nowrap'
                    }
                  }
                ]
              })}
            >
              {phoneNumber && !isSubscription && (
                <ProviderPhoneNumber
                  phoneNumber={phoneNumber}
                  phoneNumberPreText={phoneNumberPreText}
                  phoneNumberPostText={phoneNumberPostText}
                  isEnhanced={isEnhanced}
                  elementClicked={elementClicked}
                />
              )}

              {isSubscription && (
                <ProviderPhoneNumber
                  phoneNumber={
                    provider.phoneNumber.replace('+1', '') || phoneNumber
                  }
                  phoneNumberPreText={phoneNumberPreText}
                  phoneNumberPostText={phoneNumberPostText}
                  isEnhanced={isEnhanced}
                  elementClicked={elementClicked}
                />
              )}
              {showClaimListingButton && (
                <Button
                  as={Link}
                  isExternal
                  variant="outline"
                  colorScheme="primary"
                  size="sm"
                  aria-labelledby="claim-this-listing"
                  onClick={() => {
                    elementClicked({
                      element: {
                        type: ElementTypes.BUTTON,
                        action: ElementActions.INTERNAL_LINK,
                        name: ElementNames.CLAIM_LISTING,
                        text: 'Claim this listing',
                        color: 'primary',
                        textColor: 'primary'
                      },
                      destinationUrl: '/partners/get-listed/'
                    });

                    window.location.href = '/partners/get-listed/';
                  }}
                  rel="nofollow"
                  alignSelf="center"
                  css={containerQueryAdapter({
                    properties: [
                      {
                        property: 'margin-left',
                        values: {
                          base: '0',
                          md: '20px'
                        }
                      }
                    ]
                  })}
                >
                  <Text id="claim-this-listing" fontSize="xs" fontWeight="700">
                    Claim this listing
                  </Text>
                </Button>
              )}
            </Flex>
          </Box>
        </Flex>
        <Flex
          flex={1}
          height="100%"
          gap={3}
          css={containerQueryAdapter({
            properties: [
              {
                property: 'flex-direction',
                values: {
                  base: 'column',
                  sm: 'row'
                }
              },
              {
                property: 'align-items',
                values: {
                  base: 'stretch',
                  sm: 'center'
                }
              }
            ]
          })}
        >
          {(isSubscription || isEnhanced) && (
            <Flex
              flex={0}
              alignContent="center"
              css={containerQueryAdapter({
                properties: [
                  {
                    property: 'flex-wrap',
                    values: {
                      base: 'wrap',
                      sm: 'nowrap'
                    }
                  }
                ]
              })}
            >
              {<Divider hideBorder={true} />}
              <Verified verifiedText={verifiedText} />
            </Flex>
          )}

          {!!(
            displayReviews &&
            provider?.reviewCount &&
            provider?.reviewCount > 0
          ) && (
            <Flex
              flex={0}
              alignItems="center"
              css={containerQueryAdapter({
                properties: [
                  {
                    property: 'flex-wrap',
                    values: {
                      base: 'wrap',
                      sm: 'nowrap'
                    }
                  }
                ]
              })}
            >
              <Divider />
              <Flex
                as="a"
                display="flex"
                href="#all_reviews_section"
                color={`${ratingColor}.${ratingColorRange}`}
                flexWrap="wrap"
                alignItems="center"
                onClick={() => {
                  elementClicked({
                    element: {
                      type: ElementTypes.LINK,
                      action: ElementActions.JUMP_LINK,
                      name: ElementNames.PROVIDER_INFO_BANNER,
                      text: `${formatRating(provider?.averageRating)} ${
                        provider.reviewCount
                      } {pluralizedReviewCount}`,
                      color: `${ratingColor}.${ratingColorRange}`,
                      textColor: 'gray.900'
                    },
                    destinationUrl: '#all_reviews_section'
                  });
                }}
                css={containerQueryAdapter({
                  properties: [
                    {
                      property: 'padding-right',
                      values: {
                        base: '4px',
                        md: '0'
                      }
                    }
                  ]
                })}
              >
                <Text
                  display="flex"
                  fontSize="20px"
                  fontWeight={700}
                  alignItems="center"
                  aria-label={`${formatRating(
                    provider?.averageRating
                  )} star rating`}
                  height="24px"
                  css={containerQueryAdapter({
                    properties: [
                      {
                        property: 'padding-right',
                        values: {
                          base: '4px',
                          md: '0'
                        }
                      }
                    ]
                  })}
                >
                  <Icon
                    aria-label="full star"
                    as={MdStar}
                    boxSize={6}
                    role="figure"
                  />
                  {formatRating(provider.averageRating)}
                </Text>

                <Text color="gray.700" fontSize="md" whiteSpace="nowrap">
                  ({provider.reviewCount} {pluralizedReviewCount})
                </Text>
              </Flex>
            </Flex>
          )}
          {displayBadges &&
            provider.caringStars &&
            provider.caringStars.length > 0 && (
              <Flex
                as="a"
                flex={0}
                alignItems="center"
                href="#all_reviews_section"
                onClick={() => {
                  elementClicked({
                    element: {
                      type: ElementTypes.LINK,
                      action: ElementActions.JUMP_LINK,
                      name: ElementNames.PROVIDER_INFO_BANNER,
                      text: 'Caring Stars',
                      color: `primary`,
                      textColor: 'gray.900'
                    },
                    destinationUrl: '#all_reviews_section'
                  });
                }}
                css={containerQueryAdapter({
                  properties: [
                    {
                      property: 'flex-wrap',
                      values: {
                        base: 'wrap',
                        sm: 'nowrap'
                      }
                    }
                  ]
                })}
              >
                <Divider />
                <Image
                  src={caringStar.src}
                  alt="Top rated on Caring.com"
                  maxWidth="unset"
                  height="48px"
                  width="57px"
                />
                <Flex
                  flexWrap="wrap"
                  alignItems="center"
                  lineHeight="19px"
                  paddingLeft={1}
                >
                  <Text
                    fontWeight="700"
                    whiteSpace="nowrap"
                    css={containerQueryAdapter({
                      properties: [
                        {
                          property: 'padding-right',
                          values: {
                            base: '4px',
                            md: '0'
                          }
                        }
                      ]
                    })}
                  >
                    Caring Stars
                  </Text>
                  <Text whiteSpace="nowrap">award winner</Text>
                </Flex>
              </Flex>
            )}
          {displayPricing &&
            provider.minimumCost &&
            provider.minimumCost?.costCents > 0 && (
              <Flex
                as="a"
                flex={0}
                alignItems="center"
                href={`#provider-costs-${provider.id}`}
                onClick={() => {
                  elementClicked({
                    element: {
                      type: ElementTypes.LINK,
                      action: ElementActions.JUMP_LINK,
                      name: ElementNames.PROVIDER_INFO_BANNER,
                      text: 'Starting Price',
                      color: `${ratingColor}.${ratingColorRange}`,
                      textColor: 'gray.900'
                    },
                    destinationUrl: `#provider-costs-${provider.id}`
                  });
                }}
                css={containerQueryAdapter({
                  properties: [
                    {
                      property: 'flex-wrap',
                      values: {
                        base: 'wrap',
                        sm: 'nowrap'
                      }
                    }
                  ]
                })}
              >
                <Divider />
                <Flex flexWrap="wrap" alignItems="center" lineHeight="24px">
                  <Text
                    height="22px"
                    whiteSpace="nowrap"
                    css={containerQueryAdapter({
                      properties: [
                        {
                          property: 'padding-right',
                          values: {
                            base: '8px',
                            md: '0'
                          }
                        }
                      ]
                    })}
                  >
                    Starting Price
                  </Text>
                  <Text
                    fontSize="xl"
                    fontWeight="700"
                    filter={shouldShowPrice ? '' : 'blur(10px)'}
                  >{`$${formatNumberLocale(
                    provider.minimumCost.costCents / 100
                  )}`}</Text>
                </Flex>
              </Flex>
            )}
          {cta && cta?.actionBehavior?.inquiryId && (
            <Flex
              flex={0}
              alignItems="center"
              css={containerQueryAdapter({
                properties: [
                  {
                    property: 'display',
                    values: {
                      base: 'none',
                      md: 'flex'
                    }
                  }
                ]
              })}
            >
              <Divider />
              <CTA
                {...cta}
                padding={0}
                margin={0}
                buttonProps={{
                  padding: '8px 16px',
                  elementAction: ElementActions.OPEN_MODAL
                }}
              />
            </Flex>
          )}
        </Flex>
      </Flex>
      <Flex
        justifyContent="space-between"
        borderColor="gray.400"
        borderTopStyle="solid"
        css={containerQueryAdapter({
          properties: [
            {
              property: 'flex-direction',
              values: {
                base: 'column',
                lg: 'row'
              }
            },
            {
              property: 'gap',
              values: {
                base: undefined,
                lg: '6px'
              }
            },
            {
              property: 'border-top-width',
              values: {
                base: '1px',
                sm: '0'
              }
            },
            {
              property: 'padding-top',
              values: {
                base: '16px',
                sm: '0'
              }
            }
          ]
        })}
      >
        {provider.tags && provider.isIndexed && (
          <Box
            color="gray.700"
            css={containerQueryAdapter({
              properties: [
                {
                  property: 'font-size',
                  values: {
                    base: 'sm',
                    md: 'md'
                  }
                }
              ]
            })}
          >
            {HtmlToReact({
              html: `Care Offered: ${provider.careTypesDescription}`
            })}
          </Box>
        )}
        {hasFeaturedAmenities && (
          <Flex
            as="a"
            href="#offering-listing"
            width="fit-content"
            alignItems="center"
            css={containerQueryAdapter({
              properties: [
                {
                  property: 'gap',
                  values: {
                    base: '0',
                    sm: '8px'
                  }
                },
                {
                  property: 'padding-top',
                  values: {
                    base: '8px',
                    lg: '0'
                  }
                },
                {
                  property: 'justify-content',
                  values: {
                    base: 'left',
                    md: 'right'
                  }
                }
              ]
            })}
          >
            {featuredAmenities?.internet && (
              <FeaturedAmenity icon={MdWifi} label="Wi-Fi" />
            )}
            {featuredAmenities?.pets && (
              <FeaturedAmenity icon={MdPets} label="Pet Friendly" />
            )}
            {featuredAmenities?.wheelchair && (
              <FeaturedAmenity
                icon={MdWheelchairPickup}
                label="Mobility Assistance"
              />
            )}
            {featuredAmenities?.transportation && (
              <FeaturedAmenity
                icon={MdDirectionsBus}
                label="Transportation Services"
              />
            )}
          </Flex>
        )}
        {cta && cta?.actionBehavior?.inquiryId && (
          <Flex
            flex={0}
            alignItems="center"
            css={containerQueryAdapter({
              properties: [
                {
                  property: 'padding-top',
                  values: {
                    base: '16px',
                    md: '0'
                  }
                },
                {
                  property: 'display',
                  values: {
                    base: 'flex',
                    md: 'none'
                  }
                }
              ]
            })}
          >
            <CTA
              {...cta}
              width="100%"
              padding={0}
              margin={0}
              buttonProps={{
                width: '100%',
                elementAction: ElementActions.OPEN_MODAL
              }}
            />
          </Flex>
        )}
      </Flex>
    </Container>
  );
};

export default ProviderInfoBanner;
