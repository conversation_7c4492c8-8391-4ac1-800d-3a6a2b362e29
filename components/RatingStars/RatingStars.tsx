import { Icon, IconProps } from '@chakra-ui/icon';
import { HStack, StackProps, Text, TextProps } from '@chakra-ui/layout';
import Container from '@components/LayoutStructure/Container';
import { MdStar } from 'react-icons/md';

import { formatRating } from './utils';

interface RatingStarsProps {
  labelSize?: TextProps['fontSize'];
  mb?: number;
  maxRating?: number;
  minRating?: number;
  rating: number;
  showLabel?: boolean;
  spacing?: StackProps['spacing'];
  starColor?: IconProps['color'];
  starSize?: IconProps['boxSize'];
  withContainer?: boolean;
}

export const RatingStars: React.FC<RatingStarsProps> = ({
  labelSize = 'md',
  mb = 0,
  maxRating = 5,
  minRating = 0,
  rating,
  showLabel = true,
  spacing = 0,
  starColor = 'tertiary.400',
  starSize = '18px',
  withContainer = false
}) => {
  if (rating < minRating || rating > maxRating) {
    console.error('RatingStars: Invalid rating.', rating);
    return null;
  }
  const starCount = Math.floor(rating);
  const displayRating = formatRating(rating);

  const ratingStars = () => (
    <HStack
      mb={mb}
      spacing={spacing}
      align="center"
      // Aria attributes
      role="img"
      aria-label={`${displayRating} of ${maxRating} stars`}
    >
      {Array.from({ length: starCount }).map((_, i) => (
        <Icon
          key={i}
          as={MdStar}
          boxSize={starSize}
          color={starColor}
          data-testid="star-icon"
        />
      ))}

      {showLabel && (
        <Text
          color={starColor}
          fontSize={labelSize}
          fontWeight="bold"
          lineHeight="120%"
          // An extra padding to ensure the label has the same base spacing as the stars
          paddingLeft="0.5"
        >
          {displayRating}
        </Text>
      )}
    </HStack>
  );

  if (!withContainer) return ratingStars();

  return <Container>{ratingStars()}</Container>;
};

export default RatingStars;
