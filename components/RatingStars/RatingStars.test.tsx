import { render, screen } from '@testing-library/react';

import axe from '~/axe-helper';

import RatingStars from './RatingStars';

describe('RatingStars', () => {
  it('renders the stars without violations', async () => {
    const { container } = render(<RatingStars rating={3.5} />);

    expect(screen.getByLabelText('3.5 of 5 stars')).toBeVisible();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders the stars with a label', () => {
    render(<RatingStars rating={3.5} showLabel={true} />);

    expect(screen.getByLabelText('3.5 of 5 stars')).toBeVisible();
    expect(screen.getByText('3.5')).toBeVisible();
  });

  it('parses the rating to a number with one decimal place', () => {
    render(<RatingStars rating={3.15} />);

    expect(screen.getByLabelText('3.2 of 5 stars')).toBeVisible();
  });

  it('parses the rating to a number with a decimal place if the rating is a whole number', () => {
    render(<RatingStars rating={3} />);

    expect(screen.getByLabelText('3.0 of 5 stars')).toBeVisible();
  });

  it('renders the correct number of stars when the rating is decimal', () => {
    render(<RatingStars rating={3.5} />);

    expect(screen.getAllByTestId('star-icon').length).toBe(3);
  });

  it('renders the correct star types when the rating is a whole number', () => {
    render(<RatingStars rating={3} />);

    expect(screen.getAllByTestId('star-icon').length).toBe(3);
  });
});
