import { GridItem, GridItemProps, GridProps } from '@chakra-ui/layout';

import BaseLayout, { BaseLayoutProps } from './BaseLayout';
import LAYOUT from './layoutConstants';

interface CompoundComponents {
  Main: typeof MainContent;
  Sidebar: typeof SidebarContent;
}

export type SidebarContentProps = GridItemProps;
export type MainContentProps = GridItemProps;
export type SidebarLayoutProps = GridProps;

const MainContent: React.FC<GridItemProps> = ({ children, ...rest }) => {
  return (
    <GridItem as="main" colSpan={{ base: 2, xl: 2 }} {...rest}>
      {children}
    </GridItem>
  );
};

const SidebarContent: React.FC<GridItemProps> = ({ children, ...rest }) => {
  return (
    <GridItem as="aside" colSpan={{ base: 2, xl: 1 }} {...rest}>
      {children}
    </GridItem>
  );
};

const SidebarLayout: React.FC<BaseLayoutProps> & CompoundComponents = ({
  children,
  ...rest
}) => {
  return (
    <BaseLayout maxW={LAYOUT.CONTAINER_MAX_WIDTH} {...rest}>
      {children}
    </BaseLayout>
  );
};

SidebarLayout.Main = MainContent;
SidebarLayout.Sidebar = SidebarContent;

export default SidebarLayout;
