import { Grid, GridItem, GridItemProps, GridProps } from '@chakra-ui/layout';
import React, { useMemo } from 'react';

import { SiteDefinition } from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';

import { getDynamicLayout } from './layoutConstants';

interface CompoundComponents {
  Header: typeof Header;
  Content: typeof Content;
  Footer: typeof Footer;
}

const Header: React.FC<GridItemProps> = ({ children, ...rest }) => {
  const template = React.isValidElement(children)
    ? children.props?.page?.['mgnl:template']
    : undefined;
  const isSemPage = template === 'spa-lm:pages/sem';
  const stickyHeader: GridItemProps = isSemPage
    ? { maxHeight: '72px' }
    : { position: 'sticky', top: 0 };
  return (
    <GridItem area="header" {...stickyHeader} zIndex="sticky" {...rest}>
      {children}
    </GridItem>
  );
};

const Content: React.FC<GridItemProps> = ({ children, ...rest }) => {
  return (
    <GridItem
      area="content"
      display="flex"
      flexDirection="column"
      alignItems="center"
      sx={{
        // This is a workaround for the EditablePage component not supporting custom CSS classes.
        '& > div': {
          w: '100%'
        }
      }}
      {...rest}
    >
      {children}
    </GridItem>
  );
};

const Footer: React.FC<GridItemProps> = ({ children, ...rest }) => {
  return (
    <GridItem area="footer" {...rest}>
      {children}
    </GridItem>
  );
};

interface RootLayoutProps extends GridProps {
  site?: SiteDefinition;
  template?: string;
}

const RootLayout: React.FC<RootLayoutProps> & CompoundComponents = ({
  children,
  site,
  template,
  ...rest
}) => {
  const sitePath = site?.path || CaringDomains.LIVE;
  const isSemPage = template === 'spa-lm:pages/sem';
  const layout = useMemo(() => getDynamicLayout(sitePath), [sitePath]);
  const headerDesktopHeight = isSemPage ? '72px' : layout.HEADER_DESKTOP_HEIGHT;
  return (
    <Grid
      minH="100vh"
      templateAreas={`"header"
                      "content"
                      "footer"`}
      gridTemplateRows={{
        base: `${layout.HEADER_MOBILE_HEIGHT} 1fr min-content`,
        xl: `${headerDesktopHeight} 1fr min-content`
      }}
      {...rest}
    >
      {children}
    </Grid>
  );
};

RootLayout.Header = Header;
RootLayout.Content = Content;
RootLayout.Footer = Footer;

export default RootLayout;
