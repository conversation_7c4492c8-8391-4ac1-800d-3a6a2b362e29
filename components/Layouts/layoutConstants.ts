import { Domain } from '~/types/Domains';
import { checkIfIsASeniorHomesDomain } from '~/utils/isSeniorHomesDomain';

const DEFAULT_HEADER_DESKTOP_HEIGHT = 72;
const DEFAULT_HEADER_MOBILE_HEIGHT = 56;
const STICKY_TOP_OFFSET = 90;

const SITE_HEADER_HEIGHTS = {
  caring: {
    desktop: 140,
    mobile: 56
  },
  seniorhomes: {
    desktop: 72,
    mobile: 56
  }
};

export const getHeaderHeights = (sitePath?: Domain) => {
  if (!sitePath) {
    return {
      desktop: DEFAULT_HEADER_DESKTOP_HEIGHT,
      mobile: DEFAULT_HEADER_MOBILE_HEIGHT
    };
  }

  const isSeniorHomes = checkIfIsASeniorHomesDomain(sitePath);

  if (isSeniorHomes) {
    return SITE_HEADER_HEIGHTS.seniorhomes;
  }

  return SITE_HEADER_HEIGHTS.caring;
};

export const getDynamicLayout = (sitePath?: Domain) => {
  const heights = getHeaderHeights(sitePath);

  return {
    COLUMN_GAP: '20px',
    DESKTOP_TEMPLATE_COLUMNS: 'repeat(3, 1fr)',
    MOBILE_TEMPLATE_COLUMNS: 'repeat(2, 1fr)',

    CONTAINER_HORIZONTAL_PADDING: '32px',
    CONTAINER_VERTICAL_PADDING: '32px',
    CONTAINER_MAX_WIDTH: '1280px',
    CONTAINER_DESKTOP_SPACING: '32px',
    CONTAINER_MOBILE_SPACING: '32px',

    DESKTOP_TOP_PADDING: '48px',
    MOBILE_TOP_PADDING: '32px',

    SIDEBAR_DESKTOP_SPACING: '16px',
    SIDEBAR_MOBILE_SPACING: '16px',

    HEADER_DESKTOP_HEIGHT: `${heights.desktop}px`,
    HEADER_MOBILE_HEIGHT: `${heights.mobile}px`,

    STICKY_TOP_OFFSET: `${heights.desktop + STICKY_TOP_OFFSET}px`
  };
};

// Backward compatibility - default layout
const LAYOUT = getDynamicLayout();

export default LAYOUT;
