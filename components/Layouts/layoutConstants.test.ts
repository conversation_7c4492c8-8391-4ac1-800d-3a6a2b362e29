import { Domain } from '~/types/Domains';
import { checkIfIsASeniorHomesDomain } from '~/utils/isSeniorHomesDomain';

import { getDynamicLayout, getHeaderHeights } from './layoutConstants';

jest.mock('~/utils/isSeniorHomesDomain');
const mockCheckIfIsASeniorHomesDomain =
  checkIfIsASeniorHomesDomain as jest.MockedFunction<
    typeof checkIfIsASeniorHomesDomain
  >;

describe('layoutConstants', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getHeaderHeights', () => {
    it('should return default heights when no sitePath is provided', () => {
      const result = getHeaderHeights();

      expect(result).toEqual({
        desktop: 72,
        mobile: 56
      });
    });

    it('should return default heights when sitePath is undefined', () => {
      const result = getHeaderHeights(undefined);

      expect(result).toEqual({
        desktop: 72,
        mobile: 56
      });
    });

    it('should return seniorhomes heights when domain is senior homes', () => {
      const mockDomain = 'seniorhomes.com' as Domain;
      mockCheckIfIsASeniorHomesDomain.mockReturnValue(true);

      const result = getHeaderHeights(mockDomain);

      expect(result).toEqual({
        desktop: 72,
        mobile: 56
      });
      expect(mockCheckIfIsASeniorHomesDomain).toHaveBeenCalledWith(mockDomain);
    });

    it('should return caring heights when domain is not senior homes', () => {
      const mockDomain = 'caring.com' as Domain;
      mockCheckIfIsASeniorHomesDomain.mockReturnValue(false);

      const result = getHeaderHeights(mockDomain);

      expect(result).toEqual({
        desktop: 140,
        mobile: 56
      });
      expect(mockCheckIfIsASeniorHomesDomain).toHaveBeenCalledWith(mockDomain);
    });
  });

  describe('getDynamicLayout', () => {
    it('should return layout with default header heights when no sitePath is provided', () => {
      const result = getDynamicLayout();

      expect(result).toEqual({
        COLUMN_GAP: '20px',
        DESKTOP_TEMPLATE_COLUMNS: 'repeat(3, 1fr)',
        MOBILE_TEMPLATE_COLUMNS: 'repeat(2, 1fr)',
        CONTAINER_HORIZONTAL_PADDING: '32px',
        CONTAINER_VERTICAL_PADDING: '32px',
        CONTAINER_MAX_WIDTH: '1280px',
        CONTAINER_DESKTOP_SPACING: '32px',
        CONTAINER_MOBILE_SPACING: '32px',
        DESKTOP_TOP_PADDING: '48px',
        MOBILE_TOP_PADDING: '32px',
        SIDEBAR_DESKTOP_SPACING: '16px',
        SIDEBAR_MOBILE_SPACING: '16px',
        HEADER_DESKTOP_HEIGHT: '72px',
        HEADER_MOBILE_HEIGHT: '56px',
        STICKY_TOP_OFFSET: '162px' // 72 + 90
      });
    });

    it('should return layout with seniorhomes header heights', () => {
      const mockDomain = 'seniorhomes.com' as Domain;
      mockCheckIfIsASeniorHomesDomain.mockReturnValue(true);

      const result = getDynamicLayout(mockDomain);

      expect(result.HEADER_DESKTOP_HEIGHT).toBe('72px');
      expect(result.HEADER_MOBILE_HEIGHT).toBe('56px');
      expect(result.STICKY_TOP_OFFSET).toBe('162px'); // 72 + 90
      expect(mockCheckIfIsASeniorHomesDomain).toHaveBeenCalledWith(mockDomain);
    });

    it('should return layout with caring header heights', () => {
      const mockDomain = 'caring.com' as Domain;
      mockCheckIfIsASeniorHomesDomain.mockReturnValue(false);

      const result = getDynamicLayout(mockDomain);

      expect(result.HEADER_DESKTOP_HEIGHT).toBe('140px');
      expect(result.HEADER_MOBILE_HEIGHT).toBe('56px');
      expect(result.STICKY_TOP_OFFSET).toBe('230px'); // 140 + 90
      expect(mockCheckIfIsASeniorHomesDomain).toHaveBeenCalledWith(mockDomain);
    });

    it('should include all expected layout properties', () => {
      const result = getDynamicLayout();

      const expectedProperties = [
        'COLUMN_GAP',
        'DESKTOP_TEMPLATE_COLUMNS',
        'MOBILE_TEMPLATE_COLUMNS',
        'CONTAINER_HORIZONTAL_PADDING',
        'CONTAINER_VERTICAL_PADDING',
        'CONTAINER_MAX_WIDTH',
        'CONTAINER_DESKTOP_SPACING',
        'CONTAINER_MOBILE_SPACING',
        'DESKTOP_TOP_PADDING',
        'MOBILE_TOP_PADDING',
        'SIDEBAR_DESKTOP_SPACING',
        'SIDEBAR_MOBILE_SPACING',
        'HEADER_DESKTOP_HEIGHT',
        'HEADER_MOBILE_HEIGHT',
        'STICKY_TOP_OFFSET'
      ];

      expectedProperties.forEach((property) => {
        expect(result).toHaveProperty(property);
        expect(result[property as keyof typeof result]).toBeDefined();
      });
    });

    it('should calculate STICKY_TOP_OFFSET correctly for different domains', () => {
      mockCheckIfIsASeniorHomesDomain.mockReturnValue(false);
      const caringResult = getDynamicLayout('caring.com' as Domain);
      expect(caringResult.STICKY_TOP_OFFSET).toBe('230px'); // 140 + 90

      mockCheckIfIsASeniorHomesDomain.mockReturnValue(true);
      const seniorHomesResult = getDynamicLayout('seniorhomes.com' as Domain);
      expect(seniorHomesResult.STICKY_TOP_OFFSET).toBe('162px'); // 72 + 90
    });
  });

  describe('default export (LAYOUT)', () => {
    it('should export default layout with default header heights', () => {
      const LAYOUT = require('./layoutConstants').default;

      expect(LAYOUT.HEADER_DESKTOP_HEIGHT).toBe('72px');
      expect(LAYOUT.HEADER_MOBILE_HEIGHT).toBe('56px');
      expect(LAYOUT.STICKY_TOP_OFFSET).toBe('162px');
    });
  });
});
