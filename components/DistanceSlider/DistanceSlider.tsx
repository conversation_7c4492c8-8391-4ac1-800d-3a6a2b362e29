import {
  Slider,
  SliderFilledTrack,
  SliderMark,
  SliderThumb,
  SliderTrack,
  Text,
  Tooltip
} from '@chakra-ui/react';
import isEqual from 'lodash/isEqual';
import { memo, useCallback, useState } from 'react';

interface SearchSliderProps {
  min?: number;
  max?: number;
  unity?: string;
  onChange: (name: string, value: number) => void;
  value: number;
}

const DistanceSearchSlider: React.FC<SearchSliderProps> = (props) => {
  const name = 'distanceInMiles';
  const { min = 10, max = 50, value = max, unity = 'miles', onChange } = props;
  const [showTooltip, setShowTooltip] = useState(false);

  // State to keep track of the distance when the user is sliding
  const [distance, setDistance] = useState<number>(value);

  const handleSliderChange = useCallback(
    (newValue: number) => {
      // Prevent unnecessary re-renders when the value is the same
      if (isEqual(newValue, value)) {
        return;
      }

      onChange(name, newValue);
    },
    [value, onChange]
  );

  return (
    <Slider
      name={name}
      value={distance}
      defaultValue={max}
      min={min}
      max={max}
      step={10}
      focusThumbOnChange={false}
      colorScheme="blue"
      onChange={(distance) => setDistance(distance)}
      // When the user stops sliding, notify the parent component
      onChangeEnd={handleSliderChange}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <SliderMark value={min} mt="2" fontWeight="bold">
        <Text>
          {min} {unity}
        </Text>
      </SliderMark>
      <SliderMark value={max} mt="2" fontWeight="bold">
        <Text textAlign="right" whiteSpace="nowrap" ml="-16">
          {max} {unity}
        </Text>
      </SliderMark>
      <SliderTrack bg="gray.500">
        <SliderFilledTrack bg="blue.400" />
      </SliderTrack>
      <Tooltip
        hasArrow
        bg="blue.400"
        color="white"
        placement="top"
        isOpen={showTooltip}
        label={value}
      >
        <SliderThumb boxSize={4} />
      </Tooltip>
    </Slider>
  );
};

export default memo(DistanceSearchSlider);
