import Link from 'next/link';
import { UrlObject } from 'url';

interface ShallowLinkProps {
  children: React.ReactNode;
  className?: string;
  href: string | UrlObject;
  onClick?: () => void;
}

const ShallowLink: React.FC<ShallowLinkProps> = ({
  children,
  className,
  href,
  onClick
}) => {
  return (
    <Link
      href={href}
      scroll={false}
      shallow
      className={className}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export default ShallowLink;
