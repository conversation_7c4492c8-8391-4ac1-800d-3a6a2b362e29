import { render, screen, waitFor } from '@utils/test-utils';
import fetch from 'jest-fetch-mock';

import StreetViewModal from './StreetViewModal';

jest.mock('next/router', () => ({
  useRouter: () => ({
    pathname: '/gallery',
    query: {},
    push: jest.fn()
  })
}));

describe('StreetViewModal', () => {
  beforeEach(() => {
    fetch.resetMocks();
  });

  it('should use outdoor source when available', async () => {
    fetch.mockResponseOnce(
      JSON.stringify({
        status: 'OK'
      })
    );

    render(
      <StreetViewModal
        coordinates={{ latitude: 123, longitude: 456 }}
        isStreetViewModalOpen={true}
        setIsStreetViewModalOpen={jest.fn()}
      />
    );

    await waitFor(() => {
      const iframe = screen.getByTestId('street-view-iframe');
      expect(iframe).toHaveAttribute(
        'src',
        expect.stringContaining('source=outdoor')
      );
    });
  });

  it('should fallback to default source when outdoor is not available', async () => {
    fetch.mockResponseOnce(
      JSON.stringify({
        status: 'ZERO_RESULTS'
      })
    );

    render(
      <StreetViewModal
        coordinates={{ latitude: 123, longitude: 456 }}
        isStreetViewModalOpen={true}
        setIsStreetViewModalOpen={jest.fn()}
      />
    );

    await waitFor(() => {
      const iframe = screen.getByTestId('street-view-iframe');
      expect(iframe).toHaveAttribute(
        'src',
        expect.not.stringContaining('source=outdoor')
      );
    });
  });

  it('should handle error cases', async () => {
    fetch.mockRejectOnce(new Error('Network Error'));

    render(
      <StreetViewModal
        coordinates={{ latitude: 123, longitude: 456 }}
        isStreetViewModalOpen={true}
        setIsStreetViewModalOpen={jest.fn()}
      />
    );

    await waitFor(() => {
      const iframe = screen.getByTestId('street-view-iframe');
      expect(iframe).toHaveAttribute(
        'src',
        expect.not.stringContaining('source=outdoor')
      );
    });
  });
});
