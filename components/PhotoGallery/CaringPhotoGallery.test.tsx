/* These need to be disabled to enable mocking next/image */
/* eslint-disable react/display-name */
/* eslint-disable @next/next/no-img-element */

import {
  cleanup,
  render,
  screen,
  setDesktopScreen,
  setMobileScreen,
  waitFor
} from '@utils/test-utils';
import { act } from 'react';

import PhotoGallery from './CaringPhotoGallery';

jest.mock('next/router', () => ({
  useRouter: () => ({
    pathname: '/gallery',
    query: {},
    push: jest.fn()
  })
}));

// Mock dynamic imports for street view images
jest.mock('~/assets/streetview/streetview_small.jpg', () => ({
  default: 'streetview_small.jpg'
}));
jest.mock('~/assets/streetview/streetview_tall.jpg', () => ({
  default: 'streetview_tall.jpg'
}));
jest.mock('~/assets/streetview/streetview_wide.jpg', () => ({
  default: 'streetview_wide.jpg'
}));

jest.mock('next/image', () => {
  return ({ src, alt, fill, priority, ...props }) => {
    // Check if src is an object (imported image) and extract the default value
    const resolvedSrc =
      typeof src === 'object' && src.default ? src.default : src;

    return <img src={resolvedSrc} alt={alt} {...props} />;
  };
});

describe('PhotoGallery', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  const photos = [
    { url: '/photo1.jpg', alt: 'Photo 1' },
    { url: '/photo2.jpg', alt: 'Photo 2' },
    { url: '/photo3.jpg', alt: 'Photo 3' }
  ];

  it('should render the gallery on desktop', async () => {
    await act(async () => {
      setDesktopScreen();
      render(
        <PhotoGallery
          photos={photos}
          setIsStreetViewModalOpen={jest.fn()}
          setGalleryIndex={jest.fn()}
        />
      );
    });
    await waitFor(() => {
      const images = document.querySelectorAll('img');
      expect(images).toHaveLength(4); // 3 photos + 1 street view image
      expect(screen.getByAltText('Photo 1')).toBeInTheDocument();
      expect(screen.getByAltText('Photo 2')).toBeInTheDocument();
      expect(screen.getByAltText('Photo 3')).toBeInTheDocument();
      expect(screen.getByAltText('streetview')).toBeInTheDocument();
      expect(screen.getByText('Show all photos')).toBeInTheDocument();
    });
  });

  it('should render the gallery on mobile', async () => {
    await act(async () => {
      setMobileScreen();
      render(
        <PhotoGallery
          photos={photos}
          setIsStreetViewModalOpen={jest.fn()}
          setGalleryIndex={jest.fn()}
        />
      );
    });
    await waitFor(() => {
      const images = document.querySelectorAll('img');
      // With the responsive photo gallery, only 1 photo is visible though 4 are present in the DOM
      expect(images).toHaveLength(4);
      expect(screen.getByAltText('Photo 1')).toBeInTheDocument();
      // Ensure the other photos are hidden on mobile
      expect(screen.queryByAltText('Photo 2')?.parentElement).toHaveStyle(
        'display: none'
      );
      expect(screen.queryByAltText('Photo 3')?.parentElement).toHaveStyle(
        'display: none'
      );
      expect(screen.queryByAltText('streetview')?.parentElement).toHaveStyle(
        'display: none'
      );
    });
  });

  it('should render the street view button when there is 1 photo', async () => {
    await act(async () => {
      setDesktopScreen();
      render(
        <PhotoGallery
          photos={photos.slice(0, 1)}
          setIsStreetViewModalOpen={jest.fn()}
          setGalleryIndex={jest.fn()}
        />
      );
    });
    await waitFor(() => {
      const streetViewButton = screen.getByText('Street view');
      expect(streetViewButton).toBeInTheDocument();
    });
  });

  it('should render the show all photos and street view button when there are more than 1 photo', async () => {
    await act(async () => {
      setDesktopScreen();
      render(
        <PhotoGallery
          photos={photos}
          setIsStreetViewModalOpen={jest.fn()}
          setGalleryIndex={jest.fn()}
        />
      );
    });
    await waitFor(() => {
      const showAllPhotosButton = screen.getByText('Show all photos');
      expect(showAllPhotosButton).toBeInTheDocument();

      const streetViewButton = screen.getByText('Street view');
      expect(streetViewButton).toBeInTheDocument();
    });
  });
});
