import { Icon } from '@chakra-ui/icon';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button, { ButtonProps } from '@components/Button';
import React from 'react';
import { Md360, MdOutlineCameraAlt } from 'react-icons/md';

interface IconButtonProps extends Omit<ButtonProps, 'elementAction'> {}

export const StreetViewIconButton: React.FC<IconButtonProps> = ({
  children,
  ...rest
}) => {
  return (
    <Button
      bg="white"
      borderRadius="4px"
      borderWidth="1px"
      colorScheme="primary"
      h="30px"
      width={7}
      leftIcon={<Icon as={Md360} boxSize={6} />}
      iconSpacing={0}
      minWidth={0}
      variant="outline"
      data-testid="street-view"
      elementAction={ElementActions.OPEN_MODAL}
      elementName={ElementNames.STREET_VIEW}
      elementType={ElementTypes.BUTTON}
      {...rest}
    />
  );
};

export const ShowAllPhotosIconButton: React.FC<IconButtonProps> = ({
  children,
  ...rest
}) => {
  return (
    <Button
      bg="white"
      borderRadius="4px"
      borderWidth="1px"
      colorScheme="primary"
      fontSize="16px"
      fontWeight="700"
      h="30px"
      leftIcon={<Icon as={MdOutlineCameraAlt} mr="-1.5" boxSize={5} />}
      px="4px"
      variant="outline"
      elementAction={ElementActions.OPEN_MODAL}
      elementName={ElementNames.VIEW_GALLERY}
      elementType={ElementTypes.BUTTON}
      {...rest}
    >
      {children}
    </Button>
  );
};
