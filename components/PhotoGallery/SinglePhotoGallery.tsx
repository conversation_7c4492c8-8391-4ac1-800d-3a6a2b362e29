import { Box } from '@chakra-ui/layout';
import { SEARCH_PARAM } from '@constants/search-params';
import Image from 'next/image';

import {
  ShowAllPhotosIconButton,
  StreetViewIconButton
} from './PhotoGalleryIconButtons'; // Import the buttons
import { Photo } from './types';

interface SinglePhotoGalleryProps {
  photos: Array<Photo>;
  setIsStreetViewModalOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  setGalleryIndex: React.Dispatch<React.SetStateAction<null | number>>;
}

export const SinglePhotoGallery: React.FC<SinglePhotoGalleryProps> = ({
  photos,
  setIsStreetViewModalOpen,
  setGalleryIndex
}) => {
  const [photoToDisplay] = photos;

  return (
    <>
      <Box
        onClick={() => {
          setGalleryIndex(1);
          window.history.replaceState(
            window.history.state,
            '',
            `?${SEARCH_PARAM.PHOTO_INDEX}=1`
          );
        }}
      >
        <Box position="relative" sx={{ aspectRatio: '627 / 407' }}>
          <Image
            alt={photoToDisplay.alt}
            fill
            priority
            sizes="(max-width: 992px) 100vw, 850px"
            src={photoToDisplay.url}
            style={{ objectFit: 'cover', objectPosition: 'center' }}
          />
        </Box>
      </Box>
      <Box
        bottom="12px"
        left="12px"
        position="absolute"
        gap="4px"
        display="flex"
      >
        {photos.length > 0 ? (
          <ShowAllPhotosIconButton
            onClick={() => {
              setGalleryIndex(1);
              window.history.replaceState(
                window.history.state,
                '',
                `?${SEARCH_PARAM.PHOTO_INDEX}=1`
              );
            }}
          >
            {photos.length}
          </ShowAllPhotosIconButton>
        ) : null}
        <StreetViewIconButton
          onClick={() => {
            setIsStreetViewModalOpen && setIsStreetViewModalOpen(true);
            window.history.replaceState(
              window.history.state,
              '',
              `?${SEARCH_PARAM.STREET_VIEW}=true`
            );
          }}
        />
      </Box>
    </>
  );
};

export default SinglePhotoGallery;
