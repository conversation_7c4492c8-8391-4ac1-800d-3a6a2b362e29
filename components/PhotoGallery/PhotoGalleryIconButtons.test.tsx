import { fireEvent, render, screen } from '@utils/test-utils';

import {
  ShowAllPhotosIconButton,
  StreetViewIconButton
} from './PhotoGalleryIconButtons';

jest.mock('next/router', () => ({
  useRouter: () => ({
    pathname: '/gallery',
    query: {},
    push: jest.fn()
  })
}));

describe('PhotoGalleryButtons', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  it('should render StreetViewIconButton and handle click', () => {
    const handleClick = jest.fn();
    render(<StreetViewIconButton onClick={handleClick} />);

    const button = screen.getByTestId('street-view');
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should render ShowAllPhotosIconButton and handle click', () => {
    const handleClick = jest.fn();
    render(
      <ShowAllPhotosIconButton onClick={handleClick}>
        View all photos
      </ShowAllPhotosIconButton>
    );

    const button = screen.getByText('View all photos');
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
