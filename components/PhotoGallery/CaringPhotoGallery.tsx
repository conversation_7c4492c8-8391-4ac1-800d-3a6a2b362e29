import { Icon } from '@chakra-ui/icon';
import { Grid, GridItem } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import WhichImage from '@components/Image/WhichImage';
import { SEARCH_PARAM } from '@constants/search-params';
import { useEffect, useState } from 'react';
import { BsGrid3X3Gap } from 'react-icons/bs';
import { Md360 } from 'react-icons/md';

import {
  ShowAllPhotosIconButton,
  StreetViewIconButton
} from './PhotoGalleryIconButtons'; // Import the buttons
import { Photo } from './types';
import { getAreaForItem, getTemplateAreas } from './utils';

const MAX_PHOTO_COUNT = 5;

const TEMPLATE_AREAS = [
  // One photo
  `"A A A"
   "A A A"
   "A A A"`,
  // Two photos
  `"A A A"
   "A A A"
   "B B B"`,
  // Three photos
  `"A A B"
   "A A B"
   "C C C"`,
  // Four photos
  `"A A B"
   "A A B"
   "C C D"`,
  // Five photos
  `"A A B"
   "A A B"
   "C D E"`,
  // Six photos
  `"A A B"
   "A A C"
   "D E F"`
];

interface CaringPhotoGalleryProps {
  photos: Array<Photo>;
  setIsStreetViewModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setGalleryIndex: React.Dispatch<React.SetStateAction<null | number>>;
}

// Non-icon variant of the street view button
const ShowStreetViewButton = (props) => {
  return (
    <Button
      color="white"
      leftIcon={<Icon as={Md360} boxSize={6} />}
      pos={'absolute'}
      variant="outline"
      _hover={{ backgroundColor: 'gray.600' }}
      elementAction={ElementActions.OPEN_MODAL}
      elementName={ElementNames.STREET_VIEW}
      elementType={ElementTypes.BUTTON}
      {...props}
    >
      Street view
    </Button>
  );
};

// Non-icon variant of the show all photos button
const ShowAllPhotosButton = (props: any) => {
  return (
    <Button
      data-testid="show-all-photos"
      color="white"
      leftIcon={<BsGrid3X3Gap />}
      pos={'absolute'}
      variant="outline"
      _hover={{ backgroundColor: 'gray.600' }}
      elementAction={ElementActions.OPEN_MODAL}
      elementName={ElementNames.VIEW_GALLERY}
      elementType={ElementTypes.BUTTON}
      display={{ base: 'none', sm: 'flex' }} // Hide on mobile
      {...props}
    >
      Show all photos
    </Button>
  );
};

const PhotoGallery: React.FC<CaringPhotoGalleryProps> = ({
  photos,
  setIsStreetViewModalOpen,
  setGalleryIndex
}) => {
  const [streetViewImage, setStreetViewImage] = useState<Photo>({
    url: '',
    alt: 'streetview'
  });

  useEffect(() => {
    const loadImage = async (path: string) => {
      try {
        const image = await import(`~/assets/streetview/${path}`);
        setStreetViewImage({ url: image.default, alt: 'streetview' });
      } catch (error) {
        console.error('Error loading image:', error);
      }
    };
    switch (photos.length) {
      case 2:
        loadImage('streetview_tall.jpg');
        break;
      case 3:
        loadImage('streetview_wide.jpg');
        break;
      default:
        loadImage('streetview_small.jpg');
    }
  }, [photos.length]);

  const photosToDisplay = photos.slice(0, MAX_PHOTO_COUNT);
  const hasMultiplePhotos = photosToDisplay.length > 1;
  const templateAreas = getTemplateAreas(
    hasMultiplePhotos ? photosToDisplay.length + 1 : photosToDisplay.length,
    TEMPLATE_AREAS
  );

  const streetViewIndex = photosToDisplay.length - 1;
  const photosWithStreetView = [...photosToDisplay];

  hasMultiplePhotos
    ? photosWithStreetView.splice(streetViewIndex, 0, streetViewImage)
    : photosWithStreetView;

  return (
    <Grid
      gap={3}
      height={{ base: '200px', sm: 'xl', lg: 'xl' }}
      // Template area is intentionally set to '"A"' for mobile
      templateAreas={{ base: `"A"`, sm: templateAreas }}
      templateColumns={{ base: '1fr', sm: 'repeat(3, 1fr)' }}
      templateRows={{ base: '1fr', sm: 'repeat(3, 1fr)' }}
    >
      {photosWithStreetView.map((photo, index) => {
        const isFirstPhoto = index === 0;
        const isLastPhoto = index === photosWithStreetView.length - 1;
        const shouldDisplayShowAllButton = isLastPhoto && hasMultiplePhotos;
        const isStreetView = index === streetViewIndex;

        if (isStreetView && hasMultiplePhotos) {
          return (
            <GridItem
              key={index}
              area={{ base: 'A', sm: getAreaForItem(index, TEMPLATE_AREAS) }}
              alignItems="center"
              bg="gray.900"
              // Hide all except first on mobile
              display={{ base: isFirstPhoto ? 'flex' : 'none', sm: 'flex' }}
              justifyContent="center"
              overflow="hidden"
              position="relative"
              rounded="8"
              cursor="pointer"
              onClick={() => {
                window.history.replaceState(
                  window.history.state,
                  '',
                  `?${SEARCH_PARAM.STREET_VIEW}=true`
                );
                setIsStreetViewModalOpen(true);
              }}
            >
              {photo.url && (
                <WhichImage
                  title={photo.alt}
                  fill={true}
                  sizes="(max-width: 992px) 100vw, 850px"
                  path={photo.url}
                  quality={50}
                  style={{
                    objectFit: 'cover',
                    objectPosition: 'center',
                    opacity: 0.3
                  }}
                  role="img"
                />
              )}
              <ShowStreetViewButton />
            </GridItem>
          );
        }

        return (
          <GridItem
            key={index}
            area={{ base: 'A', sm: getAreaForItem(index, TEMPLATE_AREAS) }}
            alignItems="center"
            bg={shouldDisplayShowAllButton ? 'gray.900' : 'gray.100'}
            // Hide all except first on mobile
            display={{ base: isFirstPhoto ? 'flex' : 'none', sm: 'flex' }}
            justifyContent="center"
            overflow="hidden"
            position="relative"
            rounded="8"
            cursor="pointer"
            onClick={() => {
              const galleryIndex = index > streetViewIndex ? index : index + 1;
              if (hasMultiplePhotos) {
                window.history.replaceState(
                  window.history.state,
                  '',
                  `?${SEARCH_PARAM.PHOTO_INDEX}=${galleryIndex}`
                );
                setGalleryIndex(galleryIndex);
              }
            }}
          >
            <WhichImage
              title={photo.alt}
              fill
              priority
              sizes="(max-width: 992px) 100vw, 850px"
              path={photo.url}
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                objectPosition: 'center',
                opacity: shouldDisplayShowAllButton ? 0.3 : 1,
                gap: '4px'
              }}
              role="img"
            />
            {isFirstPhoto && (
              <>
                <ShowAllPhotosIconButton
                  position="absolute"
                  bottom={6}
                  left={6}
                  display={{ base: 'flex', sm: 'none' }}
                  onClick={() => {
                    setGalleryIndex(1);
                    window.history.replaceState(
                      window.history.state,
                      '',
                      `?${SEARCH_PARAM.PHOTO_INDEX}=1`
                    );
                  }}
                >
                  {photos.length}
                </ShowAllPhotosIconButton>
                <StreetViewIconButton
                  position="absolute"
                  bottom={6}
                  left={16}
                  marginLeft="5px"
                  display={{ base: 'flex', sm: 'none' }}
                  onClick={() => {
                    setIsStreetViewModalOpen(true);
                    window.history.replaceState(
                      window.history.state,
                      '',
                      `?${SEARCH_PARAM.STREET_VIEW}=true`
                    );
                  }}
                />
              </>
            )}
            {photosToDisplay.length === 1 && (
              <ShowStreetViewButton
                position="absolute"
                bottom={6}
                left={6}
                color="primary.500"
                borderColor="primary.500"
                borderWidth={2}
                backgroundColor="white"
                display={{ base: 'none', sm: 'flex' }}
                _hover={{ backgroundColor: 'primary.100' }}
                onClick={() => {
                  window.history.replaceState(
                    window.history.state,
                    '',
                    `?${SEARCH_PARAM.STREET_VIEW}=true`
                  );
                  setIsStreetViewModalOpen(true);
                }}
              />
            )}
            {shouldDisplayShowAllButton ? <ShowAllPhotosButton /> : null}
          </GridItem>
        );
      })}
    </Grid>
  );
};

const CaringPhotoGallery: React.FC<CaringPhotoGalleryProps> = ({
  photos,
  setIsStreetViewModalOpen,
  setGalleryIndex
}) => {
  return (
    <PhotoGallery
      photos={photos}
      setIsStreetViewModalOpen={setIsStreetViewModalOpen}
      setGalleryIndex={setGalleryIndex}
    />
  );
};

export default CaringPhotoGallery;
