import { CallToActionProps } from '@components/NewCallToAction/NewCallToAction';
import { DAM_IMAGE_FIXTURE, fireEvent, render } from '@utils/test-utils';

import { useModalDispatch } from '~/contexts/ModalContext';

import BannerCallOut from './BannerCallOut';

jest.mock('~/contexts/ModalContext');

describe('BannerCallOutComponent', () => {
  const mockShowModal = jest.fn();
  const mockUseModalDispatch = useModalDispatch as jest.Mock;
  mockUseModalDispatch.mockReturnValue({ showModal: mockShowModal });

  const mockTitle = 'Example title';
  const mockDescription = 'Example description';
  const mockBackgroundColor = 'primary';
  const mockBackgroundColorRange = '500';
  const mockCta: CallToActionProps = {
    text: 'Example CTA',
    textColor: 'white',
    bgColor: 'primary',
    isInquiry: true,
    inquiryId: 'exampleInquiryId',
    type: 'tel',
    state: 'solid',
    behavior: '_blank',
    rel: ['noreferrer'],
    url: 'exampleUrl'
  };

  beforeEach(() => {
    mockShowModal.mockClear();
  });

  test('renders the component with given props', () => {
    const { getByText, getByRole, getByAltText } = render(
      <BannerCallOut
        title={mockTitle}
        description={mockDescription}
        image={DAM_IMAGE_FIXTURE}
        backgroundColor={mockBackgroundColor}
        backgroundColorRange={mockBackgroundColorRange}
        cta={mockCta}
      />
    );

    expect(getByText(mockTitle)).toBeInTheDocument();
    expect(getByText(mockDescription)).toBeInTheDocument();
    expect(getByAltText(DAM_IMAGE_FIXTURE.metadata.caption));
    expect(getByRole('button')).toHaveTextContent('Example CTA');
  });

  test('opens the modal when inquiry CTA is clicked', () => {
    const { getByRole } = render(
      <BannerCallOut
        title={mockTitle}
        description={mockDescription}
        image={DAM_IMAGE_FIXTURE}
        backgroundColor={mockBackgroundColor}
        backgroundColorRange={mockBackgroundColorRange}
        cta={mockCta}
      />
    );

    fireEvent.click(getByRole('button'));

    expect(mockShowModal).toHaveBeenCalledWith(mockCta.inquiryId);
  });

  test('renders a link when non-inquiry CTA is clicked', () => {
    const { getByRole } = render(
      <BannerCallOut
        title={mockTitle}
        description={mockDescription}
        image={DAM_IMAGE_FIXTURE}
        backgroundColor={mockBackgroundColor}
        backgroundColorRange={mockBackgroundColorRange}
        cta={{ ...mockCta, isInquiry: false }}
      />
    );

    expect(getByRole('link')).toHaveAttribute(
      'href',
      `${mockCta.type}:${mockCta.url}`
    );
  });
});
