import { DAM_IMAGE_FIXTURE, render } from '@utils/test-utils';

import BannerDivider, { SwitchableProps } from './BannerDivider';
jest.mock('next/router', () => require('next-router-mock'));

describe('BannerDivider', () => {
  const metadata = {
    '@name': 'metadataName',
    '@path': '/metadataPath',
    '@id': 'metadataId',
    '@nodeType': 'mgnl:page',
    'mgnl:lastModified': '2022-05-14T01:32:27.765Z',
    'mgnl:template': 'standard',
    'mgnl:created': '2022-05-14T01:32:27.765Z',
    title: 'Banner Divider Title',
    '@nodes': [],
    dataName: 'metadataDataName'
  };

  const switchableProps: SwitchableProps = {
    images: {
      '@nodes': ['images0'],
      images0: {
        image: DAM_IMAGE_FIXTURE
      }
    },
    links: {
      '@nodes': ['links0'],
      links0: { linkHref: 'https://example.com', linkText: 'Example Link' }
    },
    field: 'images'
  };

  it('renders with title and images', () => {
    const { getByText, getByAltText } = render(
      <BannerDivider
        element="h2"
        headingSize="md"
        title={metadata.title}
        floating={false}
        switchable={switchableProps}
        backgroundColor="secondary"
        backgroundColorRange="200"
        metadata={metadata}
      />
    );

    const title = getByText(metadata.title);
    expect(title).toBeInTheDocument();

    const image = getByAltText(DAM_IMAGE_FIXTURE.metadata.caption);
    expect(image).toBeInTheDocument();
    expect(getByAltText(DAM_IMAGE_FIXTURE.metadata.caption));
  });

  it('renders with title and links', () => {
    const { getByText } = render(
      <BannerDivider
        element="h2"
        headingSize="md"
        title={metadata.title}
        floating={false}
        switchable={{ ...switchableProps, field: 'links' }}
        backgroundColor="secondary"
        backgroundColorRange="200"
        metadata={metadata}
      />
    );

    const title = getByText(metadata.title);
    expect(title).toBeInTheDocument();

    const link = getByText('Example Link');
    expect(link).toBeInTheDocument();
    expect(link.getAttribute('href')).toBe('https://example.com');
  });
});
