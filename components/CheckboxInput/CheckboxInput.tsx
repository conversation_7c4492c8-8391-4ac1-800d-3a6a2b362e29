import { memo, useCallback, useMemo, useState } from 'react';

import Checkbox from '../Checkbox';
import styles from './CheckboxInput.module.css';

type Item = {
  value: string;
  label?: string;
  component?: React.ReactNode;
};

interface PromotionsFilterProps {
  name: string;
  items: Item[];
  value?: string[];
  initialValues?: string[];
  maxItemsBeforeCollapse?: number;
  onChange(name: string, value: (string | number)[]): void;
}

function PromotionsFilter({
  name,
  items,
  value = [],
  initialValues,
  maxItemsBeforeCollapse,
  onChange
}: PromotionsFilterProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const handleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const visibleItems = useMemo(() => {
    if (!maxItemsBeforeCollapse || !isCollapsed) {
      return items;
    }

    return items.slice(0, maxItemsBeforeCollapse);
  }, [maxItemsBeforeCollapse, isCollapsed, items]);

  const handleChange = useCallback(
    (value: string[]) => {
      onChange(name, value);
    },
    [name, onChange]
  );

  return (
    <>
      <div>
        <div>
          {visibleItems.map((item) => (
            <div key={item.value}>
              <Checkbox
                name={name}
                value={item.value}
                checked={value.includes(item.value)}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.preventDefault();
                  const newValue = value.includes(item.value)
                    ? value.filter((v) => v !== item.value)
                    : [...value, item.value];
                  handleChange(newValue);
                }}
                component={item.component}
                label={item.label}
              />
            </div>
          ))}
        </div>
      </div>
      {maxItemsBeforeCollapse && (
        <div className={styles.collapseText} onClick={handleCollapse}>
          {isCollapsed ? 'See more' : 'See less'}
        </div>
      )}
    </>
  );
}

export default memo(PromotionsFilter);
