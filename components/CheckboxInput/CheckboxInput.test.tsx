import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import CheckboxInput from './CheckboxInput';

describe('CheckboxInput', () => {
  const mockItems = [
    { value: 'Spanish', label: 'Spanish' },
    { value: 'French', label: 'French' },
    { value: 'German', label: 'German' },
    { value: 'Cantonese', label: 'Cantonese' },
    { value: 'Farsi', label: 'Farsi' },
    { value: 'Hebrew', label: 'Hebrew' },
    { value: 'Hindi', label: 'Hindi' },
    { value: 'Italian', label: 'Italian' },
    { value: 'Korean', label: 'Korean' },
    { value: 'Mandarin', label: 'Mandarin' },
    { value: 'Russian', label: 'Russian' },
    { value: 'Japanese', label: 'Japanese' },
    { value: 'Tagalog', label: 'Tagalog' },
    { value: 'Vietnamese', label: 'Vietnamese' }
  ];

  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders all checkbox items', () => {
    render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={[]}
      />
    );

    mockItems.forEach((item) => {
      expect(screen.getByLabelText(item.label)).toBeInTheDocument();
    });
  });

  it('reflects the current value prop in checkbox states', () => {
    const currentValue = ['Spanish', 'German'];
    render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={currentValue}
      />
    );

    expect(screen.getByLabelText('Spanish')).toBeChecked();
    expect(screen.getByLabelText('French')).not.toBeChecked();
    expect(screen.getByLabelText('German')).toBeChecked();
    expect(screen.getByLabelText('Cantonese')).not.toBeChecked();
    expect(screen.getByLabelText('Farsi')).not.toBeChecked();
  });

  it('calls onChange with updated value when a checkbox is clicked', () => {
    const { rerender } = render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={['Spanish']}
      />
    );

    fireEvent.click(screen.getByLabelText('French'));
    expect(mockOnChange).toHaveBeenCalledWith('languages', [
      'Spanish',
      'French'
    ]);

    mockOnChange.mockClear();

    rerender(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={['Spanish', 'French']}
      />
    );

    fireEvent.click(screen.getByLabelText('Spanish'));
    expect(mockOnChange).toHaveBeenCalledWith('languages', ['French']);
  });

  it('renders "See more" button when maxItemsBeforeCollapse is set', () => {
    render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={[]}
        maxItemsBeforeCollapse={3}
      />
    );

    expect(screen.getByText('See more')).toBeInTheDocument();
    expect(screen.queryByLabelText('Cantonese')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Farsi')).not.toBeInTheDocument();
  });

  it('toggles between "See more" and "See less" when clicked', () => {
    render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={[]}
        maxItemsBeforeCollapse={3}
      />
    );

    const toggleButton = screen.getByText('See more');
    fireEvent.click(toggleButton);
    expect(screen.getByText('See less')).toBeInTheDocument();
    expect(screen.getByLabelText('Cantonese')).toBeInTheDocument();
    expect(screen.getByLabelText('Farsi')).toBeInTheDocument();

    fireEvent.click(screen.getByText('See less'));
    expect(screen.getByText('See more')).toBeInTheDocument();
    expect(screen.queryByLabelText('Cantonese')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Farsi')).not.toBeInTheDocument();
  });

  it('shows all items when "See more" is clicked', () => {
    render(
      <CheckboxInput
        name="languages"
        items={mockItems}
        onChange={mockOnChange}
        value={[]}
        maxItemsBeforeCollapse={3}
      />
    );

    expect(screen.queryByLabelText('Cantonese')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Farsi')).not.toBeInTheDocument();

    fireEvent.click(screen.getByText('See more'));
    expect(screen.getByLabelText('Cantonese')).toBeInTheDocument();
    expect(screen.getByLabelText('Farsi')).toBeInTheDocument();
  });
});
