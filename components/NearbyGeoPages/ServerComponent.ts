import { getNearbyGeoPages } from '@services/providers/fetchLegacyData';
import {
  LocalProperty,
  NearbyGeoPageLink,
  OtherTypeHereLink,
  OtherTypeNearbyLink
} from '@services/providers/queries/getNearbyGeoPages';
import { careTypeRollups } from '@utils/careTypeRollups';
import { modifyTrailingSlash } from '@utils/modifyTrailingSlash';
import kebabCase from 'lodash/kebabCase';
import { GetServerSidePropsContext } from 'next';

import {
  GeoPageLink,
  NearbyGeoPagesLinks,
  NearbyGeoPagesProps
} from './NearbyGeoPages';

const getFullCountyName = (localProperty: LocalProperty) => {
  return [localProperty.county?.name, localProperty.state.code]
    .filter(Boolean)
    .join(', ');
};

const getFullCityName = (localProperty: LocalProperty) => {
  return [localProperty.city?.name, localProperty.state.code]
    .filter(Boolean)
    .join(', ');
};

const getGeoPageLink = ({
  text,
  careType,
  state,
  region,
  enableTrailingSlash = false
}: {
  text: string;
  careType: string;
  state: string;
  region: string;
  enableTrailingSlash?: boolean;
}): GeoPageLink => {
  const rollup = careTypeRollups[careType];
  const parsedCareType = careType === rollup ? '' : careType;
  const parsedState = kebabCase(state);
  const parsedRegion = kebabCase(region);
  const routeSegments = [
    rollup,
    parsedCareType,
    parsedState,
    parsedRegion
  ].filter((segment) => Boolean(segment));
  const path = `/${routeSegments.join('/')}`;

  return {
    text,
    url: modifyTrailingSlash(enableTrailingSlash, path)
  };
};

const isValidGeoPageLink = (
  geoPageLink: NearbyGeoPageLink | OtherTypeHereLink | OtherTypeNearbyLink
) => {
  return geoPageLink.localResourceTypeRegion.isValid;
};

export const getServerSideComponentProps = async (
  { options }: NearbyGeoPagesProps,
  context: GetServerSidePropsContext
): Promise<NearbyGeoPagesLinks | null> => {
  const providerSlug = options?.providerSlug;
  const maxLinks = Number(options?.maxLinks) || 6;
  const enableTrailingSlash = Boolean(options?.enableTrailingSlash);

  // If there is no provider slug, return the default links.
  if (!providerSlug) {
    return null;
  }

  const response = await getNearbyGeoPages(providerSlug);

  // If there is no local property, return the default links.
  if (!response.localEntity) {
    return null;
  }
  // Get the encoded ID for a property with the primary resource id.
  // If there is no primary resource, get the encoded ID for the first resource.
  // If there is no resource, use an empty string.
  // This is temporary until we can get the encoded ID from the API.
  const encodedId =
    (
      response.localEntity.localResources.find(
        (localResource) =>
          localResource.id === response.localEntity?.primaryResource.id
      ) ?? response.localEntity.localResources[0]
    ).encodedId ?? '';
  const localProperty = response.localEntity;

  // Get the rollup type name, county name, and city name.
  const rollupTypeName = localProperty.rollupType.pluralName;
  const fullCountyName = getFullCountyName(localProperty);
  const fullCityName = getFullCityName(localProperty);

  // Define the headings for each section.
  const nearbyCountiesSameTypeHeading = `${rollupTypeName} near ${fullCountyName}`;
  const nearbyCitiesSameTypeHeading = `${rollupTypeName} near ${fullCityName}`;
  const otherTypesHereHeading = `Other Senior Care in ${fullCityName}`;
  const otherTypesNearbyHeading = `More Senior Care near ${fullCityName}`;

  // Get the links for nearby Geo Pages.
  const linksToNearbyCountiesForSameType =
    localProperty.linksToNearbyCountiesForSameType
      .filter(isValidGeoPageLink)
      .map((geoPageLink) =>
        getGeoPageLink({
          text: geoPageLink.linkText,
          careType: localProperty.rollupType.urlName,
          state: geoPageLink.localResourceTypeRegion.state.urlName,
          region: geoPageLink.localResourceTypeRegion.region.urlName,
          enableTrailingSlash
        })
      )
      .slice(0, maxLinks);
  const linksToNearbyCitiesForSameType =
    localProperty.linksToNearbyCitiesForSameType
      .filter(isValidGeoPageLink)
      .map((geoPageLink) =>
        getGeoPageLink({
          text: geoPageLink.linkText,
          careType: localProperty.rollupType.urlName,
          state: geoPageLink.localResourceTypeRegion.state.urlName,
          region: geoPageLink.localResourceTypeRegion.region.urlName,
          enableTrailingSlash
        })
      )
      .slice(0, maxLinks);
  const linksToOtherTypesHere = localProperty.linksToOtherTypesHere
    .filter(isValidGeoPageLink)
    .map((geoPageLink) =>
      getGeoPageLink({
        text: geoPageLink.linkText,
        careType: geoPageLink.localResourceTypeRegion.localResourceType.urlName,
        state: localProperty.state.urlName,
        region: localProperty.city.urlName,
        enableTrailingSlash
      })
    )
    .slice(0, maxLinks);
  const linksToOtherTypesNearby = localProperty.linksToOtherTypesNearby
    .filter(isValidGeoPageLink)
    .map((geoPageLink) =>
      getGeoPageLink({
        text: geoPageLink.linkText,
        careType: geoPageLink.localResourceTypeRegion.localResourceType.urlName,
        state: geoPageLink.localResourceTypeRegion.state.urlName,
        region: geoPageLink.localResourceTypeRegion.region.urlName,
        enableTrailingSlash
      })
    )
    .slice(0, maxLinks);

  return {
    encodedId: encodedId,
    nearbyCountiesSameType: {
      heading: nearbyCountiesSameTypeHeading,
      links: linksToNearbyCountiesForSameType
    },
    nearbyCitiesSameType: {
      heading: nearbyCitiesSameTypeHeading,
      links: linksToNearbyCitiesForSameType
    },
    otherTypesHere: {
      heading: otherTypesHereHeading,
      links: linksToOtherTypesHere
    },
    otherTypesNearby: {
      heading: otherTypesNearbyHeading,
      links: linksToOtherTypesNearby
    }
  };
};
