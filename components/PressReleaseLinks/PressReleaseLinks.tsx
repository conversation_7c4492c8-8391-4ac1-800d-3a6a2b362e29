import { Box, Link, List, ListItem, Text } from '@chakra-ui/react';
import Heading from '@components/Heading/Heading';
import Container from '@components/LayoutStructure/Container';
import parse, {
  attributesToProps,
  DOMNode,
  domToReact,
  Element,
  HTMLReactParserOptions
} from 'html-react-parser';
import { DateTimeFormatOptions } from 'luxon';
import React from 'react';
const PressReleaseLinks = ({
  title = '',
  text = ''
}: {
  title: string;
  text: string;
}) => {
  const parserOptions: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode instanceof Element) {
        switch (domNode.tagName) {
          case 'ul':
            return (
              <List>
                {domToReact(domNode.children as DOMNode[], parserOptions)}
              </List>
            );
          case 'li':
            return (
              <ListItem lineHeight="24px">
                {domToReact(domNode.children as DOMNode[], parserOptions)}
              </ListItem>
            );
          case 'a':
            const props = attributesToProps(domNode.attribs);
            return (
              <Link
                {...props}
                fontWeight="700"
                fontSize="xl"
                color="primary.700"
              >
                {domToReact(domNode.children as DOMNode[], parserOptions)}
              </Link>
            );
          case 'p':
            const data = domNode.children[0]['data'];
            const dateRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
            if (data && dateRegex.test(data)) {
              const date = new Date(data);
              const options = {
                year: 'numeric' as DateTimeFormatOptions['year'],
                month: 'long' as DateTimeFormatOptions['month'],
                day: 'numeric' as DateTimeFormatOptions['day']
              };
              const formattedDate = date.toLocaleDateString('en-US', options);
              return (
                <Text
                  color="primary.900"
                  fontSize="sm"
                  fontWeight="400"
                  textTransform="uppercase"
                  pt={3}
                  borderTop="1px"
                  borderColor="gray.300"
                >
                  {formattedDate}
                </Text>
              );
            }

            if (data && data === '\u00A0') return <></>;
            return (
              <Text pb={4} pt={2} fontSize="lg" color="gray.800">
                {domToReact(domNode.children as DOMNode[], parserOptions)}
              </Text>
            );
        }
      }
    }
  };
  return (
    <Container>
      {title && (
        <Heading
          withContainer={false}
          color="primary.900"
          headingElement="h2"
          headingSize="xl"
          title={title}
          mb={3}
        />
      )}
      <Box>{parse(text, parserOptions)}</Box>
    </Container>
  );
};

export default PressReleaseLinks;
