import { Box, SimpleGrid } from '@chakra-ui/layout';
import { List, ListIcon, ListItem } from '@chakra-ui/react';
import Heading from '@components/Heading';
import dynamic from 'next/dynamic';
import { MdCheck, MdClose } from 'react-icons/md';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

interface Reasons {
  prosText?: string;
  consText?: string;
}

interface ProsAndConsProps {
  title: string;
  headingElement?: HeadingElements;
  headingSize?: HeadingSizes | HeadingSizes[];
  withContainer?: boolean;
  titleAlignment?: 'left' | 'center' | 'right';
  paddingTop: string;
  paddingBottom: string;
  pros: Reasons[];
  cons: Reasons[];
}

const ProsAndCons = ({
  title,
  headingElement = 'h2',
  titleAlignment = 'left',
  headingSize = 'xl',
  withContainer = true,
  paddingTop = '32px',
  paddingBottom = '16px',
  pros,
  cons
}: ProsAndConsProps): JSX.Element => {
  const prosList = () =>
    pros &&
    pros['@nodes'].map((key) => {
      return (
        <ListItem key={key} fontSize="sm">
          <ListIcon as={MdCheck} color="primary.500" />
          {pros[key].prosText}
        </ListItem>
      );
    });

  const consList = () =>
    cons &&
    cons['@nodes'].map((key) => {
      return (
        <ListItem key={key} fontSize="sm">
          <ListIcon as={MdClose} color="secondary.500" />
          {cons[key].consText}
        </ListItem>
      );
    });

  const prosAndCons = () => (
    <Box>
      {title && (
        <Heading
          title={title}
          headingElement={headingElement}
          headingSize={headingSize}
          withContainer={false}
          titleAlignment={titleAlignment}
          paddingTop={paddingTop}
          paddingBottom={paddingBottom}
        />
      )}
      <SimpleGrid columns={2} spacing={10}>
        <Box>
          <Box pb="16px" fontWeight="bold">
            Pros:
          </Box>
          <List>{prosList()}</List>
        </Box>
        <Box>
          <Box pb="16px" fontWeight="bold">
            Cons:
          </Box>
          <List>{consList()}</List>
        </Box>
      </SimpleGrid>
    </Box>
  );

  if (!withContainer) return prosAndCons();

  return <Container>{prosAndCons()}</Container>;
};

export default ProsAndCons;
