.container {
  border-radius: 6px;
  width: 100%;
  border: 1px solid var(--chakra-colors-gray-400);
}

.title {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 32px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--chakra-colors-gray-400);
  border-radius: 6px 6px 0 0;
  color: var(--chakra-colors-primary-900);
  background-color: var(--chakra-colors-background-50);
}

.tab {
  cursor: pointer;
  font-size: 20px;
  font-weight: 600;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 32px;
}

.content.blurred .price strong,
.content.blurred .overview .price {
  filter: blur(4px);
  user-select: none;
}
.content.blurred .overview .price {
  filter: blur(8px);
}

.overview {
  color: var(--chakra-colors-primary-900);
  font-size: 12px;
}

.overview .price {
  font-size: 36px;
  line-height: 1.2;
  font-weight: 700;
}

.overview .description {
  font-size: 18px;
  padding-top: 24px;
}

.roomTypes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.roomType {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--chakra-colors-gray-400);
}

.price {
  font-weight: normal;
}

.inclusivePricing {
  font-size: 16px;
  border-radius: 6px;
  padding: 10px 20px;
  background-color: var(--chakra-colors-primary-600);
  color: white;
}

.bottomDescription {
  font-size: 14px;
}

.arrowButton {
  background: none;
  cursor: pointer;
  padding: 8px;
  border: none;
  border-radius: 4px;
}

.arrowButton:hover {
  background-color: var(--chakra-colors-gray-100);
}

.desktopTabs {
  display: none;
}

.mobileTabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px 24px;
}

@media (min-width: 768px) {
  .desktopTabs {
    display: flex;
    padding: 16px 32px;
    gap: 24px;
  }

  .tab {
    border-bottom: 2px solid transparent;
  }

  .tab.active {
    border-bottom: 2px solid var(--chakra-colors-primary-500);
  }

  .mobileTabs {
    display: none;
  }
}

@media (max-width: 767px) {
  .tab {
    font-size: 18px;
    padding: 8px 12px;
  }

  .roomType {
    flex-direction: column;
    gap: 8px;
  }

  .content {
    padding: 16px 24px;
  }
}
