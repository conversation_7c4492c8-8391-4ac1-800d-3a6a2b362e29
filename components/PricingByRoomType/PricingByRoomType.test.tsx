import '@testing-library/jest-dom';

import { ElementNames } from '@components/Analytics/events/ElementClicked';
import { CTAProps } from '@components/CTA/CTA';
import { mockModMonProvider } from '@mocks/modmon.mock';
import {
  cleanup,
  fireEvent,
  render,
  screen,
  setDesktopScreen,
  waitFor
} from '@utils/test-utils';

import { ProviderContextWrapper } from '~/contexts/Provider';
import { mockMedicareReviews } from '~/utils/test-utils/mocks/medicareReviews';

import PricingByRoomType from './PricingByRoomType';

const mockProps = {
  title: 'Pricing Information',
  overview: 'Overview description',
  assistedLivingDescription: 'Assisted living custom description',
  independentLivingDescription: 'Independent living custom description',
  memoryCareDescription: 'Memory care custom description',
  disclaimer: 'Pricing disclaimer',
  cta: {
    text: 'Contact Us',
    state: 'solid',
    bgColor: 'primary',
    textColor: 'white',
    trackingName: ElementNames.PRICING_BY_ROOM_TYPE,
    metadata: {}
  } as CTAProps
};

describe('PricingByRoomType', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  const renderPricingByRoomType = () => {
    return render(
      <ProviderContextWrapper provider={mockModMonProvider}>
        <PricingByRoomType {...mockProps} />
      </ProviderContextWrapper>
    );
  };

  it('should render the component with title on desktop', async () => {
    setDesktopScreen();
    renderPricingByRoomType();

    await waitFor(() => {
      expect(screen.getByText(mockProps.title)).toBeInTheDocument();
      expect(screen.getAllByText('Overview')).toHaveLength(2);
      expect(screen.getByText('Assisted Living')).toBeInTheDocument();
      expect(screen.getByText('Memory Care')).toBeInTheDocument();
    });
  });

  it('should show overview content by default on desktop', async () => {
    setDesktopScreen();
    renderPricingByRoomType();

    await waitFor(() => {
      expect(screen.getByText('Starting at')).toBeInTheDocument();
      expect(screen.getByText('$2,500')).toBeInTheDocument();
      expect(screen.getByText(mockProps.overview)).toBeInTheDocument();
    });
  });

  it('should switch content when clicking care types on desktop', async () => {
    setDesktopScreen();
    renderPricingByRoomType();
    await waitFor(() => {
      fireEvent.click(screen.getByText('Memory Care'));
      expect(
        screen.getByText(mockProps.memoryCareDescription)
      ).toBeInTheDocument();
      expect(screen.getByText('One Bedroom')).toBeInTheDocument();
      expect(screen.getByText('Two Bedroom')).toBeInTheDocument();
    });
  });

  it('should the next tab when the navigation button is pressed on mobile', async () => {
    renderPricingByRoomType();
    fireEvent.click(screen.getByLabelText('Next'));

    await waitFor(() => {
      // Initial state should show Overview tab

      // Click next to go to Assisted Living
      expect(
        screen.getByText(mockProps.memoryCareDescription)
      ).toBeInTheDocument();
    });
  });

  it('should the previous tab when the navigation button is pressed on mobile', async () => {
    renderPricingByRoomType();
    fireEvent.click(screen.getByLabelText('Previous'));
    await waitFor(() => {
      expect(
        screen.getByText(mockProps.memoryCareDescription)
      ).toBeInTheDocument();
    });
  });

  it('should show all-inclusive pricing message when provider has the amenity', async () => {
    renderPricingByRoomType();
    fireEvent.click(screen.getByText('Memory Care'));

    await waitFor(() => {
      expect(screen.getByText(/ALL-INCLUSIVE PRICING/)).toBeInTheDocument();
    });
  });

  it('should show disclaimer when provided', async () => {
    renderPricingByRoomType();
    await waitFor(() => {
      fireEvent.click(screen.getByText('Memory Care'));
      expect(screen.getByText(mockProps.disclaimer)).toBeInTheDocument();
    });
  });

  it('should not render with null provider', async () => {
    render(
      <ProviderContextWrapper provider={null}>
        <PricingByRoomType {...mockProps} />
      </ProviderContextWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText(mockProps.title)).not.toBeInTheDocument();
    });
  });

  it('should not render with empty services', async () => {
    render(
      <ProviderContextWrapper
        provider={{ ...mockModMonProvider, services: [] }}
      >
        <PricingByRoomType {...mockProps} />
      </ProviderContextWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText(mockProps.title)).not.toBeInTheDocument();
    });
  });

  it('should not render when room type pricing is null', async () => {
    render(
      <ProviderContextWrapper
        provider={{
          ...mockModMonProvider,
          services: [
            {
              id: '00000000-0000-0000-0000-000000000000',
              costs: {
                currency: 'USD',
                startingPriceCents: 370000
              },
              category: {
                imageURL:
                  'https://caring-files-prod.s3.amazonaws.com/costs-of-community-images/memory-care.png',
                description: 'Memory Care description',
                name: 'Memory Care'
              },
              accommodations: [],
              amenities: [],
              legacyResourceId: '12345',
              medicares: [mockMedicareReviews],
              contract: {
                type: 'cpa',
                isSuppressed: false
              },
              roomTypeCosts: [
                {
                  category: 'ONE_BEDROOM',
                  startingPriceCents: null
                }
              ],
              isClaimed: true,
              license: null
            }
          ]
        }}
      >
        <PricingByRoomType {...mockProps} />
      </ProviderContextWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText(mockProps.title)).not.toBeInTheDocument();
    });
  });
});
