'use client';

import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import CTA from '@components/CTA';
import { CTAProps } from '@components/CTA/CTA';
import Container from '@components/LayoutStructure/Container';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { toCapitalizedWordsNew } from '@utils/strings';
import { useContext, useMemo, useState } from 'react';
import { MdChevronLeft, MdChevronRight } from 'react-icons/md';

import ProviderContext from '~/contexts/Provider';
import en from '~/locales/en';
import { formatNumberLocale } from '~/utils/number';

import styles from './PricingByRoomType.module.css';

interface RoomTypeCost {
  category: string;
  startingPriceCents: number;
}

interface CareTypeData {
  description: string;
  pricing: number;
  roomTypeCosts: RoomTypeCost[];
  inclusivePricing?: boolean;
}

interface PricingByRoomTypeProps {
  title: string;
  overview: string;
  assistedLivingDescription: string;
  independentLivingDescription: string;
  memoryCareDescription: string;
  disclaimer: string;
  cta: CTAProps;
}

const CARE_TYPES = [
  en.careTypes['assisted-living'],
  en.careTypes['independent-living'],
  en.careTypes['memory-care-facilities']
];
const OVERVIEW_TYPE = 'overview';
const INCLUSIVE_PRICING_AMENITY_ID = 448;

const PricingByRoomType: React.FC<PricingByRoomTypeProps> = ({
  title,
  overview,
  assistedLivingDescription,
  independentLivingDescription,
  memoryCareDescription,
  disclaimer,
  cta
}) => {
  const { provider } = useContext(ProviderContext) || {};
  const elementClicked = useElementClicked();
  const [selectedType, setSelectedType] = useState(OVERVIEW_TYPE);
  const [currentIndex, setCurrentIndex] = useState(0);
  const hasSubmittedInquiry = useInquiryFormSubmitted();
  const availableCareTypes = useMemo(() => {
    if (!provider?.services) return [OVERVIEW_TYPE];

    return [
      OVERVIEW_TYPE,
      ...Array.from(
        provider.services.reduce((types, service) => {
          const name = service.category?.name?.toLowerCase();
          if (!name || !service.roomTypeCosts?.length) return types;

          if (
            CARE_TYPES.some((careType) => name.includes(careType.toLowerCase()))
          ) {
            types.add(name);
          }

          return types;
        }, new Set<string>())
      )
    ];
  }, [provider?.services]);

  if (!provider || availableCareTypes.length <= 1) return null;

  const getCareTypeData = (type: string): CareTypeData => {
    if (type === OVERVIEW_TYPE) {
      return {
        description: overview,
        pricing: provider.minimumCost?.costCents || 0,
        roomTypeCosts: []
      };
    }

    const service = provider.services?.find((s) =>
      s.category?.name?.toLowerCase().includes(type)
    );

    if (!service?.roomTypeCosts) {
      return {
        description: '',
        pricing: 0,
        roomTypeCosts: []
      };
    }
    const description =
      type === 'assisted-living'
        ? assistedLivingDescription
        : type === 'independent-living'
        ? independentLivingDescription
        : memoryCareDescription;

    const validRoomTypeCosts = service.roomTypeCosts.filter(
      (cost) => cost.startingPriceCents && cost.startingPriceCents > 10000
    );

    return {
      description: description || service.category?.description || '',
      pricing: service.costs.startingPriceCents || 0,
      roomTypeCosts: validRoomTypeCosts.filter(
        (cost): cost is RoomTypeCost => cost.startingPriceCents !== null
      ),
      inclusivePricing: service.amenities?.some(
        (amenity) => amenity.amenityId === INCLUSIVE_PRICING_AMENITY_ID
      )
    };
  };

  const renderOverviewContent = (pricing: number, description: string) =>
    pricing > 0 &&
    description != '' && (
      <div className={styles.overview}>
        <p>Starting at</p>
        <div className={styles.price}>${formatNumberLocale(pricing / 100)}</div>
        {description && <p className={styles.description}>{description}</p>}
      </div>
    );

  const renderRoomTypeContent = (
    description: string,
    roomTypeCosts: RoomTypeCost[],
    type: string,
    inclusivePricing: boolean
  ) => (
    <div className={styles.roomTypes}>
      <p className={styles.description}>{description}</p>
      {roomTypeCosts.map((room) => (
        <div key={room.category} className={styles.roomType}>
          <span className={styles.roomTypeLabel}>
            {toCapitalizedWordsNew(room.category.split('_').join(' '))}
          </span>
          <span className={styles.price}>
            Starting at{' '}
            <strong>
              ${formatNumberLocale(room.startingPriceCents / 100)}/mo
            </strong>
          </span>
        </div>
      ))}
      {inclusivePricing && (
        <p className={styles.inclusivePricing}>
          <strong>ALL-INCLUSIVE PRICING</strong> for{' '}
          {toCapitalizedWordsNew(type)} residents. Speak to a Family Advisor to
          learn more.
        </p>
      )}
      {disclaimer && <p className={styles.bottomDescription}>{disclaimer}</p>}
    </div>
  );

  const renderPricing = (type: string) => {
    const {
      pricing,
      roomTypeCosts,
      description,
      inclusivePricing = false
    } = getCareTypeData(type);

    if (type === OVERVIEW_TYPE && !pricing) return null;
    if (type !== OVERVIEW_TYPE && !roomTypeCosts?.length) return null;

    return (
      <div>
        {type === OVERVIEW_TYPE
          ? renderOverviewContent(pricing, description)
          : renderRoomTypeContent(
              description,
              roomTypeCosts,
              type,
              inclusivePricing
            )}
      </div>
    );
  };

  const handleNext = () => {
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.NEXT,
        name: ElementNames.PRICING_BY_ROOM_TYPE,
        text: 'Next',
        color: 'primary',
        textColor: 'primary'
      }
    });
    setCurrentIndex((prev) => {
      const nextIndex = (prev + 1) % availableCareTypes.length;
      setSelectedType(availableCareTypes[nextIndex]);
      return nextIndex;
    });
  };

  const handlePrev = () => {
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.PREVIOUS,
        name: ElementNames.PRICING_BY_ROOM_TYPE,
        text: 'Previous',
        color: 'primary',
        textColor: 'primary'
      }
    });
    setCurrentIndex((prev) => {
      const nextIndex = prev === 0 ? availableCareTypes.length - 1 : prev - 1;
      setSelectedType(availableCareTypes[nextIndex]);
      return nextIndex;
    });
  };

  const handleInteraction = (type) => {
    elementClicked({
      element: {
        type: ElementTypes.BUTTON,
        action: ElementActions.CARETYPE_PRICING,
        name: ElementNames.PRICING_BY_ROOM_TYPE,
        text: toCapitalizedWordsNew(type),
        color: 'primary',
        textColor: 'primary'
      }
    });
    setSelectedType(type);
  };

  const hasAnyRoomTypeData = availableCareTypes.some((type) => {
    const { roomTypeCosts } = getCareTypeData(type);
    return roomTypeCosts?.length > 0;
  });

  if (!hasAnyRoomTypeData) {
    return null;
  }

  return (
    <Container>
      <h2 className={styles.title}>{title}</h2>
      <div className={styles.container}>
        <div className={styles.tabs}>
          <div className={styles.desktopTabs}>
            {availableCareTypes.map((type) => {
              const { pricing, roomTypeCosts } = getCareTypeData(type);
              if (type === OVERVIEW_TYPE && !pricing) return null;
              if (type !== OVERVIEW_TYPE && !roomTypeCosts?.length) return null;

              return (
                <button
                  key={type}
                  className={`${styles.tab} ${
                    selectedType === type ? styles.active : ''
                  }`}
                  onClick={() => handleInteraction(type)}
                >
                  {toCapitalizedWordsNew(type)}
                </button>
              );
            })}
          </div>

          <div className={styles.mobileTabs}>
            <button
              onClick={handlePrev}
              className={styles.arrowButton}
              aria-label="Previous"
            >
              <MdChevronLeft size={29} />
            </button>
            <button
              className={`${styles.tab} ${styles.active}`}
              aria-label="Current tab"
            >
              {toCapitalizedWordsNew(availableCareTypes[currentIndex])}
            </button>
            <button
              onClick={handleNext}
              className={styles.arrowButton}
              aria-label="Next"
            >
              <MdChevronRight size={29} />
            </button>
          </div>
        </div>

        <div
          className={`${styles.content} ${
            hasSubmittedInquiry ? '' : styles.blurred
          }`}
        >
          {renderPricing(selectedType)}
        </div>
        <CTA {...cta} marginBottom={6} />
      </div>
    </Container>
  );
};

export default PricingByRoomType;
