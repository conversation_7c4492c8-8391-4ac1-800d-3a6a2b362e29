import { render, screen } from '@utils/test-utils';

import Table from './Table';

describe('Table', () => {
  const children = (
    <tbody>
      <tr>
        <td>Cell 1</td>
        <td>Cell 2</td>
      </tr>
    </tbody>
  );

  const props = {
    cellPadding: '1',
    cellSpacing: '1',
    className: 'a-table'
  };

  it('should render the table component', () => {
    render(<Table props={props}>{children}</Table>);
    expect(screen.getByText('Cell 1')).toBeInTheDocument();
    expect(screen.getByText('Cell 2')).toBeInTheDocument();
  });

  it('should have mobile styling', () => {
    render(<Table props={props}>{children}</Table>);
    const tableContainer = screen.getByTestId('table-container');

    expect(tableContainer).toHaveStyle('overflow-x: scroll');
  });
});
