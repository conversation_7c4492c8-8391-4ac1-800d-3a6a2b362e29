import { render } from '@testing-library/react';
import { createID } from '@utils/strings';

import HtmlToReact from './HtmlToReact';
describe('HtmlToReact', () => {
  it('renders the HTML content correctly', () => {
    const html = '<div><p>Some Content</p></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const textElement = getByText('Some Content');
    expect(textElement).toBeInTheDocument();
  });

  it('renders the phone number link correctly', () => {
    const html = '<div><a href="tel:1234567890">Call us</a></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const phoneNumberLink = getByText('Call us');
    expect(phoneNumberLink).toBeInTheDocument();
    expect(phoneNumberLink).toHaveAttribute('href', 'tel:1234567890');
  });

  it('renders the table correctly', () => {
    const html =
      '<div><table><tbody><tr><td>Cell 1</td><td>Cell 2</td></tr></tbody></table></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const cell1 = getByText('Cell 1');
    const cell2 = getByText('Cell 2');
    expect(cell1).toBeInTheDocument();
    expect(cell2).toBeInTheDocument();
  });

  it('renders the h2 heading correctly', () => {
    const html = '<div><h2>Some Content</h2></div>';
    const id = createID('Some Content');
    const { getByText } = render(<HtmlToReact html={html} />);
    const heading = getByText('Some Content');
    expect(heading).toBeInTheDocument();
    expect(heading.tagName).toBe('H2');
    expect(heading.id).toBe(id);
  });

  it('renders the h3 heading correctly', () => {
    const html = '<div><h3>Some Content</h3></div>';
    const id = createID('Some Content');
    const { getByText } = render(<HtmlToReact html={html} />);
    const heading = getByText('Some Content');
    expect(heading).toBeInTheDocument();
    expect(heading.tagName).toBe('H3');
    expect(heading.id).toBe(id);
  });

  it('renders the unordered list and list items correctly', () => {
    const html = '<div><ul><li>Item 1</li><li>Item 2</li></ul></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const item1 = getByText('Item 1');
    const item2 = getByText('Item 2');
    expect(item1).toBeInTheDocument();
    expect(item2).toBeInTheDocument();
  });

  it('renders the link correctly', () => {
    const html = '<div><a href="https://www.google.com">Google</a></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const link = getByText('Google');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', 'https://www.google.com');
  });

  it('renders the italic text correctly', () => {
    const html = '<div><em>Some Content</em></div>';
    const { getByText } = render(<HtmlToReact html={html} />);
    const italicText = getByText('Some Content');
    expect(italicText).toBeInTheDocument();
    expect(italicText.tagName).toBe('I');
  });
});
