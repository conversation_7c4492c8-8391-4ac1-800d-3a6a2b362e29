import { Box } from '@chakra-ui/layout';
import {
  Table as ChakraTable,
  TableProps as ChakraTableProps
} from '@chakra-ui/table';
import React from 'react';

interface TableProps {
  props: ChakraTableProps;
  children: string | JSX.Element | JSX.Element[];
}

const Table: React.FC<TableProps> = ({ props, children }) => {
  const { border } = props;
  return (
    <Box
      data-testid="table-container"
      overflowX="scroll"
      border={border ? `${border}px` : '1px'}
      borderStyle={'solid'}
      borderColor="gray.200"
      borderRadius={12}
      mt={2.5}
      mb={4}
    >
      <ChakraTable {...props} width="100%">
        {children}
      </ChakraTable>
    </Box>
  );
};

export default Table;
