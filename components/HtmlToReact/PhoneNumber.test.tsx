import { fireEvent, render, screen } from '@testing-library/react';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';

import PhoneNumber from './PhoneNumber';

describe('PhoneNumber', () => {
  const props = {
    href: 'tel:1234567890'
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the phone number link', () => {
    render(<PhoneNumber props={props}>Call us</PhoneNumber>);

    const phoneNumberLink = screen.getByText('Call us');
    expect(phoneNumberLink).toBeInTheDocument();
    expect(phoneNumberLink).toHaveAttribute('href', 'tel:1234567890');
  });

  it('triggers a "Phone Number Clicked" event when clicked', () => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;

    render(<PhoneNumber props={props}>Call us</PhoneNumber>);
    const phoneNumberLink = screen.getByText('Call us');
    fireEvent.click(phoneNumberLink);
    expect(mockAnalytics.track).toHaveBeenCalled();
    expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
      destination_url: '1234567890',
      element: {
        action: 'phone_call',
        color: undefined,
        id: undefined,
        name: 'phone_call_button',
        text: '1234567890',
        text_color: undefined,
        type: 'tel'
      },
      page_session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID,
      query: {
        list_id: '',
        location_id: '',
        query_id: ''
      },
      session_id: ''
    });
  });
});
