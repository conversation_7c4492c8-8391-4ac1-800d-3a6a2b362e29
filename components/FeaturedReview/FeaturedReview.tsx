'use client';

import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import Heading from '@components/Heading';
import Container from '@components/LayoutStructure/Container';
import ProviderReviewsCard from '@components/Reviews/ProviderReviewsCard';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import {
  ResponsiveQuery,
  responsiveQueryAdapter
} from '@utils/responsiveQueryAdapter';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import ProviderContext from '~/contexts/Provider';

interface FeaturedReviewProps {
  title: string;
  headingSize?: HeadingSizes;
  headingElement: HeadingElements;
  reviewRatingColor: string;
  reviewRatingColorRange: string;
  backgroundColor: string;
  backgroundColorRange: string;
  cta: {
    ctaText: string;
    variant: 'solid' | 'outline' | 'ghost';
    colorScheme: string;
    width: 'fit-content' | '100%';
  };
  deviceVisibility?: DeviceVisibility;
  templateId?: string;
}

const FeaturedReview: React.FC<FeaturedReviewProps> = ({
  title,
  headingSize,
  headingElement,
  reviewRatingColor,
  reviewRatingColorRange,
  backgroundColor,
  backgroundColorRange,
  cta,
  templateId,
  deviceVisibility
}) => {
  const { provider } = useContext(ProviderContext) || {};
  const topReview = useMemo(() => {
    if (!provider?.reviews) return null;

    return (
      provider.reviews
        .filter((review) => review.rating >= 4)
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )[0] || null
    );
  }, [provider]);

  const [containerWidth, setContainerWidth] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const responsiveStyles = (styles: ResponsiveQuery) =>
    responsiveQueryAdapter({
      responsiveQuery: styles,
      width: containerWidth
    });
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(container);
      return () => {
        resizeObserver.unobserve(container);
      };
    }
  }, []);

  const isHidden = useResponsiveDisplay(deviceVisibility);
  if (isHidden || !provider || !topReview) {
    return <></>;
  }

  return (
    <Container
      backgroundColor={`${backgroundColor}.${backgroundColorRange}`}
      paddingY={responsiveStyles({ base: 5, md: 8 })}
      className="featured-review"
      borderRadius="md"
    >
      <Heading
        title={title}
        headingElement={headingElement}
        headingSize={headingSize}
        withContainer={false}
      />
      <ProviderReviewsCard
        author={topReview?.authorName || ''}
        serviceCategoryName={topReview?.serviceCategoryName || ''}
        reviewText={topReview?.content || ''}
        createdAt={topReview?.createdAt}
        rating={topReview?.rating || 0}
        title={topReview?.title || ''}
        ratingColor={reviewRatingColor}
        ratingColorRange={reviewRatingColorRange}
        tagsColor={reviewRatingColor}
        tagsColorRange={reviewRatingColorRange}
        providerResponse={topReview?.providerResponse}
        bg="unset"
        paddingX={0}
        marginBottom={0}
        boxShadow="none"
        isVisible={true}
        showProviderResponse={false}
      />
      <Button
        as="a"
        colorScheme={cta.colorScheme}
        variant={cta.variant}
        width={cta.width}
        destinationUrl="#all_reviews_section"
        elementAction={ElementActions.JUMP_LINK}
        elementName={ElementNames.FEATURED_REVIEW}
        elementType={ElementTypes.BUTTON}
        href="#all_reviews_section"
      >
        {cta.ctaText}
      </Button>
    </Container>
  );
};

export default FeaturedReview;
