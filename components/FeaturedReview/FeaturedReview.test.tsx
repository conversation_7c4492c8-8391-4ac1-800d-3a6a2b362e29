import { Chakra<PERSON>rovider } from '@chakra-ui/react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { HeadingElements } from '~/@types/heading';
import ProviderContext from '~/contexts/Provider';

import FeaturedReview from './FeaturedReview';

describe('<FeaturedReview />', () => {
  const mockProps = {
    title: 'Featured Review',
    headingElement: 'h2' as HeadingElements,
    reviewRatingColor: 'blue',
    reviewRatingColorRange: '500',
    backgroundColor: 'gray',
    backgroundColorRange: '100',
    cta: {
      ctaText: 'Read All Reviews',
      variant: 'solid' as 'solid' | 'outline' | 'ghost',
      colorScheme: 'blue',
      width: 'fit-content' as 'fit-content' | '100%'
    }
  };

  const mockProvider = {
    reviews: [
      {
        id: '1',
        rating: 5,
        authorName: '<PERSON>',
        content: 'Excellent service',
        title: 'Great experience',
        createdAt: '2023-01-15',
        serviceCategoryName: 'Assisted Living'
      },
      {
        id: '2',
        rating: 4,
        authorName: '<PERSON>',
        content: 'Very good overall',
        title: 'Good care',
        createdAt: '2023-02-20',
        serviceCategoryName: 'Memory Care'
      },
      {
        id: '3',
        rating: 3,
        authorName: 'Bob <PERSON>',
        content: 'Average experience',
        title: 'Okay service',
        createdAt: '2023-03-10',
        serviceCategoryName: 'Independent Living'
      },
      {
        id: '4',
        rating: 5,
        authorName: 'Alice Brown',
        content: 'Outstanding care',
        title: 'Highly recommend',
        createdAt: '2023-01-05',
        serviceCategoryName: 'Assisted Living'
      }
    ]
  };

  const renderWithProvider = (provider) => {
    return render(
      <ChakraProvider>
        <ProviderContext.Provider value={{ provider, setProvider: jest.fn() }}>
          <FeaturedReview {...mockProps} />
        </ProviderContext.Provider>
      </ChakraProvider>
    );
  };

  test('renders without accessibility violations', async () => {
    const { container } = renderWithProvider(mockProvider);
    expect(await axe(container)).toHaveNoViolations();
  });

  test('selects the most recent review with rating >= 4', () => {
    renderWithProvider(mockProvider);

    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Very good overall')).toBeInTheDocument();
    expect(screen.getByText('Good care')).toBeInTheDocument();

    expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument();
    expect(screen.queryByText('Average experience')).not.toBeInTheDocument();

    expect(screen.queryByText('Alice Brown')).not.toBeInTheDocument();
  });

  test('does not render when provider has no reviews with rating >= 4', () => {
    const providerWithLowRatings = {
      reviews: [
        {
          id: '1',
          rating: 3,
          authorName: 'Low Rating User',
          content: 'Not great',
          title: 'Below threshold',
          createdAt: '2023-01-01',
          serviceCategoryName: 'Assisted Living'
        }
      ]
    };

    renderWithProvider(providerWithLowRatings);
    expect(screen.queryByText('Featured Review')).not.toBeInTheDocument();
  });

  test('does not render when provider has no reviews', () => {
    const providerWithNoReviews = {
      reviews: []
    };

    renderWithProvider(providerWithNoReviews);
    expect(screen.queryByText('Featured Review')).not.toBeInTheDocument();
  });

  test('does not render when provider is null', () => {
    renderWithProvider(null);
    expect(screen.queryByText('Featured Review')).not.toBeInTheDocument();
  });
});
