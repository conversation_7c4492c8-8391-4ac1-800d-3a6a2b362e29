import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Heading,
  Text
} from '@chakra-ui/react';
import DAMImage from '@components/Image/DAMImage';

import { MagnoliaImage, StoryAuthor } from '~/types/Magnolia';

interface Item {
  author: StoryAuthor;
  isFirst?: boolean;
  isLast?: boolean;
}
const AuthorAccordionItem = ({
  author,
  isFirst = false,
  isLast = false
}: Item) => {
  return (
    <AccordionItem
      key={author.id}
      borderTop={isFirst ? 'none' : ''}
      borderBottom={isLast ? 'none' : ''}
    >
      {({ isExpanded }) => (
        <>
          <AccordionButton px="5" pt="5" pb={isExpanded ? '3' : '5'}>
            <Heading
              className="magnolia-text"
              flex="1"
              textAlign="left"
              size="md"
              fontWeight={700}
            >
              {author.name}
              {author.jobTitle && (
                <Text as="span" fontWeight={400} lineHeight="30px">
                  , {author.jobTitle}
                </Text>
              )}
            </Heading>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel className="magnolia-text" pt={0} pl={5} pr={14}>
            <Box
              display="flex"
              flexDirection={{ base: 'column-reverse', md: 'row' }}
              gap={5}
            >
              <Box fontWeight="400" fontSize="sm">
                <Box dangerouslySetInnerHTML={{ __html: author.bio }} />
                {author.linkedIn && (
                  <Text mt={4}>
                    For more on {author.name.split(' ')[0]}’s background and
                    experience, you can view their{' '}
                    {
                      <a href={author.linkedIn} target="_blank">
                        LinkedIn page.
                      </a>
                    }
                  </Text>
                )}
              </Box>
              {author.image && (
                <DAMImage
                  src={author.image as MagnoliaImage}
                  style={{
                    width: '100%',
                    height: 'fit-content',
                    maxWidth: '200px',
                    borderRadius: '12px',
                    border: '1px'
                  }}
                />
              )}
            </Box>
          </AccordionPanel>
        </>
      )}
    </AccordionItem>
  );
};

export default AuthorAccordionItem;
