import { Accordion } from '@chakra-ui/react';
import { render, screen } from '@testing-library/react';

import { StoryAuthor } from '~/types/Magnolia';

import AuthorAccordionItem from './AuthorAccordionItem';

const mockAuthor: StoryAuthor = {
  bio: 'Author bio',
  staffPosition: 'Staff Position',
  name: 'Tester McTester',
  linkedIn: 'linkedin.com'
};
describe('AuthorAccordionItem', () => {
  it('renders the component with given props', () => {
    render(
      <Accordion
        allowMultiple
        defaultIndex={[0]}
        borderColor="gray.300"
        borderWidth={1}
        borderRadius="5"
      >
        <AuthorAccordionItem
          author={mockAuthor}
          isFirst={false}
          isLast={false}
        />
      </Accordion>
    );
    expect(screen.getByText(mockAuthor.name)).toBeInTheDocument();
    expect(screen.getByText(mockAuthor.bio)).toBeInTheDocument();
  });
});
