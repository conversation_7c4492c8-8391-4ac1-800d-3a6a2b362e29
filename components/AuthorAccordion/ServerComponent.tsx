import { fetchResponseJSON } from '~/services/magnolia/api';
import { Story } from '~/types/Magnolia';
const defaultBaseUrl = process.env.NEXT_PUBLIC_MGNL_HOST;

interface AuthorAccordionProps {
  staffPosition: string;
}

export const getServerSideComponentProps = async (
  props: AuthorAccordionProps
): Promise<Array<Story> | null> => {
  let url = new URL(
    `${defaultBaseUrl}/.rest/delivery/storyauthor?staffPosition=${props.staffPosition}`
  );

  url.searchParams.set('limit', '100');

  const res = await fetchResponseJSON(url.href);

  if (res.total === 0 || res.error) {
    return null;
  }

  return res?.results;
};
