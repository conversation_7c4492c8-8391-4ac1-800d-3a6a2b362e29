import { Accordion } from '@chakra-ui/react';
import Container from '@components/LayoutStructure/Container';

import { StoryAuthor } from '~/types/Magnolia';

import AuthorAccordionItem from './AuthorAccordionItem';

interface Props {
  data?: StoryAuthor[];
}

const AuthorAccordion = ({ data }: Props) => {
  return (
    <Container>
      {data && data.length > 0 ? (
        <Accordion
          allowMultiple
          defaultIndex={[0]}
          borderColor="gray.300"
          borderWidth={1}
          borderRadius="5"
        >
          {data.map((author, index) => {
            const isFirst = index === 0;
            const isLast = data.length - 1 === index;
            return (
              <AuthorAccordionItem
                key={author.id}
                author={author}
                isFirst={isFirst}
                isLast={isLast}
              />
            );
          })}
        </Accordion>
      ) : (
        <></>
      )}
    </Container>
  );
};
export default AuthorAccordion;
