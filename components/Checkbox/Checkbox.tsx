import React from 'react';

import styles from './Checkbox.module.css';

export interface CheckboxProps {
  name: string;
  value: string;
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  component?: React.ReactNode;
  label?: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
  name,
  value,
  checked,
  onChange,
  component,
  label
}) => {
  const labelId = `${name}-${value}-label`;
  return (
    <label className={styles.checkboxContainer}>
      <input
        type="checkbox"
        name={name}
        value={value}
        checked={checked}
        onChange={onChange}
        className={styles.checkboxInput}
        aria-checked={checked}
        aria-labelledby={labelId}
      />
      <span className={styles.checkmark} aria-hidden="true"></span>
      {component || (
        <span id={labelId} className={styles.checkboxLabel}>
          {label || value}
        </span>
      )}
    </label>
  );
};

export default Checkbox;
