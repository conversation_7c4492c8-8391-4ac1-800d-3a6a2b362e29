import { fireEvent, render } from '@testing-library/react';
import React from 'react';

import axe from '~/axe-helper';

import Checkbox from './Checkbox';

describe('Checkbox', () => {
  it('renders correctly', () => {
    const { getByLabelText } = render(
      <Checkbox
        name="test"
        value="test"
        checked={false}
        onChange={() => {}}
        label="Test Checkbox"
      />
    );
    expect(getByLabelText('Test Checkbox')).toBeInTheDocument();
  });

  it('calls onChange when clicked', () => {
    const handleChange = jest.fn();
    const { getByLabelText } = render(
      <Checkbox
        name="test"
        value="test"
        checked={false}
        onChange={handleChange}
        label="Test Checkbox"
      />
    );
    fireEvent.click(getByLabelText('Test Checkbox'));
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  it('has correct accessibility attributes', () => {
    const { container } = render(
      <Checkbox
        name="test"
        value="test"
        checked={false}
        onChange={() => {}}
        label="Test Checkbox"
      />
    );

    const input = container.querySelector('input[type="checkbox"]');
    const labelElement = container.querySelector('#test-test-label');

    expect(input).toHaveAttribute('aria-labelledby', 'test-test-label');
    expect(input).toHaveAttribute('aria-checked', 'false');
    expect(labelElement).toHaveTextContent('Test Checkbox');
  });

  it('should not have any accessibility violations', async () => {
    const { container } = render(
      <Checkbox
        name="test"
        value="test"
        checked={false}
        onChange={() => {}}
        label="Test Checkbox"
      />
    );

    expect(await axe(container)).toHaveNoViolations();
  });
});
