.checkboxContainer {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.checkboxInput {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  user-select: none;
}

.checkmark {
  height: 1rem;
  width: 1rem;
  background-color: #fff;
  border: 2px solid #A3A3A3;
  border-radius: 0.125rem;
  margin-right: 0.5rem;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.checkboxInput:checked ~ .checkmark {
  background-color: #3182CE;
  border-color: #3182CE;
}

.checkmark:after {
  content: "";
  display: none;
  width: 0.25rem;
  height: 0.5rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  margin-bottom: 0.15rem;
}

.checkboxInput:checked ~ .checkmark:after {
  display: block;
}

