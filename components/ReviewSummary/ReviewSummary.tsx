'use client';

import { ButtonGroup } from '@chakra-ui/button';
import { Heading, Stack } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { StringToIconKeys } from '@components/RenderIcon';
import { formatRating } from '@utils/number';
import orderBy from 'lodash/orderBy';
import dynamic from 'next/dynamic';
import { useContext } from 'react';

import ProviderContext, { ParametricReviewData } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Metadata } from '~/types/Magnolia';

import {
  ParameterizeRatings,
  ParametrizedRatings
} from './ParameterizeRatings';
import ReviewProgress from './ReviewProgress';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
const ReviewWidget = dynamic(() => import('@components/ReviewWidget'));
interface ReviewSummaryProps extends ParametricReviewData {
  showViewAllReviews?: boolean;
  reviewSummaryTitle?: string;
  writeReviewText?: string;
  viewAllReviewsText?: string;
  viewAllReviewsLink?: string;
  writeReviewLink?: string;
  viewAllReviewsIcon?: StringToIconKeys;
  writeReviewIcon?: StringToIconKeys;
  metadata: Metadata;
  orderBy?: {
    field?: string;
    order: 'asc' | 'desc';
  };
}

function sortRatings(
  ratings: ParametrizedRatings[],
  field: string,
  order: 'asc' | 'desc'
) {
  return field === 'default' ? ratings : orderBy(ratings, [field], [order]);
}

const ReviewSummary: React.FC<ReviewSummaryProps> = (props) => {
  const {
    showViewAllReviews = true,
    metadata,
    orderBy,
    reviewSummaryTitle,
    writeReviewText,
    viewAllReviewsText,
    viewAllReviewsLink,
    writeReviewLink,
    viewAllReviewsIcon,
    writeReviewIcon,
    ...parametricReviewData
  } = props;
  const siteContext = useContext(SiteContext);

  const domain = siteContext.site?.path || '';

  const provider = useContext(ProviderContext)?.provider;
  const reviewCount = provider?.reviewCount;
  const hasReviews = !!(reviewCount && reviewCount > 0);
  const parameterizedRatings = ParameterizeRatings(parametricReviewData);
  const sortedRatings = orderBy?.field
    ? sortRatings(parameterizedRatings, orderBy.field, orderBy.order)
    : parameterizedRatings;
  if (!provider) return null;

  return (
    <Container>
      <Stack spacing="8">
        <Heading size="lg">{reviewSummaryTitle ?? 'Review Summary'}</Heading>
        <Stack spacing={hasReviews ? '8' : '4'}>
          {provider?.averageRating && provider?.reviewCount && (
            <ReviewWidget
              averageRating={
                formatRating(provider.averageRating)?.toString() || '1'
              }
              reviewCount={provider.reviewCount || 0}
              provider={provider.name}
              icon={writeReviewIcon}
              text={writeReviewText}
              metadata={metadata}
            />
          )}
          {hasReviews && <ReviewProgress parametrizedRatings={sortedRatings} />}
          <ButtonGroup>
            {showViewAllReviews && hasReviews && (
              <Button
                as="a"
                title={viewAllReviewsText ?? 'View all reviews'}
                href={viewAllReviewsLink ?? '#all_reviews_section'}
                colorScheme="primary"
                variant="outline"
                leftIcon={viewAllReviewsIcon ? viewAllReviewsIcon : undefined}
                elementAction={ElementActions.INTERNAL_LINK}
                elementName={ElementNames.READ_MORE_REVIEW}
                elementType={ElementTypes.BUTTON}
              >
                {viewAllReviewsText ?? 'View all reviews'}
              </Button>
            )}
          </ButtonGroup>
        </Stack>
      </Stack>
    </Container>
  );
};

export default ReviewSummary;
