import { Grid, GridProps } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const ProgressChart = dynamic(() => import('@components/Charts/ProgressChart'));
import { formatRating } from '~/utils/number';

import { ParametrizedRatings } from './ParameterizeRatings';

interface Props extends GridProps {
  parametrizedRatings: ParametrizedRatings[];
  color?: string;
}
const ratingLabels = {
  Facilities: 'Facility',
  Activity: 'Activities'
};
const ratingOrder = ['Facilities', 'Staff', 'Food', 'Activity', 'Value'];
const ReviewProgress: React.FC<Props> = ({
  parametrizedRatings,
  color,
  ...rest
}) => {
  const sortedRatings = [...parametrizedRatings].sort((a, b) => {
    const aIndex = ratingOrder.indexOf(a.label);
    const bIndex = ratingOrder.indexOf(b.label);
    return aIndex - bIndex;
  });
  return (
    <Grid
      as="ul"
      templateColumns={{ lg: 'repeat(4, 1fr)' }}
      rowGap={{ base: '4', lg: '6' }}
      columnGap={10}
      paddingTop={{ base: '8', lg: '6' }}
      paddingBottom={{ base: '5', lg: '8' }}
      {...rest}
    >
      {sortedRatings.map((param, index) => (
        <ProgressChart
          as="li"
          key={`${index}-${param.label}`}
          id={`progress-label-${param.label}`}
          ariaLabel={`${String(formatRating(param.rating))} star rating for ${
            param.label
          }`}
          label={ratingLabels[param.label] ?? param.label}
          value={(100 * (param.rating - param.min)) / (param.max - param.min)}
          displayValue={String(formatRating(param.rating))}
          color={color}
        />
      ))}
    </Grid>
  );
};

export default ReviewProgress;
