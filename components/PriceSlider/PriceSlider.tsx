import {
  RangeSlider,
  RangeSliderFilledTrack,
  RangeSliderMark,
  RangeSliderThumb,
  RangeSliderTrack,
  Stack,
  Text
} from '@chakra-ui/react';
import CheckboxInput from '@components/CheckboxInput';
import isEqual from 'lodash/isEqual';
import { memo, useState } from 'react';

interface SearchSliderProps {
  min?: number;
  max?: number;
  onChange: (name: string, value: (string | number)[]) => void;
  priceRange: [number, number];
  ongoingPromotion: string[];
}

export const valueToSymbol = (value: number) => {
  return '$'.repeat(value);
};

const PriceSearchSlider: React.FC<SearchSliderProps> = (props) => {
  const name = 'priceRange';
  const { min = 1, max = 4, ongoingPromotion = [], onChange } = props;

  // State to keep track of the price range when the user is sliding
  const [priceRange, setPriceRange] = useState<[number, number]>(
    props.priceRange || [min, max]
  );

  const handleSliderChange = (value: number[]) => {
    // Prevent unnecessary re-renders when the value is the same
    if (isEqual(value, props.priceRange)) {
      return;
    }

    onChange(name, value);
  };

  const handleCheckboxChange = (name: string, value: string[]) => {
    onChange(name, value);
  };

  return (
    <Stack spacing="8">
      <RangeSlider
        name={name}
        value={priceRange}
        defaultValue={[min, max]}
        min={min}
        max={max}
        step={1}
        minStepsBetweenThumbs={1}
        mb="6"
        onChange={(value) => setPriceRange(value as [number, number])}
        // When the user stops sliding, notify the parent component
        onChangeEnd={handleSliderChange}
      >
        {Array.from({ length: max }, (_, i) => i + 1).map((value) => {
          const ml = Math.round(((value - min) / max) * 12 * -1);
          return (
            <RangeSliderMark value={value} key={value} mt="4" fontWeight="bold">
              <Text textAlign="right" ml={ml}>
                {valueToSymbol(value)}
              </Text>
            </RangeSliderMark>
          );
        })}
        <RangeSliderTrack bg="gray.500">
          <RangeSliderFilledTrack bg="blue.400" />
        </RangeSliderTrack>
        <RangeSliderThumb index={0} />
        <RangeSliderThumb index={1} />
      </RangeSlider>
      <CheckboxInput
        name="ongoingPromotion"
        onChange={handleCheckboxChange}
        items={[
          {
            value: 'true',
            label: 'Providers With Ongoing Promotions'
          }
        ]}
        value={ongoingPromotion}
      />
    </Stack>
  );
};

export default memo(PriceSearchSlider);
