import { fireEvent, render, screen, waitFor } from '@utils/test-utils';
import { mockProviderV2 } from '@utils/test-utils/mocks/provider';
import { add, sub } from 'date-fns';

import ProviderContext from '~/contexts/Provider';

import Promotion from './Promotion';

const today = new Date();

const yesterday = sub(today, {
  days: 1
});

const tomorrow = add(today, {
  days: 1
});

const mockedPromotionData = {
  id: '2e2ab1de-ea25-4fed-93d0-2bc92d56cd3a',
  startsAt: yesterday.toISOString(),
  endsAt: tomorrow.toISOString(),
  externalPromotionText:
    'Residents that pay full community fee, get $1,000 off of base rent every other month for the first year. Residents that pay half of the community fee, get $1,000 each quarter for a year',
  visibleOnlyToFa: false
};

// 2023-08-30T20:14:03.415Z
const ContextWrapper = ({ provider, children }) => {
  return (
    <ProviderContext.Provider
      value={{ setProvider: () => {}, provider: provider }}
    >
      {children}
    </ProviderContext.Provider>
  );
};

describe('Promotion', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  it('doesnt render the component when provider isnt available', () => {
    render(
      <ContextWrapper provider={{}}>
        <Promotion />
      </ContextWrapper>
    );
    expect(screen.queryByText(/PROMOTION/i)).toBeFalsy();
  });

  it('renders the component when provider is available', () => {
    mockProviderV2['promotions'] = [mockedPromotionData];
    render(
      <ContextWrapper provider={mockProviderV2}>
        <Promotion />
      </ContextWrapper>
    );
    expect(screen.getByText(/PROMOTION/i)).toBeVisible();
    expect(
      screen.getByText(
        'Residents that pay full community fee, get $1,000 off of base rent every ot...'
      )
    ).toBeVisible();
    expect(
      screen.getByRole('button', {
        name: /Read More/i
      })
    ).toBeVisible();
  });

  it('renders a modal when button is pressed', async () => {
    mockProviderV2['promotions'] = [mockedPromotionData];
    render(
      <ContextWrapper provider={mockProviderV2}>
        <Promotion />
      </ContextWrapper>
    );
    const readMoreButton = screen.getByRole('button', {
      name: /Read More/i
    });
    expect(readMoreButton).toBeInTheDocument();
    fireEvent.click(readMoreButton);
    await waitFor(() => {
      expect(
        screen.getByText(
          'Residents that pay full community fee, get $1,000 off of base rent every other month for the first year. Residents that pay half of the community fee, get $1,000 each quarter for a year'
        )
      ).toBeVisible();
    });
  });
});
