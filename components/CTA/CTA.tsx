'use client';

import { BoxProps } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { ButtonProps } from '@components/Button';
import Container from '@components/LayoutStructure/Container';
import { StringToIconKeys } from '@components/RenderIcon';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import { DEFAULT_BUTTON_ALIGNMENT } from '@utils/buttons';
import { filterNonReactAttributes } from '@utils/filterNonReactAttributes';

import { Metadata } from '~/types/Magnolia';

import InquiryCTA from './InquiryCTA';
import ReviewModalCTA from './ReviewModalCTA';
import UrlCTA from './UrlCTA';

export interface CTAProps extends BoxProps {
  text?: string;
  textColor?: string;
  bgColor?: string;
  state?: 'solid' | 'outline' | 'ghost';
  icon?: StringToIconKeys;
  alignment?: 'left' | 'center' | 'right';
  buttonWidth?: 'fit-content' | '100%' | any;
  useWidthOnMobile?: boolean;
  buttonProps?: ButtonProps;
  trackingName: ElementNames;
  templateId?: string;
  className?: string;
  mobileWidth?: 'fit-content' | '100%';
  actionBehavior?: {
    field: 'openInquiry' | 'openLink' | 'openReviewModal';
    inquiryId?: string;
    url?: string;
    type?: 'tel' | 'mailto' | 'sms';
    behavior?: '_blank' | '_self' | '_parent' | '_top';
    rel?: Array<'external' | 'nofollow' | 'noopener' | 'noreferrer' | 'opener'>;
  };
  deviceVisibility?: DeviceVisibility;
  metadata: Pick<Metadata, '@id'>;
}
interface CTAButton extends CTAProps {
  field?: string;
}
const renderCTAButton = ({
  field,
  state,
  icon,
  bgColor,
  textColor,
  actionBehavior,
  trackingName,
  buttonWidth,
  buttonProps,
  text,
  ...props
}: CTAButton) => {
  switch (field) {
    case 'openInquiry':
      return (
        <InquiryCTA
          state={state}
          icon={icon}
          bgColor={bgColor}
          textColor={textColor}
          actionBehavior={actionBehavior}
          trackingName={trackingName}
          buttonWidth={buttonWidth}
          buttonProps={buttonProps}
          text={text}
        />
      );
    case 'openLink':
      return (
        <UrlCTA
          state={state}
          icon={icon}
          bgColor={bgColor}
          textColor={textColor}
          actionBehavior={actionBehavior}
          trackingName={trackingName}
          buttonWidth={buttonWidth}
          buttonProps={buttonProps}
          text={text}
          templateId={props?.templateId}
        />
      );
    case 'openReviewModal':
      return (
        <ReviewModalCTA
          text={text}
          width={buttonWidth}
          colorScheme={bgColor}
          leftIcon={icon}
          variant={state}
          metadata={props?.metadata}
          bgColor={bgColor}
          textColor={textColor}
          elementType={ElementTypes.BUTTON}
          elementAction={ElementActions.OPEN_MODAL}
          elementName={ElementNames.WRITE_A_REVIEW}
        />
      );
    default:
      return <></>;
  }
};

const CTA = ({
  state,
  icon,
  bgColor,
  textColor,
  actionBehavior,
  trackingName = ElementNames.GENERIC_BUTTON,
  buttonWidth,
  useWidthOnMobile,
  buttonProps,
  text,
  alignment,
  templateId,
  className,
  mobileWidth,
  deviceVisibility,
  ...boxProps
}: CTAProps): React.ReactElement => {
  const parsedBoxProps = filterNonReactAttributes(boxProps);
  const isHidden = useResponsiveDisplay(deviceVisibility);
  if (isHidden) {
    return <></>;
  }
  const finalWidth = useWidthOnMobile
    ? buttonWidth
    : { base: '100%', md: buttonWidth };

  return actionBehavior?.field ? (
    <Container
      display="flex"
      justifyContent={alignment || DEFAULT_BUTTON_ALIGNMENT}
      className={className}
      width="100%"
      {...parsedBoxProps}
    >
      {renderCTAButton({
        field: actionBehavior?.field,
        state,
        icon,
        bgColor,
        textColor,
        actionBehavior,
        trackingName,
        buttonWidth: finalWidth,
        buttonProps,
        text,
        ...boxProps
      })}
    </Container>
  ) : (
    <></>
  );
};

export default CTA;
