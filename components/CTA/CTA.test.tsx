import { render, screen } from '@utils/test-utils';
import React from 'react';

jest.mock('~/contexts/ModalContext');

import { ElementNames } from '@components/Analytics/events/ElementClicked';

import { useModalDispatch } from '~/contexts/ModalContext';

import CTA, { CTAProps } from './CTA';

describe('CallToAction', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  const mockShowModal = jest.fn();
  const mockUseModalDispatch = useModalDispatch as jest.Mock;
  mockUseModalDispatch.mockReturnValue({ showModal: mockShowModal });

  const inquiryId = 'exampleInquiryId';
  const url = 'https://www.google.com';
  const phone = '+1234567890';

  beforeEach(() => {
    mockShowModal.mockClear();
  });

  it('renders CTA button with text and URL', () => {
    const mockCta: CTAProps = {
      actionBehavior: { field: 'openLink', url: url },
      text: 'Example CTA',
      textColor: 'white',
      bgColor: 'primary',
      state: 'solid',
      trackingName: ElementNames.GENERIC_BUTTON
    };
    render(<CTA {...mockCta} />);
    const buttonElement = screen.getByText(/Example CTA/i);
    expect(buttonElement).toBeInTheDocument();
    expect(buttonElement).toHaveAttribute('href', url);
  });
  it('renders CTA button with text and inquiry', () => {
    const mockCta: CTAProps = {
      actionBehavior: { field: 'openInquiry', inquiryId: inquiryId },
      text: 'Example CTA',
      textColor: 'white',
      bgColor: 'primary',
      state: 'solid',
      trackingName: ElementNames.GENERIC_BUTTON
    };
    render(<CTA {...mockCta} />);
    const buttonElement = screen.getByText(/Example CTA/i);
    expect(buttonElement).toBeInTheDocument();
    buttonElement.click();
    expect(mockShowModal).toHaveBeenCalledWith(inquiryId);
  });
});
