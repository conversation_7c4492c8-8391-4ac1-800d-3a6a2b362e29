import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { DEFAULT_CTA_BEHAVIOR } from '@utils/buttons';
import { Parser } from '@utils/parser';
import { useContext } from 'react';

import ProviderContext from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';

import { CTAProps } from './CTA';

function UrlCTA({
  state,
  text,
  icon,
  bgColor,
  textColor,
  actionBehavior,
  buttonWidth,
  buttonProps,
  trackingName = ElementNames.GENERIC_BUTTON,
  templateId
}: Omit<CTAProps, 'metadata'>) {
  const siteContext = useContext(SiteContext);

  const provider = useContext(ProviderContext)?.provider;

  const parsedUrl = Parser({
    source: actionBehavior?.url ?? '',
    values: { provider: provider || {} },
    strip: true
  });
  return (
    <Button
      as="a"
      href={`${
        actionBehavior?.type ? `${actionBehavior?.type}:` : ''
      }${parsedUrl}`}
      target={actionBehavior?.behavior || DEFAULT_CTA_BEHAVIOR}
      rel={actionBehavior?.rel?.join(' ') || ''}
      colorScheme={bgColor}
      textColor={textColor}
      leftIcon={icon}
      variant={state}
      width={buttonWidth}
      padding={'12px 24px'}
      {...buttonProps}
      elementAction={
        parsedUrl?.startsWith('/') ||
        siteContext.site?.domains.map(
          (domain) => domain.indexOf(parsedUrl || '') >= 0
        )
          ? ElementActions.INTERNAL_LINK
          : ElementActions.EXTERNAL_LINK
      }
      elementType={(actionBehavior?.type as ElementTypes) || ElementTypes.LINK}
      elementName={trackingName}
      destinationUrl={parsedUrl}
    >
      {text}
    </Button>
  );
}

export default UrlCTA;
