import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';

import { useModalDispatch } from '~/contexts/ModalContext';

import { CTAProps } from './CTA';

function InquiryCTA({
  state,
  text,
  icon,
  bgColor,
  textColor,
  actionBehavior,
  buttonWidth,
  buttonProps,
  trackingName = ElementNames.GENERIC_BUTTON
}: Omit<CTAProps, 'metadata'>) {
  const { showModal } = useModalDispatch();
  const openModal = (inquiryId) => {
    showModal(inquiryId);
  };
  return (
    <Button
      colorScheme={bgColor}
      textColor={textColor}
      leftIcon={icon}
      variant={state}
      width={buttonWidth}
      padding={'12px 24px'}
      fontSize={16}
      elementAction={ElementActions.OPEN_MODAL}
      elementType={ElementTypes.BUTTON}
      elementName={trackingName}
      onClick={() => openModal(actionBehavior?.inquiryId)}
      {...buttonProps}
    >
      {text}
    </Button>
  );
}
export default InquiryCTA;
