'use client';

import { getRandomProviderPic } from '@utils/getRandomProviderPic';
import Image from 'next/image';
import { useContext, useId, useState } from 'react';

import ProviderContext, { Provider } from '~/contexts/Provider';

interface Props {
  provider: Provider;
}
const ProviderImageComponent = (props: Props): React.ReactElement => {
  const providerCtx = useContext(ProviderContext)?.provider;
  const seed = useId();

  let provider = props.provider;
  if (!provider && providerCtx) {
    provider = providerCtx;
  }
  const fallbackImage = getRandomProviderPic(
    provider?.photos?.[0]?.url || provider?.name || seed
  );
  const [src, setSrc] = useState<string>(() =>
    provider?.hasImages
      ? provider?.photos?.[0]?.url ?? fallbackImage
      : fallbackImage
  );

  const onError = () => {
    setSrc(fallbackImage);
  };

  return (
    <Image
      src={src}
      alt={provider?.name ?? ''}
      width={500}
      height={500}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        maxHeight: '350px'
      }}
      onError={() => onError()}
    />
  );
};

export default ProviderImageComponent;
