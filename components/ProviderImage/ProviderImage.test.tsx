import { render } from '@utils/test-utils';
import {
  mockProviderV2,
  mockProviderV2NoImages
} from '@utils/test-utils/mocks/provider';

import ProviderImage from './ProviderImage';

describe('Image', () => {
  it('should render component when a provider has images', async () => {
    const { container } = render(<ProviderImage provider={mockProviderV2} />);

    expect(container).not.toBeEmptyDOMElement();
  });
  it('should render component when a provider does not have images', async () => {
    const { container } = render(
      <ProviderImage provider={mockProviderV2NoImages} />
    );
    expect(container).not.toBeEmptyDOMElement();
  });
});
