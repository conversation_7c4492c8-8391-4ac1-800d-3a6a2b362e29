import { CloseButton, HStack, Text, VStack } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';

import ChatIcon from './ChatIcon';
import { useChat } from './ChatProvider';
interface ChatWidgetTitleBarProps {
  title: string;
}

const ChatWidgetTitleBar = ({ title }: ChatWidgetTitleBarProps) => {
  const { dispatch } = useChat();
  const elementClicked = useElementClicked();

  return (
    <HStack
      p={4}
      fontWeight="bold"
      fontSize="sm"
      lineHeight={4}
      borderBottom="1px solid"
      borderColor="gray.100"
    >
      <ChatIcon isOnline={true} />
      <VStack width="100%" alignItems="flex-start">
        <Text p={0}>{title}</Text>
        <Text color="secondary.400" p={0} style={{ marginTop: 0 }}>
          Caring
        </Text>
      </VStack>
      <CloseButton
        color="gray.400"
        justifyContent="flex-end"
        width="min-content"
        _hover={{ color: 'gray.600' }}
        onClick={() => {
          dispatch({ type: 'TOGGLE_CHAT' });
          elementClicked({
            element: {
              type: ElementTypes.BUTTON,
              action: ElementActions.CLOSE_MODAL,
              name: ElementNames.CHAT_WIDGET,
              text: '',
              color: '',
              textColor: ''
            }
          });
        }}
      />
    </HStack>
  );
};

export default ChatWidgetTitleBar;
