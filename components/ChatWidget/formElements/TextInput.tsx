import { Input, InputProps } from '@chakra-ui/react';
import { getSavedValues } from '@components/InquiryForm/saveUtils';
import React, { useEffect, useRef } from 'react';
import { Control, Controller } from 'react-hook-form';

interface TextInputProps extends InputProps {
  name: string;
  type: string;
  control: Control<any>;
  placeholder?: string;
  setValue: (name: string, value: any) => void;
}

const TextInput: React.FC<TextInputProps> = ({
  name,
  type,
  control,
  placeholder,
  setValue,
  ...rest
}) => {
  useEffect(() => {
    const savedValues = getSavedValues();
    if (savedValues && savedValues.careLocation && name === 'location') {
      setValue('location', savedValues.careLocation);
    }
  }, [name, setValue]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Input
          {...field}
          type={type}
          value={field.value || ''}
          borderColor="blue.50"
          borderWidth="2px"
          placeholder={placeholder}
          ref={inputRef}
          {...rest}
        />
      )}
    />
  );
};

export default TextInput;
