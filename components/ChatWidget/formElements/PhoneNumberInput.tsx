import { Input, InputProps } from '@chakra-ui/react';
import React, { forwardRef } from 'react';
import { Control, Controller } from 'react-hook-form';
import InputMask from 'react-input-mask';

interface PhoneInputProps extends InputProps {
  name: string;
  type: string;
  control: Control<any>;
  placeholder: string;
}

const PhoneNumberInput: React.FC<PhoneInputProps> = ({
  name,
  type,
  control,
  placeholder,
  ...rest
}) => {
  const Mask = forwardRef<HTMLInputElement, any>(function PhoneInput(
    props,
    ref
  ) {
    return (
      <InputMask {...props} inputRef={ref} mask="(*************" maskChar="_" />
    );
  });

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Input
          as={Mask}
          {...field}
          type={type}
          value={field.value || ''}
          borderColor="blue.50"
          borderWidth="2px"
          placeholder={placeholder}
          ref={field.ref}
          {...rest}
        />
      )}
    />
  );
};

export default PhoneNumberInput;
