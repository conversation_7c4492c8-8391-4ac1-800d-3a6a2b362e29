import { Button, ButtonProps, Divider, <PERSON>lex, VStack } from '@chakra-ui/react';
import { Center, Spinner } from '@chakra-ui/react';
import { DEFAULT_CARE_TYPES } from '@components/Account/Reviews/contants';
import { fetchProvidersWithFacetedSearch } from '@services/faceted-search/api';
import { getLatLongMapBoxAPI } from '@utils/search';
import kebabCase from 'lodash/kebabCase';
import { memo, useContext, useEffect } from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Domain, Domains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import ChatBubble from './ChatBubble';
import { useChat } from './ChatProvider';
import NearbyProviders from './NearbyProviders';
import { replaceTemplateVariables } from './utils';

interface ChatHistoryButtonProps extends ButtonProps {
  text: string;
  isUserInput: boolean;
}

const ChatHistoryButton: React.FC<ChatHistoryButtonProps> = ({
  text,
  isUserInput,
  ...rest
}) => (
  <Button
    display="inline-block"
    position="relative"
    marginTop={2}
    marginRight={1}
    padding={3}
    fontSize="xs"
    borderRadius="3xl"
    boxShadow="0px 2px 6px rgb(216, 216, 216)"
    whiteSpace="nowrap"
    lineHeight="1"
    textOverflow="ellipsis"
    verticalAlign="middle"
    overflow="hidden"
    cursor="default"
    colorScheme={isUserInput ? 'secondary' : 'white'}
    textColor={isUserInput ? 'white' : 'gray.600'}
    {...rest}
  >
    {text}
  </Button>
);

export const fetchProviders = async (
  location: string,
  careType: string = 'assisted-living',
  domain: Domain = Domains.CaringDomains.LIVE
): Promise<{ results: Provider[]; queryId: string; listId: string }> => {
  const careTypeNormalized = kebabCase(DEFAULT_CARE_TYPES[careType]);

  let lng = '';
  let lat = '';
  let latLng = '';
  if (location) {
    const mapboxResponse = await getLatLongMapBoxAPI(location);
    if (mapboxResponse) {
      lng = mapboxResponse[0]?.toString() || '';
      lat = mapboxResponse[1]?.toString() || '';
      latLng = lng && lat ? `${lat},${lng}` : '';
    }
  }

  const searchParams = {
    accessibility: [],
    awards: [],
    careType: careTypeNormalized,
    dining: [],
    distanceInMiles: 30,
    healthServices: [],
    keyword: location,
    latLng: latLng,
    languages: [],
    lifestyle: [],
    matchAllFilters: true,
    ongoingPromotion: [],
    otherAmenities: [],
    page: 0,
    personalCare: [],
    priceRange: [1, 4] as [number, number],
    providersWith: [],
    reviews: [],
    roomAmenities: [],
    roomType: [],
    sortBy: 'distance' as const,
    staffQualifications: [],
    verified: []
  };

  const componentProps = {
    amenityCategory: '',
    careType: careTypeNormalized,
    city: '',
    county: '',
    displayMode: 'search' as const,
    distanceFilterEnabled: false,
    domain: domain,
    latitude: lat,
    longitude: lng,
    resultsPerPage: 4,
    source: Source.ALGOLIA,
    state: '',
    shouldShowOnlyIndexedProviders: false
  };

  const providerResponse = await fetchProvidersWithFacetedSearch({
    searchParams,
    componentProps
  });

  return {
    results: providerResponse.results,
    queryId: providerResponse.queryId,
    listId: providerResponse.listId
  };
};

const ChatHistory: React.FC<{
  isInactive?: boolean;
}> = memo(({ isInactive = false }) => {
  const { site } = useContext(SiteContext);
  const { state, dispatch } = useChat();
  const { chatHistory, inactiveChatHistory, providers, isLoading, userInput } =
    state;
  const chatHistoryToRender = isInactive ? inactiveChatHistory : chatHistory;
  const isLocationInput = chatHistoryToRender.find(
    (step) => step.stepValues[0].name === 'location'
  );
  useEffect(() => {
    if (isLocationInput && providers.length === 0) {
      dispatch({ type: 'SET_THINKING', payload: false });
      dispatch({ type: 'IS_LOADING', payload: true });
      fetchProviders(
        isLocationInput.selectedValue,
        userInput.typeOfCare,
        site?.domains[0] as Domain
      )
        .then((response) => {
          dispatch({ type: 'SET_PROVIDERS', payload: response.results });
          dispatch({ type: 'SET_QUERY_ID', payload: response.queryId });
          dispatch({ type: 'SET_LIST_ID', payload: response.listId });
        })
        .catch((error) => console.error('Error fetching providers:', error))
        .finally(() => {
          dispatch({ type: 'IS_LOADING', payload: false });
        });
    }
  }, [
    isLocationInput,
    dispatch,
    providers.length,
    site?.domains,
    userInput.typeOfCare
  ]);
  return (
    <>
      {chatHistoryToRender.map((step, index) => (
        <VStack key={index} width="100%" align="start">
          <ChatBubble
            dangerouslySetInnerHTML={{
              __html: replaceTemplateVariables(step.stepQuestion, userInput)
            }}
          />
          {step.stepValues.length > 0 && (
            <Flex
              flexFlow="row-reverse wrap"
              justifyContent="flex-start"
              paddingBottom={6}
              width="100%"
            >
              {step.stepValues.find((t) => t.type === 'button') ? (
                step.stepValues.map((text, x) => (
                  <ChatHistoryButton
                    key={x}
                    text={text.label}
                    isUserInput={text.value === step.selectedValue}
                  />
                ))
              ) : step.stepValues[0].name === 'location' ? (
                <>
                  {isLoading ? (
                    <Center width="100%" py={6}>
                      <Spinner size="lg" color="secondary.500" />
                    </Center>
                  ) : (
                    <>
                      {providers?.length > 0 && !isLoading ? (
                        <NearbyProviders providers={providers} />
                      ) : (
                        <ChatBubble marginTop={5}>
                          {
                            "I'm sorry, we didn't find any properties in that area."
                          }
                        </ChatBubble>
                      )}
                    </>
                  )}
                </>
              ) : (
                <ChatHistoryButton
                  key={index}
                  text={step.selectedValue}
                  isUserInput={true}
                  borderRadius="md"
                  colorScheme="white"
                  textColor="gray.600"
                />
              )}
            </Flex>
          )}
        </VStack>
      ))}
      {isInactive && <Divider marginBottom={6} />}
    </>
  );
});

ChatHistory.displayName = 'ChatHistory';

export default ChatHistory;
