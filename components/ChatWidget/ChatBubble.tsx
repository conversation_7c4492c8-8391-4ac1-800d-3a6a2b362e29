import { Text, TextProps } from '@chakra-ui/react';

const ChatBubble = ({ ...rest }: TextProps) => {
  return (
    <Text
      backgroundColor="gray.100"
      display="inline-block"
      position="relative"
      marginBottom="4px"
      borderRadius="24px 24px 24px 3px"
      padding="10px 21px 11px 16px"
      lineHeight="20px"
      marginRight="auto"
      maxWidth="calc(100% - 40px)"
      fontSize="14px"
      fontWeight="600"
      sx={{
        a: {
          color: 'blue.500',
          textDecoration: 'underline',
          '&:hover': {
            color: 'blue.700'
          }
        }
      }}
      {...rest}
    />
  );
};
export default ChatBubble;
