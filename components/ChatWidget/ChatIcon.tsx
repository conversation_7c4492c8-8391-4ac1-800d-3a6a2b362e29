import { Box, BoxProps } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { MODAL } from '@constants/modals';
import Image from 'next/image';

import caring_chat from '~/assets/badges/caring_chat.jpg';
import { useVisibleModals } from '~/contexts/ModalContext';

import { useChat } from './ChatProvider';

interface ChatIconProps extends BoxProps {
  isOnline?: boolean;
}

const ChatIcon = ({ isOnline, ...rest }: ChatIconProps) => {
  const { isVisible } = useVisibleModals();
  const isMapVisible = isVisible(MODAL.MAP_VIEW);

  const elementClicked = useElementClicked();

  const { state, dispatch } = useChat();
  const { isOpen } = state;

  const imageSize = isOpen ? 48 : 36;

  if (isMapVisible) {
    return null;
  }

  return (
    <Box
      position="relative"
      p={2}
      borderRadius="50%"
      boxShadow="0px 0px 6px 1px #949494"
      backgroundColor="white"
      cursor={isOpen ? 'default' : 'pointer'}
      maxWidth="52px"
      _after={{
        content: '""',
        display: 'block',
        position: 'absolute',
        backgroundColor: isOnline ? '#18dc72' : '#ffd000',
        width: '7px',
        height: '7px',
        borderRadius: '7px',
        right: '8px',
        top: '2px'
      }}
      onClick={() => {
        dispatch({ type: 'TOGGLE_CHAT' });
        elementClicked({
          element: {
            type: ElementTypes.BUTTON,
            action: ElementActions.OPEN_MODAL,
            name: ElementNames.CHAT_WIDGET,
            text: '',
            color: '',
            textColor: ''
          }
        });
      }}
      aria-label="Open chat"
      float="right"
      {...rest}
    >
      <Image
        src={caring_chat}
        alt="Caring Chat"
        width={imageSize}
        height={imageSize}
      />
    </Box>
  );
};

export default ChatIcon;
