import { Button } from '@chakra-ui/react';
import React from 'react';

import { HandleButtonClickProps } from './ChatSteps';
import { ButtonOption } from './types';

interface ChatButtonProps {
  config: ButtonOption;
  isLastStep: boolean;
  handleButtonClick: HandleButtonClickProps;
}

const ChatButton = React.memo(
  ({ config, handleButtonClick, isLastStep }: ChatButtonProps) => {
    return (
      <Button
        onClick={() =>
          handleButtonClick(
            config.value,
            config.name,
            config.nextStep,
            isLastStep
          )
        }
        width="100%"
        colorScheme="secondary"
        size="md"
        zIndex="sticky"
        whiteSpace="normal"
      >
        {config.label}
      </Button>
    );
  }
);

ChatButton.displayName = 'ChatButton';
export default ChatButton;
