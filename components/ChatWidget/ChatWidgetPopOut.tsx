import { Box, CloseButton, Grid, Stack, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import dynamic from 'next/dynamic';

const ChatIcon = dynamic(() => import('./ChatIcon'));
const StepInput = dynamic(() => import('./StepInput'));

const ChatWidgetPopOut = ({
  title,
  isHeaderVisible,
  areButtonsVisible,
  isOnline,
  step,
  legalText,
  handleButtonClickWrapper,
  handlePopOutClose,
  elementClicked
}) => {
  return (
    <>
      <Box
        position="absolute"
        display="flex"
        right={{ base: 0, md: 5 }}
        bottom={{ base: 0, md: 5 }}
        borderRadius={{ base: 0, md: 12 }}
        bg="white"
        width={isHeaderVisible ? { base: '100vw', md: '350px' } : '350px'}
        height={isHeaderVisible ? (areButtonsVisible ? '180px' : '80px') : '0'}
        opacity={isHeaderVisible ? 1 : 0}
        boxShadow="0px 4px 20px 0px rgba(18, 18, 18, 0.2)"
        transition="all 0.5s ease-in, height 0.2s ease-in"
        alignContent="center"
      >
        <Stack
          width="100%"
          alignItems="flex-start"
          fontWeight="bold"
          fontSize="sm"
          lineHeight={4}
          paddingTop={5}
          paddingLeft={20}
        >
          <Text p={0}>{title}</Text>
          <Text color="secondary.400" p={0} style={{ marginTop: 0 }}>
            Caring
          </Text>
          <CloseButton
            position="absolute"
            right={2}
            top="15px"
            color="gray.400"
            _hover={{ color: 'gray.600' }}
            onClick={(e) => {
              e.stopPropagation();
              handlePopOutClose();
              elementClicked({
                element: {
                  type: ElementTypes.BUTTON,
                  action: ElementActions.CLOSE_MODAL,
                  name: ElementNames.CHAT_WIDGET,
                  text: '',
                  color: '',
                  textColor: ''
                }
              });
            }}
          />
        </Stack>
      </Box>
      <Box
        position="absolute"
        bottom={{
          base: areButtonsVisible ? '120px' : 4,
          md: areButtonsVisible ? '138px' : 9
        }}
        left={{ base: 4, md: 0 }}
        opacity={isHeaderVisible ? 1 : 0}
        transition="opacity .5s ease-in, bottom .2s ease-in"
      >
        <ChatIcon isOnline={isOnline} width="52px" height="50px" />
      </Box>
      <Box
        position="absolute"
        bottom={5}
        right={5}
        opacity={isHeaderVisible ? 0 : 1}
        transition="opacity .2s ease-in"
      >
        <ChatIcon isOnline={isOnline} width="52px" height="50px" />
      </Box>
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap={2}
        paddingRight={{ base: 5, md: 9 }}
        paddingBottom={{ base: 5, md: 9 }}
        paddingLeft={{ base: 5, md: 0 }}
        width={isHeaderVisible ? { base: '100vw', md: '350px' } : '72px'}
        opacity={areButtonsVisible ? 1 : 0}
        height={areButtonsVisible ? 'auto' : '0'}
        transition="opacity 0.5s ease-in"
      >
        {step && (
          <StepInput
            options={step.options}
            type={step.type}
            isLastStep={step.isLastStep}
            showLegalText={step.showLegalText || false}
            legalText={legalText}
            handleButtonClick={handleButtonClickWrapper}
          />
        )}
      </Grid>
    </>
  );
};

export default ChatWidgetPopOut;
