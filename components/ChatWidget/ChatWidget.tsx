import { useFeatureIsOn, useGrowthBook } from '@growthbook/growthbook-react';
import React from 'react';

import { FEATURE_FLAGS } from '~/constants/features';

import { ChatProvider } from './ChatProvider';
import ChatWidgetContent from './ChatWidgetContent';
import { ChatWidgetProps } from './types';

const ChatWidget: React.FC<ChatWidgetProps> = (props) => {
  const { ready } = useGrowthBook();

  const isHubSpotChatWidgetOn = useFeatureIsOn(FEATURE_FLAGS.HUBSPOT_CHAT);
  const { data } = props;

  // Hide the in-house chat widget if HubSpot Chat is enabled or if Growthbook isn't loaded yet
  if (!ready || isHubSpotChatWidgetOn) return <></>;

  return (
    <ChatProvider chatConfig={data.chatConfig}>
      <ChatWidgetContent {...props} />
    </ChatProvider>
  );
};

export default ChatWidget;
