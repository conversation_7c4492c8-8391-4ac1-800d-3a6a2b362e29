'use client';

import React, { createContext, ReactNode, useContext, useReducer } from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Domains } from '~/types/Domains';
import { InquiryFormAPIPayload } from '~/types/Inquiry';

import { ChatConfig, ChatHistoryProps, ChatStep, StepType } from './types';
import { chatReducer } from './utils';
interface ChatState {
  formTemplateId: string;
  chatHistory: ChatHistoryProps[];
  inactiveChatHistory: ChatHistoryProps[];
  currentStepIndex: number;
  userInput: InquiryFormAPIPayload;
  isFirstOpen: boolean;
  step: ChatStep;
  thinking: boolean;
  isOpen: boolean;
  providers: Array<Provider>;
  queryId: string;
  listId: string;
  isLoading: boolean;
  submitInquiry: boolean;
  userLocation: {
    city: string;
    state: string;
    latitude: number;
    longitude: number;
  };
}

const initialState: ChatState = {
  formTemplateId: '',
  chatHistory: [],
  inactiveChatHistory: [],
  currentStepIndex: 0,
  userInput: {
    fullName: '',
    email: '',
    phoneNumber: '',
    payingWithMedicaid: false,
    whoAreYouLookingFor: '',
    typeOfCare: '',
    anonId: '',
    formId: '',
    trackingNotes: '',
    providerId: '',
    ip: '',
    careLocation: '',
    source: ''
  },
  isFirstOpen: true,
  step: {
    type: StepType.Input,
    isFirstStep: false,
    isLastStep: false,
    showLegalText: false,
    submitInquiry: false,
    text: '',
    options: []
  },
  thinking: true,
  isOpen: false,
  providers: [],
  queryId: '',
  listId: '',
  isLoading: false,
  submitInquiry: false,
  userLocation: {
    city: '',
    state: '',
    latitude: 0,
    longitude: 0
  }
};

const ChatContext = createContext<
  | {
      state: ChatState;
      dispatch: React.Dispatch<any>;
    }
  | undefined
>(undefined);

export const ChatProvider: React.FC<{
  children: ReactNode;
  chatConfig: ChatConfig;
}> = ({ children, chatConfig }) => {
  const { site } = useContext(SiteContext);
  const domain = site?.path || Domains.CaringDomains.LIVE;
  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    source: domain,
    step: chatConfig[Object.keys(chatConfig)[0]] || {}
  });

  return (
    <ChatContext.Provider value={{ state, dispatch }}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
