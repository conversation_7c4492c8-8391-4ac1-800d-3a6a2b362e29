import { FEATURE_FLAGS } from '@constants/features';
import { useFeatureIsOn, useGrowthBook } from '@growthbook/growthbook-react';
import { render } from '@testing-library/react';

import ChatWidget from './ChatWidget';
import { ChatWidgetProps, StepType } from './types';

// Mock dependencies
jest.mock('@growthbook/growthbook-react', () => ({
  useFeatureIsOn: jest.fn(),
  useGrowthBook: jest.fn()
}));

jest.mock('./ChatWidgetContent', () => {
  return function MockChatWidgetContent(props: ChatWidgetProps) {
    return (
      <div
        data-testid="chat-widget-content"
        data-props={JSON.stringify(props)}
      />
    );
  };
});

describe('ChatWidget', () => {
  const defaultProps: ChatWidgetProps = {
    data: {
      chatConfig: {
        step1: {
          type: StepType.Button,
          text: 'Hello',
          isFirstStep: true,
          isLastStep: false,
          showLegalText: false,
          submitInquiry: false,
          options: [
            {
              label: 'Test Button',
              name: 'test',
              value: 'test',
              nextStep: false,
              type: 'button'
            }
          ]
        }
      },
      chatName: 'Test Chat'
    },
    formName: 'test-form'
  };

  beforeEach(() => {
    (useGrowthBook as jest.Mock).mockReturnValue({ ready: true });
    (useFeatureIsOn as jest.Mock).mockReturnValue(false);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders ChatWidgetContent when GrowthBook is ready and HubSpot chat is disabled', () => {
    const { getByTestId } = render(<ChatWidget {...defaultProps} />);
    expect(getByTestId('chat-widget-content')).toBeInTheDocument();
  });

  it('does not render when GrowthBook is not ready', () => {
    (useGrowthBook as jest.Mock).mockReturnValue({ ready: false });
    const { container } = render(<ChatWidget {...defaultProps} />);
    expect(container).toBeEmptyDOMElement();
  });

  it('does not render when HubSpot chat is enabled', () => {
    (useFeatureIsOn as jest.Mock).mockReturnValue(true);
    const { container } = render(<ChatWidget {...defaultProps} />);
    expect(container).toBeEmptyDOMElement();
  });

  it('checks for the correct feature flag', () => {
    render(<ChatWidget {...defaultProps} />);
    expect(useFeatureIsOn).toHaveBeenCalledWith(FEATURE_FLAGS.HUBSPOT_CHAT);
  });

  // New tests for core functionality
  it('passes all props correctly to ChatWidgetContent', () => {
    const { getByTestId } = render(<ChatWidget {...defaultProps} />);
    const content = getByTestId('chat-widget-content');
    const passedProps = JSON.parse(content.getAttribute('data-props') || '{}');

    expect(passedProps).toEqual(defaultProps);
  });

  it('handles empty chat config correctly', () => {
    const propsWithEmptyConfig = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        chatConfig: {}
      }
    };

    const { getByTestId } = render(<ChatWidget {...propsWithEmptyConfig} />);
    const content = getByTestId('chat-widget-content');
    const passedProps = JSON.parse(content.getAttribute('data-props') || '{}');

    expect(passedProps.data.chatConfig).toEqual({});
  });

  it('handles missing metadata correctly', () => {
    const propsWithoutMetadata = {
      ...defaultProps,
      metadata: undefined
    };

    const { getByTestId } = render(<ChatWidget {...propsWithoutMetadata} />);
    const content = getByTestId('chat-widget-content');
    const passedProps = JSON.parse(content.getAttribute('data-props') || '{}');

    expect(passedProps.metadata).toBeUndefined();
  });

  it('provides chat config to ChatProvider correctly', () => {
    const { getByTestId } = render(<ChatWidget {...defaultProps} />);
    const content = getByTestId('chat-widget-content');
    const passedProps = JSON.parse(content.getAttribute('data-props') || '{}');

    expect(passedProps.data.chatConfig).toEqual(defaultProps.data.chatConfig);
  });
});
