import { fetchResponseJSON } from '~/services/magnolia/api';

import { ChatConfig, ChatWidgetAppResponse } from './types';
import { transformResponseToConfig } from './utils';

const defaultBaseUrl = process.env.NEXT_PUBLIC_MGNL_HOST;

interface ChatWidgetProps {
  title?: string;
  legalText?: string;
  steps: string;
}

export const getServerSideComponentProps = async (
  props: ChatWidgetProps
): Promise<{ chatConfig: ChatConfig; chatName: string }> => {
  let url = new URL(
    `${defaultBaseUrl}/.rest/delivery/chat-widget?@jcr:uuid=${props.steps}`
  );
  const response = (await fetchResponseJSON(url.href)) as ChatWidgetAppResponse;

  if (response.total === 0 || response.error) {
    return { chatConfig: {}, chatName: '' };
  }

  const chatName = response?.results[0]?.name ?? 'Chat Widget';
  const chatConfig = transformResponseToConfig(response?.results[0]);

  return { chatConfig: chatConfig, chatName: chatName };
};
