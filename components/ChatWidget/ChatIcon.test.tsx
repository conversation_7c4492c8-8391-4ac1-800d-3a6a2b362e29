import { fireEvent, render, screen } from '@utils/test-utils';

import ChatIcon from './ChatIcon';
import { ChatProvider } from './ChatProvider';

const mockDispatch = jest.fn();
jest.mock('./ChatProvider', () => {
  const actualModule = jest.requireActual('./ChatProvider');
  return {
    ...actualModule,
    useChat: jest.fn(() => ({
      state: {},
      dispatch: mockDispatch
    }))
  };
});

let mockIsMapVisible = false;
jest.mock('~/contexts/ModalContext', () => ({
  useVisibleModals: jest.fn(() => ({
    isVisible: () => mockIsMapVisible
  }))
}));

const mockElementClicked = jest.fn();
jest.mock('@components/Analytics/events/ElementClicked', () => {
  const actualModule = jest.requireActual(
    '@components/Analytics/events/ElementClicked'
  );
  return {
    ...actualModule,
    __esModule: true,
    default: jest.fn(() => mockElementClicked)
  };
});

describe('ChatIcon', () => {
  const renderComponent = (props = {}, chatConfig = {}) => {
    return render(
      <ChatProvider chatConfig={chatConfig}>
        <ChatIcon {...props} />
      </ChatProvider>
    );
  };

  afterEach(() => {
    mockDispatch.mockClear();
    mockElementClicked.mockClear();
    mockIsMapVisible = false;
  });

  it('renders chat icon button', () => {
    renderComponent();
    expect(screen.getByAltText('Caring Chat')).toBeInTheDocument();
  });

  it('dispatches toggle event and logs analytics on click', () => {
    renderComponent();
    const chatIcon = screen.getByAltText('Caring Chat');
    fireEvent.click(chatIcon);
    expect(mockDispatch).toHaveBeenCalled();
    expect(mockElementClicked).toHaveBeenCalled();
  });

  it('does not render if the map modal is open', () => {
    mockIsMapVisible = true;
    renderComponent();
    expect(screen.queryByAltText('Caring Chat')).not.toBeInTheDocument();
  });
});
