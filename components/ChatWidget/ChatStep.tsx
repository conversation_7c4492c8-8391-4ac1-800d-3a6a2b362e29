import { Flex, VStack } from '@chakra-ui/react';

import ChatBubble from './ChatBubble';

interface ChatStepProps {
  text: string;
  buttons: React.ReactNode;
  isHistory?: boolean;
}

const ChatStep = ({ text, buttons, isHistory = false }: ChatStepProps) => (
  <VStack width="100%" align="start" marginBottom={2}>
    <ChatBubble zIndex="toast" dangerouslySetInnerHTML={{ __html: text }} />
    {isHistory ? (
      <Flex
        flexFlow="row-reverse wrap"
        justifyContent="flex-start"
        padding="0px 0px 20px"
      >
        {buttons}
      </Flex>
    ) : (
      buttons
    )}
  </VStack>
);

export default ChatStep;
