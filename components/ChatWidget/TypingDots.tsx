import { Box, Text } from '@chakra-ui/react';
import { keyframes } from '@emotion/react';
import styled from '@emotion/styled';

const bounce = keyframes`
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
`;

const TypingDotsContainer = styled(Text)`
  span {
    display: inline-block;
    animation: ${bounce} 1s infinite;
  }

  span:nth-of-type(1) {
    animation-delay: 0s;
  }

  span:nth-of-type(2) {
    animation-delay: 0.2s;
  }

  span:nth-of-type(3) {
    animation-delay: 0.4s;
  }
`;

const TypingDots: React.FC = () => {
  return (
    <Box textAlign="left" width="100%" mt={4} fontSize="lg">
      <TypingDotsContainer
        mt={10}
        backgroundColor="gray.100"
        display="inline-block"
        marginBlock={1}
        borderRadius="24px 24px 24px 3px"
        padding="10px 21px 11px 16px"
        lineHeight="20px"
        marginRight="auto"
        maxWidth="calc(100% - 40px)"
        fontSize="30px"
        fontWeight="600"
      >
        <span>.</span>
        <span>.</span>
        <span>.</span>
      </TypingDotsContainer>
    </Box>
  );
};

export default TypingDots;
