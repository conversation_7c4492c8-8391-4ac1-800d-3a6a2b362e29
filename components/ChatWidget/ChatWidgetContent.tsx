'use client';

import { Box, Flex, Stack } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import {
  stepContent,
  stepSubmission
} from '@components/Analytics/events/eventContracts';
import useFormStepSubmission, {
  FormType
} from '@components/Analytics/events/FormStepSubmission';
import useFormSubmission from '@components/Analytics/events/FormSubmission';
import useInquirySubmission from '@components/Analytics/events/InquirySubmission';
import { sendInquiryForm } from '@components/InquiryForm/InquiryForm.utils';
import { getSavedValues } from '@components/InquiryForm/saveUtils';
import { saveInquiryForm } from '@components/InquiryForm/saveUtils';
import debounce from 'lodash/debounce';
import dynamic from 'next/dynamic';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

import CatalogContext from '~/contexts/CatalogContext';
import ProviderContext from '~/contexts/Provider';
import { useSessionContext } from '~/contexts/SessionContext';
import SiteContext from '~/contexts/SiteContext';
import useTracking from '~/hooks/useTracking';
import { InquiryFormAPIPayload } from '~/types/Inquiry';
import { getClientIp } from '~/utils/ipify';

import { useChat } from './ChatProvider';
import ChatWidgetPopOut from './ChatWidgetPopOut';
import { ChatHistoryProps, ChatWidgetProps, StepType } from './types';
import { handleButtonClick } from './utils';
import { generateFormFieldsNamespace, getNextValidStep } from './utils';
const ChatHistory = dynamic(() => import('./ChatHistory'));
const ChatIcon = dynamic(() => import('./ChatIcon'));
const ChatSteps = dynamic(() => import('./ChatSteps'));
const ChatWidgetTitleBar = dynamic(() => import('./ChatWidgetTitleBar'));
const TypingDots = dynamic(() => import('./TypingDots'));

const ChatWidgetContent = ({
  templateId,
  title = 'Senior Living Assistant',
  legalText,
  data,
  metadata
}: ChatWidgetProps) => {
  const { state, dispatch } = useChat();

  const {
    isOpen,
    thinking,
    step,
    userInput,
    chatHistory,
    currentStepIndex,
    isFirstOpen,
    inactiveChatHistory,
    isLoading,
    submitInquiry,
    userLocation
  } = state;

  const chatWidgetId = metadata?.['@id'] || templateId || uuidv4();
  const catalogContext = useContext(CatalogContext);
  const providerContext = useContext(ProviderContext)?.provider;
  const siteContext = useContext(SiteContext);
  const { sessionId } = useSessionContext();

  const [isOnline, setIsOnline] = useState(false);
  const { anonId, trackingNotes } = useTracking();
  const [stepSubmission, setStepSubmission] = useState<stepSubmission[]>([]);
  const chatEndRef = useRef(null);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const [areButtonsVisible, setAreButtonsVisible] = useState(false);
  const elementClicked = useElementClicked();

  const inquirySubmission = useInquirySubmission();
  const formStepSubmission = useFormStepSubmission();
  const formSubmission = useFormSubmission();

  const getRandomDuration = useCallback(
    () => (isHeaderVisible ? 0 : Math.random() * 2000 + 1000),
    [isHeaderVisible]
  );
  const formInstanceId = useMemo(() => uuidv4(), []);
  const formTemplateId = useMemo(() => {
    const formFieldsNamespace = generateFormFieldsNamespace(
      data.chatConfig,
      title,
      legalText || ''
    );
    return uuidv5(formFieldsNamespace, chatWidgetId);
  }, [data.chatConfig, title, legalText, chatWidgetId]);

  useEffect(() => {
    dispatch({
      type: 'SET_FORM_TEMPLATE_ID',
      payload: formTemplateId
    });
  }, [formTemplateId, dispatch]);

  const updateUserInput = useCallback(
    (newData: Partial<InquiryFormAPIPayload>) => {
      dispatch({
        type: 'UPDATE_USER_INPUT',
        payload: newData
      });
    },
    [dispatch]
  );

  const debouncedUpdateUserInput = useMemo(
    () => debounce(updateUserInput, 300),
    [updateUserInput]
  );

  const updateUserInputOnLoad = useCallback(() => {
    const savedValues = getSavedValues();
    dispatch({
      type: 'UPDATE_USER_INPUT',
      payload: {
        anonId: anonId || '',
        formId: formInstanceId || '',
        trackingNotes: trackingNotes || '',
        providerId: providerContext?.id || '',
        source: siteContext?.site?.domains[0] ?? 'caring.com',
        careLocation:
          catalogContext?.city && catalogContext?.state
            ? `${catalogContext?.city}, ${catalogContext?.state}`
            : '',
        ...savedValues
      }
    });
  }, [
    dispatch,
    anonId,
    trackingNotes,
    providerContext?.id,
    siteContext?.site?.domains,
    catalogContext?.city,
    catalogContext?.state,
    formInstanceId
  ]);

  useEffect(() => {
    updateUserInputOnLoad();
  }, [updateUserInputOnLoad]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsOnline(true);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const fetchIpAndLocation = async () => {
      const ip = userInput.ip === '' ? await getClientIp() : userInput.ip;
      if (ip) {
        debouncedUpdateUserInput({ ip: ip });
        const response = await fetch(`/api/geoip?ip=${ip}`);
        const locationData = await response.json();
        dispatch({
          type: 'SET_USER_LOCATION',
          payload: locationData
        });
      }
    };
    fetchIpAndLocation();
  }, [debouncedUpdateUserInput, userInput.ip, dispatch]);

  useEffect(() => {
    const newCareLocation =
      userLocation.city && userLocation.state
        ? `${userLocation.city}, ${userLocation.state}`
        : catalogContext?.city && catalogContext?.state
        ? `${catalogContext?.city}, ${catalogContext?.state}`
        : '';

    debouncedUpdateUserInput({
      careLocation: newCareLocation
    });
  }, [userLocation, catalogContext, debouncedUpdateUserInput]);

  useEffect(() => {
    if (
      Object.values(userInput).some(
        (value) => value !== '' && value !== undefined
      )
    ) {
      const values = Object.fromEntries(
        Object.entries(userInput).filter(
          ([_, value]) => value !== '' && value !== undefined
        )
      );
      saveInquiryForm(values);
    }
  }, [userInput]);

  useEffect(() => {
    if (chatEndRef.current) {
      (chatEndRef.current as HTMLElement).scrollIntoView({ behavior: 'auto' });
    }
  }, [step, thinking]);

  useEffect(() => {
    if (chatEndRef.current) {
      (chatEndRef.current as HTMLElement).scrollIntoView();
    }
  }, [isOpen, isFirstOpen]);

  useEffect(() => {
    if (isOpen && isFirstOpen) {
      dispatch({ type: 'SET_FIRST_OPEN', payload: false });
      setTimeout(() => {
        dispatch({ type: 'SET_THINKING', payload: false });
      }, getRandomDuration());
    }
  }, [isOpen, isFirstOpen, dispatch, getRandomDuration]);

  useEffect(() => {
    if (submitInquiry) {
      sendInquiryForm(userInput);
      inquirySubmission({
        ...userInput,
        name: data.chatName,
        display: 'chat-widget',
        type: 'inquiry'
      });
      formSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        step_submissions: stepSubmission,
        form_type: FormType.INQUIRY
      });
      dispatch({ type: 'SUBMIT_INQUIRY', payload: false });
    }
  }, [
    submitInquiry,
    formInstanceId,
    formSubmission,
    formTemplateId,
    inquirySubmission,
    step,
    stepSubmission,
    userInput,
    dispatch,
    data.chatName
  ]);

  const handleOptionSelect = (
    stepQuestion,
    selectedValue,
    stepValues,
    nextStep,
    field,
    isLastStep
  ) => {
    if (nextStep === false && isOpen === true) {
      dispatch({ type: 'TOGGLE_CHAT' });
      return;
    }

    const { nextStepKey, submitInquiry } = getNextValidStep(
      nextStep,
      data.chatConfig,
      userInput
    );
    dispatch({ type: 'SET_THINKING', payload: true });

    const chatHistoryItem: ChatHistoryProps = {
      stepQuestion,
      selectedValue,
      stepValues,
      field
    };

    dispatch({ type: 'ADD_TO_HISTORY', payload: chatHistoryItem });
    handleStepEvent({
      chatHistory: chatHistoryItem,
      formInstanceId: formInstanceId,
      metaDataId: chatWidgetId,
      stepType: step.type || StepType.Button
    });
    dispatch({ type: 'SUBMIT_INQUIRY', payload: submitInquiry });
    setTimeout(() => {
      dispatch({ type: 'SET_THINKING', payload: false });
      dispatch({ type: 'SET_STEP', payload: data.chatConfig[nextStepKey] });
      if (isLastStep) {
        const history = [...chatHistory, chatHistoryItem];
        dispatch({
          type: 'SET_INACTIVE_HISTORY',
          payload: history
        });
        dispatch({ type: 'CLEAR_HISTORY' });
      }
    }, getRandomDuration());
  };

  const handleStepEvent = useCallback(
    ({
      chatHistory,
      formInstanceId,
      metaDataId,
      stepType
    }: {
      chatHistory: ChatHistoryProps;
      formInstanceId: string;
      metaDataId: string;
      stepType: StepType;
    }) => {
      const { stepQuestion, stepValues } = chatHistory;
      const formStepInstanceId = uuidv4();
      const stepContent: stepContent[] = [];
      const stepId = uuidv5(String(currentStepIndex), metaDataId);
      const responseArray = [
        { response_value: chatHistory.selectedValue, response_id: uuidv4() }
      ];
      stepContent.push({
        prompt_id: uuidv5(stepQuestion, metaDataId),
        prompt_type: stepType,
        prompt_instance_id: uuidv4(),
        prompt_index: 1,
        prompt_value: stepQuestion,
        response_array: responseArray
      });

      setStepSubmission((prev) => [
        ...prev,
        {
          step_id: stepId,
          step_instance_id: formStepInstanceId,
          step_index: currentStepIndex
        }
      ]);
      formStepSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        form_type: FormType.INQUIRY,
        step_id: stepId,
        step_instance_id: formStepInstanceId,
        step_index: currentStepIndex,
        step_content: stepContent
      });
      dispatch({ type: 'UPDATE_STEP_INDEX', payload: currentStepIndex + 1 });
    },
    [formStepSubmission, formTemplateId, currentStepIndex, dispatch]
  );

  useEffect(() => {
    const sessionKey = `chatInteraction-${sessionId}`;
    const hasInteracted = localStorage.getItem(sessionKey);
    if (!hasInteracted) {
      const timer = setTimeout(() => {
        setIsHeaderVisible(true);
      }, 5000);
      const buttonsTimer = setTimeout(() => {
        setAreButtonsVisible(true);
      }, 7000);
      localStorage.setItem(sessionKey, 'true');

      return () => {
        clearTimeout(timer);
        clearTimeout(buttonsTimer);
      };
    }
  }, [sessionId]);

  const handleInteraction = () => {
    if (!isOpen && isHeaderVisible && isFirstOpen) {
      localStorage.setItem(`chatInteraction-${sessionId}`, 'true');
      setIsHeaderVisible(false);
      setAreButtonsVisible(false);
      dispatch({ type: 'TOGGLE_CHAT' });
      elementClicked({
        element: {
          type: ElementTypes.BUTTON,
          action: ElementActions.EXPAND,
          name: ElementNames.CHAT_WIDGET,
          text: '',
          color: '',
          textColor: ''
        }
      });
    }
  };

  const handlePopOutClose = () => {
    if (!isOpen && isHeaderVisible && isFirstOpen) {
      localStorage.setItem(`chatInteraction-${sessionId}`, 'true');
      setIsHeaderVisible(false);
      setAreButtonsVisible(false);
      elementClicked({
        element: {
          type: ElementTypes.BUTTON,
          action: ElementActions.COLLAPSE,
          name: ElementNames.CHAT_WIDGET,
          text: '',
          color: '',
          textColor: ''
        }
      });
    }
  };

  const handleButtonClickWrapper = (
    selectedValue: string,
    inputName: string,
    nextStep: string | false,
    isLastStep = false
  ) => {
    handleButtonClick(
      dispatch,
      handleOptionSelect,
      step,
      userInput,
      selectedValue,
      inputName,
      nextStep,
      isLastStep
    );
  };

  return (
    <>
      <Box
        position="fixed"
        bottom={isOpen ? { base: 0, md: 5 } : isFirstOpen ? 0 : 5}
        right={isOpen ? { base: 0, md: 5 } : isFirstOpen ? 0 : 5}
        display="flex"
        flexDir="column"
        zIndex="2000"
        height={
          isOpen
            ? { base: '100%', md: 'auto' }
            : areButtonsVisible
            ? '180px'
            : 'min-content'
        }
        maxHeight={
          isOpen
            ? { base: '100%', md: '620px' }
            : areButtonsVisible
            ? '180px'
            : 'min-content'
        }
        minHeight={isOpen ? { base: 'unset', md: '400px' } : 'unset'}
        minWidth={isOpen ? { base: '100%', md: '350px' } : '52px'}
        className="chat"
        onClick={handleInteraction}
        justifyContent="flex-end"
      >
        {!isOpen && isFirstOpen && (
          <ChatWidgetPopOut
            title={title}
            isHeaderVisible={isHeaderVisible}
            areButtonsVisible={areButtonsVisible}
            isOnline={isOnline}
            step={step}
            legalText={legalText}
            handleButtonClickWrapper={handleButtonClickWrapper}
            handlePopOutClose={handlePopOutClose}
            elementClicked={elementClicked}
          />
        )}
        {!isOpen && !isFirstOpen && <ChatIcon isOnline={isOnline} />}
        {isOpen && (
          <Box
            borderRadius={{ base: 0, md: 12 }}
            bg="white"
            width={{ base: '100%', md: '350px' }}
            height="100%"
            boxShadow="0px 4px 20px 0px rgba(18, 18, 18, 0.2)"
            float="right"
            minHeight={{ base: 'unset', md: '400px' }}
            opacity={isFirstOpen ? 0 : 1}
            transition="transform 0.3s ease-in, opacity 0.3s ease-in"
          >
            <Stack height="100%">
              <ChatWidgetTitleBar title={title} />
              <Flex
                flexDirection="column"
                alignItems="flex-end"
                justifyContent="flex-end"
                paddingRight={0}
                paddingLeft={4}
                height="100%"
                width="100%"
                maxHeight={{ base: '100%', md: '500px' }}
                minHeight={{ base: 'unset', md: '400px' }}
                position="relative"
                style={{ marginTop: '0' }}
                paddingBottom="9px"
              >
                {chatHistory.length > 0 && (
                  <Box
                    height={10}
                    width="100%"
                    position="absolute"
                    top={0}
                    zIndex="sticky"
                    background="linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%)"
                  />
                )}
                <Box
                  position="absolute"
                  bottom={0}
                  margin="auto"
                  height="100%"
                  overflowY="auto"
                  overflowX="hidden"
                  padding={4}
                  flexGrow={1}
                  alignContent="flex-end"
                  width="100%"
                  style={{ scrollbarWidth: 'thin' }}
                >
                  <>
                    {inactiveChatHistory !== undefined &&
                      inactiveChatHistory.length > 0 && (
                        <ChatHistory isInactive={true} />
                      )}
                    <ChatHistory />
                    {!thinking && !isLoading && (
                      <ChatSteps
                        handleOptionSelect={handleOptionSelect}
                        legalText={legalText}
                      />
                    )}
                    <div ref={chatEndRef} />
                  </>
                  {thinking && !isLoading && <TypingDots />}
                </Box>
              </Flex>
            </Stack>
          </Box>
        )}
      </Box>
    </>
  );
};

export default ChatWidgetContent;
