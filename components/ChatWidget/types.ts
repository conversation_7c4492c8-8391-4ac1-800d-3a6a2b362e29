export interface ChatWidgetProps {
  templateId?: string;
  title?: string;
  legalText?: string;
  data: { chatConfig: ChatConfig; chatName: string };
  formName: string;
  metadata?: {
    ['@id']: string;
  };
}
interface StepValues {
  label: string;
  name: string;
  value: string;
  type: 'text' | 'button';
  nextStep: string;
}
export interface ChatHistoryProps {
  stepQuestion: string;
  selectedValue: string;
  stepValues: StepValues[];
  field?: string;
}

export type ButtonOption = {
  label: string;
  name: string;
  value: string;
  nextStep: string | false;
  type: string;
  action?: string;
};

export type InputField = {
  name: string;
  type: 'text' | 'email' | 'tel';
  hasValidation: boolean;
  validationMessage: string;
  placeholder: string;
  nextStep: string | false;
};

export type ButtonStep = {
  type: StepType.Button;
  isFirstStep: boolean;
  isLastStep: boolean;
  showLegalText: boolean;
  submitInquiry: boolean;
  text: string;
  options: ButtonOption[];
};

export type InputStep = {
  type: StepType.Input;
  isFirstStep: boolean;
  isLastStep: boolean;
  showLegalText: boolean;
  submitInquiry: boolean;
  text: string;
  options: InputField[];
};

export enum StepType {
  Button = 'button',
  Input = 'input'
}

export type ChatStep = ButtonStep | InputStep;

export type ChatConfig = { [key: string]: ChatStep };

type HandleOptionSelect = (
  stepQuestion: ChatHistoryProps['stepQuestion'],
  selectedValue: ChatHistoryProps['selectedValue'],
  stepValues: ButtonOption[] | InputField[],
  nextStep: string | false,
  field: string,
  isLastStep: boolean
) => void;

export type { HandleOptionSelect };

// Chat Widget app response types
export interface ChatWidgetAppResponse {
  total: number;
  error: boolean;
  '@id': string;
  name: string;
  results: [ChatWidgetResults];
}
export interface ChatWidgetResults {
  '@name': string;
  name: string;
  '@nodes': string[] & {
    [key: string]: {
      '@name': string;
      '@path': string;
      '@id': string;
      '@nodeType': string;
      position: string;
      name: string;
      question: string;
      showLegalText: boolean;
      inputs: InputsTextFieldResponse | InputsButtonResponse;
      '@nodes': string[];
    };
  };
}

export interface NextStep {
  step?: string;
  field: boolean;
}

export interface InputsButtonResponse {
  '@name': string;
  '@path': string;
  '@id': string;
  '@nodeType': string;
  field: string;
  inputType: {
    button: {
      '@name': string;
      '@path': string;
      '@id': string;
      '@nodeType': string;
      '@nodes': string[];
    } & {
      [key: string]: ButtonResponse;
    };
    '@nodes': string[];
  };
}
export interface ButtonResponse {
  '@name': string;
  '@path': string;
  '@id': string;
  '@nodeType': string;
  buttonValue: string;
  buttonText: string;
  buttonName: string;
  nextStep: NextStep;
  '@nodes': string[];
}
export interface InputsTextFieldResponse {
  '@name': string;
  '@path': string;
  '@id': string;
  '@nodeType': string;
  field: string;
  inputType: {
    '@name': string;
    '@path': string;
    '@id': string;
    '@nodeType': string;
    inputName: string;
    inputPlaceholder: string;
    field: string;
    validationMessage: string;
    '@nodes': string[];
  };
  inputPlaceholder: string;
  inputName: string;
  nextStep: NextStep;
  hasValidation: boolean;
  '@nodes': string[];
}
