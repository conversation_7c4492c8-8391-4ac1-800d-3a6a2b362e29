import {
  FormControl,
  FormErrorMessage,
  IconButton,
  InputGroup,
  InputRightElement
} from '@chakra-ui/react';
import useFormFieldFocused from '@components/Analytics/events/FormFieldFocused';
import { FormType } from '@components/Analytics/events/FormStepSubmission';
import {
  handleFormBlur,
  handleFormFocus
} from '@components/InquiryForm/InquiryForm.utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { PiPaperPlaneRightFill } from 'react-icons/pi';
import * as z from 'zod';

import ChatLegalText from './ChatLegalText';
import { useChat } from './ChatProvider';
import { HandleButtonClickProps } from './ChatSteps';
import PhoneNumberInput from './formElements/PhoneNumberInput';
import TextInput from './formElements/TextInput';
import { InputField } from './types';

interface ChatInputProps {
  config: InputField;
  isLastStep: boolean;
  showLegalText: boolean;
  legalText?: string;
  handleButtonClick: HandleButtonClickProps;
}

const ChatInput: React.FC<ChatInputProps> = ({
  config,
  isLastStep,
  showLegalText,
  legalText,
  handleButtonClick
}) => {
  const { state } = useChat();
  const { formTemplateId } = state;
  const [formFocused, setFormFocused] = useState(false);
  const chatForm = useRef<HTMLDivElement>(null);

  const formFieldFocused = useFormFieldFocused();

  const schema = z.object({
    [config.name]: z
      .string()
      .min(1, 'This field is required')
      .catch('')
      .refine(
        (value) => {
          if (!config.hasValidation) return true;
          const validationPattern =
            config.type === 'tel'
              ? `^\\(\\d{3}\\) \\d{3}-\\d{4}$`
              : config.type === 'email'
              ? `^\\S+@\\S+\\.\\S+$`
              : `^[a-zA-Z]+ [a-zA-Z]+$`;
          return new RegExp(validationPattern).test(value);
        },
        {
          message: config.validationMessage
        }
      )
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm({
    resolver: zodResolver(schema)
  });
  const hasError = !!errors[config.name];

  return (
    <FormControl key={config.name} isInvalid={hasError} ref={chatForm}>
      {errors[config.name] && (
        <FormErrorMessage pb={2}>
          {errors[config.name]?.message as string}
        </FormErrorMessage>
      )}
      <InputGroup size="md">
        {config.type === 'tel' ? (
          <PhoneNumberInput
            name={config.name}
            type={config.type}
            control={control}
            placeholder={config.placeholder}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSubmit((data) => {
                  setValue(config.name, data[config.name]);
                  handleButtonClick(
                    data[config.name],
                    config.name,
                    config.nextStep,
                    isLastStep
                  );
                })();
              }
            }}
            onClick={(e) =>
              handleFormFocus(
                e,
                formFocused,
                setFormFocused,
                formFieldFocused,
                formTemplateId,
                FormType.INQUIRY
              )
            }
            onBlur={(e) =>
              handleFormBlur(e, formFocused, chatForm, setFormFocused)
            }
          />
        ) : (
          <TextInput
            name={config.name}
            type={config.type}
            control={control}
            placeholder={config.placeholder}
            setValue={setValue}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSubmit((data) => {
                  setValue(config.name, data[config.name]);
                  handleButtonClick(
                    data[config.name],
                    config.name,
                    config.nextStep,
                    isLastStep
                  );
                })();
              }
            }}
            onClick={(e) =>
              handleFormFocus(
                e,
                formFocused,
                setFormFocused,
                formFieldFocused,
                formTemplateId,
                FormType.INQUIRY
              )
            }
            onBlur={(e) =>
              handleFormBlur(e, formFocused, chatForm, setFormFocused)
            }
          />
        )}

        <InputRightElement width="auto">
          <IconButton
            aria-label="submit"
            backgroundColor="unset"
            height="100%"
            _hover={{ backgroundColor: 'unset' }}
            isDisabled={hasError}
            onClick={handleSubmit((data) => {
              setValue(config.name, data[config.name]);
              handleButtonClick(
                data[config.name],
                config.name,
                config.nextStep,
                isLastStep
              );
            })}
            color="blue.500"
            icon={<PiPaperPlaneRightFill size="20" />}
          />
        </InputRightElement>
      </InputGroup>
      {showLegalText && (
        <ChatLegalText legalText={legalText} field={config.name} />
      )}
    </FormControl>
  );
};

export default ChatInput;
