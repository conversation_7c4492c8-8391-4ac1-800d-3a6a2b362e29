import ChatButton from './ChatButton';
import ChatInput from './ChatInput';
import { HandleButtonClickProps } from './ChatSteps';
import { ButtonOption, ChatStep, InputField } from './types';

interface StepInputProps {
  options: ChatStep['options'];
  type: ChatStep['type'];
  isLastStep: boolean;
  showLegalText: boolean;
  legalText?: string;
  handleButtonClick: HandleButtonClickProps;
}

const StepInput = ({
  options,
  type,
  isLastStep,
  showLegalText,
  legalText,
  handleButtonClick
}: StepInputProps): JSX.Element => {
  return (
    <>
      {options?.map((option, index) => {
        if (type === 'button') {
          return (
            <ChatButton
              key={`${option.label}-${option.name}-${index}`}
              config={option as ButtonOption}
              isLastStep={isLastStep}
              handleButtonClick={handleButtonClick}
            />
          );
        }
        return (
          <ChatInput
            key={`${option.label}-${option.name}-${index}`}
            config={option as InputField}
            isLastStep={isLastStep}
            showLegalText={showLegalText}
            legalText={legalText}
            handleButtonClick={handleButtonClick}
          />
        );
      })}
    </>
  );
};

export default StepInput;
