import { fetchProvidersWithFacetedSearch } from '@services/faceted-search/api';
import { render, screen } from '@testing-library/react';
import { getLatLongMapBoxAPI } from '@utils/search';
import React from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext, { SiteDefinition } from '~/contexts/SiteContext';
import { Domains } from '~/types/Domains';

import ChatHistory, { fetchProviders } from './ChatHistory';
import { ChatHistoryProps } from './types';

// Mock dependencies
jest.mock('@services/faceted-search/api');
jest.mock('@utils/search');

// Mock ChatBubble
jest.mock('./ChatBubble', () => {
  return function MockChatBubble({ children, ...props }: any) {
    return (
      <div data-testid="chat-bubble" {...props}>
        {children}
      </div>
    );
  };
});

// Mock NearbyProviders
jest.mock('./NearbyProviders', () => {
  return function MockNearbyProviders({
    providers
  }: {
    providers: Provider[];
  }) {
    return (
      <div data-testid="nearby-providers">
        {providers.map((provider, index) => (
          <div key={index} data-testid="provider-item">
            {provider.name}
          </div>
        ))}
      </div>
    );
  };
});

// Mock the useChat hook
const mockDispatch = jest.fn();
let mockChatState = {
  chatHistory: [] as ChatHistoryProps[],
  inactiveChatHistory: [] as ChatHistoryProps[],
  providers: [] as Provider[],
  isLoading: false,
  userInput: {
    typeOfCare: 'assisted-living'
  }
};

jest.mock('./ChatProvider', () => ({
  useChat: () => ({
    state: mockChatState,
    dispatch: mockDispatch
  })
}));

const mockFetchProvidersWithFacetedSearch =
  fetchProvidersWithFacetedSearch as jest.MockedFunction<
    typeof fetchProvidersWithFacetedSearch
  >;
const mockGetLatLongMapBoxAPI = getLatLongMapBoxAPI as jest.MockedFunction<
  typeof getLatLongMapBoxAPI
>;

const mockSiteContext = {
  site: {
    name: 'Caring',
    domains: [Domains.CaringDomains.LIVE],
    path: Domains.CaringDomains.LIVE,
    segmentWriteKey: 'test-key',
    segmentCdnURL: 'test-url',
    partnerToken: 'test-token',
    publicFolder: 'caring_public',
    storyPaths: []
  } as SiteDefinition
};

const mockProvider: Provider = {
  id: '123',
  name: 'Test Provider',
  slug: 'test-provider'
} as Provider;

const renderComponent = () => {
  return render(
    <SiteContext.Provider value={mockSiteContext}>
      <ChatHistory />
    </SiteContext.Provider>
  );
};

describe('ChatHistory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetLatLongMapBoxAPI.mockResolvedValue([-122.4194, 37.7749]);
    mockFetchProvidersWithFacetedSearch.mockResolvedValue({
      results: [mockProvider],
      queryId: 'test-query-id',
      listId: 'test-list-id',
      totalItems: 1,
      totalPages: 1,
      totalNearbyItems: 1,
      totalRegionItems: 0
    });

    // Reset mock state
    mockChatState = {
      chatHistory: [],
      inactiveChatHistory: [],
      providers: [],
      isLoading: false,
      userInput: {
        typeOfCare: 'assisted-living'
      }
    };
  });

  it('renders empty chat history', () => {
    renderComponent();
    // Should not crash with empty chat history
    expect(screen.queryByTestId('chat-bubble')).not.toBeInTheDocument();
  });

  it('renders chat history with button options', () => {
    mockChatState.chatHistory = [
      {
        stepQuestion: 'What type of care are you looking for?',
        stepValues: [
          {
            label: 'Assisted Living',
            name: 'careType',
            value: 'assisted-living',
            type: 'button',
            nextStep: 'step2'
          },
          {
            label: 'Memory Care',
            name: 'careType',
            value: 'memory-care',
            type: 'button',
            nextStep: 'step2'
          }
        ],
        selectedValue: 'assisted-living'
      }
    ];

    renderComponent();

    expect(screen.getByTestId('chat-bubble')).toBeInTheDocument();
    expect(screen.getByText('Assisted Living')).toBeInTheDocument();
    expect(screen.getByText('Memory Care')).toBeInTheDocument();
  });

  it('renders providers when available', () => {
    mockChatState.providers = [mockProvider];
    mockChatState.chatHistory = [
      {
        stepQuestion: 'Where are you looking?',
        stepValues: [
          {
            label: '',
            name: 'location',
            value: '',
            type: 'text',
            nextStep: 'step3'
          }
        ],
        selectedValue: 'San Francisco, CA'
      }
    ];

    renderComponent();

    expect(screen.getByTestId('nearby-providers')).toBeInTheDocument();
    expect(screen.getByTestId('provider-item')).toBeInTheDocument();
    expect(screen.getByText('Test Provider')).toBeInTheDocument();
  });

  it('shows no results message when no providers found', () => {
    mockChatState.providers = [];
    mockChatState.isLoading = false;
    mockChatState.chatHistory = [
      {
        stepQuestion: 'Where are you looking?',
        stepValues: [
          {
            label: '',
            name: 'location',
            value: '',
            type: 'text',
            nextStep: 'step3'
          }
        ],
        selectedValue: 'San Francisco, CA'
      }
    ];

    renderComponent();

    expect(
      screen.getByText("I'm sorry, we didn't find any properties in that area.")
    ).toBeInTheDocument();
  });

  describe('fetchProviders function', () => {
    it('calls Mapbox API with location and uses coordinates in search params', async () => {
      // Import the fetchProviders function to test it directly
      const result = await fetchProviders(
        'San Francisco, CA',
        'assisted_living_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockGetLatLongMapBoxAPI).toHaveBeenCalledWith('San Francisco, CA');
      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: 'San Francisco, CA',
          latLng: '37.7749,-122.4194',
          careType: 'assisted-living'
        }),
        componentProps: expect.objectContaining({
          latitude: '37.7749',
          longitude: '-122.4194',
          careType: 'assisted-living'
        })
      });

      expect(result).toEqual({
        results: [mockProvider],
        queryId: 'test-query-id',
        listId: 'test-list-id'
      });
    });

    it('handles empty location gracefully', async () => {
      await fetchProviders(
        '',
        'assisted_living_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockGetLatLongMapBoxAPI).not.toHaveBeenCalled();
      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: '',
          latLng: '',
          careType: 'assisted-living'
        }),
        componentProps: expect.objectContaining({
          latitude: '',
          longitude: '',
          careType: 'assisted-living'
        })
      });
    });

    it('handles null Mapbox response gracefully', async () => {
      mockGetLatLongMapBoxAPI.mockResolvedValue(null as any);

      await fetchProviders(
        'Invalid Location',
        'assisted_living_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockGetLatLongMapBoxAPI).toHaveBeenCalledWith('Invalid Location');
      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: 'Invalid Location',
          latLng: '',
          careType: 'assisted-living'
        }),
        componentProps: expect.objectContaining({
          latitude: '',
          longitude: '',
          careType: 'assisted-living'
        })
      });
    });

    it('handles missing coordinates in Mapbox response', async () => {
      mockGetLatLongMapBoxAPI.mockResolvedValue([]);

      await fetchProviders(
        'Partial Location',
        'alzheimers_care_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: 'Partial Location',
          latLng: '',
          careType: 'memory-care'
        }),
        componentProps: expect.objectContaining({
          latitude: '',
          longitude: '',
          careType: 'memory-care'
        })
      });
    });

    it('sets latLng only when both coordinates are present', async () => {
      mockGetLatLongMapBoxAPI.mockResolvedValue([-122.4194, 37.7749]);

      await fetchProviders(
        'Complete Location',
        'assisted_living_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: 'Complete Location',
          latLng: '37.7749,-122.4194',
          careType: 'assisted-living'
        }),
        componentProps: expect.objectContaining({
          latitude: '37.7749',
          longitude: '-122.4194',
          careType: 'assisted-living'
        })
      });
    });

    it('sets latLng when both coordinates are present (including zero)', async () => {
      mockGetLatLongMapBoxAPI.mockResolvedValue([0, 37.7749]);

      await fetchProviders(
        'Zero Longitude',
        'alzheimers_care_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          keyword: 'Zero Longitude',
          latLng: '37.7749,0',
          careType: 'memory-care'
        }),
        componentProps: expect.objectContaining({
          latitude: '37.7749',
          longitude: '0',
          careType: 'memory-care'
        })
      });
    });

    it('normalizes care type using kebab-case', async () => {
      await fetchProviders(
        'New York, NY',
        'alzheimers_care_facilities',
        Domains.CaringDomains.LIVE
      );

      expect(mockFetchProvidersWithFacetedSearch).toHaveBeenCalledWith({
        searchParams: expect.objectContaining({
          careType: 'memory-care'
        }),
        componentProps: expect.objectContaining({
          careType: 'memory-care'
        })
      });
    });

    it('handles Mapbox API errors gracefully', async () => {
      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      mockGetLatLongMapBoxAPI.mockRejectedValue(new Error('Mapbox API Error'));
      await expect(
        fetchProviders(
          'Error Location',
          'assisted_living_facilities',
          Domains.CaringDomains.LIVE
        )
      ).rejects.toThrow('Mapbox API Error');

      expect(mockGetLatLongMapBoxAPI).toHaveBeenCalledWith('Error Location');
      expect(mockFetchProvidersWithFacetedSearch).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});
