import { Dispatch } from 'react';

import { InquiryFormAPIPayload } from '~/types/Inquiry';

import {
  ButtonOption,
  ButtonResponse,
  ChatConfig,
  ChatStep,
  ChatWidgetResults,
  InputField,
  InputsButtonResponse,
  InputsTextFieldResponse,
  NextStep,
  StepType
} from './types';

type Action = {
  type: string;
  payload?: any;
};
import { HandleOptionSelect } from './types';

export const getNextValidStep = (
  stepKey: string,
  chatConfig: ChatConfig,
  userInput: InquiryFormAPIPayload
): { nextStepKey: string; submitInquiry: boolean } => {
  let submitInquiry = false;
  const checkNextStep = (
    nextStepKey: string
  ): { nextStepKey: string; submitInquiry: boolean } => {
    const nextStep = chatConfig[nextStepKey];
    if (!nextStep) return { nextStepKey: stepKey, submitInquiry };
    if (nextStep.submitInquiry) {
      submitInquiry = true;
    }
    if (nextStep.type === 'input') {
      const inputField = nextStep.options.find(
        (field) =>
          field.name === 'fullName' ||
          field.name === 'email' ||
          field.name === 'phoneNumber'
      );
      if (
        inputField &&
        userInput[inputField.name] !== '' &&
        userInput[inputField.name] !== undefined &&
        inputField.nextStep !== false
      ) {
        return checkNextStep(inputField.nextStep);
      }
      return { nextStepKey: nextStepKey, submitInquiry };
    }
    return { nextStepKey: nextStepKey, submitInquiry };
  };
  return checkNextStep(stepKey);
};

export const replaceTemplateVariables = (
  text: string,
  userInput: InquiryFormAPIPayload
): string => {
  return (
    text &&
    text.replace(/\{\{(\w+)\}\}/g, (_, key) => {
      if (key === 'fullName' && userInput[key]) {
        const firstName = userInput[key].split(' ')[0].trim();
        return firstName.charAt(0).toUpperCase() + firstName.slice(1);
      }
      return (userInput[key] || '').trim();
    })
  );
};

const createButtonInput = (button: ButtonResponse): ButtonOption => ({
  label: button.buttonText,
  name: button.buttonName,
  value: button.buttonValue,
  type: StepType.Button,
  nextStep: button.nextStep.field ? button.nextStep.step || '' : false
});

const createTextInput = (
  input: InputsTextFieldResponse['inputType'],
  nextStep: NextStep
): InputField => ({
  name: input.inputName,
  type:
    input.field === 'fullName'
      ? 'text'
      : input.field === 'email'
      ? 'email'
      : input.field === 'tel'
      ? 'tel'
      : 'text',
  hasValidation:
    (input.field === 'fullName' ||
      input.field === 'email' ||
      input.field === 'tel') ??
    false,
  validationMessage: input.validationMessage || '',
  placeholder: input.inputPlaceholder || 'Type here...',
  nextStep: nextStep?.field ? nextStep.step || '' : false
});

export const transformResponseToConfig = (
  response: ChatWidgetResults
): ChatConfig => {
  return response['@nodes'].reduce((config: ChatConfig, node: string) => {
    const step = response[node];
    const { inputs, question, position, showLegalText, submitInquiry } = step;
    const stepId = step['@id'];
    const isButtonStep = inputs.field === 'button';
    const options = isButtonStep
      ? (inputs as InputsButtonResponse).inputType.button['@nodes'].map(
          (buttonKey) =>
            createButtonInput(
              (inputs as InputsButtonResponse).inputType.button[buttonKey]
            )
        )
      : [
          createTextInput(
            (inputs as InputsTextFieldResponse).inputType,
            (inputs as InputsTextFieldResponse).nextStep
          )
        ];

    config[stepId] = {
      text: question,
      type: isButtonStep ? StepType.Button : StepType.Input,
      isFirstStep: position === 'first',
      isLastStep: position === 'last',
      showLegalText: showLegalText || false,
      submitInquiry: submitInquiry || false,
      options
    } as ChatStep;
    return config;
  }, {});
};

export const chatReducer = (state, action) => {
  try {
    switch (action.type) {
      case 'SET_FORM_TEMPLATE_ID':
        return { ...state, formTemplateId: action.payload };
      case 'SET_QUERY_ID':
        return { ...state, queryId: action.payload };
      case 'SET_LIST_ID':
        return { ...state, listId: action.payload };
      case 'ADD_TO_HISTORY':
        return {
          ...state,
          chatHistory: [...state.chatHistory, action.payload]
        };
      case 'CLEAR_HISTORY':
        return {
          ...state,
          chatHistory: []
        };
      case 'SET_INACTIVE_HISTORY':
        return {
          ...state,
          inactiveChatHistory: [
            ...state.inactiveChatHistory,
            ...action.payload
          ],
          chatHistory: []
        };
      case 'UPDATE_STEP_INDEX':
        return { ...state, currentStepIndex: action.payload };
      case 'TOGGLE_CHAT':
        return { ...state, isOpen: !state.isOpen };
      case 'SET_THINKING':
        return { ...state, thinking: action.payload };
      case 'SET_STEP':
        return { ...state, step: action.payload };
      case 'UPDATE_USER_INPUT':
        return {
          ...state,
          userInput: {
            ...state.userInput,
            ...action.payload
          }
        };
      case 'CURRENT_STEP':
        return { ...state, currentStep: action.payload };
      case 'SET_FIRST_OPEN':
        return { ...state, isFirstOpen: action.payload };
      case 'SET_PROVIDERS':
        return { ...state, providers: action.payload };
      case 'IS_LOADING':
        return { ...state, isLoading: action.payload };
      case 'SUBMIT_INQUIRY':
        return { ...state, submitInquiry: action.payload };
      case 'SET_USER_LOCATION':
        return { ...state, userLocation: action.payload };
      default:
        return state;
    }
  } catch (error) {
    console.error('Error in chatReducer:', error);
    return state;
  }
};

export const generateFormFieldsNamespace = (
  chatConfig: ChatConfig,
  title: string,
  legalText: string
) => {
  const configString = Object.entries(chatConfig)
    .map(([key, step]) => {
      const optionNames = step.options
        ? step.options.map((option) => option.name).join(',')
        : '';
      return `${key}:${optionNames}`;
    })
    .join('|');

  return [configString, title, legalText].join(' ');
};

export const getFirstStep = (chatConfig: ChatConfig) => {
  const [key, step] =
    Object.entries(chatConfig).find(([_, step]) => step.isFirstStep === true) ||
    [];
  return step
    ? { ...step, id: key }
    : chatConfig[Object.keys(chatConfig)[0]] || {};
};

export const handleButtonClick = (
  dispatch: Dispatch<Action>,
  handleOptionSelect: HandleOptionSelect,
  step: any,
  userInput: any,
  selectedValue: string,
  inputName: string,
  nextStep: string | false,
  isLastStep = false
) => {
  const inquiryInputNames = [
    'fullName',
    'email',
    'phoneNumber',
    'whoAreYouLookingFor',
    'typeOfCare',
    'careLocation',
    'tourTime'
  ];
  const stepQuestion = step.text;
  const stepValues = step.options;
  const isInquiryInput = inquiryInputNames.includes(inputName);
  const value =
    inputName === 'tourTime'
      ? (() => {
          const tourTime = new Date();
          tourTime.setDate(tourTime.getDate() + Number(selectedValue));
          return tourTime
            .toLocaleString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
              month: '2-digit',
              day: '2-digit',
              year: 'numeric'
            })
            .replace(',', '');
        })()
      : selectedValue;
  isInquiryInput &&
    dispatch({
      type: 'UPDATE_USER_INPUT',
      payload: {
        [inputName]: value
      }
    });
  handleOptionSelect(
    stepQuestion,
    selectedValue,
    stepValues,
    nextStep,
    step.text,
    isLastStep
  );
};
