import { VStack } from '@chakra-ui/react';
import dynamic from 'next/dynamic';

import { useChat } from './ChatProvider';
import { HandleOptionSelect } from './types';
import { handleButtonClick, replaceTemplateVariables } from './utils';

const StepInput = dynamic(() => import('./StepInput'));
const ChatBubble = dynamic(() => import('./ChatBubble'));

type HandleButtonClickProps = (
  selectedValue: string,
  inputName: string,
  nextStep: string | false,
  isLastStep?: boolean
) => void;

export type { HandleButtonClickProps };
interface ChatStepProps {
  handleOptionSelect: HandleOptionSelect;
  legalText?: string;
}

export const ChatSteps = ({
  handleOptionSelect,
  legalText
}: ChatStepProps): JSX.Element => {
  const { state, dispatch } = useChat();

  const handleButtonClickWrapper = (
    selectedValue: string,
    inputName: string,
    nextStep: string | false,
    isLastStep = false
  ) => {
    handleButtonClick(
      dispatch,
      handleOptionSelect,
      step,
      userInput,
      selectedValue,
      inputName,
      nextStep,
      isLastStep
    );
  };

  const { step, userInput } = state;

  if (!step) return <></>;
  return (
    <VStack width="100%" align="start" marginBottom={2}>
      <ChatBubble
        zIndex="toast"
        dangerouslySetInnerHTML={{
          __html: replaceTemplateVariables(step?.text, userInput)
        }}
      />
      <StepInput
        options={step?.options}
        type={step.type}
        isLastStep={step.isLastStep}
        showLegalText={step.showLegalText || false}
        legalText={legalText}
        handleButtonClick={handleButtonClickWrapper}
      />
    </VStack>
  );
};

export default ChatSteps;
