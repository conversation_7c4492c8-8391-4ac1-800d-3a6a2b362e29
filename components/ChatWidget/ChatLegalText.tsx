import { Text } from '@chakra-ui/react';
import { TextProps } from '@chakra-ui/react';

interface ChatLegalTextProps extends TextProps {
  legalText?: string;
  field?: string;
}

const ChatLegalText = ({
  legalText = 'By entering your phone number, you agree to our <a href="https://www.caring.com/about/terms/" target="_blank">Terms of Use</a>, and <a href="https://www.caring.com/about/privacy/" target="_blank">Privacy Policy</a>. You also consent to <a href="https://www.caring.com/about/contact-by-telephone/" target="_blank">receive texts and calls</a> which may be auto-dialed, from us and our partner providers; however, your consent is not a condition to using our service',
  field = 'phone',
  ...rest
}: ChatLegalTextProps): JSX.Element => {
  return (
    <Text
      fontSize="11px"
      lineHeight="normal"
      paddingTop={1}
      sx={{
        a: {
          color: 'secondary.500',
          fontSize: '11px',
          textDecoration: 'underline',
          '&:hover': {
            color: 'secondary.700'
          }
        }
      }}
      dangerouslySetInnerHTML={{
        __html: legalText
      }}
      {...rest}
    />
  );
};

export default ChatLegalText;
