import { Box, HStack, Image, Link, Text, VStack } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Rating from '@components/ProviderCard/Rating';
import { useContext } from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Domains } from '~/types/Domains';

import { useChat } from './ChatProvider';

const NearbyProviders = ({ providers }: { providers: Provider[] }) => {
  const { state } = useChat();
  const { queryId, listId } = state;
  const { site } = useContext(SiteContext);
  const domain = site?.path || Domains.CaringDomains.LIVE;
  const elementClicked = useElementClicked();

  return (
    <VStack spacing={4} align="stretch">
      {providers.map((property) => (
        <Link
          href={property.url}
          key={property.id}
          target="_blank"
          _hover={{ textDecoration: 'none' }}
          onClick={() => {
            elementClicked({
              element: {
                type: ElementTypes.LINK,
                action: ElementActions.INTERNAL_LINK,
                name: ElementNames.CHAT_WIDGET,
                text: property.name,
                color: 'white',
                textColor: 'black'
              },
              destinationUrl: property.url,
              query: {
                locationId: property.legacyId ?? property.id,
                queryId: queryId,
                listId: listId
              }
            });
          }}
        >
          <Box borderWidth="1px" borderRadius="lg" p={3}>
            <HStack width="100%" justifyContent="space-between">
              <VStack align="start">
                <Text fontWeight="bold">{property.name}</Text>
                {property.averageRating && (
                  <Rating
                    domain={domain}
                    starColor={'secondary.500'}
                    rating={property.averageRating}
                  />
                )}
                {property.description && (
                  <Text
                    fontSize="sm"
                    noOfLines={2}
                    dangerouslySetInnerHTML={{ __html: property.description }}
                  />
                )}
              </VStack>
              {property.images && property.images.length > 0 && (
                <Image
                  src={property.images[0]}
                  boxSize="100px"
                  objectFit="cover"
                  alt={`${property.name} image`}
                  borderRadius="lg"
                  dropShadow="dark-lg"
                  marginRight={0}
                />
              )}
            </HStack>
          </Box>
        </Link>
      ))}
    </VStack>
  );
};

export default NearbyProviders;
