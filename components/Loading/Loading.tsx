import { Flex } from '@chakra-ui/layout';
import { Spinner } from '@chakra-ui/spinner';

interface Props {
  title?: string;
}

const Loading: React.FC<Props> = ({ title = 'Loading...' }) => {
  return (
    <Flex justify="center" height="full" data-testid="loading-phase" my="auto">
      <Spinner title={title} size="xl" speed="0.8s" color="caringBlue.500" />
    </Flex>
  );
};

export default Loading;
