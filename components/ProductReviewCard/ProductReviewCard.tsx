import {
  Box,
  Heading as ChakraHeading,
  SimpleGrid,
  Text
} from '@chakra-ui/layout';
import { Link, useBreakpointValue } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Heading from '@components/Heading';
import HtmlToReact from '@components/HtmlToReact';
import StoryImage from '@components/Image/StoryImage';
import RatingStars from '@components/RatingStars';
import { isEmptyText } from '@utils/isEmptyText';
import { createID } from '@utils/strings';
import dynamic from 'next/dynamic';
import { useContext } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import SiteContext from '~/contexts/SiteContext';
import { MagnoliaImage } from '~/types/Magnolia';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

export interface rating {
  title: string;
  headingElement?: HeadingElements;
  headingSize?: HeadingSizes | HeadingSizes[];
  stars: string;
  body: string;
  showLabel: boolean;
  starSize: string;
  starColor: string;
}

export interface ProductReviewCardProps {
  linkableHeadingElement?: HeadingElements;
  linkableHeadingSize?: HeadingSizes | HeadingSizes[];
  linkableHeadingText?: string;
  linkableHeadingUrl?: string;
  linkableHeadingBehavior?: '_blank' | '_self' | '_parent' | '_top';
  linkableHeadingRel?: Array<
    'external' | 'nofollow' | 'noopener' | 'noreferrer' | 'opener'
  >;
  headingText: string;
  headingElement?: HeadingElements;
  headingSize?: HeadingSizes | HeadingSizes[];
  ratings: {
    ratings0: rating;
    ratings1?: rating;
    ratings2?: rating;
    ratings3?: rating;
    ratings4?: rating;
    ratings5?: rating;
    ratings6?: rating;
    ratings7?: rating;
    ratings8?: rating;
    ratings9?: rating;
    '@nodes': Array<string>;
  };
  image: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  imageMarginTop: number;
  withContainer?: boolean;
}

const ProductReviewCard = ({
  linkableHeadingElement = 'h2',
  linkableHeadingSize,
  linkableHeadingText,
  linkableHeadingUrl,
  linkableHeadingBehavior,
  linkableHeadingRel,
  headingText,
  headingElement = 'h4',
  headingSize = 'xl',
  ratings,
  image,
  imageMarginTop = 10,
  withContainer = true
}: ProductReviewCardProps): JSX.Element => {
  const elementClicked = useElementClicked();
  const siteContext = useContext(SiteContext);
  const numberOfColumns = useBreakpointValue({
    base: 1,
    sm: 2
  });

  const headingLink = () => (
    <Link
      ml={4}
      href={linkableHeadingUrl}
      target={linkableHeadingBehavior || '_self'}
      rel={linkableHeadingRel?.join(' ') || ''}
      fontWeight="700"
      color="primary.600"
      fontSize={linkableHeadingSize}
      textDecoration="underline"
      m={0}
      onClick={() => {
        elementClicked({
          element: {
            type: ElementTypes.LINK,
            action: linkableHeadingUrl?.includes(
              siteContext.site?.path ? siteContext.site?.path : ''
            )
              ? ElementActions.INTERNAL_LINK
              : ElementActions.EXTERNAL_LINK,
            name: ElementNames.PRODUCT_OVERVIEW_CARD,
            text: linkableHeadingText ?? '',
            color: '',
            textColor: 'primary.600'
          },
          destinationUrl: linkableHeadingUrl
        });
      }}
    >
      {linkableHeadingText}
    </Link>
  );

  const productReviewCard = () => (
    <Box>
      {linkableHeadingText && (
        <ChakraHeading
          as={linkableHeadingElement}
          size={linkableHeadingSize}
          id={createID(linkableHeadingText)}
          textAlign="left"
          style={{ scrollMarginTop: 48 }}
          pb={4}
        >
          {linkableHeadingUrl ? headingLink() : linkableHeadingText}
        </ChakraHeading>
      )}
      {headingText && (
        <Heading
          title={headingText}
          headingElement={headingElement}
          headingSize={headingSize}
          withContainer={false}
          titleAlignment="left"
          mb={2}
        />
      )}
      <SimpleGrid columns={numberOfColumns} spacing={8}>
        <Box>
          {ratings['@nodes'].map((key) => {
            const rating = ratings[key] as rating;
            const parsedRating =
              rating.body && !isEmptyText(rating.body)
                ? HtmlToReact({ html: rating.body })
                : null;
            return (
              <Box key={key}>
                {rating.title && (
                  <Heading
                    title={rating.title}
                    headingElement={rating.headingElement}
                    headingSize={rating.headingSize}
                    withContainer={false}
                    mb={2}
                  />
                )}
                {rating.stars && (
                  <RatingStars
                    key={1}
                    rating={Number(rating.stars)}
                    mb={2}
                    showLabel={rating.showLabel}
                    starSize={rating.starSize}
                    starColor={rating.starColor}
                  />
                )}
                {key === 'ratings0' &&
                  numberOfColumns &&
                  numberOfColumns === 1 && (
                    <StoryImage
                      switchable={image}
                      displayAsBackground={false}
                      desktopHeight="265px"
                      mobileHeight="265px"
                      backgroundSize="contain"
                      containerMarginBottom="16px"
                      withContainer={false}
                    />
                  )}
                {parsedRating && (
                  <Text fontSize="sm" mb={2}>
                    {parsedRating}
                  </Text>
                )}
              </Box>
            );
          })}
        </Box>
        {numberOfColumns && numberOfColumns > 1 && (
          <Box mt={imageMarginTop}>
            <StoryImage
              switchable={image}
              displayAsBackground={false}
              desktopHeight="265px"
              mobileHeight="265px"
              backgroundSize="contain"
              containerMarginBottom="0"
              withContainer={false}
            />
          </Box>
        )}
      </SimpleGrid>
    </Box>
  );

  if (!withContainer) return productReviewCard();

  return <Container>{productReviewCard()}</Container>;
};

export default ProductReviewCard;
