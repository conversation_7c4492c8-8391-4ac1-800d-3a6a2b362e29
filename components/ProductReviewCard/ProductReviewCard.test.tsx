import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';
import {
  DAM_IMAGE_FIXTURE,
  fireEvent,
  render,
  screen
} from '@utils/test-utils';
import React from 'react';

import segmentEvents from '~/config/segment-events';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { MagnoliaImage } from '~/types/Magnolia';

import ProductReviewCard, {
  ProductReviewCardProps,
  rating
} from './ProductReviewCard';

describe('Product review Card', () => {
  const mockAnalytics = {
    track: jest.fn()
  };
  window.tracking = mockAnalytics;

  const mockSiteContext: SiteContextType = {
    site: {
      path: 'caring.com',
      name: 'Caring.com',
      domains: ['caring.com'],
      segmentWriteKey: 'key',
      segmentCdnURL: 'cdn',
      partnerToken: 'token',
      publicFolder: 'folder'
    }
  };

  const mockImage = {
    ...DAM_IMAGE_FIXTURE,
    '@name': 'BMALifeFone-768x520',
    '@path': '/Caring.com/BMALifeFone-768x520.jpeg',
    metadata: {
      fileName: 'BMALifeFone-768x520.jpeg',
      mimeType: 'image/jpeg',
      caption: 'Life Phone',
      fileSize: 39925,
      height: 520,
      width: 768,
      format: 'image/jpeg'
    }
  } as MagnoliaImage;

  const mockRating = {
    title: 'Rating 0',
    headingElement: 'h5',
    headingSize: 'md',
    stars: '5',
    body: 'Earning a five-star rating',
    showLabel: false
  } as rating;

  const card: ProductReviewCardProps = {
    linkableHeadingElement: 'h2',
    linkableHeadingSize: 'xl',
    linkableHeadingText: 'LifeFone Review',
    linkableHeadingUrl:
      'https://www.caring.com/senior-products/best-medical-alert-systems/lifefone/',
    linkableHeadingBehavior: '_blank',
    linkableHeadingRel: ['external'],
    headingText: 'Best Battery Life',
    headingElement: 'h4',
    headingSize: 'md',
    ratings: {
      ratings0: {
        ...mockRating
      },
      ratings1: {
        ...mockRating,
        stars: '4.5',
        body: 'Earning a 4 star rating',
        title: 'Rating 1'
      },
      '@nodes': ['ratings0', 'ratings1']
    },
    image: {
      field: 'damChooser',
      image: mockImage
    },
    imageMarginTop: 0,
    withContainer: true
  };

  it('render feature card', () => {
    const { getByText, getByRole, getByAltText } = render(
      <SiteContext.Provider value={mockSiteContext}>
        <ProductReviewCard
          linkableHeadingElement={card.linkableHeadingElement}
          linkableHeadingSize={card.linkableHeadingSize}
          linkableHeadingText={card.linkableHeadingText}
          linkableHeadingUrl={card.linkableHeadingUrl}
          linkableHeadingBehavior={card.linkableHeadingBehavior}
          linkableHeadingRel={card.linkableHeadingRel}
          headingText={card.headingText}
          headingElement={card.headingElement}
          headingSize={card.headingSize}
          ratings={card.ratings}
          image={card.image}
          imageMarginTop={card.imageMarginTop}
          withContainer={card.withContainer}
        />
      </SiteContext.Provider>
    );

    const linkableHeadingText = screen.getByRole('heading', {
      name: `${card.linkableHeadingText}`,
      level: 2
    });
    expect(linkableHeadingText).toBeInTheDocument();
    const linkableHeadingLink = screen.getByRole('link', {
      name: `${card.linkableHeadingText}`
    });
    expect(linkableHeadingLink).toHaveAttribute(
      'href',
      `${card.linkableHeadingUrl}`
    );
    expect(linkableHeadingLink).toHaveAttribute(
      'target',
      `${card.linkableHeadingBehavior}`
    );
    expect(linkableHeadingLink).toHaveAttribute(
      'rel',
      `${card.linkableHeadingRel?.join(' ')}`
    );
    fireEvent.click(linkableHeadingLink);
    expect(mockAnalytics.track).toHaveBeenCalled();
    expect(mockAnalytics.track).toHaveBeenCalledWith(
      segmentEvents.ELEMENT_CLICKED,
      {
        destination_url: card.linkableHeadingUrl,
        element: {
          action: ElementActions.INTERNAL_LINK,
          color: '',
          id: undefined,
          name: ElementNames.PRODUCT_OVERVIEW_CARD,
          text: card.linkableHeadingText,
          text_color: 'primary.600',
          type: ElementTypes.LINK
        },
        page_session_id: '',
        dice_roll_uuid: FALLBACK_DICE_ROLL_UUID,
        query: {
          list_id: '',
          location_id: '',
          query_id: ''
        },
        session_id: ''
      }
    );

    const headingText = screen.getByRole('heading', {
      name: `${card.headingText}`,
      level: 4
    });
    expect(headingText).toBeInTheDocument();
    expect(getByText(card.ratings['ratings0'].body)).toBeInTheDocument();
    expect(getByAltText(mockImage.metadata.caption));
    expect(
      getByText(card.ratings?.ratings1?.body ? card.ratings.ratings1?.body : '')
    ).toBeInTheDocument();
  });
});
