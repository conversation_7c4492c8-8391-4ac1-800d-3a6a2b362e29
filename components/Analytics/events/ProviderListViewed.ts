import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';
import { getActivePromotion } from '@utils/getActivePromotion';

import segmentEvents from '~/config/segment-events';
import { Provider } from '~/contexts/Provider';
import { SessionContextType } from '~/contexts/SessionContext';

import { location, providerListViewed } from './eventContracts';

interface ProviderListViewedProps {
  session: SessionContextType;
  providers: Provider[];
  listName?: string;
  listVersion?: string;
}

const mapProviderToLocation = (
  provider: Provider,
  position: number
): location => {
  const { id, name, url, photos } = provider;
  const hasActivePromotion = getActivePromotion(provider.promotions ?? []);
  const location: location = {
    location_id: id,
    name,
    position,
    slug: url,
    image_url: photos?.[0]?.url ?? '',
    promotion_active: !!hasActivePromotion
  };

  return location;
};

export const trackProviderListViewed = ({
  session,
  providers,
  listName,
  listVersion
}: ProviderListViewedProps) => {
  if (!session?.sessionId || !session?.pageSessionId) {
    console.warn(
      'Attempted to track ProviderListViewed without valid session data'
    );
    return;
  }

  const eventName = segmentEvents.FAVORITES_LIST_VIEWED;
  const locations = providers.map((provider, index) =>
    mapProviderToLocation(provider, index)
  );

  const eventProperties: providerListViewed = {
    page_session_id: session.pageSessionId,
    session_id: session.sessionId,
    location_count: locations.length,
    list_name: listName ?? '',
    list_version: listVersion ?? '',
    locations,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };

  window.tracking?.track(eventName, eventProperties);
};
