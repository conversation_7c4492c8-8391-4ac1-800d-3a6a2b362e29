import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';

import { SearchSubmission, searchSubmissionFields } from './eventContracts';

interface SearchSubmissionType {
  sessionContext: SessionContextType;
  searchSubmissionData: searchSubmissionFields;
}

const searchSubmit = ({
  sessionContext,
  searchSubmissionData
}: SearchSubmissionType) => {
  const searchSubmissionContract: SearchSubmission = {
    search_submission_json: JSON.stringify({
      search_submission: [searchSubmissionData]
    }),
    page_session_id: sessionContext.pageSessionId,
    session_id: sessionContext.sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    form_type: searchSubmissionData.search_type ?? ''
  };
  window.tracking.track(
    segmentEvents.SEARCH_SUBMITTED,
    searchSubmissionContract
  );
};

const useSearchSubmission = () => {
  const sessionContext = useSessionContext();
  return (searchSubmissionData: searchSubmissionFields) => {
    return searchSubmit({ sessionContext, searchSubmissionData });
  };
};

export default useSearchSubmission;
