import { flattenObject, getFormValues } from './InquirySubmission';

describe('InquirySubmission', () => {
  describe('flattenObject', () => {
    it('returns an empty object when the source is empty', () => {
      expect(flattenObject({})).toEqual({});
    });

    it('returns a flat object when there is only one level on the source', () => {
      expect(
        flattenObject({
          one: 1,
          two: 2
        })
      ).toEqual({
        one: 1,
        two: 2
      });
    });

    it('returns a flat object when there are multiple levels on the source', () => {
      expect(
        flattenObject({
          level0: '0',
          withChildren: {
            level1: '1',
            withChildren: {
              level2: '2'
            }
          }
        })
      ).toEqual({
        level0: '0',
        level1: '1',
        level2: '2'
      });
    });
  });

  describe('getFormValues', () => {
    it('returns an empty array when the source is empty', () => {
      expect(getFormValues({})).toEqual([]);
    });

    it('returns a flat array of FormData when there is only one level on the source', () => {
      expect(
        getFormValues({
          one: 1,
          two: 2
        })
      ).toEqual([
        { name: 'one', value: 1 },
        { name: 'two', value: 2 }
      ]);
    });

    it('returns a flat array of FormData when there are multiple levels on the source', () => {
      expect(
        getFormValues({
          level0: '0',
          withChildren: {
            level1: '1',
            withChildren: {
              level2: '2'
            }
          }
        })
      ).toEqual([
        { name: 'level0', value: '0' },
        { name: 'level1', value: '1' },
        { name: 'level2', value: '2' }
      ]);
    });
  });
});
