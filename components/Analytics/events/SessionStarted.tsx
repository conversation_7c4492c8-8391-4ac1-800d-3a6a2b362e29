import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';

import { SessionStarted } from './eventContracts';

export function trackSessionStarted(pageSessionId: string, sessionId: string) {
  const sessionContract: SessionStarted = {
    page_session_id: pageSessionId,
    session_id: sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };
  window?.tracking?.track(segmentEvents.SESSION_STARTED, sessionContract);
}
