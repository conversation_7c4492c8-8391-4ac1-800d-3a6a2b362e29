import { SessionData } from '@hooks/InitializeAnalytics';
import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';

import { ScrollEngagement } from './eventContracts';

interface ScrollEngagementType {
  sessionData: SessionData;
  scrollDepth: number;
}

/**
 * @param sessionData - The session data.
 * @param scrollDepth - The scroll depth.
 */
const scrollEngagement = ({
  sessionData,
  scrollDepth
}: ScrollEngagementType) => {
  const scrollEngagementContract: ScrollEngagement = {
    page_session_id: sessionData.pageSessionId ?? '',
    session_id: sessionData.sessionId ?? '',
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    scroll_depth: scrollDepth
  };
  window?.tracking?.track(
    segmentEvents.SCROLL_ENGAGEMENT,
    scrollEngagementContract
  );
};

export default scrollEngagement;
