import { isTypeEvent } from './ElementClicked';

describe('ElementClicked', () => {
  describe('isTypeEvent', () => {
    it('returns true if param is one of the enum values', () => {
      expect(isTypeEvent('button')).toEqual(true);
      expect(isTypeEvent('link')).toEqual(true);
      expect(isTypeEvent('tel')).toEqual(true);
      expect(isTypeEvent('sms')).toEqual(true);
      expect(isTypeEvent('mailto')).toEqual(true);
    });

    it('returns false if param is null', () => {
      expect(isTypeEvent(null)).toEqual(false);
    });

    it('returns false if param is undefined', () => {
      expect(isTypeEvent(undefined)).toEqual(false);
    });

    it('returns false if param is not one of the enum values', () => {
      expect(isTypeEvent('unknown')).toEqual(false);
    });

    it('returns false if param is one of the enum keys', () => {
      expect(isTypeEvent('BUTTON')).toEqual(false);
      expect(isTypeEvent('LINK')).toEqual(false);
      expect(isTypeEvent('TELEPHONE')).toEqual(false);
      expect(isTypeEvent('SMS')).toEqual(false);
      expect(isTypeEvent('EMAIL')).toEqual(false);
    });
  });
});
