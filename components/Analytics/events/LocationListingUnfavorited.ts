import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';

import { locationListingUnfavorited } from './eventContracts';

interface LocationListingUnfavoritedProps {
  session: SessionContextType;
  providerId: string;
  providerName?: string;
  providerSlug?: string;
}

export const trackLocationListingUnfavorited = ({
  session,
  providerId,
  providerName,
  providerSlug
}: LocationListingUnfavoritedProps) => {
  const eventName = segmentEvents.LOCATION_LISTING_UNFAVORITED;
  const eventProperties: locationListingUnfavorited = {
    page_session_id: session.pageSessionId,
    session_id: session.sessionId,
    location_id: providerId,
    location_name: providerName,
    location_slug: providerSlug,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };

  window.tracking?.track(eventName, eventProperties);
};

const useLocationListingUnfavorited = () => {
  const sessionContext = useSessionContext();

  return ({
    providerId,
    providerName,
    providerSlug
  }: Omit<LocationListingUnfavoritedProps, 'session'>) => {
    return trackLocationListingUnfavorited({
      session: sessionContext,
      providerId,
      providerName,
      providerSlug
    });
  };
};

export default useLocationListingUnfavorited;
