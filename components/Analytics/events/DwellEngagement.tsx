import { SessionData } from '@hooks/InitializeAnalytics';
import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';

import { DwellEngagement } from './eventContracts';

interface DwellEngagementType {
  sessionData: SessionData;
  dwellTime: number;
}

/**
 * @param sessionData - The session data.
 * @param dwellTime - The dwell time in milliseconds.
 */
const dwellEngagement = ({ sessionData, dwellTime }: DwellEngagementType) => {
  const dwellEngagementContract: DwellEngagement = {
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    page_session_id: sessionData.pageSessionId ?? '',
    session_id: sessionData.sessionId ?? '',
    dwell: {
      time_in_seconds: dwellTime / 1000
    }
  };
  window?.tracking?.track(
    segmentEvents.DWELL_ENGAGEMENT,
    dwellEngagementContract
  );
};

export default dwellEngagement;
