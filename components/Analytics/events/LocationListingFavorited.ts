import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';

import { locationListingFavorited } from './eventContracts';

interface LocationListingFavoritedProps {
  session: SessionContextType;
  providerId: string;
  providerName?: string;
  providerSlug?: string;
}

export const trackLocationListingFavorited = ({
  session,
  providerId,
  providerName,
  providerSlug
}: LocationListingFavoritedProps) => {
  const eventName = segmentEvents.LOCATION_LISTING_FAVORITED;
  const eventProperties: locationListingFavorited = {
    page_session_id: session.pageSessionId,
    session_id: session.sessionId,
    location_id: providerId,
    location_name: providerName,
    location_slug: providerSlug,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };

  window.tracking?.track(eventName, eventProperties);
};

const useLocationListingFavorited = () => {
  const sessionContext = useSessionContext();

  return ({
    providerId,
    providerName,
    providerSlug
  }: Omit<LocationListingFavoritedProps, 'session'>) => {
    return trackLocationListingFavorited({
      session: sessionContext,
      providerId,
      providerName,
      providerSlug
    });
  };
};

export default useLocationListingFavorited;
