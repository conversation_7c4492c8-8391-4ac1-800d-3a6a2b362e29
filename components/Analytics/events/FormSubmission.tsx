import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';
import { useContext } from 'react';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';

import { FormSubmission, formSubmissionFields } from './eventContracts';

interface FormSubmissionType {
  sessionContext: SessionContextType;
  formSubmissionData: formSubmissionFields;
  siteContext: SiteContextType;
}

const formSubmit = ({
  sessionContext,
  formSubmissionData,
  siteContext
}: FormSubmissionType) => {
  const formSubmissionContract: FormSubmission = {
    form_submission_json: JSON.stringify({
      form_submission: [
        { source: siteContext?.site?.path, ...formSubmissionData }
      ]
    }),
    page_session_id: sessionContext.pageSessionId,
    session_id: sessionContext.sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    form_type: formSubmissionData.form_type
  };
  window.tracking.track(segmentEvents.FORM_SUBMITTED, formSubmissionContract);
};

const useFormSubmission = () => {
  const sessionContext = useSessionContext();
  const siteContext = useContext(SiteContext);
  return (formSubmissionData: formSubmissionFields) => {
    return formSubmit({ sessionContext, formSubmissionData, siteContext });
  };
};

export default useFormSubmission;
