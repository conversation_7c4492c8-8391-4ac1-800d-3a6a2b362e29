import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';

import {
  LocationComparisonViewed,
  locationComparisonViewedFields
} from './eventContracts';

interface FormSubmissionType {
  sessionContext: SessionContextType;
  data: locationComparisonViewedFields[];
}

const locationComparisonView = ({
  sessionContext,
  data
}: FormSubmissionType) => {
  const LocationsViewed: LocationComparisonViewed = {
    locations: JSON.stringify(data),
    page_session_id: sessionContext.pageSessionId,
    session_id: sessionContext.sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };
  window.tracking.track(
    segmentEvents.LOCATION_COMPARISON_VIEWED,
    LocationsViewed
  );
};

const useLocationComparisonViewed = () => {
  const sessionContext = useSessionContext();
  return (data: locationComparisonViewedFields[]) => {
    return locationComparisonView({ sessionContext, data });
  };
};

export default useLocationComparisonViewed;
