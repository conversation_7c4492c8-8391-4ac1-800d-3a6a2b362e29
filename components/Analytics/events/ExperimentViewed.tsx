import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';

import { ExperimentViewed } from './eventContracts';

interface ExperimentViewedType {
  sessionId;
  pageSessionId;
  experimentId: string;
  experimentName: string;
  variationId: string;
  variationName: string;
}

export const experimentView = ({
  sessionId,
  pageSessionId,
  experimentId,
  experimentName,
  variationId,
  variationName
}: ExperimentViewedType) => {
  const experimentViewedContract: ExperimentViewed = {
    page_session_id: pageSessionId ?? '',
    session_id: sessionId ?? '',
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    experimentation: {
      experiment_id: experimentId ?? '',
      experiment_name: experimentName ?? '',
      variation_id: variationId ?? '',
      variation_name: variationName ?? ''
    }
  };
  window?.tracking?.track(
    segmentEvents.EXPERIMENT_VIEWED,
    experimentViewedContract
  );
};

const useExperimentViewed = () => {
  return (
    sessionId: string,
    pageSessionId: string,
    experimentId: string,
    experimentName: string,
    variationId: string,
    variationName: string
  ) => {
    return experimentView({
      sessionId,
      pageSessionId,
      experimentId,
      experimentName,
      variationId,
      variationName
    });
  };
};

export default useExperimentViewed;
