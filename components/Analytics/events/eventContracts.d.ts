import { ElementActions, ElementNames, ElementTypes } from './ElementClicked';
export interface SessionStarted {
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
}

export interface PageView {
  location: {
    city: string;
    county: string;
    state: string;
    zip: string;
  };
  page: {
    ad_present: boolean;
    attributes: {
      counts: {
        in_region: number;
        nearby: number;
        total: number;
      };
      page_number: number;
      sort_order: string;
    };
    theme: string;
    topic: string;
    category: string;
    layout: string;
    tags: string;
    title?: string;
    type: string;
  };
  page_session_id: string;
  path?: string;
  property: {
    id: string;
  };
  referrer?: string;
  resource: {
    care_type: string;
    id: string;
    rollup_care_type: string;
  };
  search?: string;
  session_id: string;
  dice_roll_uuid: string;
  title?: string;
  url?: string;
}

export interface formValue {
  name: string;
  value: string | number | boolean | null | undefined;
}

export interface InquirySubmission {
  form: {
    display: string;
    filters: {};
    form_field_name: string;
    name: string;
    page_session_id: string;
    session_id: string;
    dice_roll_uuid: string;
    term: string;
    type: string;
    values: formValue[];
  };
  page_session_id: string;
  dice_roll_uuid: string;
  session_id: string;
}

export interface ExperimentViewed {
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  experimentation: {
    experiment_id: string;
    experiment_name: string;
    variation_id: string;
    variation_name: string;
  };
}

export interface SearchStepSubmission {
  search_step_submission_json: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  form_type: string;
}

export interface searchStep {
  search_template_id: string;
  search_instance_id: string;
  step_id: string;
  step_instance_id: string;
  step_index: number;
  step_content: stepContent[];
  form_type?: string;
}

export interface stepContent {
  prompt_id: string;
  prompt_type: string;
  prompt_instance_id: string;
  prompt_index: number;
  prompt_value: string;
  response_array: { response_value: string; response_id: string }[];
}

export interface SearchSubmission {
  search_submission_json: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  form_type: string;
}

export interface searchSubmissionFields {
  search_template_id?: string;
  search_instance_id?: string;
  search_type?: string;
  step_submissions?: stepSubmission[];
}
export interface stepSubmission {
  step_id?: string;
  step_instance_id?: string;
  step_index?: number;
}

export interface FormSubmission {
  form_submission_json: string;
  page_session_id: string;
  dice_roll_uuid: string;
  form_type: string;
  session_id: string;
}

export interface formSubmissionFields {
  form_template_id?: string;
  form_instance_id?: string;
  step_submissions?: stepSubmission[];
  form_type: string;
}

export interface LocationComparisonViewed {
  locations: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
}
export interface locationComparisonViewedFields {
  index: number;
  location_id?: string;
}

export interface formStep {
  form_template_id: string;
  form_instance_id: string;
  form_type: string;
  step_id: string;
  step_instance_id: string;
  step_index: number;
  step_content: stepContent[];
}
export interface FormStepSubmission {
  form_step_submission_json: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  form_type: string;
}

export interface ScrollEngagement {
  scroll_depth: number;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
}

export interface DwellEngagement {
  dwell: {
    time_in_seconds: number;
  };
  dice_roll_uuid: string;
  page_session_id: string;
  session_id: string;
}

export interface ElementClicked {
  element: {
    id?: string;
    type: ElementTypes;
    action: ElementActions;
    name: ElementNames;
    text: string;
    color: string;
    text_color: string;
  };
  query?: {
    location_id: string;
    query_id: string;
    list_id: string;
  };
  destination_url: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
}

export interface FormFieldFocused {
  form_details_json: string;
  form_type: string;
  focus_field: string;
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
}

export interface providerListViewed {
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  location_count: number;
  list_name: string;
  list_version: string;
  locations: Array<location>;
}
export interface location {
  location_id: string;
  name?: string;
  min_price?: number;
  position: number;
  slug?: string;
  image_url?: string;
  promotion_active?: boolean;
}

export interface locationListingFavorited {
  page_session_id: string;
  session_id: string;
  location_id: string;
  location_name?: string;
  location_slug?: string;
  dice_roll_uuid: string;
}

export interface locationListingUnfavorited {
  page_session_id: string;
  session_id: string;
  location_id: string;
  location_name?: string;
  location_slug?: string;
  dice_roll_uuid: string;
}
