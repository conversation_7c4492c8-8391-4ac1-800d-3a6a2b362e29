'use client';

import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';
import { useContext } from 'react';

import segmentEvents from '~/config/segment-events';
import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';
import SiteContext from '~/contexts/SiteContext';

interface ProductClickProps {
  productName: string | null;
  productCategory: string | null;
  productCategoryId: string | null;
  productSubCategory: string | null;
  productId: string | null;
  productLocationId: string | null;
  productProvider: string | null;
  productProviderId: string | null;
  productMonetized: string | null;
  productUrl: string;
  productUrlQuery: {};
  action: ElementActions;
  color?: string;
  name: ElementNames;
  text?: string;
  textColor?: string;
  type: ElementTypes;
  sessionContext: SessionContextType;
}

const productClicked = ({
  productName,
  productCategory,
  productCategoryId,
  productSubCategory,
  productId,
  productLocationId,
  productProvider,
  productProviderId,
  productMonetized,
  productUrl,
  productUrlQuery,
  action,
  color,
  name,
  text,
  textColor,
  type,
  sessionContext
}: ProductClickProps) => {
  if (!productName) {
    return;
  }

  const contract = {
    product_name: productName ?? null,
    product_category: productCategory ?? null,
    product_category_id: productCategoryId ?? null,
    product_sub_category: productSubCategory ?? null,
    product_id: productId ?? null,
    product_location_id: productLocationId ?? null,
    product_provider: productProvider ?? null,
    product_provider_id: productProviderId ?? null,
    product_monetized: productMonetized ?? null,
    product_url: productUrl ?? null,
    product_url_query:
      Object.keys(productUrlQuery).length === 0 ? null : productUrlQuery,
    action: action,
    color: color,
    name: name,
    text: text,
    textColor: textColor,
    type: type,
    page_session_id: sessionContext.pageSessionId,
    session_id: sessionContext.sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };
  window.tracking?.track(segmentEvents.PRODUCT_CLICKED, contract);
};

interface ProductProps
  extends Omit<
    ProductClickProps,
    'action' | 'type' | 'productUrlQuery' | 'sessionContext'
  > {}

export const useProductClicked = (props: ProductProps) => {
  const sessionContext = useSessionContext();
  const siteContext = useContext(SiteContext);
  const {
    productName,
    productCategory,
    productCategoryId,
    productSubCategory,
    productId,
    productLocationId,
    productProvider,
    productProviderId,
    productMonetized,
    productUrl
  } = props;

  return () => {
    let url = '';
    let queryParams = {};
    if (productUrl.length > 1) {
      let parseUrl = productUrl.split(/\?(.+)/).filter((item) => item);
      if (typeof parseUrl === 'object' && 0 in parseUrl) {
        url = parseUrl[0];
        if (1 in parseUrl) {
          parseUrl[1].split('&').forEach((query) => {
            let parsedQuery = query.split('=');
            queryParams[parsedQuery[0]] = parsedQuery[1];
          });
        }
      }
    }

    productClicked({
      productName: productName,
      productCategory: productCategory,
      productCategoryId: productCategoryId,
      productSubCategory: productSubCategory,
      productId: productId,
      productLocationId: productLocationId,
      productProvider: productProvider,
      productProviderId: productProviderId,
      productMonetized: productMonetized,
      productUrl: url,
      productUrlQuery: {
        ...queryParams
      },
      action: productUrl?.includes(
        siteContext.site?.path ? siteContext.site?.path : ''
      )
        ? ElementActions.INTERNAL_LINK
        : ElementActions.EXTERNAL_LINK,
      color: props.color,
      name: props.name,
      text: props.text,
      textColor: props.textColor,
      type: ElementTypes.BUTTON,
      sessionContext: sessionContext
    });
  };
};
