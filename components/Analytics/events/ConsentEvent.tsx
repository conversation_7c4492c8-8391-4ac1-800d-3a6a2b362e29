import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';

import segmentEvents from '~/config/segment-events';
import { SessionContextType } from '~/contexts/SessionContext';

interface ConsentType {
  Advertising: boolean;
  // TODO: Uncomment when we have more consent categories
  // Analytics: boolean;
  // Functional: boolean;
  // DataSharing: boolean;
}

export function consentEvent(
  sessionContext: SessionContextType,
  advertising: boolean
  // TODO: Uncomment when we have more consent categories
  // analytics: boolean,
  // functional: boolean,
  // dataSharing: boolean
) {
  const categoryPreferences: ConsentType = {
    Advertising: advertising
    // TODO: Uncomment when we have more consent categories
    // Analytics: analytics,
    // Functional: functional,
    // DataSharing: dataSharing
  };

  const consentContract = {
    consent: {
      categoryPreferences: {
        ...categoryPreferences
      }
    },
    page_session_id: sessionContext.pageSessionId,
    session_id: sessionContext.sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide()
  };

  window?.tracking?.track(
    segmentEvents.CONSENT_PREFERENCE_UPDATED,
    consentContract
  );
}
