import storeJs from 'store';

import { SessionContextType } from '~/contexts/SessionContext';

import { consentEvent } from './events/ConsentEvent';

interface Consent {
  Advertising: boolean;
  Analytics: boolean;
  Functional: boolean;
  DataSharing: boolean;
}

export const getCategories = (): Consent => {
  const categories: Consent = {
    Advertising: true,
    Analytics: true,
    Functional: true,
    DataSharing: true
  };

  return categories;
};

export function getConsent(): Consent {
  const consent = storeJs.get('consent');
  if (!consent) {
    return getCategories();
  }
  return consent.value as Consent;
}

export function setConsent(
  sessionContext: SessionContextType,
  categories: Consent
) {
  const defaultCategories: Consent = getCategories();
  const consent = {
    Advertising: categories.Advertising ?? defaultCategories.Advertising,
    Analytics: categories.Analytics ?? defaultCategories.Analytics,
    Functional: categories.Functional ?? defaultCategories.Functional,
    DataSharing: categories.DataSharing ?? defaultCategories.DataSharing
  };
  storeJs.set('consent', { value: consent });
  consentEvent(
    sessionContext,
    consent.Advertising
    // We will only send advertising consent for now.
    // consent.Analytics,
    // consent.Functional,
    // consent.DataSharing
  );

  return consent;
}
