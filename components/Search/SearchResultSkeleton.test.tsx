import { render } from '@testing-library/react';

import SearchResultSkeleton from './SearchResultSkeleton';

describe('SearchResultSkeleton', () => {
  test('renders without crashing', () => {
    render(<SearchResultSkeleton />);
  });

  test('renders with fullWidth prop', () => {
    const { getByTestId } = render(<SearchResultSkeleton fullWidth />);
    expect(getByTestId('search-result-skeleton')).toHaveStyle({
      width: '100%'
    });
  });

  test('renders without fullWidth prop', () => {
    const { getByTestId } = render(<SearchResultSkeleton />);
    expect(getByTestId('search-result-skeleton')).toHaveStyle({
      maxWidth: 'sm'
    });
  });
});
