import { fireEvent, render } from '@testing-library/react';

import { useModalControls } from '~/contexts/ModalContext';

import SearchResultFullWidth from './SearchResultFullWidth';

jest.mock('~/contexts/ModalContext');

describe('SearchResultFullWidth', () => {
  const mockShowInquiryForm = jest.fn();

  const defaultProps = {
    id: '1',
    images: [],
    title: 'Provider Name',
    address: 'Provider Address',
    reviewCount: 5,
    displayBadges: false,
    averageRating: 3,
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
    caringStars: [],
    path: '/',
    displayLearnMoreButton: true,
    learnMoreButtonText: 'Learn More',
    displayRequestInfoButton: true,
    modalId: 'modal',
    requestInfoButtonText: 'Request Info',
    readMoreButton: 'redirect_to_provider_page',
    isHidden: false,
    requestInfoButtonColorScheme: 'accent',
    learnMoreButtonColorScheme: 'accent',
    ratingStarsColor: 'accent',
    ratingStarsColorRange: '500',
    providerTitleColor: 'primary',
    providerTitleColorRange: '600',
    boxShadow: 'lg',
    border: '1px solid',
    borderColor: 'blue',
    borderColorRange: '500',
    price: 10,
    displayProviderPhoneNumber: false
  };

  beforeEach(() => {
    (useModalControls as jest.Mock).mockReturnValue({
      show: mockShowInquiryForm
    });
  });

  it('renders provider name and address', () => {
    const { getByText } = render(<SearchResultFullWidth {...defaultProps} />);
    expect(getByText(defaultProps.title)).toBeInTheDocument();
    expect(getByText(defaultProps.address)).toBeInTheDocument();
  });

  it('renders rating and review count', () => {
    const { getByText, getAllByTestId } = render(
      <SearchResultFullWidth {...defaultProps} />
    );
    const ratingStars = getAllByTestId('star-icon');
    expect(ratingStars).toHaveLength(3);
    expect(getByText('3.0')).toBeInTheDocument();
    expect(getByText('(5)')).toBeInTheDocument();
  });

  it('renders description truncated at MAX_LENGTH_OF_DESCRIPTION', () => {
    const { container } = render(<SearchResultFullWidth {...defaultProps} />);
    const description = defaultProps.description.slice(0, 140) + '...';
    expect(container.firstChild).toHaveTextContent(description);
  });

  it('calls showInquiryForm when request info button is clicked', () => {
    window.tracking = {
      track: jest.fn()
    };
    const { getByTestId } = render(<SearchResultFullWidth {...defaultProps} />);
    if (defaultProps.displayRequestInfoButton) {
      const requestInfoButton = getByTestId('request-info-button');
      fireEvent.click(requestInfoButton);
      expect(mockShowInquiryForm).toHaveBeenCalled();
    } else {
      expect(() => getByTestId('request-info-button')).toThrow();
    }
  });
});
