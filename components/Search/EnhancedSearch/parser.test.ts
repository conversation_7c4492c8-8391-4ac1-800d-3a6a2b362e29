import { DEFAULT_VALUES, DISPLAY_MODE } from '../../FacetedSearch/constants';
import {
  ComponentConfigSchema,
  EnhancedSearchConfigParser,
  getDefaultComponentConfig,
  parseComponentConfig
} from './parser';

// Define the expected default component config object
const defaultComponentConfig = {
  amenityCategoryChips: {
    amenityCategory: null,
    basePath: null,
    visible: null
  },
  componentStyle: {
    backgroundColor: null,
    borderRadius: null,
    boxShadow: null
  },
  guidedSearch: { id: null, visible: null },
  heading: { alignment: null, content: null, element: null, size: null },
  mapView: { visible: null },
  metadata: null,
  providerList: {
    genericBlock1: null,
    genericBlock2: null,
    itemsPerRow: DEFAULT_VALUES.ITEMS_PER_ROW,
    noData: { phoneNumber: null },
    providerCard: {
      blurCosts: null,
      border: null,
      borderColor: null,
      heading: { color: null },
      learnMoreButton: { colorScheme: null, content: null, visible: null },
      phoneNumber: { source: null, visible: null },
      ratingStars: { color: null },
      readMoreButton: { behavior: null },
      requestInfoButton: {
        colorScheme: null,
        content: null,
        formId: null,
        visible: null
      }
    }
  },
  searchBar: {
    backgroundColor: null,
    careTypeSelect: { visible: null },
    locationInput: { placeholder: null, readonly: null, visible: null },
    mapSwitch: { visible: null },
    maxWidth: null,
    submitButton: { colorScheme: null, visible: null },
    summary: { visible: null },
    visible: null
  },
  searchOptions: {
    amenityCategory: null,
    careType: null,
    city: null,
    county: null,
    displayMode: DISPLAY_MODE.LIST,
    itemsPerPage: DEFAULT_VALUES.ITEMS_PER_PAGE,
    latitude: 0,
    longitude: 0,
    radiusForSearch: DEFAULT_VALUES.RADIUS_FOR_SEARCH_IN_MILES,
    state: null
  },
  searchSidebar: { filters: [], visible: null }
};

describe('getDefaultComponentConfig', () => {
  it('should return a valid ComponentConfig object', () => {
    const result = getDefaultComponentConfig();
    const validationResult = ComponentConfigSchema.safeParse(result);
    expect(validationResult.success).toBe(true);
  });

  it('should return a ComponentConfig object with default values', () => {
    const result = getDefaultComponentConfig();
    expect(result).toEqual(defaultComponentConfig);
  });
});

describe('parseComponentConfig', () => {
  it('should return the parsed ComponentConfig object if the input is valid', () => {
    const validComponentConfig = {
      bgBorderRadius: 12,
      bgColor: 'primary',
      bgColorRange: '500',
      blurCosts: true,
      boxShadow: 'none',
      careType: 'assisted-living',
      city: 'San Francisco',
      countOfTiles: '30',
      countOfTilesPerRow: '4',
      county: 'San Francisco',
      desktopTitleSize: 'xl',
      displayCareTypeFilter: true,
      displayGuidedSearchBanner: true,
      displayLearnMoreButton: true,
      displayLocationInput: true,
      displayMode: 'list',
      displayProviderPhoneNumber: true,
      displayRequestInfoButton: true,
      displaySearchBar: true,
      displayToggleMap: true,
      displayTotal: true,
      facetedSearch: {
        '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
        '@name': 'facetedSearch',
        '@nodeType': 'mgnl:contentNode',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01/facetedSearch',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00',
        enabled: false
      },
      filterAndSort: {
        '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
        '@name': 'filterAndSort',
        '@nodeType': 'mgnl:contentNode',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01/filterAndSort',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00',
        filters: []
      },
      genericBlock1: {
        '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
        '@name': 'genericBlock1',
        '@nodeType': 'mgnl:contentNode',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01/genericBlock1',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00'
      },
      genericBlock2: {
        '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
        '@name': 'genericBlock2',
        '@nodeType': 'mgnl:contentNode',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01/genericBlock2',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00'
      },
      guidedSearchInquiryId: 'd28fe637-fdd4-407e-94d7-4953df9797f6',
      hideSearchButton: false,
      infoButtonInquiryId: 'd28fe637-fdd4-407e-94d7-4953df9797f6',
      latitude: '37.7749',
      learnMoreButtonColorScheme: 'primary',
      learnMoreButtonText: 'Learn More',
      longitude: '-122.4194',
      mapView: { enabled: true },
      maxWidth: 70,
      metadata: {
        '@id': 'b32c97d4-dc0b-42b6-96e2-240ab64cd5a5',
        '@index': 1,
        '@name': '01',
        '@nodeType': 'mgnl:component',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00',
        'mgnl:lastModified': '2024-03-02T12:29:18.513+01:00',
        'mgnl:template': 'spa-lm:components/component'
      },
      providerPhoneNumberSource: {
        '@id': 'c2fe42aa-7e67-4c77-8102-a7305c3dcd6e',
        '@name': 'providerPhoneNumberSource',
        '@nodeType': 'mgnl:contentNode',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01/providerPhoneNumberSource',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00',
        field: 'globalCatalog',
        providerPhoneNumber: ''
      },
      providerTitleColor: 'primary',
      providerTitleColorRange: '500',
      radiusForSearch: 25,
      ratingStarsColor: 'primary',
      ratingStarsColorRange: '500',
      readMoreButton: 'redirect_to_provider_page',
      readOnlyLocationInput: true,
      readOnlyLocationInputPlaceholder: 'Enter a location',
      requestInfoButtonColorScheme: 'primary',
      requestInfoButtonText: 'Request Info',
      searchBarBgColor: 'secondary',
      searchBarBgColorRange: '200',
      searchBarButtonColorScheme: 'primary',
      state: 'CA',
      tileBorder: 'none',
      tileBorderColor: 'secondary',
      tileBorderColorRange: '500',
      title: 'Assisted Living in San Francisco, CA',
      titleAlignment: 'center',
      titleElement: 'h1',
      titleSize: 'lg'
    };

    const result = parseComponentConfig(validComponentConfig);
    const validationResult = EnhancedSearchConfigParser.safeParse(result);
    expect(validationResult.success).toBe(true);
    expect(result).toEqual({
      amenityCategoryChips: {
        amenityCategory: null,
        basePath: null,
        visible: false
      },
      componentStyle: {
        backgroundColor: { color: 'primary', range: '500' },
        borderRadius: 12,
        boxShadow: 'none'
      },
      guidedSearch: {
        id: 'd28fe637-fdd4-407e-94d7-4953df9797f6',
        visible: true
      },
      heading: {
        alignment: 'center',
        content: 'Assisted Living in San Francisco, CA',
        element: null,
        size: { base: 'lg', desktop: 'xl' }
      },
      mapView: { visible: true },
      metadata: {
        '@id': 'b32c97d4-dc0b-42b6-96e2-240ab64cd5a5',
        '@index': 1,
        '@name': '01',
        '@nodeType': 'mgnl:component',
        '@nodes': [],
        '@path': '/caring.com/local/search/main/01',
        'mgnl:created': '2024-02-27T20:47:36.949+01:00',
        'mgnl:lastModified': '2024-03-02T12:29:18.513+01:00',
        'mgnl:template': 'spa-lm:components/component'
      },
      providerList: {
        genericBlock1: {
          '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
          '@name': 'genericBlock1',
          '@nodeType': 'mgnl:contentNode',
          '@nodes': [],
          '@path': '/caring.com/local/search/main/01/genericBlock1',
          'mgnl:created': '2024-02-27T20:47:36.949+01:00'
        },
        genericBlock2: {
          '@id': 'd28fe637-fdd4-407e-94d7-4953df9797f6',
          '@name': 'genericBlock2',
          '@nodeType': 'mgnl:contentNode',
          '@nodes': [],
          '@path': '/caring.com/local/search/main/01/genericBlock2',
          'mgnl:created': '2024-02-27T20:47:36.949+01:00'
        },
        itemsPerRow: 4,
        noData: { phoneNumber: null },
        providerCard: {
          blurCosts: true,
          border: 'none',
          borderColor: { color: 'secondary', range: '500' },
          heading: { color: { color: 'primary', range: '500' } },
          learnMoreButton: {
            colorScheme: 'primary',
            content: 'Learn More',
            visible: true
          },
          phoneNumber: {
            source: {
              '@id': 'c2fe42aa-7e67-4c77-8102-a7305c3dcd6e',
              '@name': 'providerPhoneNumberSource',
              '@nodeType': 'mgnl:contentNode',
              '@nodes': [],
              '@path':
                '/caring.com/local/search/main/01/providerPhoneNumberSource',
              'mgnl:created': '2024-02-27T20:47:36.949+01:00',
              field: 'globalCatalog',
              providerPhoneNumber: ''
            },
            visible: true
          },
          ratingStars: { color: { color: 'primary', range: '500' } },
          readMoreButton: { behavior: 'redirect_to_provider_page' },
          requestInfoButton: {
            colorScheme: 'primary',
            content: 'Request Info',
            formId: 'd28fe637-fdd4-407e-94d7-4953df9797f6',
            visible: true
          }
        }
      },
      searchBar: {
        backgroundColor: { color: 'secondary', range: '200' },
        careTypeSelect: { visible: true },
        locationInput: {
          placeholder: 'Enter a location',
          readonly: null,
          visible: true
        },
        mapSwitch: { visible: true },
        maxWidth: 70,
        submitButton: { colorScheme: 'primary', visible: true },
        summary: { visible: true },
        visible: true
      },
      searchOptions: {
        amenityCategory: null,
        careType: 'assisted-living',
        city: 'San Francisco',
        county: 'San Francisco',
        displayMode: 'list',
        itemsPerPage: 30,
        latitude: 37.7749,
        longitude: -122.4194,
        radiusForSearch: 25,
        state: 'CA'
      },
      searchSidebar: { filters: [], visible: true }
    });
  });

  it('should return the default ComponentConfig object if the input is invalid', () => {
    const invalidComponentConfig = {};

    const result = parseComponentConfig(invalidComponentConfig);

    expect(result).toEqual(getDefaultComponentConfig());
  });
});
