import { SearchStates } from '@components/FacetedSearch/constants';
import { SearchComponentDialogProps } from '@components/FacetedSearch/types';
import { SEARCH_PARAM } from '@constants/search-params';
import { getProviders } from '@services/enhanced-search/api';
import {
  AlgoliaProvider,
  GetProvidersResponse
} from '@services/modular-monolith/types/search.type';
import { mockProvider } from '@utils/test-utils/mocks/provider';
import { GetServerSidePropsContext } from 'next';

import { AlgoliaAmenities, Amenities } from '~/contexts/Provider';
import { Source } from '~/types/json-api-response';
import { parseResultAmenities, parseResultContracts } from '~/utils/search';

import { getServerSideComponentProps } from './ServerComponent';

const mockedComponentConfig: SearchComponentDialogProps = {
  bgBorderRadius: 0,
  bgColor: 'green',
  bgColorRange: '500',
  boxShadow: 'none',
  desktopTitleSize: 'md',
  displayCareTypeFilter: true,
  displayLocationFilter: true,
  displayProviderPhoneNumber: true,
  genericBlock1: {
    '@id': '',
    '@name': '',
    '@nodeType': '',
    '@nodes': [],
    '@path': '',
    'mgnl:created': '',
    'mgnl:lastModified': '',
    'mgnl:template': ''
  },
  genericBlock2: {
    '@id': '',
    '@name': '',
    '@nodeType': '',
    '@nodes': [],
    '@path': '',
    'mgnl:created': '',
    'mgnl:lastModified': '',
    'mgnl:template': ''
  },
  headingElement: 'h2',
  learnMoreButtonColorScheme: 'primary',
  metadata: {
    '@id': '',
    '@name': '',
    '@nodeType': '',
    '@nodes': [],
    '@path': '',
    'mgnl:created': '',
    'mgnl:lastModified': '',
    'mgnl:template': ''
  },
  providerTitleColor: 'green',
  providerTitleColorRange: '500',
  ratingStarsColor: 'blue',
  ratingStarsColorRange: '500',
  readOnlyLocationInput: false,
  readOnlyLocationInputPlaceholder: 'Enter a location',
  requestInfoButtonColorScheme: 'primary',
  searchBarBgColor: 'green',
  searchBarBgColorRange: '500',
  searchBarButtonColorScheme: 'primary',
  tileBorder: 'none',
  tileBorderColor: 'green',
  tileBorderColorRange: '500',
  titleSize: 'md',
  data: {
    amenityCategories: [],
    careTypes: [],
    currentPage: 1,
    nearbyCount: 0,
    outOfBounds: false,
    regionCount: 0,
    resultCount: 0,
    results: [],
    resultsPerPage: 10,
    searchState: SearchStates.INPUT_WITH_NO_RESULTS
  },
  enablePredictiveSearch: false,
  dontOpenInNewTab: false
};

const mockedContext: GetServerSidePropsContext = {
  req: {
    headers: {
      host: 'caring.com'
    }
  }
} as unknown as GetServerSidePropsContext;

const mockedGetProvidersResponse: GetProvidersResponse = {
  totalRegionItems: 0,
  totalNearbyItems: 0,
  totalItems: 0,
  totalPages: 0,
  results: [],
  queryId: '',
  listId: ''
};

jest.mock('@services/enhanced-search/api', () => ({
  getProviders: jest.fn()
}));

jest.mock('@services/magnolia/getDataByDomain', () => ({
  getDataByDomain: jest.fn().mockReturnValue({ careTypes: [] })
}));

describe('EnhancedSearch ServerComponent', () => {
  beforeEach(() => {
    (getProviders as jest.Mock).mockClear();
  });

  it('should correctly read the current page from the query string', async () => {
    (getProviders as jest.Mock).mockResolvedValueOnce(
      mockedGetProvidersResponse
    );

    const data = await getServerSideComponentProps(mockedComponentConfig, {
      ...mockedContext,
      query: { [SEARCH_PARAM.PAGE]: '2' }
    });

    // Page is zero-indexed
    expect(data).toHaveProperty('currentPage', 1);
  });

  it('should return outOfBounds as true if the current page is out of bounds', async () => {
    (getProviders as jest.Mock).mockResolvedValueOnce({
      ...mockedGetProvidersResponse,
      totalPages: 3
    });

    const data = await getServerSideComponentProps(mockedComponentConfig, {
      ...mockedContext,
      query: { [SEARCH_PARAM.PAGE]: '4' }
    });

    expect(data).toHaveProperty('outOfBounds', true);
  });

  it('should return outOfBounds as false if the current page is within bounds', async () => {
    (getProviders as jest.Mock).mockResolvedValueOnce({
      ...mockedGetProvidersResponse,
      totalPages: 3
    });

    const data = await getServerSideComponentProps(mockedComponentConfig, {
      ...mockedContext,
      query: { [SEARCH_PARAM.PAGE]: '3' }
    });

    expect(data).toHaveProperty('outOfBounds', false);
  });
});

describe('parseResultAmenities', () => {
  it('should parse amenities for Algolia', () => {
    const source = Source.ALGOLIA;
    const results: AlgoliaProvider[] = [
      {
        ...mockProvider,
        amenities: [
          {
            id: '00000000-0000-0000-0000-000000000000',
            amenityType: {
              name: 'Pets',
              id: 98,
              amenityCategory: {
                name: 'Pets',
                id: 22
              }
            }
          },
          {
            id: '00000000-0000-0000-0000-000000000000',
            amenityType: {
              name: 'Wifi/Internet In Unit',
              id: 105,
              amenityCategory: {
                name: 'Room Amenities',
                id: 24
              }
            }
          },
          {
            id: '00000000-0000-0000-0000-000000000000',
            amenityType: {
              name: 'Mobility & Wheelchair Assistance',
              id: 2,
              amenityCategory: {
                name: 'Ambulatory Assistance & Accessibility',
                id: 1
              }
            }
          }
        ] as Amenities & AlgoliaAmenities
      }
    ];

    const parsedResults = parseResultAmenities(source, results);
    expect(parsedResults).toEqual([
      {
        ...mockProvider,
        amenities: [
          {
            amenityCategoryId: 22,
            amenityCategoryName: 'Pets',
            amenityName: 'Pets',
            amenityId: 98,
            id: '00000000-0000-0000-0000-000000000000'
          },
          {
            amenityCategoryId: 24,
            amenityCategoryName: 'Room Amenities',
            amenityName: 'Wifi/Internet In Unit',
            amenityId: 105,
            id: '00000000-0000-0000-0000-000000000000'
          },
          {
            amenityCategoryId: 1,
            amenityCategoryName: 'Ambulatory Assistance & Accessibility',
            amenityName: 'Mobility & Wheelchair Assistance',
            amenityId: 2,
            id: '00000000-0000-0000-0000-000000000000'
          }
        ]
      }
    ]);
  });

  it('should not parse amenities for Api', () => {
    const source = Source.API;
    const results: AlgoliaProvider[] = [
      {
        ...mockProvider,
        amenities: [
          {
            amenityCategoryId: 1,
            amenityCategoryName: 'Ambulatory Assistance & Accessibility',
            amenityName: 'Mobility & Wheelchair Assistance',
            amenityId: 2,
            id: '00000000-0000-0000-0000-000000000000'
          },
          {
            amenityCategoryId: 24,
            amenityCategoryName: 'Room Amenities',
            amenityName: 'Wifi/Internet In Unit',
            amenityId: 105,
            id: '00000000-0000-0000-0000-000000000000'
          }
        ] as Amenities & AlgoliaAmenities
      }
    ];

    const parsedResults = parseResultAmenities(source, results);
    expect(parsedResults).toEqual([
      {
        ...mockProvider,
        amenities: [
          {
            amenityCategoryId: 1,
            amenityCategoryName: 'Ambulatory Assistance & Accessibility',
            amenityName: 'Mobility & Wheelchair Assistance',
            amenityId: 2,
            id: '00000000-0000-0000-0000-000000000000'
          },
          {
            amenityCategoryId: 24,
            amenityCategoryName: 'Room Amenities',
            amenityName: 'Wifi/Internet In Unit',
            amenityId: 105,
            id: '00000000-0000-0000-0000-000000000000'
          }
        ]
      }
    ]);
  });
});

describe('parseResultContracts', () => {
  it('should parse contracts correctly for Algolia source', () => {
    const source = Source.ALGOLIA;
    const results: AlgoliaProvider[] = [
      {
        services: [
          {
            category: { name: 'Service 1' },
            contracts: [{ type: 'Contract 1', isSuppressed: false }]
          },
          {
            category: { name: 'Service 2' },
            contract: { type: 'Contract 2', isSuppressed: true }
          },
          { category: { name: 'Service 3' } }
        ]
      }
    ];

    const parsedResults = parseResultContracts(source, results);

    expect(parsedResults[0].services[0].contract).toEqual({
      type: 'Contract 1',
      isSuppressed: false
    });
    expect(parsedResults[0].services[1].contract).toEqual({
      type: 'Contract 2',
      isSuppressed: true
    });
    expect(parsedResults[0].services[2].contract).toEqual({
      type: '',
      isSuppressed: false
    });
  });
});
