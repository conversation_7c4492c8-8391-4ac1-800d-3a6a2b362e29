import {
  DEFAULT_HITS_PER_PAGE,
  SEARCH_KEYWORD_PARAM
} from '@components/FacetedSearch/constants';
import { PageContext } from '@components/LayoutStructure/Contexts';
import { LISTING_PAGE } from '@constants/search-params';
import { GetDataByDomainResponse } from '@services/magnolia/getDataByDomain';
import { GetProvidersResponse } from '@services/modular-monolith/types/search.type';
import { fireEvent, render, screen, waitFor } from '@utils/test-utils';
import { add, sub } from 'date-fns';
import { useRouter } from 'next/router';
import React from 'react';

import { ModalProvider } from '~/contexts/ModalContext';
import SiteContext, {
  SiteDefinition,
  sitesConfig
} from '~/contexts/SiteContext';

import EnhancedSearch from './';
const defaultSiteProps: { site: SiteDefinition } = {
  site: sitesConfig[0]
};

const defaultPageProps: PageContext = {
  page: {
    title: 'Test',
    description: 'Test page',
    keywords: 'test, page',
    canonical: 'https://test.com',
    main: {
      '@nodes': []
    },
    sidebar: {
      '@nodes': []
    },
    '@name': '',
    '@path': '',
    '@id': '',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:template': '',
    'mgnl:created': '',
    '@nodes': [],
    metaDescription: '',
    metaTitle: '',
    metaKeywords: ''
  },
  context: {
    params: {
      careTypeOrState: 'search'
    }
  },
  templateAnnotations: null
};

// The following is a custom render function that wraps the component with the SiteContext and PageContext providers.
const customRender = (
  component: React.ReactNode,
  { siteProps = defaultSiteProps, pageProps = defaultPageProps } = {}
) => {
  return render(
    <SiteContext.Provider value={siteProps}>
      <ModalProvider>
        <PageContext.Provider value={pageProps}>
          {component}
        </PageContext.Provider>
      </ModalProvider>
    </SiteContext.Provider>
  );
};

const mockedPropsEmpty = {
  data: {
    providerData: {
      hits: [],
      exhaustiveNbHits: false,
      query: '',
      params: '',
      processingTimeMS: 0,
      page: 0,
      hitsPerPage: 10,
      nbHits: 0,
      nbPages: 1
    }
  },
  enable: true
};

const mockedProviderResponse: GetProvidersResponse = {
  totalRegionItems: 0,
  totalNearbyItems: 0,
  totalItems: 0,
  totalPages: 0,
  results: [],
  queryId: '',
  listId: ''
};

const mockedDomainResponse: GetDataByDomainResponse = {
  careTypes: []
};

const today = new Date();

const yesterday = sub(today, {
  days: 1
});

const tomorrow = add(today, {
  days: 1
});

const mockedEnhancedSearchData = [
  {
    address: {
      state: 'FL',
      city: 'miami',
      formattedAddress: '925 N Main Street, 2001'
    },
    reviewCount: '9',
    hidePricing: false,
    name: 'Lorem Ipsum',
    averageRating: 4.63,
    slug: 'lorem-ipsum',
    images: [],
    services: [
      {
        id: '1',
        careLevels: ['test'],
        costs: {
          currency: 'USD',
          id: '2',
          startingPriceCents: 266500
        }
      }
    ],
    promotions: [
      {
        startsAt: yesterday.toISOString(),
        endsAt: tomorrow.toISOString(),
        externalPromotionText: 'External promotion text',
        visibleOnlyToFa: false
      }
    ]
  }
];

jest.mock('@services/enhanced-search/api', () => ({
  getProviders: jest.fn(() => Promise.resolve(mockedProviderResponse))
}));

jest.mock('@services/magnolia/getDataByDomain', () => ({
  getDataByDomain: jest.fn(() => Promise.resolve(mockedDomainResponse))
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    push: jest.fn()
  }))
}));

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams())
}));

jest.mock('~/contexts/TenantFunctionsContext', () => {
  return {
    useTenantFunctions: () => ({
      getProviderDetailsPath: jest.fn(),
      getProviderDescription: jest.fn()
    })
  };
});

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt }) => {
    return <img src={src.src} alt={alt} />; // eslint-disable-line
  }
}));

const mockAnalytics = {
  track: jest.fn()
};
window.tracking = mockAnalytics;

describe('EnhancedSearch', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it('should render component EnhancedSearch with data', async () => {
    customRender(
      <EnhancedSearch
        data={{
          results: mockedEnhancedSearchData,
          searchState: 1,
          resultCount: 24
        }}
        countOfTiles={12}
        displaySearchBar
      />
    );

    const title = screen.getByText('Lorem Ipsum');
    expect(title).toBeVisible();

    const btn = screen.getByText('Search');
    expect(btn).toBeVisible();

    const link = screen.getByRole('link', { name: /Next page/i });
    expect(link).toBeVisible();
  });

  it('should validate default vars', async () => {
    const queryParamPage = LISTING_PAGE;
    const queryKeyword = 'keyword';
    const defaultHitsPerPage = 20;

    expect(LISTING_PAGE).toBe(queryParamPage);
    expect(SEARCH_KEYWORD_PARAM).toBe(queryKeyword);
    expect(DEFAULT_HITS_PER_PAGE).toBe(defaultHitsPerPage);
  });

  it('should render component EnhancedSearch with blurCosts param', async () => {
    jest.mock('../EnhancedSearch.utils', () => ({
      getPages: jest.fn(() =>
        Promise.resolve([mockedPropsEmpty.data.providerData])
      )
    }));
    const mockRouter = { query: { [LISTING_PAGE]: 'test' } };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    customRender(
      <EnhancedSearch
        data={{ results: mockedEnhancedSearchData, searchState: 1 }}
        displaySearchBar
        blurCosts={true}
      />
    );

    const title = screen.getByText('Lorem Ipsum');
    expect(title).toBeVisible();
    const btn = screen.getByText('Search');
    expect(btn).toBeVisible();
  });

  it('should render component EnhancedSearch with show more link', async () => {
    const mockRouter = { query: { [LISTING_PAGE]: 4 }, push: jest.fn() };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    customRender(
      <EnhancedSearch
        data={{
          results: mockedEnhancedSearchData,
          searchState: 1,
          resultCount: 24
        }}
        displaySearchBar
        countOfTiles={24}
        visibleLimit={1}
      />
    );

    const title = screen.getByText('Lorem Ipsum');
    expect(title).toBeVisible();

    const btnMore = screen.getByText('Show more');
    expect(btnMore).toBeVisible();

    const btnSearch = screen.getByText('Search');
    expect(btnSearch).toBeVisible();
  });

  it('should render component EnhancedSearch with empty results', async () => {
    jest.mock('../EnhancedSearch.utils', () => ({
      getPages: jest.fn(() =>
        Promise.resolve([mockedPropsEmpty.data.providerData])
      )
    }));
    customRender(
      <EnhancedSearch
        data={{ results: null, searchState: 0, resultCount: 0 }}
        displaySearchBar
      />
    );

    const title = screen.getByText(
      'Please change your filters to produce results'
    );
    expect(title).toBeVisible();
  });

  it('should render the component with promotion banners', async () => {
    customRender(
      <EnhancedSearch
        data={{
          results: mockedEnhancedSearchData,
          searchState: 1,
          resultCount: 24
        }}
        countOfTiles={24}
        visibleLimit={1}
        fullWidthTile
      />
    );

    const promotionButton = screen.getByRole('button', {
      name: /PROMOTION!/i
    });
    fireEvent.click(promotionButton);
    await waitFor(() => {
      expect(screen.getByText('External promotion text')).toBeInTheDocument();
    });
  });
});
