import {
  DISPLAY_MODE,
  SearchStates
} from '@components/FacetedSearch/constants';
import {
  FacetedSearchParsedParams,
  getFacetedSearchParams
} from '@components/FacetedSearch/search-params';
import {
  CareTypeObject,
  FacetedSearchParsedProps,
  SearchComponentDialogProps,
  SearchData
} from '@components/FacetedSearch/types';
import {
  AmenityCategoryNodeWithCount,
  createEnhancedSearchQuery,
  createFacetedSearchQuery,
  fetchAmenityCategoryCounts,
  getAmenityCategoriesFromResponse,
  getCareTypeId,
  getSourceForFacetedSearch,
  hasDistanceFilter
} from '@components/FacetedSearch/utils';
import {
  EnhancedSearchParsedConfig,
  parseComponentConfig
} from '@components/Search/EnhancedSearch/parser';
import { getFeatureOverrideFromContextServerSide } from '@lib/featureOverride';
import { getProviders } from '@services/enhanced-search/api';
import { getDataByDomain } from '@services/magnolia/getDataByDomain';
import { ModularMonolithClient } from '@services/modular-monolith/client';
import {
  AlgoliaProvider,
  FacetedSearchResponse
} from '@services/modular-monolith/types/search.type';
import { getLatLongMapBoxAPI } from '@utils/search';
import cloneDeep from 'lodash/cloneDeep';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';
import { tenantSwitcher } from '~/contexts/TenantFunctionsContext';
import { Domain, Domains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';
import { parseResultAmenities, parseResultContracts } from '~/utils/search';

const fetchProvidersUsingEnhancedSearch = async ({
  amenityCategoryChips,
  searchParams,
  componentProps,
  apiVersion,
  featureOverride,
  geoUrl
}: {
  amenityCategoryChips: EnhancedSearchParsedConfig['amenityCategoryChips'];
  searchParams: FacetedSearchParsedParams;
  componentProps: FacetedSearchParsedProps;
  apiVersion: string;
  featureOverride: string;
  geoUrl: string | undefined;
}): Promise<[FacetedSearchResponse, AmenityCategoryNodeWithCount[]]> => {
  const enhancedSearchQuery = createEnhancedSearchQuery({
    searchParams,
    componentProps
  });

  const facetedSearchQuery = createFacetedSearchQuery({
    searchParams,
    componentProps
  });

  const [response, amenityCategories] = await Promise.all([
    getProviders({
      domain: componentProps.domain,
      filters: {
        ...enhancedSearchQuery,
        geoUrl
      },
      apiVersion,
      featureOverride
    }),
    amenityCategoryChips.visible
      ? fetchAmenityCategoryCounts(facetedSearchQuery)
      : []
  ]);

  return [response, amenityCategories];
};

const fetchProvidersUsingFacetedSearch = async ({
  searchParams,
  componentProps
}: {
  searchParams: FacetedSearchParsedParams;
  componentProps: FacetedSearchParsedProps;
}): Promise<[FacetedSearchResponse, AmenityCategoryNodeWithCount[]]> => {
  const facetedSearchQuery = createFacetedSearchQuery({
    searchParams,
    componentProps
  });

  const modularMonolithClient = new ModularMonolithClient(
    componentProps.domain
  );
  const response = await modularMonolithClient.searchProvidersByFacets(
    facetedSearchQuery
  );

  const amenityCategories = getAmenityCategoriesFromResponse(
    response,
    componentProps.careType
  );

  return [response, amenityCategories];
};

const fetchProvidersUsingFacetedSearchV2 = async ({
  searchParams,
  componentProps
}: {
  searchParams: FacetedSearchParsedParams;
  componentProps: FacetedSearchParsedProps;
}): Promise<[FacetedSearchResponse, AmenityCategoryNodeWithCount[]]> => {
  const facetedSearchQuery = createFacetedSearchQuery({
    searchParams,
    componentProps
  });

  if (searchParams.latLng === '' && searchParams.keyword !== '') {
    const results = await getLatLongMapBoxAPI(searchParams.keyword);
    if (results) {
      facetedSearchQuery.criteria.latitude = String(results[1]);
      facetedSearchQuery.criteria.longitude = String(results[0]);
      searchParams.latLng = results.reverse().join(',');
    }
  }

  const modularMonolithClient = new ModularMonolithClient(
    componentProps.domain
  );
  const response = await modularMonolithClient.searchProvidersByFacetsV2(
    facetedSearchQuery
  );

  const amenityCategories = getAmenityCategoriesFromResponse(
    response,
    componentProps.careType
  );

  return [response, amenityCategories];
};

const fetchCareTypes = async (domain: Domain): Promise<CareTypeObject[]> => {
  const domainData = await getDataByDomain({ domain });
  if (domainData.careTypes && domainData.careTypes.length > 0) {
    const filteredCareTypes = domainData.careTypes.filter(
      (careType) => careType.name
    );
    return filteredCareTypes;
  }
  return [];
};

export const getServerSideComponentProps = async (
  componentConfig: SearchComponentDialogProps,
  context: GetServerSidePropsContext
): Promise<SearchData> => {
  const parsedConfig = parseComponentConfig(componentConfig);
  const query = cloneDeep(context.query);
  const site = findSiteForContext(context);
  const domain = site.path;
  const { getCountyForSearch } = tenantSwitcher(domain);
  const distanceFilterEnabled = hasDistanceFilter(parsedConfig.searchSidebar);
  const careTypeResult = parsedConfig.searchBar.careTypeSelect.visible
    ? await fetchCareTypes(domain)
    : [];

  if (query['care-type']) {
    query['care-type'] = getCareTypeId(
      query['care-type'] as string,
      careTypeResult
    );
  }

  const searchParams = getFacetedSearchParams(query);

  const source = getSourceForFacetedSearch({
    displayMode: parsedConfig.searchOptions.displayMode,
    domain,
    amenityCategoryChips: parsedConfig.amenityCategoryChips,
    searchParams
  });

  const componentProps = {
    amenityCategory: parsedConfig.searchOptions.amenityCategory ?? '',
    careType:
      parsedConfig.searchOptions.displayMode === DISPLAY_MODE.LIST
        ? parsedConfig.searchOptions.careType ?? ''
        : searchParams.careType,
    city: parsedConfig.searchOptions.city ?? '',
    county: getCountyForSearch(parsedConfig.searchOptions.county ?? ''),
    displayMode: parsedConfig.searchOptions.displayMode,
    distanceFilterEnabled,
    domain,
    latitude: String(parsedConfig.searchOptions.latitude),
    longitude: String(parsedConfig.searchOptions.longitude),
    resultsPerPage: parsedConfig.searchOptions.itemsPerPage,
    source,
    state: parsedConfig.searchOptions.state ?? '',
    shouldShowOnlyIndexedProviders:
      componentConfig?.filterAndSort?.defaultValues
        ?.shouldShowOnlyIndexedProviders ?? false
  };

  const [providersResult] = await Promise.all([
    // This ensures we only use faceted search for Caring.com and only if the search sidebar is visible
    domain === Domains.CaringDomains.LIVE && parsedConfig.searchSidebar.visible
      ? source === Source.LEGACY
        ? await fetchProvidersUsingEnhancedSearch({
            searchParams,
            componentProps,
            amenityCategoryChips: parsedConfig.amenityCategoryChips,
            apiVersion: String(context.query?.geo),
            featureOverride: getFeatureOverrideFromContextServerSide(context),
            geoUrl: context.resolvedUrl
          })
        : componentProps.displayMode === 'search'
        ? await fetchProvidersUsingFacetedSearchV2({
            searchParams,
            componentProps
          })
        : await fetchProvidersUsingFacetedSearch({
            searchParams,
            componentProps
          })
      : await fetchProvidersUsingEnhancedSearch({
          searchParams,
          componentProps,
          amenityCategoryChips: parsedConfig.amenityCategoryChips,
          apiVersion: String(context.query?.geo),
          featureOverride: getFeatureOverrideFromContextServerSide(context),
          geoUrl: context.resolvedUrl
        })
  ]);

  const [data, amenityCategories] = providersResult;

  const careTypes = careTypeResult;

  const parsedResultsAmenities = parseResultAmenities(
    source,
    data.results as AlgoliaProvider[]
  );

  const parsedResults = parseResultContracts(
    source,
    parsedResultsAmenities as AlgoliaProvider[]
  );

  return {
    amenityCategories,
    careTypes,
    currentPage: searchParams.page,
    listId: data.listId,
    nearbyCount: data.totalNearbyItems,
    // Page is zero-indexed
    outOfBounds: searchParams.page + 1 > data.totalPages,
    queryId: data.queryId,
    regionCount: data.totalRegionItems,
    resultCount: data.totalItems,
    results: parsedResults,
    resultsPerPage: parsedConfig.searchOptions.itemsPerPage,
    searchState:
      data.totalItems === 0
        ? SearchStates.INPUT_WITH_NO_RESULTS
        : SearchStates.INPUT_WITH_RESULTS
  };
};

export const dataName = 'enhancedSearch';
