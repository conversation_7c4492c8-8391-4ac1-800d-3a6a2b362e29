import SearchSeoCarousel from '@components/Search/SearchSeoCarousel';
import { render } from '@utils/test-utils';
import React from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';

jest.mock('~/contexts/TenantFunctionsContext');
jest.mock('~/contexts/SiteContext', () => ({
  __esModule: true,
  ...jest.requireActual('~/contexts/SiteContext'),
  useSiteContext: jest.fn()
}));

const mockGetProviderDetailsPath = jest.fn((provider) => `/${provider.name}`);
const mockUseTenantFunctions = {
  getProviderDetailsPath: mockGetProviderDetailsPath
};

jest.mock('next/head', () => {
  return {
    __esModule: true,
    default: ({ children }: { children: Array<React.ReactElement> }) => {
      return children;
    }
  };
});

const mockResults = [
  {
    name: 'Provider 1',
    images: ['https://example.com/image1.jpg']
  },
  {
    name: 'Provider 2',
    images: ['https://example.com/image2.jpg']
  }
] as Provider[];

describe('SearchSeoCarousel', () => {
  it('renders correctly for caring.com', () => {
    (useTenantFunctions as jest.Mock).mockReturnValue(mockUseTenantFunctions);
    const fakeCaringSite = {
      site: {
        path: 'caring.com'
      }
    } as SiteContextType;

    render(
      <SiteContext.Provider value={fakeCaringSite}>
        <SearchSeoCarousel
          careType="adult-day-care"
          results={mockResults}
          city="Wellington"
          state="Florida"
          totalCount={50}
        />
      </SiteContext.Provider>
    );

    const script = document.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    const jsonContent = JSON.parse(script?.innerHTML || '');

    expect(jsonContent).toEqual({
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      itemListElement: [
        {
          '@type': 'ListItem',
          image: {
            '@type': 'ImageObject',
            name: 'Provider 1',
            url: 'https://example.com/image1.jpg'
          },
          position: 1,
          url: 'https://www.caring.com/Provider 1'
        },
        {
          '@type': 'ListItem',
          image: {
            '@type': 'ImageObject',
            name: 'Provider 2',
            url: 'https://example.com/image2.jpg'
          },
          position: 2,
          url: 'https://www.caring.com/Provider 2'
        }
      ],
      name: 'List of 50 Adult Day Care Services near Wellington, FL',
      numberOfItems: 50
    });
  });

  it("skips image attribute if it doesn't exist", () => {
    (useTenantFunctions as jest.Mock).mockReturnValue(mockUseTenantFunctions);
    const fakeCaringSite = {
      site: {
        path: 'caring.com'
      }
    } as SiteContextType;

    render(
      <SiteContext.Provider value={fakeCaringSite}>
        <SearchSeoCarousel
          careType="adult-day-care"
          results={[
            ...mockResults,
            {
              name: 'Provider 3',
              images: [] as string[]
            } as Provider
          ]}
          city="Wellington"
          state="Florida"
          totalCount={50}
        />
      </SiteContext.Provider>
    );

    const script = document.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();
    const jsonContent = JSON.parse(script?.innerHTML || '');

    expect(jsonContent).toEqual({
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      itemListElement: [
        {
          '@type': 'ListItem',
          image: {
            '@type': 'ImageObject',
            name: 'Provider 1',
            url: 'https://example.com/image1.jpg'
          },
          position: 1,
          url: 'https://www.caring.com/Provider 1'
        },
        {
          '@type': 'ListItem',
          image: {
            '@type': 'ImageObject',
            name: 'Provider 2',
            url: 'https://example.com/image2.jpg'
          },
          position: 2,
          url: 'https://www.caring.com/Provider 2'
        },
        {
          '@type': 'ListItem',
          position: 3,
          url: 'https://www.caring.com/Provider 3'
        }
      ],
      name: 'List of 50 Adult Day Care Services near Wellington, FL',
      numberOfItems: 50
    });
  });

  it('render correctly for seniorhomes.com', () => {
    (useTenantFunctions as jest.Mock).mockReturnValue(mockUseTenantFunctions);
    const fakeSeniorHomesSite = {
      site: {
        path: 'seniorhomes.com'
      }
    } as SiteContextType;

    render(
      <SiteContext.Provider value={fakeSeniorHomesSite}>
        <SearchSeoCarousel
          careType="assisted-living"
          results={mockResults}
          city="Wellington"
          state="Florida"
          totalCount={50}
        />
      </SiteContext.Provider>
    );

    const script = document.querySelector('script[type="application/ld+json"]');

    expect(script).toBeInTheDocument();

    const jsonContent = JSON.parse(script?.innerHTML || '');

    expect(jsonContent).toEqual({
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          url: 'https://www.seniorhomes.com/Provider 1'
        },
        {
          '@type': 'ListItem',
          position: 2,
          url: 'https://www.seniorhomes.com/Provider 2'
        }
      ],
      numberOfItems: 50
    });
  });
});
