import { Box, useMediaQuery } from '@chakra-ui/react';
import AccessibilityFilter from '@components/Accessibility';
import Accordion from '@components/Accordion';
import { AccordionItemProps } from '@components/Accordion/Accordion';
import AwardsFilter from '@components/AwardsFilter';
import DiningFilter from '@components/DiningFilter';
import DistanceSlider from '@components/DistanceSlider';
import {
  AWARDS_TOOLTIP,
  SearchOption,
  VERIFIED_TOOLTIP
} from '@components/FacetedSearch/constants';
import {
  useFacetedSearchActions,
  useFacetedSearchQuery
} from '@components/FacetedSearch/FacetedSearchContext';
import { QueryKeyPath } from '@components/FacetedSearch/types';
import HealthServiceFilter from '@components/HealthServicesFilter';
import LanguagesFilter from '@components/LanguagesFilter';
import LifestyleFilter from '@components/LifestyleFilter';
import OtherAmenitiesFilter from '@components/OtherAmenitiesFilter';
import PersonalCareFilter from '@components/PersonalCareFilter';
import PriceSlider, { valueToSymbol } from '@components/PriceSlider';
import ProviderMatchAllFilters from '@components/ProviderMatchAllFilters';
import ProvidersWithFilter from '@components/ProvidersWithFilter';
import ReviewsFilter from '@components/ReviewsFilter';
import RoomAmenitiesFilter from '@components/RoomAmenitiesFilter';
import RoomsFilter from '@components/RoomsFilter';
import SeeMoreButton from '@components/SeeMoreButton/SeeMoreButton';
import SortBy from '@components/SortBy';
import StaffQualificationFilter from '@components/StaffQualificationFilter';
import VerifiedPartnersFilter from '@components/VerifiedPartnersFilter';
import { SortingBy } from '@services/modular-monolith/types/search.type';
import { useEffect, useRef, useState } from 'react';

import { DefaultFilterValues, FilterTooltips } from '~/types/componentsConfig';

interface AccordionItem extends AccordionItemProps {
  id: SearchOption;
}

interface SidebarProps {
  searchOptions: Array<SearchOption>;
  searchDefaultValues: DefaultFilterValues;
  filterTooltips?: FilterTooltips;
}

const Sidebar: React.FC<SidebarProps> = ({
  searchOptions,
  searchDefaultValues,
  filterTooltips
}) => {
  const [isDesktopView] = useMediaQuery('(min-width: 992px)');
  const previousView = useRef(isDesktopView);
  const [open, setOpen] = useState(isDesktopView);
  const { updateQuery } = useFacetedSearchActions();
  const query = useFacetedSearchQuery();

  useEffect(() => {
    if (previousView.current !== isDesktopView) {
      previousView.current = isDesktopView;
      setOpen(isDesktopView);
    }
  }, [isDesktopView]);

  const handleInputChange = (name: string, value: any) => {
    updateQuery({ key: name as QueryKeyPath, value });
  };

  const handleSortByChange = (value: SortingBy) => {
    updateQuery({ key: 'sortBy', value });
  };

  const handleMatchAllFiltersChange = (value: boolean) => {
    updateQuery({ key: 'matchAllFilters', value });
  };

  const handleSeeMoreFiltersClick = () => {
    setOpen((prev) => !prev);
  };

  const accordionItems: AccordionItem[] = [
    {
      id: 'verified',
      label: 'Verified Partners',
      children: (
        <VerifiedPartnersFilter
          onChange={handleInputChange}
          value={query.verified}
        />
      ),
      infoText: filterTooltips?.verifiedInfo || VERIFIED_TOOLTIP
    },
    {
      id: 'awards',
      label: 'Caring Awards',
      children: (
        <AwardsFilter onChange={handleInputChange} value={query.awards} />
      ),
      infoText: filterTooltips?.awardsInfo || AWARDS_TOOLTIP
    },
    {
      id: 'distance',
      label: 'Distance',
      children: (
        <DistanceSlider
          onChange={handleInputChange}
          value={query.distanceInMiles}
        />
      )
    },
    {
      id: 'reviews',
      label: 'Rating',
      children: (
        <ReviewsFilter onChange={handleInputChange} value={query.reviews} />
      )
    },
    {
      id: 'fivePlusPhotos',
      label: 'Providers with',
      children: (
        <ProvidersWithFilter
          onChange={handleInputChange}
          value={query.providersWith}
        />
      )
    },
    {
      id: 'roomType',
      label: 'Room Type',
      children: (
        <RoomsFilter onChange={handleInputChange} value={query.roomType} />
      )
    },
    {
      id: 'languages',
      label: 'Languages',
      children: (
        <LanguagesFilter onChange={handleInputChange} value={query.languages} />
      )
    },
    {
      id: 'promotions',
      label: 'Price',
      children: (
        <PriceSlider
          onChange={handleInputChange}
          priceRange={query.priceRange}
          ongoingPromotion={query.ongoingPromotion}
        />
      ),
      infoText:
        filterTooltips?.priceInfo ||
        `Providers in this area range from ${valueToSymbol(
          query.priceRange[0]
        )} to ${valueToSymbol(query.priceRange[1])}`
    },
    {
      id: 'lifestyle',
      label: 'Lifestyle',
      children: (
        <LifestyleFilter onChange={handleInputChange} value={query.lifestyle} />
      )
    },
    {
      id: 'staffQualifications',
      label: 'Staff Qualifications',
      children: (
        <StaffQualificationFilter
          onChange={handleInputChange}
          value={query.staffQualifications}
        />
      )
    },
    {
      id: 'roomAmenities',
      label: 'Room Amenities',
      children: (
        <RoomAmenitiesFilter
          onChange={handleInputChange}
          value={query.roomAmenities}
        />
      )
    },
    {
      id: 'accessibility',
      label: 'Accessibility',
      children: (
        <AccessibilityFilter
          onChange={handleInputChange}
          value={query.accessibility}
        />
      )
    },
    {
      id: 'personalCare',
      label: 'Personal Care',
      children: (
        <PersonalCareFilter
          onChange={handleInputChange}
          value={query.personalCare}
        />
      )
    },
    {
      id: 'dining',
      label: 'Dining',
      children: (
        <DiningFilter onChange={handleInputChange} value={query.dining} />
      )
    },
    {
      id: 'healthServices',
      label: 'Health Services',
      children: (
        <HealthServiceFilter
          onChange={handleInputChange}
          value={query.healthServices}
        />
      )
    },
    {
      id: 'otherAmenities',
      label: 'Other Amenities',
      children: (
        <OtherAmenitiesFilter
          onChange={handleInputChange}
          value={query.otherAmenities}
        />
      )
    }
  ];

  const showSortBy = searchOptions.includes('sortBy');
  const showMatchAllFilters = searchOptions.includes('matchAllFilters');

  return (
    <>
      {isDesktopView && showSortBy && (
        <SortBy onChange={handleSortByChange} value={query.sortBy} />
      )}
      {!isDesktopView && (
        <SeeMoreButton open={open} onClick={handleSeeMoreFiltersClick}>
          More Filters
        </SeeMoreButton>
      )}
      <Box
        height={!open ? 0 : undefined}
        overflow={!open ? 'hidden' : undefined}
      >
        {!isDesktopView && showSortBy && (
          <SortBy onChange={handleSortByChange} value={query.sortBy} />
        )}
        {showMatchAllFilters && (
          <ProviderMatchAllFilters
            value={
              query.matchAllFilters === null
                ? searchDefaultValues?.matchAllFilters
                : query.matchAllFilters
            }
            onChange={handleMatchAllFiltersChange}
          />
        )}
        <Accordion
          openIndexes={[0, 1, 2, 3, 4, 5, 6, 7]}
          items={accordionItems.filter((item) =>
            searchOptions.includes(item.id)
          )}
        />
      </Box>
    </>
  );
};

export default Sidebar;
