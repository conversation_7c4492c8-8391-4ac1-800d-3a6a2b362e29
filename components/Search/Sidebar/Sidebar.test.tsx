import {
  ChakraProvider,
  createStandaloneToast,
  useMediaQuery
} from '@chakra-ui/react';
import { SEARCH_OPTION } from '@components/FacetedSearch/constants';
import { FacetedSearchProvider } from '@components/FacetedSearch/FacetedSearchContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';

import { CaringDomains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import Sidebar from './Sidebar';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

jest.mock('@chakra-ui/react', () => ({
  ...jest.requireActual('@chakra-ui/react'),
  useMediaQuery: jest.fn().mockReturnValue([false])
}));

beforeEach(() => {
  (useRouter as jest.Mock).mockImplementation(() => ({
    query: {},
    pathname: '/',
    push: jest.fn(),
    replace: jest.fn(),
    asPath: '/',
    events: {
      on: jest.fn(),
      off: jest.fn()
    }
  }));

  const portalRoot = document.createElement('div');
  portalRoot.setAttribute('id', 'chakra-toast-portal');
  document.body.appendChild(portalRoot);
});

afterEach(() => {
  jest.clearAllMocks();
  const portalRoot = document.getElementById('chakra-toast-portal');
  if (portalRoot) {
    document.body.removeChild(portalRoot);
  }
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0
    }
  }
});

const defaultProps = {
  searchOptions: [SEARCH_OPTION.VERIFIED, SEARCH_OPTION.AWARDS],
  searchDefaultValues: {}
};

const customTooltips = {
  verifiedInfo: 'Custom verified tooltip',
  awardsInfo: 'Custom awards tooltip',
  priceInfo: 'Custom price tooltip'
};

describe('Sidebar', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useMediaQuery as jest.Mock).mockReturnValue([false]);
  });
  const renderSidebar = (props = {}) => {
    const { ToastContainer } = createStandaloneToast();

    return render(
      <ChakraProvider>
        <QueryClientProvider client={queryClient}>
          <FacetedSearchProvider
            initialData={undefined}
            query={{
              accessibility: [],
              awards: [],
              dining: [],
              distanceInMiles: 0,
              healthServices: [],
              languages: [],
              lifestyle: [],
              matchAllFilters: false,
              ongoingPromotion: [],
              otherAmenities: [],
              personalCare: [],
              priceRange: [0, 0],
              providersWith: [],
              reviews: [],
              roomAmenities: [],
              roomType: [],
              sortBy: 'best-rated',
              staffQualifications: [],
              verified: [],
              keyword: '',
              page: 0,
              careType: '',
              latLng: ''
            }}
            careTypes={[]}
            componentProps={{
              amenityCategory: '',
              careType: '',
              city: '',
              county: '',
              displayMode: 'list',
              distanceFilterEnabled: false,
              domain: CaringDomains.LIVE,
              latitude: '0',
              longitude: '0',
              resultsPerPage: 10,
              source: Source.ALGOLIA,
              state: '',
              shouldShowOnlyIndexedProviders: false
            }}
          >
            <Sidebar {...defaultProps} {...props} />
            <ToastContainer />
          </FacetedSearchProvider>
        </QueryClientProvider>
      </ChakraProvider>
    );
  };

  describe('InfoText tooltips', () => {
    it('renders info icon for verified partners', () => {
      renderSidebar();

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      expect(verifiedButton).toBeInTheDocument();

      const infoIcon = verifiedButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders info icon for awards', () => {
      renderSidebar();

      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      expect(awardsButton).toBeInTheDocument();

      const infoIcon = awardsButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders info icons when custom tooltips are provided', () => {
      renderSidebar({ filterTooltips: customTooltips });

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      expect(verifiedButton).toBeInTheDocument();

      const verifiedInfoIcon = verifiedButton.querySelector(
        '[role="presentation"]'
      );
      expect(verifiedInfoIcon).toBeInTheDocument();

      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      expect(awardsButton).toBeInTheDocument();

      const awardsInfoIcon = awardsButton.querySelector(
        '[role="presentation"]'
      );
      expect(awardsInfoIcon).toBeInTheDocument();
    });

    it('renders price tooltip with dynamic range values', () => {
      renderSidebar({
        searchOptions: [...defaultProps.searchOptions, SEARCH_OPTION.PROMOTIONS]
      });

      const priceButton = screen.getByRole('button', { name: /price/i });
      expect(priceButton).toBeInTheDocument();

      const infoIcon = priceButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders price section with custom tooltip', () => {
      renderSidebar({
        searchOptions: [
          ...defaultProps.searchOptions,
          SEARCH_OPTION.PROMOTIONS
        ],
        filterTooltips: customTooltips
      });

      const priceButton = screen.getByRole('button', { name: /price/i });
      expect(priceButton).toBeInTheDocument();

      const infoIcon = priceButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders correct number of info icons based on searchOptions', () => {
      renderSidebar({
        searchOptions: [SEARCH_OPTION.VERIFIED],
        filterTooltips: customTooltips
      });

      const infoIcons = screen.queryAllByRole('presentation');
      expect(infoIcons).toHaveLength(1);
    });

    it('does not render price section when not included in searchOptions', () => {
      renderSidebar({
        searchOptions: [SEARCH_OPTION.VERIFIED],
        filterTooltips: customTooltips
      });

      const priceButton = screen.queryByRole('button', { name: /price/i });
      expect(priceButton).not.toBeInTheDocument();
    });

    it('renders all sections when all options are provided', () => {
      renderSidebar({
        searchOptions: [
          SEARCH_OPTION.VERIFIED,
          SEARCH_OPTION.AWARDS,
          SEARCH_OPTION.PROMOTIONS
        ],
        filterTooltips: customTooltips
      });

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      const priceButton = screen.getByRole('button', { name: /price/i });

      expect(verifiedButton).toBeInTheDocument();
      expect(awardsButton).toBeInTheDocument();
      expect(priceButton).toBeInTheDocument();

      const infoIcons = screen.getAllByRole('presentation');
      expect(infoIcons).toHaveLength(3);
    });
  });

  describe('Responsive behavior', () => {
    it('updates open state when view changes from mobile to desktop', () => {
      (useMediaQuery as jest.Mock).mockReturnValue([false]);
      const { rerender } = renderSidebar();
      expect(screen.getByText('More Filters')).toBeInTheDocument();

      (useMediaQuery as jest.Mock).mockReturnValue([true]);
      rerender(<>{renderSidebar().container.innerHTML}</>);
      expect(screen.queryByText('More Filters')).not.toBeInTheDocument();
    });

    it('updates open state when view changes from desktop to mobile', () => {
      (useMediaQuery as jest.Mock).mockReturnValue([true]);
      const { rerender } = renderSidebar();
      expect(screen.queryByText('More Filters')).not.toBeInTheDocument();

      (useMediaQuery as jest.Mock).mockReturnValue([false]);
      rerender(<>{renderSidebar().container.innerHTML}</>);
      expect(screen.getByText('More Filters')).toBeInTheDocument();
    });
  });
});
