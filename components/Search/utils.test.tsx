import { CareTypeObject } from '@components/FacetedSearch/types';

import { groupItemsByType, mappedCareTypes } from './utils';

describe('Search Utils', () => {
  const mockCareTypes: CareTypeObject[] = [
    {
      id: 'assited-living',
      slug: 'assisted-living',
      name: 'Assited Living',
      rollUpType: 'senior-living'
    },
    {
      id: 'adult-day-care',
      slug: 'adult-day-care',
      name: 'Adult Day Care',
      rollUpType: 'senior-living'
    },
    {
      id: 'memory-care-facilities',
      slug: 'memory-care-facilities',
      name: 'Memory Care Facilities',
      rollUpType: 'senior-living'
    },
    {
      id: 'hospices',
      slug: 'hospices',
      name: 'Hospices',
      rollUpType: 'senior-care'
    },
    {
      id: 'continuing-care-retirement-communities',
      slug: 'continuing-care-retirement-communities',
      name: 'Continuing Care Retirement Communities',
      rollUpType: 'senior-care'
    }
  ];
  test('groupItemsByType', () => {
    const groupedItems = groupItemsByType(mockCareTypes);

    expect(Object.keys(groupedItems)).toEqual(['senior-living', 'senior-care']);
    expect(groupedItems['senior-living'].map((el) => el.name)).toEqual([
      'Assited Living',
      'Adult Day Care',
      'Memory Care Facilities'
    ]);
  });
  describe('mappedCareTypes', () => {
    test('It should return an include value of careType', () => {
      const preFillCareType = 'memory-care';
      const value = mappedCareTypes(preFillCareType, mockCareTypes);
      expect(value).toBe('memory-care-facilities');
    });
    test('It should return an include value of careType if its from a know issue', () => {
      const preFillCareType = 'continuing-care-communities';
      const value = mappedCareTypes(preFillCareType, mockCareTypes);
      expect(value).toBe('continuing-care-retirement-communities');
    });
  });
});
