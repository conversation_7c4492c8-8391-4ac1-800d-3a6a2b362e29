import {
  SEARCH_KEYWORD_PARAM,
  SearchStates
} from '@components/FacetedSearch/constants';
import { getFeatureFlag } from '@services/magnolia/getFeatureFlag';
import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
import { GetServerSidePropsContext } from 'next';

import { DEFAULT_HITS_PER_PAGE, FEATURE_FLAGS } from '~/constants';
import { findSiteForContext, SiteDefinition } from '~/contexts/SiteContext';

import {
  doAlgoliaSearch,
  getKeywordQueryParams,
  SearchComponentDialogProps,
  SearchData
} from './Search';
import { getDefaultKeywords } from './Search.utils';

export const getServerSideComponentProps = async (
  {
    orderBy,
    countOfTiles,
    state,
    city,
    county,
    latitude,
    longitude
  }: SearchComponentDialogProps,
  context: GetServerSidePropsContext
): Promise<SearchData> => {
  // We should fix the catalog or Algolia
  // https://caring.atlassian.net/browse/CME-893
  const alteredCounty = county?.replace('County', '').trim();
  const site: SiteDefinition = findSiteForContext(context);
  const params = context.params || {};
  const domain = site.path;
  const queryKeyword =
    getKeywordQueryParams(context.query?.[SEARCH_KEYWORD_PARAM]) ??
    ('' as string);

  const displayGeoComparison = await getFeatureFlag({
    name: FEATURE_FLAGS.display_geo_comparison,
    domain
  });

  const careTypeFromURL = getCareTypeFromURL(params);

  const keyword = getDefaultKeywords({
    state,
    city,
    county: alteredCounty,
    queryKeyword
  });

  if (!careTypeFromURL && !keyword) {
    return {
      results: [],
      resultCount: 0,
      displayGeoComparison,
      searchState: SearchStates.NO_SEARCH_INPUT
    };
  }

  const hitsPerPage = Number(countOfTiles) || DEFAULT_HITS_PER_PAGE;
  const lat = parseFloat(latitude ?? '') || 0;
  const lng = parseFloat(longitude ?? '') || 0;
  const data = await doAlgoliaSearch({
    keyword,
    careType: careTypeFromURL,
    hitsPerPage,
    page: 0,
    orderBy,
    latitude: lat,
    longitude: lng,
    domain
  });

  return {
    displayGeoComparison: displayGeoComparison,
    results: data?.hits,
    resultCount: data?.nbHits,
    searchState:
      data?.hits?.length === 0
        ? SearchStates.INPUT_WITH_NO_RESULTS
        : SearchStates.INPUT_WITH_RESULTS
  };
};

export const dataName = 'search';
