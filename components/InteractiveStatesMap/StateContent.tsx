import { Box } from '@chakra-ui/layout';
import { Section } from '@components/Sections';
import { capitalizeFullSentence } from '@utils/strings';
import { getStateAbbreviation } from '@utils/UsStates';
import React from 'react';

import { StateData } from './InteractiveStatesMap';

interface StateContentProps {
  state: StateData;
  showAsList: boolean;
  activeState: string;
}

const StateContent: React.FC<StateContentProps> = ({
  state,
  showAsList,
  activeState
}) => {
  const stateName = Object.keys(state)[0];
  const stateAbbreviation = getStateAbbreviation(
    capitalizeFullSentence(stateName.replace(/-/g, ' '))
  );

  const stateTitle = state[stateName].title;
  const stateBody = state[stateName].body;
  const headingElement = state[stateName].headingElement;
  if (!showAsList && activeState !== stateAbbreviation) return null;
  return (
    <Box
      id={stateAbbreviation ?? ''}
      key={stateAbbreviation}
      px={{ base: 4, sm: 8 }}
      py={showAsList ? 4 : 8}
      mb={showAsList ? 0 : 8}
      border={showAsList ? '' : '1px solid'}
      borderColor="gray.400"
      borderRadius={3}
    >
      <Section
        displayInlineBlock
        headingElement={headingElement ?? 'h3'}
        title={stateTitle}
        richText={stateBody}
      />
    </Box>
  );
};

export default StateContent;
