'use client';

import { Box } from '@chakra-ui/react';
import { getColor } from '@utils/getColor';
import { states } from '@utils/isAState';
import { capitalizeFullSentence } from '@utils/strings';
import { getStateNameFromAbbreviation } from '@utils/UsStates';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';

import { HeadingElements } from '~/@types/heading';
import { TitleConfig } from '~/types/componentsConfig';

import StateContent from './StateContent';
import { stateSVGData } from './StateSVGData';
import StateSVGPath from './StateSVGPath';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
const StateDropdown = dynamic(() => import('./StateDropdown'));
import { Section } from '@components/Sections';

export interface StateData {
  [key: string]: {
    title: string;
    body: string;
    headingElement: HeadingElements;
  };
}

export const extractStateData = (props: {
  [key: string]: any;
}): StateData[] => {
  const stateData: StateData[] = [];
  states.map((state) => {
    const stateFromProps = state.replace(/-/g, '_');
    const title = props?.[`${stateFromProps}.title`];
    const body = props?.[`${stateFromProps}.body`];
    const headingElement = props?.[`${stateFromProps}.headingElement`];
    if (title !== undefined || body !== undefined)
      stateData.push({
        [state]: { title: title, body: body, headingElement: headingElement }
      });
  });
  return stateData;
};

interface InteractiveStatesMapProps {
  title?: TitleConfig;
  body?: string;
  mapColor?: string;
  mapColorRange?: string;
  showStateDropdown: boolean;
  showAsList: boolean;
  [key: string]: any;
}

const InteractiveStatesMap: React.FC<InteractiveStatesMapProps> = ({
  title,
  body,
  mapColor,
  mapColorRange,
  showStateDropdown,
  showAsList,
  ...rest
}) => {
  const [activeState, setActiveState] = useState('');
  const headingElement = title?.element || 'h2';
  const sectionHeading = title?.content;
  const showHeading = Boolean(sectionHeading) || Boolean(body);
  const stateContent = extractStateData(rest);
  const fillColor = getColor(mapColor, mapColorRange);
  const availableStates = stateContent.map((state) => {
    const parsedState = capitalizeFullSentence(
      Object.keys(state)[0].replace(/-/g, ' ')
    );
    return parsedState === 'District Of Columbia'
      ? 'District of Columbia'
      : parsedState;
  });
  useEffect(() => {
    showAsList &&
      document?.getElementById(activeState)?.scrollIntoView({
        behavior: 'smooth'
      });
  }, [activeState, showAsList]);

  return (
    <Container>
      {showHeading && (
        <Section
          headingElement={headingElement}
          title={sectionHeading}
          richText={body}
        />
      )}
      {showStateDropdown && (
        <StateDropdown {...{ activeState, availableStates, setActiveState }} />
      )}
      <Box pt={4} pb={8} px={8}>
        <svg
          data-testid="interactive-states-map"
          xmlns="http://www.w3.org/2000/svg"
          width="100%"
          viewBox="0 0 930 590"
        >
          {stateSVGData.map((state, index) => {
            const hasContent = availableStates.includes(
              getStateNameFromAbbreviation(state.stateAbbreviation) || ''
            );
            if (hasContent) {
              if (showAsList) {
                return (
                  <a key={index} href={`#${state.stateAbbreviation}`}>
                    <StateSVGPath
                      className={state.stateAbbreviation}
                      d={state.stateSVGPath}
                      fill={fillColor}
                      isActive={activeState === state.stateAbbreviation}
                      hasContent={hasContent}
                      onClick={() => setActiveState(state.stateAbbreviation)}
                    />
                  </a>
                );
              }
              return (
                <StateSVGPath
                  key={index}
                  className={state.stateAbbreviation}
                  d={state.stateSVGPath}
                  fill={fillColor}
                  isActive={activeState === state.stateAbbreviation}
                  hasContent={hasContent}
                  onClick={() => setActiveState(state.stateAbbreviation)}
                />
              );
            }
            return <StateSVGPath key={index} d={state.stateSVGPath} />;
          })}
        </svg>
      </Box>

      {stateContent.map((state, index) => {
        return (
          <StateContent
            key={index}
            state={state}
            showAsList={showAsList}
            activeState={activeState}
          />
        );
      })}
    </Container>
  );
};

export default InteractiveStatesMap;
