import { render } from '@utils/test-utils';

import StateSVGPath from './StateSVGPath';

describe('StateSVGPath', () => {
  it('should render an interactive state, that is selected', () => {
    const { container } = render(<StateSVGPath isActive hasContent />);
    const statePath = container.firstChild;
    expect(statePath).toBeInTheDocument();
    expect(statePath).toHaveAttribute('filter', 'brightness(.70)');
    expect(statePath).toHaveAttribute('fill', 'primary.500');
  });
  it('should render an interactive state, that is not selected', () => {
    const { container } = render(<StateSVGPath isActive={false} hasContent />);
    const statePath = container.firstChild;
    expect(statePath).toBeInTheDocument();
    expect(statePath).not.toHaveAttribute('filter', 'brightness(.70)');
    expect(statePath).toHaveAttribute('fill', 'primary.500');
  });
  it('should render an non-interactive state', () => {
    const { container } = render(
      <StateSVGPath isActive={false} hasContent={false} />
    );
    const statePath = container.firstChild;
    expect(statePath).toBeInTheDocument();
    expect(statePath).not.toHaveAttribute('filter', 'brightness(.70)');
    expect(statePath).not.toHaveAttribute('fill', 'primary.500');
  });
});
