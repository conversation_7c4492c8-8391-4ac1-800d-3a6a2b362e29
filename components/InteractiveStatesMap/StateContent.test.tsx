import { render } from '@utils/test-utils';

import { StateData } from './InteractiveStatesMap';
import StateContent from './StateContent';

describe('StateContent', () => {
  const states = [
    {
      washington: {
        title: 'State Title 1',
        body: 'State Body 1',
        headingElement: 'h3'
      }
    },
    {
      florida: {
        title: 'State Title 2',
        body: 'State Body 2',
        headingElement: 'h3'
      }
    },
    {
      'north-carolina': {
        title: 'State Title 3',
        body: 'State Body 3',
        headingElement: 'h3'
      }
    }
  ] as StateData[];

  it('should render all of the state content when showAsList is true', () => {
    const { container } = render(
      <>
        {states.map((state, index) => {
          return (
            <StateContent
              key={index}
              state={state}
              showAsList={true}
              activeState={'NC'}
            />
          );
        })}
      </>
    );

    expect(container.querySelectorAll('h3').length).toBe(3);
    expect(container.querySelector('#NC')).toBeInTheDocument();
    expect(container.querySelector('#WA')).toBeInTheDocument();
    expect(container.querySelector('#FL')).toBeInTheDocument();
    expect(container).toHaveTextContent('State Title 1');
    expect(container).toHaveTextContent('State Body 1');
    expect(container).toHaveTextContent('State Title 2');
    expect(container).toHaveTextContent('State Body 2');
    expect(container).toHaveTextContent('State Title 3');
    expect(container).toHaveTextContent('State Body 3');
  });

  it('should render a single, active state when showAsList is false', () => {
    const { container } = render(
      <>
        {states.map((state, index) => {
          return (
            <StateContent
              key={index}
              state={state}
              showAsList={false}
              activeState={'NC'}
            />
          );
        })}
      </>
    );
    expect(container.querySelectorAll('h3').length).toBe(1);
    expect(container.querySelector('#NC')).toBeInTheDocument();
    expect(container.querySelector('#WA')).not.toBeInTheDocument();
    expect(container.querySelector('#FL')).not.toBeInTheDocument();
    expect(container).not.toHaveTextContent('State Title 1');
    expect(container).not.toHaveTextContent('State Body 1');
    expect(container).not.toHaveTextContent('State Title 2');
    expect(container).not.toHaveTextContent('State Body 2');
    expect(container).toHaveTextContent('State Title 3');
    expect(container).toHaveTextContent('State Body 3');
  });
});
