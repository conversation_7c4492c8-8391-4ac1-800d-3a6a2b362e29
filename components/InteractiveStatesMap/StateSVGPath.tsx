import { useToken } from '@chakra-ui/react';
import { SVGAttributes, useState } from 'react';
interface StateSVGPathProps extends SVGAttributes<SVGPathElement> {
  isActive?: boolean;
  hasContent?: boolean;
}
const StateSVGPath = ({
  fill = 'primary.500',
  className,
  isActive = false,
  hasContent = false,
  ...rest
}: StateSVGPathProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [fillColor] = useToken('colors', [hasContent ? fill : 'gray.400']);
  const handleMouseEnter = () => {
    setIsHovered(true);
  };
  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const pathFillColor =
    (isHovered || isActive) && hasContent ? { filter: 'brightness(.70)' } : '';

  return (
    <>
      <path
        {...rest}
        className={className}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        fill={fillColor}
        stroke="white"
        {...pathFillColor}
        cursor={hasContent ? 'pointer' : 'default'}
      ></path>
      {className === 'DC' && (
        <circle
          {...rest}
          className={className}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          fill={fillColor}
          stroke="white"
          strokeWidth="1.5"
          cx="801.3"
          cy="251.8"
          r="5"
          cursor={hasContent ? 'pointer' : 'default'}
          {...pathFillColor}
        ></circle>
      )}
    </>
  );
};

export default StateSVGPath;
