import { render } from '@utils/test-utils';

import InteractiveStatesMap, { extractStateData } from './InteractiveStatesMap';

const interactiveStatesMapProps = {
  'idaho.title': 'State Title 1',
  'idaho.headingElement': 'h3',
  'idaho.body': 'State Body 1',
  'north_carolina.title': 'State Title 2',
  'north_carolina.headingElement': 'h3',
  'north_carolina.body': 'State Body 2',
  'oregon.title': 'State Title 3',
  'oregon.headingElement': 'h2',
  'oregon.body': 'State Body 3',
  data: {},
  metadata: {
    '@index': 0,
    '@name': '0',
    '@path': '/test-page',
    '@id': '00000000-0000-0000-0000-000000000000',
    '@nodeType': 'mgnl:component',
    'mgnl:created': '',
    'mgnl:template': 'spa-lm:components/interactiveStatesMap',
    'mgnl:lastModified': '',
    '@nodes': []
  },
  showAsList: true,
  showStateDropdown: false
};

describe('InteractiveStatesMap', () => {
  it('should parse the state data from the magnolia props', () => {
    const parsedStateData = extractStateData(interactiveStatesMapProps);
    expect(parsedStateData).toEqual([
      {
        idaho: {
          title: 'State Title 1',
          body: 'State Body 1',
          headingElement: 'h3'
        }
      },
      {
        'north-carolina': {
          title: 'State Title 2',
          body: 'State Body 2',
          headingElement: 'h3'
        }
      },
      {
        oregon: {
          title: 'State Title 3',
          body: 'State Body 3',
          headingElement: 'h2'
        }
      }
    ]);
  });

  it('should render available states with links', () => {
    const { getByTestId, getAllByRole } = render(
      <InteractiveStatesMap {...interactiveStatesMapProps} />
    );
    const interactiveMap = getByTestId('interactive-states-map');
    expect(interactiveMap).toBeInTheDocument();
    expect(interactiveMap.childNodes.length).toBe(51);
    expect(getAllByRole('link').length).toBe(3);
  });
});
