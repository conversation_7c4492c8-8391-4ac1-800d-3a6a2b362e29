import { Flex, Select } from '@chakra-ui/react';
import { getStateAbbreviation } from '@utils/UsStates';

interface StateDropdownProps {
  activeState: string;
  availableStates: string[];
  setActiveState: (state: string) => void;
}

const StateDropdown: React.FC<StateDropdownProps> = ({
  activeState,
  availableStates,
  setActiveState
}) => {
  return (
    <Flex alignItems="center" py={4}>
      <b>State</b>
      <Select
        defaultValue=""
        pl={3}
        onChange={(e) => {
          setActiveState(e.target.value);
        }}
      >
        <option disabled value="">
          Select state
        </option>
        {availableStates.map((state) => {
          const stateAbbreviation = getStateAbbreviation(state);
          return (
            <option
              key={state}
              value={stateAbbreviation ?? ''}
              selected={activeState === stateAbbreviation}
            >
              {state}
            </option>
          );
        })}
      </Select>
    </Flex>
  );
};

export default StateDropdown;
