import { fireEvent, render } from '~/utils/test-utils';

import StateDropdown from './StateDropdown';

describe('StateDropdown', () => {
  const mockSetActiveState = jest.fn();

  const availableStates = ['Colorado', 'Maine', 'South Dakota'];
  const activeState = 'Colorado';

  it('should render the StateDropdown component', () => {
    const { getByText } = render(
      <StateDropdown
        activeState={activeState}
        availableStates={availableStates}
        setActiveState={mockSetActiveState}
      />
    );
    expect(getByText('Select state')).toBeInTheDocument();
    expect(getByText('Colorado')).toBeInTheDocument();
    expect(getByText('Maine')).toBeInTheDocument();
    expect(getByText('South Dakota')).toBeInTheDocument();
  });

  it('should call setActiveState when a state is selected', () => {
    const { getByRole } = render(
      <StateDropdown
        activeState={activeState}
        availableStates={availableStates}
        setActiveState={mockSetActiveState}
      />
    );
    fireEvent.change(getByRole('combobox'), { target: { value: 'ME' } });
    expect(mockSetActiveState).toHaveBeenCalledWith('ME');
  });
});
