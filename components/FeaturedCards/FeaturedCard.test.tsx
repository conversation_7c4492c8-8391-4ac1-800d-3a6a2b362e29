import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';
import { createID } from '@utils/strings';
import {
  DAM_IMAGE_FIXTURE,
  fireEvent,
  render,
  screen
} from '@utils/test-utils';

import segmentEvents from '~/config/segment-events';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { MagnoliaImage } from '~/types/Magnolia';

import FeaturedCard, { FeaturedCardProps } from './FeaturedCard';

describe('Featured Card', () => {
  const mockAnalytics = {
    track: jest.fn()
  };
  window.tracking = mockAnalytics;

  const mockSiteContext: SiteContextType = {
    site: {
      path: 'caring.com',
      name: 'Caring.com',
      domains: ['caring.com'],
      segmentWriteKey: 'key',
      segmentCdnURL: 'cdn',
      partnerToken: 'token',
      publicFolder: 'folder'
    }
  };

  const badgeImage = {
    ...DAM_IMAGE_FIXTURE,
    '@name': 'bha_hear.com',
    '@path': '/Caring.com/bha_hear.png',
    metadata: {
      fileName: 'bha_hear.png',
      mimeType: 'image/png',
      caption: 'bha_hear',
      title: 'bha_hear',
      fileSize: 1445742,
      height: 5871,
      width: 7059
    }
  } as MagnoliaImage;

  const productImage = {
    ...DAM_IMAGE_FIXTURE,
    '@name': 'hear_com_logo.png',
    '@path': '/Caring.com/bha_hear_logo.png',
    metadata: {
      fileName: 'bha_hear_logo.png',
      mimeType: 'image/png',
      caption: 'bha_hear_logo',
      title: 'bha_hear_logo',
      fileSize: 22277,
      height: 198,
      width: 488,
      format: 'image/png'
    }
  } as MagnoliaImage;

  const card: FeaturedCardProps = {
    badgeImage: {
      field: 'damChooser',
      image: badgeImage
    },
    hideBadgeImageOnMobile: false,
    superlative: 'Best Rechargeable Digital Hearing Aids',
    text1: 'Cost: Starts at $2,900',
    text2: 'Bluetooth: Yes',
    text3: 'Rechargeable: Yes',
    text4: undefined,
    productImage: {
      field: 'damChooser',
      image: productImage
    },
    readReviewText: 'Read Our Review',
    readReviewId: 'Bay Alarm Medical Review',
    ctaText: 'See Packages',
    ctaState: 'solid',
    ctaWidth: 'md',
    ctaSize: 'md',
    ctaUrl:
      'https://m.cdn.hear.com/styletto_german_sp/&utm_clickid=adab35c6-00ce-4690-82ef-08c04b391d23',
    ctaBehavior: '_blank',
    ctaRel: ['noopener', 'noreferrer'],
    ctaBgColor: 'secondary',
    ctaTextColor: 'white',
    borderRadius: '12',
    boxShadow: 'lg',
    productName: 'Hear.com - Hearing Aid',
    productCategory: 'devices',
    productCategoryId: 'devices',
    productSubCategory: 'hearing-aids',
    productId: undefined,
    productProvider: 'Hear.com',
    productProviderId: undefined,
    productMonetized: '1',
    productLocationId: '4'
  };

  it('render feature card', () => {
    const { getByText, getByRole, getByAltText } = render(
      <SiteContext.Provider value={mockSiteContext}>
        <FeaturedCard
          key={1}
          badgeImage={card.badgeImage}
          hideBadgeImageOnMobile={card.hideBadgeImageOnMobile}
          superlative={card.superlative}
          text1={card.text1}
          text2={card.text2}
          text3={card.text3}
          text4={card.text4}
          productImage={card.productImage}
          readReviewText={card.readReviewText}
          readReviewId={card.readReviewId}
          ctaText={card.ctaText}
          ctaState={card.ctaState}
          ctaWidth={card.ctaWidth}
          ctaSize={card.ctaSize}
          ctaUrl={card.ctaUrl}
          ctaBehavior={card.ctaBehavior}
          ctaTextColor={card.ctaTextColor}
          ctaBgColor={card.ctaBgColor}
          ctaRel={card.ctaRel}
          productName={card.productName}
          productCategory={card.productCategory}
          productCategoryId={card.productCategory}
          productSubCategory={card.productSubCategory}
          productId={card.productId}
          productProvider={card.productProvider}
          productProviderId={card.productProviderId}
          productMonetized={card.productMonetized}
          productLocationId={card.productLocationId}
        />
      </SiteContext.Provider>
    );

    expect(getByAltText(badgeImage.metadata.caption));
    expect(getByText(card.superlative)).toBeInTheDocument();
    expect(getByAltText(productImage.metadata.caption));
    expect(getByText('Cost: Starts at $2,900')).toBeInTheDocument();
    expect(getByText('Bluetooth: Yes')).toBeInTheDocument();
    expect(getByText('Rechargeable: Yes')).toBeInTheDocument();
    const readReview = screen.getByRole('link', {
      name: `${card.readReviewText}`
    });
    expect(readReview).toHaveAttribute(
      'href',
      `#${createID(card.readReviewId)}`
    );
    const seePackages = screen.getByRole('link', { name: `${card.ctaText}` });
    expect(seePackages).toHaveAttribute('href', `${card.ctaUrl}`);
    expect(seePackages).toHaveAttribute('target', `${card.ctaBehavior}`);
    expect(seePackages).toHaveAttribute('rel', `${card.ctaRel?.join(' ')}`);
    fireEvent.click(seePackages);
    expect(mockAnalytics.track).toHaveBeenCalled();
    expect(mockAnalytics.track).toHaveBeenCalledWith(
      segmentEvents.PRODUCT_CLICKED,
      {
        product_name: card.productName,
        product_category: card.productCategory,
        product_category_id: card.productCategoryId,
        product_sub_category: card.productSubCategory,
        product_id: null,
        product_location_id: card.productLocationId,
        product_provider: card.productProvider,
        product_provider_id: null,
        product_monetized: card.productMonetized,
        product_url: card.ctaUrl,
        product_url_query: null,
        color: card.ctaBgColor,
        name: ElementNames.FEATURE_CARD,
        text: card.ctaText,
        textColor: card.ctaTextColor,
        action: ElementActions.EXTERNAL_LINK,
        type: ElementTypes.BUTTON,
        page_session_id: '',
        session_id: '',
        dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
      }
    );
  });

  it('should handle readReviewId as a URL', () => {
    const cardWithUrlReview = {
      ...card,
      readReviewId: 'https://example.com/review'
    };

    render(
      <SiteContext.Provider value={mockSiteContext}>
        <FeaturedCard {...cardWithUrlReview} />
      </SiteContext.Provider>
    );

    const readReview = screen.getByRole('link', {
      name: cardWithUrlReview.readReviewText
    });

    // When readReviewId is a URL, it should use the URL directly
    expect(readReview).toHaveAttribute('href', 'https://example.com/review');
  });

  it('should handle readReviewId as a non-URL string', () => {
    const cardWithStringReview = {
      ...card,
      readReviewId: 'My Review Title'
    };

    render(
      <SiteContext.Provider value={mockSiteContext}>
        <FeaturedCard {...cardWithStringReview} />
      </SiteContext.Provider>
    );

    const readReview = screen.getByRole('link', {
      name: cardWithStringReview.readReviewText
    });

    // When readReviewId is not a URL, it should create an anchor link
    expect(readReview).toHaveAttribute(
      'href',
      `#${createID('My Review Title')}`
    );
  });

  it('should handle readReviewId with hash prefix', () => {
    const cardWithHashReview = {
      ...card,
      readReviewId: '#my-review-section'
    };

    render(
      <SiteContext.Provider value={mockSiteContext}>
        <FeaturedCard {...cardWithHashReview} />
      </SiteContext.Provider>
    );

    const readReview = screen.getByRole('link', {
      name: cardWithHashReview.readReviewText
    });

    // Hash should be removed and then re-added with createID
    expect(readReview).toHaveAttribute(
      'href',
      `#${createID('my-review-section')}`
    );
  });
});
