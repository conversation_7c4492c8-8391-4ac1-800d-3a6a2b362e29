import { Button, ButtonProps } from '@chakra-ui/button';
import { Box, Text } from '@chakra-ui/layout';
import { Link, useBreakpointValue } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { useProductClicked } from '@components/Analytics/events/ProductClicked';
import StoryImage from '@components/Image/StoryImage';
import { createID } from '@utils/strings';

import { MagnoliaImage } from '~/types/Magnolia';

export interface FeaturedCardProps {
  badgeImage: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  hideBadgeImageOnMobile: boolean;
  superlative: string;
  productImage: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  text1?: string;
  text2?: string;
  text3?: string;
  text4?: string;
  readReviewText: string;
  readReviewId: string;
  ctaText: string;
  ctaState: 'solid' | 'outline' | 'ghost';
  ctaWidth: 'fit-content' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '100%';
  ctaSize: ButtonProps['size'];
  ctaUrl: string;
  ctaBehavior: '_blank' | '_self' | '_parent' | '_top';
  ctaRel?: Array<
    'external' | 'nofollow' | 'noopener' | 'noreferrer' | 'opener'
  >;
  ctaBgColor?: string;
  ctaTextColor?: string;
  borderRadius?: string;
  boxShadow?: 'lg' | 'md' | 'sm' | 'none';
  productName: string;
  productCategory: string;
  productCategoryId: string;
  productSubCategory: string;
  productId?: string | null;
  productProvider: string;
  productProviderId?: string | null;
  productMonetized: string;
  productLocationId: string;
}

const FeaturedCard = ({
  badgeImage,
  hideBadgeImageOnMobile,
  superlative,
  productImage,
  text1,
  text2,
  text3,
  text4,
  readReviewText,
  readReviewId,
  ctaText,
  ctaState,
  ctaWidth,
  ctaSize,
  ctaUrl,
  ctaBehavior,
  ctaRel,
  ctaBgColor,
  ctaTextColor,
  borderRadius = '12',
  boxShadow = 'lg',
  productName,
  productCategory,
  productCategoryId,
  productSubCategory,
  productId = null,
  productProvider,
  productProviderId = null,
  productMonetized,
  productLocationId
}: FeaturedCardProps): JSX.Element => {
  const elementClicked = useElementClicked();
  const productClicked = useProductClicked({
    productName: productName,
    productCategory: productCategory,
    productCategoryId: productCategoryId,
    productSubCategory: productSubCategory,
    productId: productId,
    productLocationId: productLocationId,
    productProvider: productProvider,
    productProviderId: productProviderId,
    productMonetized: productMonetized,
    productUrl: ctaUrl,
    color: ctaBgColor,
    name: ElementNames.FEATURE_CARD,
    text: ctaText,
    textColor: ctaTextColor
  });

  const isBadgeVisible = useBreakpointValue({
    base: !hideBadgeImageOnMobile,
    sm: !hideBadgeImageOnMobile,
    md: true
  });

  const urlRegex = /^(https?):\/\/[^\s/$.?#].[^\s]*$/i;

  readReviewId = readReviewId?.replace('#', '');

  const readReviewUrl = urlRegex.test(readReviewId)
    ? readReviewId
    : `#${createID(readReviewId)}`;

  const featuredCard = () => (
    <Box borderRadius={borderRadius} boxShadow={boxShadow} pt="0px">
      {isBadgeVisible && (
        <StoryImage switchable={badgeImage} displayAsBackground={false} />
      )}
      <Box pr={4} pl={4} pb={4}>
        <Box display="table" width="100%">
          <Box
            display="table-cell"
            textAlign="center"
            verticalAlign="middle"
            fontSize="lg"
            fontWeight="700"
            lineHeight="120%"
            height="60px"
            pb={4}
          >
            {superlative}
          </Box>
        </Box>
        <StoryImage
          switchable={productImage}
          displayAsBackground={true}
          desktopHeight="65px"
          mobileHeight="65px"
          backgroundSize="contain"
          containerMarginBottom="16px"
        />
        <Box
          height="125px"
          mb={4}
          pt={0}
          textAlign="center"
          fontSize="md"
          fontStyle="normal"
          fontWeight="400"
          lineHeight={6}
        >
          {text1 && <Text>{text1}</Text>}
          {text2 && <Text>{text2}</Text>}
          {text3 && <Text>{text3}</Text>}
          {text4 && <Text>{text4}</Text>}
        </Box>
        <Box textAlign="center" mb="16px">
          <Link
            href={readReviewUrl}
            fontWeight="400"
            color="primary.600"
            fontSize="md"
            textDecoration="underline"
            onClick={() => {
              elementClicked({
                element: {
                  type: ElementTypes.LINK,
                  action: ElementActions.JUMP_LINK,
                  name: ElementNames.FEATURE_CARD,
                  text: ctaText,
                  color: 'primary.600',
                  textColor: ctaTextColor ?? ''
                },
                destinationUrl: readReviewUrl
              });
            }}
          >
            {readReviewText}
          </Link>
        </Box>
        <Box display="flex" justifyContent="center">
          <Button
            as="a"
            href={`${ctaUrl}`}
            target={ctaBehavior || '_self'}
            rel={ctaRel?.join(' ') || ''}
            colorScheme={ctaBgColor}
            variant={ctaState}
            width={{ base: 'lg', sm: ctaWidth }}
            textColor={ctaTextColor}
            size={ctaSize}
            onClick={() => {
              productClicked();
            }}
          >
            {ctaText}
          </Button>
        </Box>
      </Box>
    </Box>
  );

  return featuredCard();
};

export default FeaturedCard;
