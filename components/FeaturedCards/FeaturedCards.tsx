import { Box, SimpleGrid } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';

import FeaturedCard, { FeaturedCardProps } from './FeaturedCard';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

interface FeaturedCardsProps {
  featuredCards: FeaturedCardProps[];
}

const FeaturedCards = ({ featuredCards }: FeaturedCardsProps): JSX.Element => {
  const featured = () => (
    <Box paddingLeft={2} paddingRight={2} mb={8}>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing="16px">
        {featuredCards['@nodes'].map((key) => {
          const card = featuredCards[key];
          return (
            <FeaturedCard
              key={key}
              badgeImage={card.badgeImage}
              hideBadgeImageOnMobile={card.hideBadgeImageOnMobile}
              superlative={card.superlative}
              text1={card.text1}
              text2={card.text2}
              text3={card.text3}
              text4={card.text4}
              productImage={card.productImage}
              readReviewText={card.readReviewText}
              readReviewId={card.readReviewId}
              ctaText={card.ctaText}
              ctaState={card.ctaState}
              ctaWidth={card.ctaWidth}
              ctaSize={card.ctaSize}
              ctaUrl={card.ctaUrl}
              ctaBehavior={card.ctaBehavior}
              ctaTextColor={card.ctaTextColor}
              ctaBgColor={card.ctaBgColor}
              ctaRel={card.ctaRel}
              productName={card.productName}
              productCategory={card.productCategory}
              productCategoryId={card.productCategory}
              productSubCategory={card.productSubCategory}
              productId={card.productId}
              productProvider={card.productProvider}
              productProviderId={card.productProviderId}
              productMonetized={card.productMonetized}
              productLocationId={card.productLocationId}
            />
          );
        })}
      </SimpleGrid>
    </Box>
  );

  return featured();
};

export default FeaturedCards;
