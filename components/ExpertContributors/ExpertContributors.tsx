import { Box, Text } from '@chakra-ui/react';
import Heading from '@components/Heading';
import HtmlToReact from '@components/HtmlToReact';
import StoryImage from '@components/Image/StoryImage';
import Container from '@components/LayoutStructure/Container';

import { MagnoliaImage } from '~/types/Magnolia';

interface Props {
  contributorImage: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  title?: string;
  name?: string;
  description?: string;
}

const ExpertContributors = ({
  contributorImage,
  name,
  title,
  description
}: Props) => {
  return (
    <>
      <Container>
        {title && <Heading title={title} withContainer={false} />}
        <Box
          mt={8}
          border="1px solid"
          borderColor="gray.200"
          borderRadius={'md'}
          padding={4}
          display="flex"
          flexDirection={{ base: 'column', sm: 'row' }}
          alignItems="center"
          gap={{ base: 8, sm: 12 }}
        >
          {contributorImage && (
            <Box
              minWidth={{ base: '294px', sm: '235px' }}
              maxWidth={{ base: '294px', sm: '235px' }}
            >
              <StoryImage
                switchable={contributorImage}
                displayAsBackground={false}
                desktopHeight="265px"
                mobileHeight="237px"
                backgroundSize="contain"
                withContainer={false}
                containerMarginBottom="0px"
              />
            </Box>
          )}
          <Box>
            {name && (
              <Text
                as="h2"
                fontSize={'xl'}
                lineHeight={'6'}
                fontWeight={700}
                mb={2}
                color="primary.900"
              >
                {name}
              </Text>
            )}
            {description && (
              <Text fontSize={'md'} fontWeight={400}>
                {HtmlToReact({ html: description })}
              </Text>
            )}
          </Box>
        </Box>
      </Container>
    </>
  );
};

export default ExpertContributors;
