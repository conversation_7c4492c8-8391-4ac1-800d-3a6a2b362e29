import { fireEvent, render } from '@utils/test-utils';
import React from 'react';

import InfoRequestSection from './InfoRequestSection';

jest.mock('~/contexts/ModalContext');

import { useModalDispatch } from '~/contexts/ModalContext';

describe('InfoRequestSection', () => {
  const mockShowModal = jest.fn();
  const mockUseModalDispatch = useModalDispatch as jest.Mock;

  beforeEach(() => {
    mockUseModalDispatch.mockReturnValue({ showModal: mockShowModal });
    mockShowModal.mockClear();
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  it('renders without crashing', () => {
    const { container } = render(<InfoRequestSection />);
    expect(container).toBeTruthy();
  });

  it('opens modal when button is clicked and has an inquiryId', () => {
    const props = {
      items: {
        '@nodes': ['items0'],
        items0: {
          '@name': 'items0',
          title: 'Title',
          icon: 'MdAccessible',
          iconColor: 'yellow',
          iconColorRange: '600',
          cta: {
            '@name': 'cta',
            text: 'Contact for costs',
            bgColor: 'green',
            state: 'outline',
            inquiryId: 'getCostForm',
            bgColorRange: '400',
            '@nodes': []
          },
          '@nodes': ['cta']
        }
      }
    };

    const { getByText } = render(<InfoRequestSection {...props} />);
    fireEvent.click(getByText('Contact for costs'));
    expect(mockShowModal).toHaveBeenCalledWith('getCostForm');
  });

  it('renders correctly with props', () => {
    const props = {
      items: {
        '@nodes': ['items0', 'items1', 'items2'],
        items0: {
          '@name': 'items0',
          title: 'Title 1',
          icon: 'MdAccessible',
          iconColor: 'yellow',
          iconColorRange: '600',
          cta: {
            '@name': 'cta',
            text: 'Button 1',
            bgColor: 'green',
            state: 'outline',
            inquiryId: 'inquiryForm',
            bgColorRange: '400',
            '@nodes': []
          },
          '@nodes': ['cta']
        },
        items1: {
          '@name': 'items1',
          title: 'Title 2',
          icon: 'MdAccessible',
          iconColor: 'yellow',
          iconColorRange: '600',
          cta: {
            '@name': 'cta',
            text: 'Button 2',
            bgColor: 'green',
            state: 'outline',
            inquiryId: 'inquiryForm',
            bgColorRange: '400',
            '@nodes': []
          },
          '@nodes': ['cta']
        },
        items2: {
          '@name': 'items2',
          title: 'Title 3',
          icon: 'MdAccessible',
          iconColor: 'yellow',
          iconColorRange: '600',
          cta: {
            '@name': 'cta',
            text: 'Button 3',
            bgColor: 'green',
            state: 'outline',
            inquiryId: 'inquiryForm',
            bgColorRange: '400',
            '@nodes': []
          },
          '@nodes': ['cta']
        }
      }
    };

    const { getByText } = render(<InfoRequestSection {...props} />);

    expect(getByText('Title 1')).toBeInTheDocument();
    expect(getByText('Title 2')).toBeInTheDocument();
    expect(getByText('Title 3')).toBeInTheDocument();

    expect(getByText('Button 1')).toBeInTheDocument();
    expect(getByText('Button 2')).toBeInTheDocument();
    expect(getByText('Button 3')).toBeInTheDocument();
  });
});
