import { ButtonGroup } from '@chakra-ui/button';
import { Flex, Text } from '@chakra-ui/layout';
import { Icon, VStack } from '@chakra-ui/react';
import { ElementNames } from '@components/Analytics/events/ElementClicked';
import CallToAction, {
  CallToActionProps
} from '@components/CallToAction/CallToAction';
import Container from '@components/LayoutStructure/Container';
import { StringToIconKeys } from '@components/RenderIcon';
import { STRING_TO_ICON_CLASS } from '@components/RenderIcon';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import { Metadata } from 'next';
import { Fragment } from 'react';

export interface ItemsWithIcon extends Metadata {
  title: string;
  icon: StringToIconKeys;
  iconColor: StringToIconKeys;
  iconColorRange: StringToIconKeys;
  cta?: CallToActionProps;
}

interface Items extends Metadata {
  [key: string]: string | string[] | ItemsWithIcon | undefined | null | any;
}

interface InfoRequestSectionProps {
  deviceVisibility?: DeviceVisibility;
  items?: Items;
}

const InfoRequestSectionComponent: React.FC<InfoRequestSectionProps> = ({
  deviceVisibility,
  items
}) => {
  const isHidden = useResponsiveDisplay(deviceVisibility);
  if (isHidden) {
    return <></>;
  }
  const nodes = items ? items['@nodes'].map((node) => items[node]) : [];

  return (
    <Container>
      <ButtonGroup spacing="4" width="full">
        <Flex
          gap="4"
          width="full"
          justifyContent="space-around"
          direction={{ base: 'column', lg: 'row' }}
        >
          {nodes.map((el, i) => (
            <Fragment key={i}>
              <VStack>
                {el.icon && (
                  <Icon
                    w={70}
                    h={70}
                    color={`${el.iconColor}.${el.iconColorRange}`}
                    as={STRING_TO_ICON_CLASS[el.icon]}
                  />
                )}
                <Text fontSize={20} as="span" fontWeight="700" pb={2}>
                  {el.title}
                </Text>
                <CallToAction
                  py={2}
                  height="100%"
                  isInquiry={true}
                  buttonProps={{ height: 58, width: 194 }}
                  bg={`${el.cta.color}.${el.cta.ColorRange}`}
                  trackingName={ElementNames.INFO_REQUEST_SECTION}
                  {...el.cta}
                />
              </VStack>
            </Fragment>
          ))}
        </Flex>
      </ButtonGroup>
    </Container>
  );
};

export default InfoRequestSectionComponent;
