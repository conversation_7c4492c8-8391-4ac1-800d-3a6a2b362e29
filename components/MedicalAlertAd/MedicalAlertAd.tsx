import { Box, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import StoryImage from '@components/Image/StoryImage';
import Container from '@components/LayoutStructure/Container';

import { MagnoliaImage } from '~/types/Magnolia';

interface Props {
  productImage: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  title?: string;
  description?: string;
  buttonText?: string;
  url?: string;
  withContainer?: boolean;
}

const MedicalAlertAd = ({
  productImage,
  title,
  description,
  buttonText,
  url,
  withContainer = true
}: Props) => {
  const card = () => {
    return (
      <Box
        border="1px solid"
        borderColor="gray.200"
        borderRadius={'md'}
        padding={4}
        display="flex"
        flexDirection={{ base: 'column', sm: 'row' }}
        alignItems="center"
        gap={{ base: 8, sm: 12 }}
      >
        {productImage && (
          <Box
            minWidth={{ base: '294px', sm: '235px' }}
            maxWidth={{ base: '294px', sm: '235px' }}
          >
            <StoryImage
              switchable={productImage}
              displayAsBackground={false}
              desktopHeight="265px"
              mobileHeight="237px"
              backgroundSize="contain"
              withContainer={false}
              containerMarginBottom="0px"
            />
          </Box>
        )}
        <Box>
          {title && (
            <Text
              as="h2"
              fontSize={'xl'}
              lineHeight={'6'}
              fontWeight={700}
              mb={2}
              color="primary.900"
            >
              {title}
            </Text>
          )}
          {description && (
            <Text fontSize={'md'} fontWeight={400}>
              {description}
            </Text>
          )}
          {(buttonText || url) && (
            <Button
              as={'a'}
              href={url}
              mt={8}
              bgColor={'blue.400'}
              color="white"
              fontWeight={700}
              width={{ base: 'full', sm: 'auto' }}
              elementAction={ElementActions.INTERNAL_LINK}
              elementName={ElementNames.PRODUCT_REVIEW_CARD}
              elementType={ElementTypes.BUTTON}
              destinationUrl={url}
            >
              {buttonText}
            </Button>
          )}
        </Box>
      </Box>
    );
  };

  return withContainer ? <Container>{card()}</Container> : card();
};

export default MedicalAlertAd;
