import CheckboxInput from '@components/CheckboxInput';

interface AccessibilityFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const accessibilityFilterItems = [
  {
    value: 'mobility-wheelchair',
    label: 'Mobility & Wheelchair Assistance'
  },
  {
    value: 'transfer-assistance',
    label: 'Transfer Assistance'
  }
];

function AccessibilityFilter({ onChange, value }: AccessibilityFilterProps) {
  return (
    <CheckboxInput
      name="accessibility"
      onChange={onChange}
      items={accessibilityFilterItems}
      value={value}
    />
  );
}

export default AccessibilityFilter;
