import { DualTestimonial } from '@components/DualTestimonial/index';
import { render, screen } from '@utils/test-utils';

describe('DualTestimonial', () => {
  it('should render two testimonies', () => {
    render(
      <DualTestimonial
        bg={{ color: 'green', range: 500 }}
        largeTestimonial={{
          author: '<PERSON>',
          authorTitle: 'Person',
          body: 'This is a text'
        }}
        smallTestimonial={{
          author: '<PERSON>',
          body: 'This is a quote'
        }}
      />
    );

    expect(screen.queryByText('<PERSON>')).toBeInTheDocument();
    expect(screen.queryByText('Person')).toBeInTheDocument();
    expect(screen.queryByText('This is a text')).toBeInTheDocument();
    expect(screen.queryByText('<PERSON>')).toBeInTheDocument();
    expect(screen.queryByText('This is a quote')).toBeInTheDocument();
  });
});
