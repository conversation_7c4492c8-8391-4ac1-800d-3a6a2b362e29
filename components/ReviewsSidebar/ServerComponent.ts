import { ModularMonolithClient } from '@services/modular-monolith/client';
import { getPathFromUrl, isPathDenied } from '@utils/denied-list';
import { strip } from '@utils/parser';
import { getStateNameFromAbbreviation } from '@utils/UsStates';
import isObject from 'lodash/isObject';
import kebabCase from 'lodash/kebabCase';
import truncate from 'lodash/truncate';
import unescape from 'lodash/unescape';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext, SiteDefinition } from '~/contexts/SiteContext';
import { Domain } from '~/types/Domains';

import { Review, ReviewsSidebarProps } from './ReviewsSidebar';

interface LocationReviewFromServer
  extends Omit<
    Review,
    'providerResponse' | 'resourceName' | 'resourceUrl' | 'content'
  > {
  text: string;
  providerResponse: { response: string } | null;
  location: {
    name: string;
    url: string;
  };
}

const DEFAULT_ITEMS_TO_SHOW = 3;
const STRING_MAX_LENGTH = 200;
const STRING_SPLIT_REGEX = /,? +/;
const STRIP_HTML_REGEX = /(<([^>]+)>)/gi;

const stripHTMLTags = (str: string): string =>
  str.replace(STRIP_HTML_REGEX, '');

const sanitizeAndTruncate = (str: string): string => {
  const sanitizedStr = unescape(stripHTMLTags(str));
  return truncate(sanitizedStr, {
    length: STRING_MAX_LENGTH,
    separator: STRING_SPLIT_REGEX
  });
};

export const getServerSideComponentProps = async (
  props: ReviewsSidebarProps,
  context: GetServerSidePropsContext
): Promise<Review[]> => {
  const { itemsToShow, careType, city, county, state } = props;
  const site: SiteDefinition = findSiteForContext(context);
  const domain = site.path as Domain;
  const formattedCareType = isObject(careType)
    ? careType.name
    : kebabCase(strip(careType).toLowerCase());
  const formattedItemsToShow = Number(itemsToShow);
  const limit = Number.isNaN(formattedItemsToShow)
    ? DEFAULT_ITEMS_TO_SHOW
    : formattedItemsToShow;

  const apiVersion = String(context.query?.geo || 'v1');
  // v1 uses state abbreviation, but v2 requires state name
  const formattedState =
    apiVersion === 'v2' && state ? getStateNameFromAbbreviation(state) : state;

  const modularMonolithClient = new ModularMonolithClient(domain);
  const reviews: LocationReviewFromServer[] =
    await modularMonolithClient.getRecentReviews({
      domain,
      careType: formattedCareType,
      state: formattedState || state,
      county,
      city,
      limit
    });

  const formattedReviews: Review[] = (reviews || [])
    .filter((review) => {
      const path = getPathFromUrl(review.location.url);
      return !isPathDenied(path);
    })
    .map((review) => {
      return {
        id: review.id,
        title: review.title,
        createdAt: review.createdAt,
        rating: review.rating,
        resourceName: review.location.name,
        resourceUrl: review.location.url,
        authorName: review.authorName,
        careType: review.careType,
        content: sanitizeAndTruncate(review.text),
        providerResponse: sanitizeAndTruncate(
          review.providerResponse?.response || ''
        )
      };
    });

  return formattedReviews;
};
