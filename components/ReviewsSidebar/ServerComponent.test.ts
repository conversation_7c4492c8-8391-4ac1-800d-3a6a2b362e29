import { getServerSideComponentProps } from '@components/ReviewsSidebar/ServerComponent';
import { GetServerSidePropsContext } from 'next';

const mockGetRecentReviews = jest.fn();

jest.mock('@services/modular-monolith/client', () => ({
  ModularMonolithClient: jest.fn().mockImplementation(() => ({
    getRecentReviews: mockGetRecentReviews
  }))
}));

jest.mock('~/contexts/SiteContext', () => ({
  findSiteForContext: jest.fn(() => ({
    path: 'caring.com'
  }))
}));

describe('ReviewsSidebar ServerComponent', () => {
  it('provides the correct params for the ModMon API', async () => {
    await getServerSideComponentProps(
      {
        itemsToShow: '3',
        careType: 'assisted-living',
        city: 'Los Angeles',
        state: 'CA',
        data: [],
        title: ''
      },
      {} as GetServerSidePropsContext
    );

    expect(mockGetRecentReviews).toHaveBeenCalledWith({
      limit: 3,
      careType: 'assisted-living',
      city: 'Los Angeles',
      county: undefined,
      state: 'CA',
      domain: 'caring.com'
    });
  });
});
