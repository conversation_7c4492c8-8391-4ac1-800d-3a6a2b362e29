import { VStack } from '@chakra-ui/layout';
import Container from '@components/LayoutStructure/Container';
import ReviewCard from '@components/ReviewCard';
import SidebarHeading from '@components/Sidebar/SidebarHeading';

import { HeadingElements } from '~/@types/heading';

export interface Review {
  id: string;
  title: string;
  rating: number;
  authorName: string;
  careType: string;
  createdAt: string;
  content: string;
  providerResponse: string | null;
  resourceName: string;
  resourceUrl: string;
}

export interface ReviewsSidebarProps {
  careType?: string | { name: string };
  city?: string;
  county?: string;
  data: Review[];
  itemsToShow: string;
  state?: string;
  title: string;
  headingElement?: HeadingElements | 'p';
  titleAlignment?: 'left' | 'center' | 'right';
  ratingColor?: string;
  ratingColorRange?: string;
  tagCareTypeColor?: string;
  tagCareTypeColorRange?: string;
  readMoreColor?: string;
  readMoreColorRange?: string;
}

const ReviewsSidebar: React.FC<ReviewsSidebarProps> = ({
  data,
  title,
  headingElement,
  titleAlignment,
  ratingColor,
  ratingColorRange,
  tagCareTypeColor,
  tagCareTypeColorRange,
  readMoreColor = 'secondary',
  readMoreColorRange = '400'
}) => {
  if (data.length === 0) {
    return null;
  }

  return (
    <Container as="section" display="flex" flexDirection="column" gap="4">
      <SidebarHeading
        headingElement={headingElement}
        titleAlignment={titleAlignment}
      >
        {title}
      </SidebarHeading>

      {data.map((review) => (
        <ReviewCard key={review.id}>
          <VStack spacing="2" alignItems="flex-start">
            <ReviewCard.CareType
              color={`${tagCareTypeColor}.${tagCareTypeColorRange}`}
            >
              {review.careType}
            </ReviewCard.CareType>

            <ReviewCard.Summary
              date={review.createdAt}
              rating={review.rating}
              reviewer={review.authorName}
              tag={review.title}
              starColor={`${ratingColor}.${ratingColorRange}`}
            />
          </VStack>

          <ReviewCard.Content title={`Review of ${review.resourceName}`}>
            {review.content}
          </ReviewCard.Content>

          {review.providerResponse && (
            <ReviewCard.Response>{review.providerResponse}</ReviewCard.Response>
          )}

          <ReviewCard.Link
            href={review.resourceUrl}
            color={`${readMoreColor}.${readMoreColorRange}`}
          >
            Read more
          </ReviewCard.Link>
        </ReviewCard>
      ))}
    </Container>
  );
};

export default ReviewsSidebar;
