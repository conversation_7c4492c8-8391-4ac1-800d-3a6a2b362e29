import { render, screen } from '@testing-library/react';

import axe from '~/axe-helper';

import ReviewsSidebar from './ReviewsSidebar';

const mockData = {
  title: 'Reviews',
  itemsToShow: '3',
  reviews: [
    {
      id: '1',
      careType: 'Assisted Living',
      content: 'This is a review',
      createdAt: '2023-03-12T15:41:11.000Z',
      resourceName: 'Provider Name',
      rating: 4,
      providerResponse: 'This is a response',
      authorName: '<PERSON>',
      title: 'This is a tag',
      resourceUrl: 'https://www.caring.com/roll-up/care-type/provider-slug'
    }
  ]
};

describe('ReviewsSidebar', () => {
  it('renders the sidebar without violations', async () => {
    const { container } = render(
      <ReviewsSidebar
        title={mockData.title}
        itemsToShow={mockData.itemsToShow}
        data={mockData.reviews}
      />
    );

    expect(screen.getByText(mockData.title)).toBeVisible();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('renders an empty component if there are no reviews', () => {
    render(
      <ReviewsSidebar
        title={mockData.title}
        itemsToShow={mockData.itemsToShow}
        data={[]}
      />
    );

    expect(screen.queryByText(mockData.title)).not.toBeInTheDocument();
  });
});
