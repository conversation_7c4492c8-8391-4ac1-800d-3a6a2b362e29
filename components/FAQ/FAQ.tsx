'use client';

import { VStack } from '@chakra-ui/layout';
import StoryImage, { StoryImageProps } from '@components/Image/StoryImage';
import { Section } from '@components/Sections';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import dynamic from 'next/dynamic';
import { useContext } from 'react';

import { HeadingElements } from '~/@types/heading';
import ProviderContext from '~/contexts/Provider';

import { FAQs } from './FAQ.types';
import { validateAndProcessFAQs } from './FAQ.utils';
import CareTypeFaqAccordion from './ProviderFAQAccordion';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

interface Props {
  templateId?: string;
  headingElement: HeadingElements;
  text?: string;
  deviceVisibility?: DeviceVisibility;
  switchable?: {
    faqs?: FAQs;
    field: string;
    dataFAQ?: string;
  };
  faqs?: FAQs;
  image?: StoryImageProps;
}

const ProviderFAQ = ({
  templateId,
  headingElement,
  text,
  title,
  deviceVisibility,
  switchable,
  faqs,
  image
}): React.ReactElement<Props> => {
  const provider = useContext(ProviderContext)?.provider;
  const isHidden = useResponsiveDisplay(deviceVisibility);
  if (isHidden) {
    return <></>;
  }
  let items = validateAndProcessFAQs(switchable, provider, faqs);

  if (!items.length) {
    return <></>;
  }

  return (
    <Container>
      <VStack align="stretch" spacing="4">
        <Section
          title={title}
          headingElement={headingElement}
          richText={text}
        />
        {image?.switchable?.imageUrl && <StoryImage {...image} />}
        <CareTypeFaqAccordion items={items} />
      </VStack>
    </Container>
  );
};

export default ProviderFAQ;
