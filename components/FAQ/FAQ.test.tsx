import { render } from '@utils/test-utils';

import { Metadata } from '~/types/Magnolia';

import ProviderFAQ from './FAQ';
import { validateAndProcessFAQs } from './FAQ.utils';

const FAQ_mock = {
  headingElement: 'h2',
  text: 'Lorem ipsum dolor sit amet, consectetur adip',
  title: 'Frequently asked questions'
};

const Metadata_mock: Metadata = {
  '@name': '',
  '@path': '',
  '@id': '',
  '@nodeType': '',
  'mgnl:lastModified': '',
  'mgnl:template': '',
  'mgnl:created': '',
  '@nodes': []
};

describe('validateAndProcessFAQs function', () => {
  test('returns an empty array if no switchable object is provided', () => {
    const result = validateAndProcessFAQs(undefined, null);
    expect(result).toEqual([]);
  });

  test('returns items from manual FAQs if "manually" field is provided', () => {
    const switchable = {
      field: 'manually',
      dataFAQ: undefined,
      faqs: {
        ...Metadata_mock,
        '@nodes': ['faqs0', 'faqs1'],
        faqs0: {
          ...Metadata_mock,
          '@id': '1',
          question: 'Question 1',
          answer: 'Answer 1'
        },
        faqs1: {
          ...Metadata_mock,
          '@id': '2',
          question: 'Question 2',
          answer: 'Answer 2'
        }
      }
    };
    const result = validateAndProcessFAQs(switchable, null);
    expect(result).toEqual([
      {
        content: 'Question 1',
        answer: {
          id: '1',
          content: 'Answer 1',
          status: ''
        }
      },
      {
        content: 'Question 2',
        answer: {
          id: '2',
          content: 'Answer 2',
          status: ''
        }
      }
    ]);
  });

  test('returns items from provider FAQs if "manually" field is not provided', () => {
    const switchable = {
      field: 'manually',
      dataFAQ: undefined
    };
    const provider = {
      id: '1',
      averageRating: 0,
      description: '',
      name: '',
      phoneNumber: '',
      reviewCount: 0,
      slug: '',
      faqs: [
        {
          content: 'Question 2',
          answer: {
            id: '2',
            content: 'Answer 2',
            status: ''
          }
        }
      ]
    };
    const result = validateAndProcessFAQs(switchable, provider);
    expect(result).toEqual([
      {
        content: 'Question 2',
        answer: {
          id: '2',
          content: 'Answer 2',
          status: ''
        }
      }
    ]);
  });

  test('returns items from data catalog if "dataCatalog" field is provided with valid JSON data', () => {
    const switchable = {
      field: 'dataCatalog',
      dataFAQ:
        '{"data": [{"content": "Question 3", "answer": {"id": "3", "content": "Answer 3", "status": ""}}]}'
    };
    const result = validateAndProcessFAQs(switchable, null);
    expect(result).toEqual([
      {
        content: 'Question 3',
        answer: {
          id: '3',
          content: 'Answer 3',
          status: ''
        }
      }
    ]);
  });

  test('returns an empty array if "dataCatalog" field is provided with invalid JSON data', () => {
    jest.spyOn(console, 'error').mockImplementationOnce(() => {});

    const switchable = {
      field: 'dataCatalog',
      dataFAQ: 'invalidJSON'
    };
    const result = validateAndProcessFAQs(switchable, null);
    expect(result).toEqual([]);
  });
});

describe('ProviderFAQ Component', () => {
  test('renders correctly with faqs array', () => {
    const mock = {
      headingElement: 'h2',
      text: 'Lorem ipsum dolor sit amet, consectetur adip',
      title: 'Frequently asked questions',
      switchable: undefined,
      faqs: {
        faqs0: {
          answer: '<p>Answer 1</p>',
          'jcr:uuid': '213f7091-ec78-46a9-969d-a1908a55d856',
          'mgnl:lastModifiedBy': 'superuser',
          question: '<p>Question 1</p>'
        },
        faqs1: {
          answer: '<p>Answer 2</p>',
          'jcr:uuid': '213f7091-ec78-46a9-969d-a1908a55d856',
          'mgnl:lastModifiedBy': 'superuser',
          question: '<p>Question 2</p>'
        },
        '@nodes': ['faqs0', 'faqs1']
      }
    };
    const { getByText } = render(<ProviderFAQ image={undefined} {...mock} />);
    expect(getByText(FAQ_mock.title)).toBeInTheDocument();
    expect(getByText(FAQ_mock.text)).toBeInTheDocument();
    expect(getByText('Question 1')).toBeInTheDocument();
    expect(getByText('Answer 1')).toBeInTheDocument();
    expect(getByText('Question 2')).toBeInTheDocument();
    expect(getByText('Answer 2')).toBeInTheDocument();
  });

  test('renders correctly with switchable manual faqs array', () => {
    const switchableManualMockFaq = {
      ...FAQ_mock,
      faqs: undefined,
      switchable: {
        field: 'manually',
        dataFAQ: undefined,
        faqs: {
          ...Metadata_mock,
          '@nodes': ['faqs0', 'faqs1'],
          faqs0: {
            ...Metadata_mock,
            '@id': '1',
            question: 'Question 1',
            answer: 'Answer 1'
          },
          faqs1: {
            ...Metadata_mock,
            '@id': '2',
            question: 'Question 2',
            answer: 'Answer 2'
          }
        }
      }
    };
    const { getByText } = render(
      <ProviderFAQ image={undefined} {...switchableManualMockFaq} />
    );
    expect(getByText(FAQ_mock.title)).toBeInTheDocument();
    expect(getByText(FAQ_mock.text)).toBeInTheDocument();
    expect(getByText('Question 1')).toBeInTheDocument();
    expect(getByText('Answer 1')).toBeInTheDocument();
    expect(getByText('Question 2')).toBeInTheDocument();
    expect(getByText('Answer 2')).toBeInTheDocument();
  });

  test('renders correctly with switchable field as dataCatalog', () => {
    const switchableDataCatalogMockFaq = {
      ...FAQ_mock,
      faqs: undefined,
      switchable: {
        field: 'dataCatalog',
        dataFAQ:
          '{"data": [{"content": "Question 3", "answer": {"id": "3", "content": "Answer 3", "status": ""}}]}'
      }
    };
    const { getByText } = render(
      <ProviderFAQ image={undefined} {...switchableDataCatalogMockFaq} />
    );

    expect(getByText(FAQ_mock.title)).toBeInTheDocument();
    expect(getByText(FAQ_mock.text)).toBeInTheDocument();
    expect(getByText('Question 3')).toBeInTheDocument();
    expect(getByText('Answer 3')).toBeInTheDocument();
  });
});
