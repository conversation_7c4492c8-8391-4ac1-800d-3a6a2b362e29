import { ParseMagnoliaPage } from '@utils/parser/magnolia';

import { CatalogFaq, Faq, Provider } from '~/contexts/Provider';

import { FAQs, MagnoliaFAQ, Switchable } from './FAQ.types';

export function validateAndProcessFAQs(
  switchable: Switchable | undefined,
  provider: Provider | null | undefined,
  manualFaqs?: FAQs
): Faq[] | CatalogFaq[] {
  let items: Faq[] | CatalogFaq[] = [];

  if (switchable?.field === 'manually' || manualFaqs) {
    const faqItems = manualFaqs || switchable?.faqs;
    const mappedFaqs =
      faqItems?.['@nodes']?.map((node) => {
        const faqItem = faqItems[node] as MagnoliaFAQ;
        return {
          content: faqItem.question,
          answer: {
            id: faqItem['@id'],
            content: faqItem.answer,
            status: ''
          }
        };
      }) || [];

    const parsedFaqs = (provider?.faqs ?? []).map((faq) => {
      const parsedQuestion = {
        question: faq.content
      };

      ParseMagnoliaPage({
        source: parsedQuestion,
        values: { provider: provider?.name || {} }
      });

      return {
        content: parsedQuestion.question,
        answer: {
          id: faq.answer?.id,
          content: faq.answer?.content,
          status: ''
        }
      };
    });

    // If the CMS is not used, the provider's FAQs are used.
    items = (mappedFaqs?.length ? mappedFaqs : parsedFaqs) as
      | Faq[]
      | CatalogFaq[];
  } else if (switchable?.field === 'dataCatalog' && switchable?.dataFAQ) {
    try {
      const json = JSON.parse(switchable?.dataFAQ);
      if (!Array.isArray(json?.data)) {
        console.error('Invalid chartData: provided JSON data is not an array');
      } else {
        items = json.data;
      }
    } catch {
      console.error(
        'Invalid chartData: please check your data is a properly formatted JSON Array'
      );
    }
  }

  return items;
}
