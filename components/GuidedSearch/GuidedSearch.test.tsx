import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';

import {
  fireEvent,
  mockGuidedSearch,
  render,
  screen,
  waitFor
} from '~/utils/test-utils';

import GuidedSearch from './GuidedSearch';

describe('GuidedSearch', () => {
  it('should not render component when question data is empty', async () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const { container } = render(
      <GuidedSearch
        inquiryId={''}
        dataSource={mockGuidedSearch.emptyDataSource}
        metadata={mockGuidedSearch.metadata}
      />
    );

    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when json question data is not empty', async () => {
    render(
      <GuidedSearch
        inquiryId={''}
        dataSource={{
          field: mockGuidedSearch.jsonDataSource.field,
          questions: mockGuidedSearch.jsonDataSource.questions
        }}
        metadata={mockGuidedSearch.metadata}
      />
    );
    await waitFor(() => {
      expect(
        screen.getByText('Who are you seeking care for?')
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {
          name: /next/i
        })
      );
    });
  });
  it('should render when manual question data is not empty', async () => {
    render(
      <GuidedSearch
        inquiryId={''}
        dataSource={{
          field: mockGuidedSearch.manualDataSource.field,
          questions: mockGuidedSearch.manualDataSource.questions
        }}
        metadata={mockGuidedSearch.metadata}
      />
    );

    expect(screen.getByText('question-text1')).toBeInTheDocument();
    expect(
      screen.getByRole('button', {
        name: /next/i
      })
    );
  });
  it('should trigger a "Search Step Submission" track event when a step is completed', async () => {
    window.HTMLElement.prototype.scrollIntoView = jest.fn();

    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;

    render(
      <GuidedSearch
        inquiryId={''}
        dataSource={mockGuidedSearch.jsonDataSource}
        metadata={mockGuidedSearch.metadata}
      />
    );

    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i
      })
    );

    expect(mockAnalytics.track).toHaveBeenCalled();
    expect(mockAnalytics.track).toHaveBeenCalledWith('Search Step Submission', {
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID,
      search_step_submission_json:
        '{"search_step_submission":[{"search_template_id":"5412434c-a5c8-414d-a900-0eb2371df38e","search_instance_id":"00000000-0000-0000-0000-000000000000","step_id":"00000000-0000-0000-0000-000000000000","step_instance_id":"00000000-0000-0000-0000-000000000000","step_index":1,"step_content":[{"prompt_id":"00000000-0000-0000-0000-000000000000","prompt_type":"select","prompt_instance_id":"00000000-0000-0000-0000-000000000000","prompt_index":1,"prompt_value":"Who are you seeking care for?","response_array":[{"response_value":"","response_id":"00000000-0000-0000-0000-000000000000"}]}]}]}',
      form_type: ''
    });
  });

  it('should trigger a "Search Submission" track event when the last step is completed', async () => {
    const mockLocation = {
      pathname: '',
      hash: '',
      search: '',
      assign: jest.fn(),
      reload: jest.fn(),
      replace: jest.fn(),
      toString: jest.fn()
    };

    delete window.location;
    window.location = mockLocation;
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;

    render(
      <GuidedSearch
        inquiryId={''}
        dataSource={mockGuidedSearch.jsonDataSourceSingleQuestion}
        metadata={mockGuidedSearch.metadata}
      />
    );

    fireEvent.click(screen.getByLabelText(/self/i));
    fireEvent.click(
      screen.getByRole('button', {
        name: /next/i
      })
    );
    await waitFor(() =>
      expect(
        screen.getByRole('button', { name: /view search results/i })
      ).toBeVisible()
    );
    fireEvent.click(
      screen.getByRole('button', {
        name: /view search results/i
      })
    );

    expect(mockAnalytics.track).toHaveBeenLastCalledWith('Search Submission', {
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID,
      search_submission_json:
        '{"search_submission":[{"search_template_id":"5412434c-a5c8-414d-a900-0eb2371df38e","search_instance_id":"00000000-0000-0000-0000-000000000000","step_submissions":[{"step_id":"00000000-0000-0000-0000-000000000000","step_instance_id":"00000000-0000-0000-0000-000000000000","step_index":1}]}]}',
      form_type: ''
    });
  });
});
