import { Button } from '@chakra-ui/button';
import { Icon } from '@chakra-ui/icon';
import { Flex, Heading } from '@chakra-ui/layout';
import { STRING_TO_ICON_CLASS, StringToIconKeys } from '@components/RenderIcon';
import { icons } from '@utils/careTypeIcons';
import { capitalizeFullSentence } from '@utils/strings';
import { useState } from 'react';

import { useGuidedSearchData } from '~/contexts/GuidedSearchContext';

import GuidedSearchFormResult from './GuidedSearchFormResult';

interface Props {
  modalId?: string;
}

const GuidedSearchBanner: React.FC<Props> = ({ modalId = '' }) => {
  const [showResult, setShowResult] = useState(false);

  const {
    guidedSearchQuestions: questions,
    guidedSearchValues: values,
    guidedSearchMatchingCareType
  } = useGuidedSearchData();

  const getIcon = (iconName?: StringToIconKeys) => {
    if (!iconName) return '';
    return (
      <Icon color="blue.500" as={STRING_TO_ICON_CLASS[iconName]} boxSize={50} />
    );
  };
  const careType = capitalizeFullSentence(
    guidedSearchMatchingCareType?.replace(/-/g, ' ')
  );

  const handleReset = () => {
    setShowResult(false);
  };
  const handleSearchSubmission = () => {
    // Not implemented
    return;
  };
  if (!Boolean(careType)) return <></>;
  return (
    <>
      {questions && values && (
        <GuidedSearchFormResult
          modalId={modalId}
          questions={questions}
          values={values}
          handleReset={handleReset}
          handleSearchSubmission={handleSearchSubmission}
          isOpen={showResult}
          onClose={() => setShowResult(false)}
          displayPersonalizedCare
          displaySearchResults={false}
          displayStartOver={false}
        />
      )}
      <Flex
        bg="info.50"
        h="5.25rem"
        rounded="md"
        p={4}
        justifyContent="space-between"
        alignItems="center"
      >
        <Flex justifyContent="center" alignItems="center">
          {getIcon(icons[guidedSearchMatchingCareType])}

          <Flex direction="column" ml={5}>
            <Heading size="sm">
              {careType} is Your Recommended Care Type
            </Heading>
            {/* <Text size="sm">Description that is short...</Text> */}
          </Flex>
        </Flex>
        <Button
          variant="outline"
          colorScheme="primary"
          onClick={() => setShowResult(true)}
        >
          View Results
        </Button>
      </Flex>
    </>
  );
};

export default GuidedSearchBanner;
