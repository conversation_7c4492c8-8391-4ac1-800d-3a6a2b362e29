import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import { useModalControls } from '~/contexts/ModalContext';

import GuidedSearchFormResult from './GuidedSearchFormResult';

jest.mock('~/contexts/ModalContext');

jest.mock('@hooks/useSearch', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    buildSearchUrl: jest.fn(() => '/mocked/search/url')
  }))
}));

jest.mock('~/contexts/GuidedSearchContext', () => ({
  useGuidedSearchData: () => ({
    setGuidedSearchQuestions: jest.fn(),
    setGuidedSearchValues: jest.fn(),
    setGuidedSearchMatchingCareType: jest.fn()
  })
}));

describe('GuidedSearchFormResult', () => {
  const mockShowInquiryForm = jest.fn();
  const mockProps = {
    modalId: 'test-modal',
    values: {},
    handleReset: jest.fn(),
    handleSearchSubmission: jest.fn(),
    onClose: jest.fn(),
    questions: [],
    isOpen: true,
    displayPersonalizedCare: true,
    displaySearchResults: true,
    displayStartOver: true
  };

  beforeEach(() => {
    (useModalControls as jest.Mock).mockReturnValue({
      show: mockShowInquiryForm
    });
  });

  it('should call showInquiryForm when "Get Personalized Care" button is clicked', () => {
    window.tracking = {
      track: jest.fn()
    };

    render(<GuidedSearchFormResult {...mockProps} />);
    if (mockProps.displayPersonalizedCare) {
      fireEvent.click(
        screen.getByRole('button', { name: /get personalized care/i })
      );
      expect(mockShowInquiryForm).toHaveBeenCalled();
    } else {
      expect(
        screen.queryByRole('button', { name: /get personalized care/i })
      ).not.toBeInTheDocument();
    }
  });
});
