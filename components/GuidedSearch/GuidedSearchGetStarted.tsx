import { Button } from '@chakra-ui/button';
import { Box, Heading, Link, Stack, Text } from '@chakra-ui/layout';
import Image from 'next/image';
import NextLink from 'next/link';

interface Props {
  onGetStarted: () => void;
}

const GuidedSearchGetStarted: React.FC<Props> = ({ onGetStarted }) => {
  return (
    <>
      <Image
        fill
        src={'/images/provider-cta.jpg'}
        role="presentation"
        alt="care type"
        priority
        style={{
          objectFit: 'cover',
          objectPosition: 'center'
        }}
      />

      <Box bg="white" maxW="3xl" p="8" rounded="md" zIndex="overlay">
        <Stack alignItems="center" spacing="2">
          <Heading size="lg">{"Let's begin refining your search"}</Heading>
          <Text align="center">
            We&apos;ll ask you a few more questions that will help you narrow
            down the search. If you need additional help, our Family Advisors
            and standing by to support you.
          </Text>
          <Stack spacing="4" pt="4">
            <Button colorScheme="primary" onClick={onGetStarted}>
              Get Started
            </Button>
            <NextLink href="/search" passHref>
              <Link variant="link" color="primary.600">
                or search listings
              </Link>
            </NextLink>
          </Stack>
        </Stack>
      </Box>
    </>
  );
};

export default GuidedSearchGetStarted;
