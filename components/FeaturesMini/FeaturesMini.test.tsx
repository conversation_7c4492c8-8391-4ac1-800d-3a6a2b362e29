import { render, screen } from '@utils/test-utils';

import FeaturesMini, { FeaturesMiniProps } from './FeaturesMini';

const mockFeaturesMini = {
  title: 'Everything you need for your search',
  headingElement: 'h2',
  description:
    'We work to ensure you are well-equipped to make the best decision for you and your loved ones.',
  titleAlignment: 'center',
  titleColor: {
    range: '900',
    color: 'black'
  },
  features: {
    features0: {
      iconAlignment: 'left',
      description:
        '<p>Get comprehensive and up-to-date information for senior living and care providers.</p>\n',
      icon: 'MdSearch',
      title: 'Detailed costs and photos',
      featureAlignment: 'left',
      iconColor: {
        range: '600',
        color: 'primary'
      },
      featureColor: {
        range: '900',
        color: 'black'
      }
    },
    features1: {
      iconAlignment: 'left',
      description:
        '<p>View your options all in one place by using our free senior care directory, with listings across the U.S.</p>\n',
      icon: 'MdChecklistRtl',
      title: 'Thousands of listings',
      featureAlignment: 'left',
      iconColor: {
        range: '600',
        color: 'primary'
      },
      featureColor: {
        range: '900',
        color: 'black'
      }
    },
    '@nodes': ['features0', 'features1']
  }
} as FeaturesMiniProps;

describe('FeaturesMini', () => {
  test('renders the component with given props', () => {
    const { getByRole } = render(<FeaturesMini {...mockFeaturesMini} />);

    expect(getByRole('heading')).toHaveTextContent(
      'Everything you need for your search'
    );
    expect(
      screen.getByText(
        'We work to ensure you are well-equipped to make the best decision for you and your loved ones.'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'Get comprehensive and up-to-date information for senior living and care providers.'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'View your options all in one place by using our free senior care directory, with listings across the U.S.'
      )
    ).toBeInTheDocument();
  });
});
