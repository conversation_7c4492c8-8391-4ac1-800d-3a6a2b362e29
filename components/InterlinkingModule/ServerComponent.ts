import { getOtherCareTypesBySameLocale } from '@services/interlinking/api';
import { strip } from '@utils/parser';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { Catalog } from '~/types/LocaleCatalog';

import { InterlinkingModuleProps } from './InterlinkingModule';

const fetchNearbyOtherCareTypeSameLocaleFromCatalog = async (
  { careType, state, city, county },
  context: GetServerSidePropsContext
) => {
  let results: { [key: string]: Array<Catalog> } = {};
  if (!city && !county && state) {
    const states = await getOtherCareTypesBySameLocale({
      context,
      state,
      careType
    });
    results = {
      ['state']: states
    };
  }
  if (!city && county && state) {
    const counties = await getOtherCareTypesBySameLocale({
      context,
      state,
      county,
      careType
    });
    results = {
      ['county']: counties
    };
  }
  if (city && state) {
    const cities = await getOtherCareTypesBySameLocale({
      context,
      state,
      city,
      careType
    });

    results = {
      ['city']: cities
    };
  }

  return results;
};

export const getServerSideComponentProps = async (
  { switchable: switchable }: InterlinkingModuleProps,
  context: GetServerSidePropsContext
): Promise<{ [key: string]: Array<Catalog> }> => {
  if (switchable?.field === 'catalogData') {
    const {
      city = '',
      state = '',
      county = '',
      careType = ''
    } = switchable || {};
    const alteredCounty = strip(county?.replace('County', '').trim());
    const alteredCity = strip(city);
    const alteredState = strip(state);
    const alteredCareType = strip(
      isObject(careType) ? careType.name : careType
    );
    const result = await fetchNearbyOtherCareTypeSameLocaleFromCatalog(
      {
        careType: alteredCareType,
        state: alteredState,
        city: alteredCity,
        county: alteredCounty
      },
      context
    );
    return result;
  }
  return {};
};
