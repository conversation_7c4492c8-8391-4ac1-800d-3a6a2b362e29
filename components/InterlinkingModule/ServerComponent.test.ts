import { getOtherCareTypesBySameLocale } from '@services/interlinking/api';
import { GetServerSidePropsContext } from 'next';

import { HeadingSizes } from '~/@types/heading';

import { InterlinkingModuleProps } from './InterlinkingModule';
import { getServerSideComponentProps } from './ServerComponent';

jest.mock('@services/interlinking/api', () => ({
  getOtherCareTypesBySameLocale: jest.fn()
}));

describe('InterlinkingModule ServerComponent', () => {
  const mockContext = {
    req: { headers: { host: 'caring.com' } },
    resolvedUrl: '/test'
  } as unknown as GetServerSidePropsContext;

  const baseProps = {
    title: 'Test Title',
    titleSize: 'lg' as HeadingSizes,
    noHorizontalPadding: false,
    columns: '1',
    bgColor: 'white',
    bgColorRange: '100',
    titleColor: 'black',
    titleColorRange: '900',
    linksColor: 'blue',
    linksColorRange: '500',
    linksTextDecoration: 'none'
  };

  const excludeCareTypes = {
    'continuing-care': false,
    'assisted-living': false,
    'memory-care': false,
    'retirement-communities': false,
    'independent-living': false,
    'nursing-homes': false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getServerSideComponentProps', () => {
    it('should return empty object when switchable is not provided', async () => {
      const props = {
        ...baseProps,
        switchable: undefined
      };

      const result = await getServerSideComponentProps(
        props as Partial<InterlinkingModuleProps> as InterlinkingModuleProps,
        mockContext
      );
      expect(result).toEqual({});
    });

    it('should return empty object when switchable.field is not catalogData', async () => {
      const props = {
        ...baseProps,
        switchable: {
          field: 'otherField' as any,
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);
      expect(result).toEqual({});
    });

    it('should fetch state data when only state is provided', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' },
        { careType: 'memory-care', urlPath: '/path/to/memory-care' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        ...baseProps,
        switchable: {
          field: 'catalogData',
          state: 'California',
          careType: 'nursing-homes',
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'California',
        careType: 'nursing-homes'
      });

      expect(result).toEqual({
        state: mockResults
      });
    });

    it('should fetch county data when state and county are provided', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' },
        { careType: 'memory-care', urlPath: '/path/to/memory-care' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        ...baseProps,
        switchable: {
          field: 'catalogData',
          state: 'California',
          county: 'Los Angeles County',
          careType: 'nursing-homes',
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'California',
        county: 'Los Angeles',
        careType: 'nursing-homes'
      });

      expect(result).toEqual({
        county: mockResults
      });
    });

    it('should fetch city data when state and city are provided', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' },
        { careType: 'memory-care', urlPath: '/path/to/memory-care' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        ...baseProps,
        switchable: {
          field: 'catalogData',
          state: 'California',
          city: 'San Francisco',
          careType: 'nursing-homes',
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'California',
        city: 'San Francisco',
        careType: 'nursing-homes'
      });

      expect(result).toEqual({
        city: mockResults
      });
    });

    it('should handle object careType properly', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        ...baseProps,
        switchable: {
          field: 'catalogData',
          state: 'California',
          city: 'San Francisco',
          careType: { name: 'nursing-homes', display: 'Nursing Homes' },
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'California',
        city: 'San Francisco',
        careType: 'nursing-homes'
      });

      expect(result).toEqual({
        city: mockResults
      });
    });

    it('should prioritize city over county when both are provided', async () => {
      const mockResults = [
        { careType: 'assisted-living', urlPath: '/path/to/assisted-living' }
      ];

      (getOtherCareTypesBySameLocale as jest.Mock).mockResolvedValue(
        mockResults
      );

      const props = {
        ...baseProps,
        switchable: {
          field: 'catalogData',
          state: 'California',
          city: 'San Francisco',
          county: 'San Francisco County',
          careType: 'nursing-homes',
          excludeCareTypes
        }
      } as InterlinkingModuleProps;

      const result = await getServerSideComponentProps(props, mockContext);

      expect(getOtherCareTypesBySameLocale).toHaveBeenCalledWith({
        context: mockContext,
        state: 'California',
        city: 'San Francisco',
        careType: 'nursing-homes'
      });

      expect(result).toEqual({
        city: mockResults
      });
    });
  });
});
