import { GridProps, SimpleGrid } from '@chakra-ui/layout';
import { chakra, ChakraStyledOptions } from '@chakra-ui/react';

const SiteStyles = (domain): ChakraStyledOptions => {
  const baseStyle: GridProps = {
    gap: 2
  };
  switch (domain) {
    case 'seniorhomes.com':
      return {
        baseStyle: {
          ...baseStyle,
          paddingTop: 12
        }
      };
    case 'caring.com':
      return {
        baseStyle: {
          ...baseStyle,
          paddingTop: 9,
          gap: 4
        }
      };
    default:
      return baseStyle;
  }
};

const StyledInterlinkingModuleLinks = (domain): typeof SimpleGrid => {
  return chakra(SimpleGrid, { ...SiteStyles(domain) });
};
export default StyledInterlinkingModuleLinks;
