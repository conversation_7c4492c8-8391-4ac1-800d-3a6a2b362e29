import { toCapitalizedWords } from '@utils/strings';
import { mockInterlinkingModule, render, screen } from '@utils/test-utils';
import { getStateAbbreviation } from '@utils/UsStates';
import startCase from 'lodash/startCase';

import InterlinkingModule from './index';

const generateText = (state, careType) =>
  `${toCapitalizedWords(state)} ${toCapitalizedWords(careType)}`;
const generateText2 = (state, careType, city) =>
  `${toCapitalizedWords(careType)} in ${toCapitalizedWords(
    city
  )}, ${getStateAbbreviation(state)}`;
const generateText3 = (state, careType, county) =>
  `${toCapitalizedWords(careType)} in ${toCapitalizedWords(
    county
  )}, ${getStateAbbreviation(state)}`;

describe('InterlinkingModule', () => {
  it('should not render component when data is empty', async () => {
    const { container } = render(
      <InterlinkingModule data={mockInterlinkingModule.emptyData} />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when states exists and field is catalogData', async () => {
    const { container } = render(
      <InterlinkingModule
        data={mockInterlinkingModule.dataStates}
        bgColor="white"
        switchable={mockInterlinkingModule.switchable}
      />
    );
    mockInterlinkingModule.dataStates.state.forEach((e) => {
      expect(
        screen.getByText(startCase(generateText(e.state, e.careType)))
      ).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: generateText(e.state, e.careType) })
      ).toHaveAttribute('href', `${e.urlPath}/`);
    });
    expect(container.children).toHaveLength(2);
  });

  it('should render when cities exists and field is catalogData', async () => {
    const { container } = render(
      <InterlinkingModule
        data={mockInterlinkingModule.dataCity}
        bgColor="Black"
        switchable={mockInterlinkingModule.switchable}
      />
    );
    mockInterlinkingModule.dataCity.city.forEach((e) => {
      expect(
        screen.getByText(generateText2(e.state, e.careType, e.city))
      ).toBeVisible();
      expect(
        screen.getByRole('link', {
          name: generateText2(e.state, e.careType, e.city)
        })
      ).toHaveAttribute('href', `${e.urlPath}/`);
    });
    expect(container.children).toHaveLength(2);
  });

  it('should render when county exists and field is catalogData', async () => {
    const { container } = render(
      <InterlinkingModule
        data={mockInterlinkingModule.dataCounty}
        bgColor="none"
        switchable={mockInterlinkingModule.switchable}
      />
    );
    mockInterlinkingModule.dataCounty.county.forEach((e) => {
      expect(
        screen.getByText(generateText2(e.state, e.careType, e.county))
      ).toBeVisible();
      expect(
        screen.getByRole('link', {
          name: generateText2(e.state, e.careType, e.county)
        })
      ).toHaveAttribute('href', `${e.urlPath}/`);
    });
    expect(container.children).toHaveLength(2);
  });

  it('should render jsonData', async () => {
    const { container } = render(
      <InterlinkingModule
        jsonData={mockInterlinkingModule.jsonData}
        bgColor="white"
      />
    );
    JSON.parse(mockInterlinkingModule.jsonData).data.forEach((e) => {
      expect(screen.getByText(e.text)).toBeInTheDocument();
      expect(screen.getByText(e.text)).toBeVisible;
      expect(screen.getByRole('link', { name: e.text })).toHaveAttribute(
        'href',
        `${e.href}`
      );
    });
    expect(container.children).toHaveLength(2);
  });

  it('should render jsonData from switchable and field is jsonData', async () => {
    const { container } = render(
      <InterlinkingModule
        jsonData={mockInterlinkingModule.switchableJson.jsonData}
        bgColor="white"
        switchable={mockInterlinkingModule.switchableJson}
      />
    );
    JSON.parse(mockInterlinkingModule.switchableJson.jsonData).data.forEach(
      (e) => {
        expect(screen.getByText(e.text)).toBeInTheDocument();
        expect(screen.getByText(e.text)).toBeVisible;
        expect(screen.getByRole('link', { name: e.text })).toHaveAttribute(
          'href',
          `${e.href}`
        );
      }
    );
    expect(container.children).toHaveLength(2);
  });

  it('should not render if jsonData.data is not an array', async () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const { container } = render(
      <InterlinkingModule
        jsonData={mockInterlinkingModule.jsonDataError}
        bgColor="white"
        switchable={mockInterlinkingModule.switchableJson}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render if jsonData is not formatted JSON Array', async () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const { container } = render(
      <InterlinkingModule
        jsonData={mockInterlinkingModule.jsonDataString}
        bgColor="white"
        switchable={mockInterlinkingModule.switchableJson}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render if switchable.jsonData.data is not an array', async () => {
    const { container } = render(
      <InterlinkingModule
        bgColor="white"
        switchable={mockInterlinkingModule.switchableJsonError}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render if switchable.jsonData does not have a valid format', async () => {
    const { container } = render(
      <InterlinkingModule
        bgColor="white"
        switchable={mockInterlinkingModule.switchableJsonString}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });
});
