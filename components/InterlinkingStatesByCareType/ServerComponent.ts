import { getStatesFilteredByCaretype } from '@services/graphql/catalog-nearby-geos';
import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
import { strip } from '@utils/parser';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';
import { Catalog } from '~/types/LocaleCatalog';

import { InterlinkingStatesByCareTypeProps } from './InterlinkingStatesByCareType';

const fetchCareTypeDataFromCatalog = async ({ site, careType }) => {
  const results = await getStatesFilteredByCaretype({
    careType,
    domain: site.path
  });

  return results;
};

export const getServerSideComponentProps = async (
  { careType }: InterlinkingStatesByCareTypeProps,
  context: GetServerSidePropsContext
): Promise<Array<Catalog>> => {
  const params = context.params || {};
  const careTypeFromURL = getCareTypeFromURL(params);
  const site = findSiteForContext(context);
  const alteredCareType =
    strip(isObject(careType) ? careType.name : careType) || careTypeFromURL;

  const result = await fetchCareTypeDataFromCatalog({
    site,
    careType: alteredCareType
  });
  return result;
};
