import { toCapitalizedWords } from '@utils/strings';
import {
  mockInterlinkingStatesByCareType,
  render,
  screen
} from '@utils/test-utils';
import startCase from 'lodash/startCase';

import InterlinkingStatesByCareType from './index';

describe('InterlinkingStatesByCareType', () => {
  it('should not render component when data is empty', async () => {
    const { container } = render(
      <InterlinkingStatesByCareType
        data={mockInterlinkingStatesByCareType.emptyData}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when data is not empty', async () => {
    const { container } = render(
      <InterlinkingStatesByCareType
        bgColor="white"
        data={mockInterlinkingStatesByCareType.data}
        careType={mockInterlinkingStatesByCareType.careType}
      />
    );
    mockInterlinkingStatesByCareType.data.forEach((e) => {
      expect(
        screen.getByText(startCase(toCapitalizedWords(e.state)))
      ).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: toCapitalizedWords(e.state) })
      ).toHaveAttribute('href', `${e.urlPath}`);
    });
    expect(container.children).toHaveLength(2);
  });
});
