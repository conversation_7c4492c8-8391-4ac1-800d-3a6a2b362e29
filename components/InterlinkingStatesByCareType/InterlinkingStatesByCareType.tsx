import InterlinkingCard from '@components/Interlinking';
import { InterlinkingProps } from '@components/Interlinking/Interlinking';
import { getBackgroundColor } from '@utils/getColor';

export type InterlinkingStatesByCareTypeProps = InterlinkingProps & {
  careType: string | { name: string };
  data: string[];
};

const InterlinkingStatesByCareType = ({
  title,
  titleSize,
  titleAlignment = 'left',
  headingElement = 'h2',
  columns = '1',
  data,
  bgColor,
  bgColorRange,
  titleColor = 'primary',
  titleColorRange = '700',
  linksColor = 'link',
  linksColorRange = '600',
  linksTextDecoration = 'none'
}: InterlinkingStatesByCareTypeProps): React.ReactElement => {
  const backgroundColor = getBackgroundColor(bgColor, bgColorRange);

  return data && data?.length > 0 ? (
    <InterlinkingCard>
      <InterlinkingCard.Container bgColor={backgroundColor}>
        {title && (
          <InterlinkingCard.Heading
            title={title}
            titleSize={titleSize}
            titleColor={titleColor}
            titleAlignment={titleAlignment}
            headingElement={headingElement}
            titleColorRange={titleColorRange}
            paddingBottom="1"
          />
        )}
        <InterlinkingCard.ListContainer
          align="stretch"
          borderRadius={12}
          backgroundColor={backgroundColor}
        >
          <InterlinkingCard.LinkGrid columns={parseInt(columns)}>
            <InterlinkingCard.LinksList
              data={data}
              type="state"
              linksColor={linksColor}
              linksColorRange={linksColorRange}
              linksTextDecoration={linksTextDecoration}
            />
          </InterlinkingCard.LinkGrid>
        </InterlinkingCard.ListContainer>
      </InterlinkingCard.Container>
    </InterlinkingCard>
  ) : (
    <></>
  );
};

export default InterlinkingStatesByCareType;
