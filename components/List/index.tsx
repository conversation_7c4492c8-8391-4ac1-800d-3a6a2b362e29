import { UnorderedList } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
import { EditableArea } from '@magnolia/react-editor';

import { Metadata } from '~/types/Magnolia';

interface ListProps extends Metadata {
  '@name': string;
  items: Array<string>;
  metadata: Metadata;
}

const List = (props: ListProps): React.ReactElement => {
  const { items, metadata } = props;
  return (
    <Container>
      <UnorderedList>
        <EditableArea
          content={items}
          parentTemplateId={metadata['mgnl:template']}
        />
      </UnorderedList>
    </Container>
  );
};

export default List;
