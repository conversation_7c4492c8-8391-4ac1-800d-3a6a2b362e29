import {
  render,
  screen,
  setDesktopScreen,
  setMobileScreen
} from '@utils/test-utils';

import ImageTestimonial, { ImageTestimonialProps } from './ImageTestimonial';

describe('ImageTestimonial', () => {
  const quote = {
    text: 'Test quote',
    color: { color: 'primary', range: '500' }
  };

  const author = {
    name: 'Test author',
    description: 'Test description',
    color: { color: 'primary', range: '500' }
  };

  const backgroundImage = {
    image: {
      '@id': '123',
      '@path': 'path'
    },
    imageUrl: 'test-image-url',
    bgImageOverlay: 'default',
    bgOverlayOpacity: '0.5',
    bgOverlayGradient: 'solid',
    bgImagePosition: 'center',
    bgBorderRadius: '10px',
    field: 'bgImageForm'
  } as ImageTestimonialProps['backgroundImage'];

  const altImageProperties = {
    bgImageOverlay: 'dark',
    bgOverlayOpacity: '0.8',
    bgOverlayGradient: 'left',
    bgImagePosition: 'top',
    bgBorderRadius: '5px',
    viewports: ['Mobile', 'Tablet']
  } as ImageTestimonialProps['altImageProperties'];

  it('renders the quote and author information', () => {
    render(
      <ImageTestimonial
        quote={quote}
        author={author}
        backgroundImage={backgroundImage}
        altImageProperties={altImageProperties}
      />
    );

    expect(screen.getByText('Test quote')).toBeInTheDocument();
    expect(screen.getByText('Test author')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('renders the background image with correct props', () => {
    setDesktopScreen();
    let { container } = render(
      <ImageTestimonial
        quote={quote}
        author={author}
        backgroundImage={backgroundImage}
        altImageProperties={altImageProperties}
      />
    );
    expect(container.firstElementChild).toHaveStyle(`
      background-image: url(${process.env.NEXT_PUBLIC_MGNL_HOST}/dam/123path);
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      border-radius: 10px;
    `);
  });

  it('renders the alternative image properties for different viewports', () => {
    setMobileScreen();
    let { container } = render(
      <ImageTestimonial
        quote={quote}
        author={author}
        backgroundImage={backgroundImage}
        altImageProperties={altImageProperties}
      />
    );

    expect(container.firstElementChild).toHaveStyle(`
      background-image: url(${process.env.NEXT_PUBLIC_MGNL_HOST}/dam/123path);
      background-position: top;
      background-repeat: no-repeat;
      background-size: cover;
      border-radius: 5px;
    `);
  });
});
