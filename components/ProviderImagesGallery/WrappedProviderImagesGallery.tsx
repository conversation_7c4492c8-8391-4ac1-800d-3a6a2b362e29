'use client';
import { Box } from '@chakra-ui/layout';
import InquiryForm from '@components/InquiryForm';
import { InquiryFormProps } from '@components/InquiryForm/InquiryForm';
import { Display } from '@components/InquiryForm/InquiryForm.types';
import Container from '@components/LayoutStructure/Container';
import CaringPhotoGallery from '@components/PhotoGallery/CaringPhotoGallery';
import PhotoGalleryModal from '@components/PhotoGallery/PhotoGalleryModal';
import SeniorHomesPhotoGallery from '@components/PhotoGallery/SeniorHomesPhotoGallery';
import StreetViewModal from '@components/PhotoGallery/StreetViewModal';
import { useProviderFallback } from '@hooks/use-provider-fallback';
import { getColor } from '@utils/getColor';
import { getRandomProviderPic } from '@utils/getRandomProviderPic';
import Image from 'next/image';
import { useContext, useState } from 'react';

import { HeadingElements } from '~/@types/heading';
import SiteContext from '~/contexts/SiteContext';
import { Metadata } from '~/types/Magnolia';

import styles from './WrappedProviderImagesGallery.module.css';

export interface OpenInquiryForm
  extends Omit<InquiryFormProps, 'metadata' | 'formId' | 'display'> {
  field: 'true' | 'false';
}

export interface PhotoGalleryProps {
  providerId: string;
  openInquiryForm?: OpenInquiryForm;
  heading?: string;
  headingElement?: HeadingElements;
  marginBottom?: string;
  metadata: Metadata;
  clickableOverlayColor: {
    color: string;
    range: string;
  };
}

interface GalleryProps {
  providerPhotos:
    | {
        url: string;
      }[]
    | {
        url: string;
        name: string;
      }[];
  providerName: string;
  providerCoordinates: { latitude: number; longitude: number };
  domain: string;
  clickableOverlayColor: string;
  desktopSidebar?: React.ReactNode;
  isStreetViewModalOpen: boolean;
  setIsStreetViewModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  galleryIndex: number | null;
  setGalleryIndex: React.Dispatch<React.SetStateAction<null | number>>;
}

const Gallery = ({
  providerPhotos,
  providerName,
  providerCoordinates,
  domain,
  desktopSidebar,
  isStreetViewModalOpen,
  setIsStreetViewModalOpen,
  galleryIndex,
  setGalleryIndex
}: GalleryProps) => {
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <>
          <SeniorHomesPhotoGallery
            photos={providerPhotos.map((image) => ({
              url: image.url,
              alt: providerName
            }))}
            setGalleryIndex={setGalleryIndex}
          />
          <PhotoGalleryModal
            photos={providerPhotos.map((image) => ({
              url: image.url,
              alt: providerName
            }))}
            modalSidebar={desktopSidebar}
            galleryIndex={galleryIndex}
            setGalleryIndex={setGalleryIndex}
          />
        </>
      );
    case 'caring.com':
      return (
        <>
          <CaringPhotoGallery
            photos={providerPhotos.map((image) => ({
              url: image.url,
              alt: providerName
            }))}
            setIsStreetViewModalOpen={setIsStreetViewModalOpen}
            setGalleryIndex={setGalleryIndex}
          />
          <PhotoGalleryModal
            photos={providerPhotos.map((image) => ({
              url: image.url,
              alt: providerName
            }))}
            modalSidebar={desktopSidebar}
            galleryIndex={galleryIndex}
            setGalleryIndex={setGalleryIndex}
          />
          <StreetViewModal
            coordinates={providerCoordinates}
            modalSidebar={desktopSidebar}
            isStreetViewModalOpen={isStreetViewModalOpen}
            setIsStreetViewModalOpen={setIsStreetViewModalOpen}
          />
        </>
      );
    default:
      return (
        <Box height="md">
          <Image
            src={providerPhotos[0].url}
            alt={providerName}
            priority
            tabIndex={0}
            fill
            className={styles.defaultPhoto}
          />
        </Box>
      );
  }
};

const WrappedProviderImagesGallery = ({
  providerId,
  openInquiryForm = {} as OpenInquiryForm,
  marginBottom,
  metadata,
  clickableOverlayColor = { color: 'primary', range: '600' }
}: PhotoGalleryProps): React.ReactElement => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const [isStreetViewModalOpen, setIsStreetViewModalOpen] = useState(false);
  const [galleryIndex, setGalleryIndex] = useState<number | null>(null);
  const { provider } = useProviderFallback(providerId);
  const color = getColor(
    clickableOverlayColor.color,
    clickableOverlayColor.range
  );

  if (!provider) {
    return <></>;
  }

  const fallbackImage = getRandomProviderPic(
    `${provider.name}${provider.slug}`
  );

  const providerPhotos = provider?.hasImages
    ? provider.photos
    : [{ url: fallbackImage, name: provider?.name || 'Provider' }];

  return (
    <Container
      position="relative"
      mb={marginBottom ? `${marginBottom} !important` : undefined}
    >
      <Gallery
        providerPhotos={providerPhotos ?? []}
        providerName={provider?.name || 'Provider'}
        providerCoordinates={{
          latitude: provider?.address?.latitude ?? 0,
          longitude: provider?.address?.longitude ?? 0
        }}
        domain={domain}
        desktopSidebar={
          openInquiryForm.field === 'true' && (
            <InquiryForm
              formId={metadata['@id']}
              display={Display.VERTICAL}
              height="100vh"
              metadata={metadata}
              {...openInquiryForm}
            />
          )
        }
        clickableOverlayColor={color}
        isStreetViewModalOpen={isStreetViewModalOpen}
        setIsStreetViewModalOpen={setIsStreetViewModalOpen}
        galleryIndex={galleryIndex}
        setGalleryIndex={setGalleryIndex}
      />
    </Container>
  );
};

export default WrappedProviderImagesGallery;
