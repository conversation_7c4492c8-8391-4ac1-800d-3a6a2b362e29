import WrappedProviderImagesGallery, {
  OpenInquiryForm,
  PhotoGalleryProps
} from './WrappedProviderImagesGallery';

// Server-side component that renders the client-side provider photo gallery
const ProviderImagesGallery = ({
  providerId,
  openInquiryForm = {} as OpenInquiryForm,
  marginBottom,
  metadata,
  clickableOverlayColor = { color: 'primary', range: '600' }
}: PhotoGalleryProps): React.ReactElement => {
  return (
    <WrappedProviderImagesGallery
      providerId={providerId}
      openInquiryForm={openInquiryForm}
      marginBottom={marginBottom}
      metadata={metadata}
      clickableOverlayColor={clickableOverlayColor}
    />
  );
};

export default ProviderImagesGallery;
