import { useProviderFallback } from '@hooks/use-provider-fallback';
import { render, screen, setDesktopScreen, waitFor } from '@utils/test-utils';
import { mockProviderV2 } from '@utils/test-utils/mocks/provider';
import { act } from 'react';

import ProviderContext from '~/contexts/Provider';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { CaringDomains, SeniorHomesDomains } from '~/types/Domains';
import { Metadata } from '~/types/Magnolia';

import ProviderImagesGallery from './ProviderImagesGallery';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {}
  }))
}));

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams(''))
}));

jest.mock('@hooks/use-provider-fallback');

const mockUseProviderFallback = useProviderFallback as jest.MockedFunction<
  typeof useProviderFallback
>;

const fakeSite: SiteContextType = {
  site: {
    name: 'Caring.com',
    domains: [CaringDomains.LIVE],
    path: CaringDomains.LIVE,
    segmentWriteKey: 'key',
    segmentCdnURL: 'cdn',
    partnerToken: 'token',
    publicFolder: 'caring_public',
    storyPaths: []
  }
};
const ContextWrapper = ({ children }) => (
  <ProviderContext.Provider
    value={{ setProvider: () => {}, provider: mockProviderV2 }}
  >
    <SiteContext.Provider value={fakeSite}>{children}</SiteContext.Provider>
  </ProviderContext.Provider>
);

describe('ProviderImagesGallery', () => {
  it('should not render when provider context errors', async () => {
    setDesktopScreen();
    mockUseProviderFallback.mockReturnValue({
      provider: mockProviderV2,
      status: 3
    });
    render(
      <ContextWrapper>
        <ProviderImagesGallery
          providerId=""
          metadata={{} as Metadata}
          clickableOverlayColor={{ color: 'primary', range: '600' }}
        />
      </ContextWrapper>
    );
    expect(screen.queryByText('Loading...')).toBeFalsy();
  });

  it('should render gallery component on desktop', async () => {
    setDesktopScreen();
    mockUseProviderFallback.mockReturnValue({
      provider: mockProviderV2,
      status: 2
    });
    await act(async () => {
      render(
        <ContextWrapper>
          <ProviderImagesGallery
            providerId="test-id"
            metadata={{} as Metadata}
            clickableOverlayColor={{ color: 'primary', range: '600' }}
          />
        </ContextWrapper>
      );
    });
    await waitFor(() => {
      const images = document.querySelectorAll('img');
      expect(screen.getByText('Show all photos')).toBeInTheDocument();
      // 6 images (5 photos + 1 street view)
      expect(images).toHaveLength(6);
    });
  });

  const morePhotos = [
    { url: '/photo1.jpg', alt: 'Photo 1' },
    { url: '/photo2.jpg', alt: 'Photo 2' },
    { url: '/photo3.jpg', alt: 'Photo 3' },
    { url: '/photo4.jpg', alt: 'Photo 4' },
    { url: '/photo5.jpg', alt: 'Photo 5' }
  ];

  it('should render up to 4 photos plus street view and show all for caring.com', async () => {
    setDesktopScreen();
    mockUseProviderFallback.mockReturnValue({
      provider: { ...mockProviderV2, photos: morePhotos },
      status: 2
    });
    await act(async () => {
      render(
        <ContextWrapper>
          <ProviderImagesGallery
            providerId="test-id"
            metadata={{} as Metadata}
            clickableOverlayColor={{ color: 'primary', range: '600' }}
          />
        </ContextWrapper>
      );
    });
    await waitFor(async () => {
      const images = document.querySelectorAll('img');
      // 4 photos + 1 street view image + 1 show all photos button
      expect(images).toHaveLength(6);
      // Alt text will be the same for all images
      expect(screen.queryAllByAltText('Test Provider')).toHaveLength(5);
      expect(screen.getByAltText('streetview')).toBeInTheDocument();
      expect(screen.getByText('Show all photos')).toBeInTheDocument();
    });
  });

  it('should render correctly for seniorhomes.com domain', async () => {
    setDesktopScreen();
    const seniorHomesSite = {
      site: {
        name: 'SeniorHomes.com',
        domains: ['seniorhomes.com'],
        path: SeniorHomesDomains.LIVE,
        segmentWriteKey: 'key',
        segmentCdnURL: 'cdn',
        partnerToken: 'token',
        publicFolder: 'caring_public',
        storyPaths: [],
        photos: morePhotos
      }
    };
    mockUseProviderFallback.mockReturnValue({
      provider: { ...mockProviderV2, photos: morePhotos },
      status: 2
    });
    await act(async () => {
      render(
        <ProviderContext.Provider
          value={{
            setProvider: () => {},
            provider: { ...mockProviderV2, photos: morePhotos }
          }}
        >
          <SiteContext.Provider value={seniorHomesSite}>
            <ProviderImagesGallery
              providerId="test-id"
              metadata={{} as Metadata}
              clickableOverlayColor={{ color: 'primary', range: '600' }}
            />
          </SiteContext.Provider>
        </ProviderContext.Provider>
      );
    });
    await waitFor(() => {
      const images = document.querySelectorAll('img');
      // 4 photos (no street view for seniorhomes.com)
      expect(images).toHaveLength(4);
      // Alt text will be the same for all images
      expect(screen.queryAllByAltText('Test Provider')).toHaveLength(4);
      expect(screen.queryByAltText('streetview')).not.toBeInTheDocument();
    });
  });
});
