import { useProviderFallback } from '@hooks/use-provider-fallback';
import { render } from '@testing-library/react';
import { mockProviderV2 } from '@utils/test-utils/mocks/provider';
import { useRouter } from 'next/router';
import { act } from 'react';

import ProviderContext from '~/contexts/Provider';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';
import { Metadata } from '~/types/Magnolia';

import WrappedProviderImagesGallery from './WrappedProviderImagesGallery';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams(''))
}));

jest.mock('@hooks/use-provider-fallback');

const mockUseProviderFallback = useProviderFallback as jest.MockedFunction<
  typeof useProviderFallback
>;

const fakeSite: SiteContextType = {
  site: {
    name: 'Caring.com',
    domains: [CaringDomains.LIVE],
    path: CaringDomains.LIVE,
    segmentWriteKey: 'key',
    segmentCdnURL: 'cdn',
    partnerToken: 'token',
    publicFolder: 'caring_public',
    storyPaths: []
  }
};

const ContextWrapper = ({ children }) => (
  <ProviderContext.Provider
    value={{ setProvider: () => {}, provider: mockProviderV2 }}
  >
    <SiteContext.Provider value={fakeSite}>{children}</SiteContext.Provider>
  </ProviderContext.Provider>
);

describe('WrappedProviderImagesGallery', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      query: {}
    });

    mockUseProviderFallback.mockReturnValue({
      provider: mockProviderV2,
      status: 2
    });
  });

  it('should render', () => {
    act(() => {
      render(
        <ContextWrapper>
          <WrappedProviderImagesGallery
            providerId="test-id"
            metadata={{} as Metadata}
            clickableOverlayColor={{ color: 'primary', range: '600' }}
          />
        </ContextWrapper>
      );
    });
    expect(document.querySelectorAll('img')).toHaveLength(5);
  });
});
