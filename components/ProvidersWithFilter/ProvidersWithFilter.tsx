import CheckboxInput from '@components/CheckboxInput';

interface ProvidersWithFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const providersWithFilterItems = [
  {
    value: '5-plus-photos',
    label: '5+ Photos'
  }
];

function ProvidersWithFilter({ onChange, value }: ProvidersWithFilterProps) {
  return (
    <CheckboxInput
      name="providersWith"
      onChange={onChange}
      items={providersWithFilterItems}
      value={value}
    />
  );
}

export default ProvidersWithFilter;
