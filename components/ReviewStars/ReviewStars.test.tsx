import ReviewStars from '@components/ReviewStars/ReviewStars';
import { render, screen } from '@utils/test-utils';

describe('ReviewStarts', () => {
  it('should render correctly half stars when 4.5', () => {
    render(<ReviewStars rating={4.5} />);
    expect(screen.getAllByLabelText('full star').length).toBe(4);
    expect(screen.getAllByLabelText('half star').length).toBe(1);
    expect(screen.queryByLabelText('empty star')).not.toBeInTheDocument();
  });

  it('should render correctly half stars when 4.44', () => {
    render(<ReviewStars rating={4.44} />);
    expect(screen.getAllByLabelText('full star').length).toBe(4);
    expect(screen.getAllByLabelText('half star').length).toBe(1);
    expect(screen.queryByLabelText('empty star')).not.toBeInTheDocument();
  });

  it('should render only full stars when 4.3', () => {
    render(<ReviewStars rating={4.3} />);
    expect(screen.getAllByLabelText('full star').length).toBe(4);
    expect(screen.getAllByLabelText('empty star').length).toBe(1);
    expect(screen.queryByLabelText('half star')).not.toBeInTheDocument();
  });

  it('should render correctly full stars', () => {
    render(<ReviewStars rating={4} />);
    expect(screen.getAllByLabelText('full star').length).toBe(4);
    expect(screen.getAllByLabelText('empty star').length).toBe(1);
    expect(screen.queryByLabelText('half star')).not.toBeInTheDocument();
  });

  it('should render correctly empty stars', () => {
    render(<ReviewStars rating={0} />);
    expect(screen.getAllByLabelText('empty star').length).toBe(5);
    expect(screen.queryByLabelText('half star')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('full star')).not.toBeInTheDocument();
  });
});
