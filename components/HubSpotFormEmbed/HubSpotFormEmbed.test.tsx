import { render, screen } from '@utils/test-utils';
import React from 'react';

import HubSpotFormEmbed from './HubSpotFormEmbed';

jest.mock('next/script', () => (props) => {
  const { onLoad, onError } = props;
  React.useEffect(() => {
    if (onLoad) onLoad();
  }, [onLoad]);

  return null;
});

beforeAll(() => {
  window.hbspt = {
    forms: {
      create: jest.fn()
    }
  };
});

describe('HubSpotFormEmbed', () => {
  const formId = 'test-form-id';
  const portalId = 'test-portal-id';
  it('loads the HubSpot form script', () => {
    render(<HubSpotFormEmbed formId={formId} portalId={portalId} />);
    const container = screen.getByTestId(`hbspt-${formId}`);
    expect(container).toBeInTheDocument();
  });
  it('renders the container  with the correct id', () => {
    render(<HubSpotFormEmbed formId={formId} portalId={portalId} />);
    const container = screen.getByTestId(`hbspt-${formId}`);
    expect(container).toBeInTheDocument();
  });

  it('calls the HubSpot form creation function after script load', () => {
    render(<HubSpotFormEmbed formId={formId} portalId={portalId} />);
    const container = screen.getByTestId(`hbspt-${formId}`);
    expect(container).toBeInTheDocument();
    expect(window.hbspt.forms.create).toHaveBeenCalledWith({
      portalId,
      formId,
      target: `#hbspt-${formId}`
    });
  });

  it('does not render the second script tag before the first script is loaded', () => {
    render(<HubSpotFormEmbed formId={formId} portalId={portalId} />);
    expect(
      screen.queryByText(
        (content, element) =>
          element?.tagName.toLowerCase() === 'script' &&
          element?.id === 'hubspot-form'
      )
    ).not.toBeInTheDocument();
  });
});
