'use client';

import Container from '@components/LayoutStructure/Container';
import Script from 'next/script';
import { useCallback, useEffect, useState } from 'react';

import styles from './HubSpotFormEmbed.module.css';

declare global {
  interface Window {
    hbspt: {
      forms: {
        create: (options: {
          portalId: string;
          formId: string;
          region?: string;
          target?: string;
          redirectUrl?: string;
          inlineMessage?: string;
          pageId?: string;
          cssRequired?: boolean;
          cssClass?: string;
          css?: string;
          submitText?: string;
          submitButtonClass?: string;
          errorClass?: string;
          errorMessageClass?: string;
          locale?: string;
          translations?: any;
          manuallyBlockedEmailDomain?: string[];
          formInstanceId?: string;
          onBeforeFormInit?: () => void;
          onFormReady?: () => void;
          onFormSubmit?: () => void;
          onBeforeFormSubmit?: () => void;
          onFormSubmitted?: () => void;
        }) => void;
      };
    };
  }
}

interface HubSpotFormEmbedProps {
  formId: string;
  portalId: string;
}

const loadHubSpotForm = (portalId: string, formId: string) => {
  if (window.hbspt) {
    window.hbspt.forms.create({
      portalId,
      formId,
      target: `#hbspt-${formId}`
    });
  }
};

const HubSpotFormEmbed = ({ formId, portalId }: HubSpotFormEmbedProps) => {
  const [scriptLoaded, setScriptLoaded] = useState(false);

  const handleScriptLoad = useCallback(() => {
    setScriptLoaded(true);
  }, []);

  useEffect(() => {
    if (scriptLoaded) {
      loadHubSpotForm(portalId, formId);
    }
  }, [scriptLoaded, portalId, formId]);

  return (
    <>
      <Script
        charSet="utf-8"
        type="text/javascript"
        src={`//js.hsforms.net/forms/v2.js`}
        strategy="lazyOnload"
        onLoad={handleScriptLoad}
        onError={(e) => console.error('Error loading HubSpot', e)}
      />
      <Container
        className={`hbst-form ${styles.hbstForm}`}
        data-testid={`hbspt-${formId}`}
        id={`hbspt-${formId}`}
      />
    </>
  );
};

export default HubSpotFormEmbed;
