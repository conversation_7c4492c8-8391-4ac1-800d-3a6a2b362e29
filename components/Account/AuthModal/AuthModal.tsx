import { Box, Modal, ModalContent, ModalOverlay } from '@chakra-ui/react';
import { ReactElement, useState } from 'react';

import SignInForm from '../SignIn/SignInForm';
import SignUpForm from '../SignUp/SignUpForm';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const AuthModal: React.FC<Props> = ({ isOpen, onClose, onSuccess }) => {
  const [viewName, setViewName] = useState('sign-in');

  const views: { [key: string]: ReactElement } = {
    'sign-in': (
      <SignInForm
        onSuccess={onSuccess}
        signUpAction={() => setViewName('sign-up')}
      />
    ),
    'sign-up': (
      <SignUpForm
        onSuccess={onSuccess}
        logInAction={() => setViewName('sign-in')}
      />
    )
  };

  return (
    <div>
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <Box paddingY={10} paddingX={6}>
            {views[viewName] || null}
          </Box>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AuthModal;
