import { getSourceName } from '@components/Account/Reviews/ReviewForm.utils';
import { getUTMParameters } from '@utils/utmParameters';

import { REVIEW_TYPES } from '~/constants';

jest.mock('@utils/utmParameters', () => ({
  getUTMParameters: jest.fn(() => ({}))
}));

describe('getSourceName', () => {
  it("should return 'portal_email' if utm_source is 'portal_email'", () => {
    (getUTMParameters as jest.Mock).mockReturnValueOnce({
      utm_source: 'portal_email'
    });
    expect(getSourceName('caring.com', REVIEW_TYPES.SENIOR_LIVING)).toBe(
      'portal_email'
    );
  });

  it("should return 'new' if domain is 'caring.com' and formType is REVIEW_TYPES.GENERIC", () => {
    expect(getSourceName('caring.com', REVIEW_TYPES.GENERIC)).toBe('new');
  });

  it("should return 'provider_page' if domain is 'caring.com' and formType is not REVIEW_TYPES.GENERIC", () => {
    expect(getSourceName('caring.com', REVIEW_TYPES.SENIOR_LIVING)).toBe(
      'provider_page'
    );
  });

  it("should return 'seniorhomes.com' if domain is 'seniorhomes.com'", () => {
    expect(getSourceName('seniorhomes.com', REVIEW_TYPES.SENIOR_LIVING)).toBe(
      'seniorhomes.com'
    );
  });
});
