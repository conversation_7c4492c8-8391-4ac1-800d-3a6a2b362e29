'use client';

import { Button } from '@chakra-ui/react';
import { useContext } from 'react';
import { MdEdit } from 'react-icons/md';

import { REVIEW_TYPES } from '~/constants';
import ProviderContext from '~/contexts/Provider';
import { useNewReview } from '~/hooks/use-new-review';
import { Metadata } from '~/types/Magnolia';

import ReviewSubmissionModal from './ReviewSubmissionModal';

interface Props {
  buttonVariant?: string;
  buttonSize?: string;
  type: REVIEW_TYPES;
  metadata: Metadata;
  successMessage?: string;
}

const CreateReviewButton: React.FC<Props> = ({
  buttonVariant = 'solid',
  buttonSize = 'xs',
  type,
  metadata,
  successMessage
}) => {
  const { provider } = useContext(ProviderContext) || {};
  const { name = '' } = provider || {};
  const {
    onClick: handleWriteReviewClick,
    isOpen,
    onClose
  } = useNewReview(name);

  return (
    <>
      <Button
        title="Write a review"
        onClick={handleWriteReviewClick}
        style={{ textDecoration: 'none' }}
        colorScheme="caringGreen"
        variant={buttonVariant}
        leftIcon={<MdEdit />}
        size={buttonSize}
        paddingX="2"
      >
        Write a review
      </Button>
      <ReviewSubmissionModal
        formType={type}
        isOpen={isOpen}
        onClose={onClose}
        providerName={name}
        metadata={metadata}
        successMessage={successMessage}
      />
    </>
  );
};

export default CreateReviewButton;
