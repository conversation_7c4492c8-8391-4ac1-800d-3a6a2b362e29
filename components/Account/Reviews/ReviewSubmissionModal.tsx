'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  Modal<PERSON>eader,
  ModalOverlay,
  Show,
  Stack,
  Text,
  useToast
} from '@chakra-ui/react';
import {
  stepContent,
  stepSubmission
} from '@components/Analytics/events/eventContracts';
import useFormFieldFocused from '@components/Analytics/events/FormFieldFocused';
import useFormStepSubmission, {
  FormType
} from '@components/Analytics/events/FormStepSubmission';
import useFormSubmission from '@components/Analytics/events/FormSubmission';
import {
  handleFormBlur,
  handleFormFocus
} from '@components/InquiryForm/InquiryForm.utils';
import { SuccessToastDescription } from '@components/ReviewForms/ReviewSubmissionsNew';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useContext } from 'react';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';
import { z as zod } from 'zod';

import { REVIEW_TYPES } from '~/constants';
import ProviderContext from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { useCreateSeniorLivingReviewMutation } from '~/hooks/reviews';
import { Metadata } from '~/types/Magnolia';
import trackAnalyticsEvent from '~/utils/analytics';
import { customErrorMap } from '~/utils/zodCustomErrorMap';

import ReviewForm from './ReviewForm';
import {
  formFieldPrompts,
  formFieldTypes,
  getSourceName
} from './ReviewForm.utils';
import ReviewGuidelines from './ReviewGuidelines';

interface Props {
  formType: REVIEW_TYPES;
  isOpen: boolean;
  onClose: () => void;
  providerName?: string;
  reviewGuidelinesURL?: string;
  contactUsURL?: string;
  successMessage?: string;
  metadata: Pick<Metadata, '@id'>;
}

zod.setErrorMap(customErrorMap);
const required_rate_message = 'Rate is required';

const ReviewSubmissionModal: React.FC<Props> = ({
  formType,
  isOpen,
  onClose,
  providerName,
  reviewGuidelinesURL,
  contactUsURL,
  successMessage,
  metadata
}) => {
  const { provider } = useContext(ProviderContext) || {};
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path || '';
  const [formFocused, setFormFocused] = useState(false);
  const formFieldFocused = useFormFieldFocused();
  const reviewForm = useRef<HTMLFormElement>(null);
  const schema = zod.object({
    reviewBody: zod.string().trim().min(1, {
      message:
        'Review text is required to submit a review. Please add 1-3 sentences of feedback.'
    }),
    authorName: zod.string().trim().min(5, {
      message: 'Please enter at least 5 characters'
    }),
    authorEmail: zod.string().email(),
    title: zod.string().min(1, {
      message: 'Relationship to provider is required'
    }),
    careType: zod.string().trim().min(1, {
      message: 'Care type or service is required'
    }),
    locationId: zod.string().nullable().optional(),
    ratingOverall: zod
      .string({
        required_error: required_rate_message
      })
      .trim()
      .min(1, {
        message: required_rate_message
      }),
    ratingFood: zod.string().nullable().optional(),
    ratingActivities: zod.string().nullable().optional(),
    ratingStaff: zod.string().nullable().optional(),
    ratingFacilities: zod.string().nullable().optional(),
    ratingValue: zod.string().nullable().optional(),
    provider: zod.string().nullable().optional()
  });

  const toast = useToast();
  const { isLoading, mutate } = useCreateSeniorLivingReviewMutation();

  const formStepSubmission = useFormStepSubmission();
  const formSubmission = useFormSubmission();

  const formFieldsNamespace = [
    formType ?? '',
    providerName,
    reviewGuidelinesURL,
    contactUsURL
  ].join(' ');

  const id = uuidv4();
  const formTemplateId = uuidv5(
    formFieldsNamespace,
    metadata?.['@id'] ? metadata['@id'] : id
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    setError,
    trigger,
    watch
  } = useForm({
    mode: 'onBlur',
    resolver: zodResolver(schema),
    defaultValues: {
      reviewBody: '',
      authorName: '',
      authorEmail: '',
      title: '',
      careType: '',
      locationId: '',
      city: '',
      state: '',
      ratingOverall: '',
      ratingFood: '',
      ratingActivities: '',
      ratingStaff: '',
      ratingFacilities: '',
      ratingValue: '',
      provider: '',
      legacyResourceId: '',
      facilityName: ''
    }
  });

  let analyticsEventName = 'reviews_senior_care_review';
  if (formType === REVIEW_TYPES.SENIOR_CARE) {
    analyticsEventName = 'reviews_senior_living_review';
  }

  switch (formType) {
    case REVIEW_TYPES.SENIOR_CARE:
      analyticsEventName = 'reviews_senior_living_review';
      break;
    case REVIEW_TYPES.GENERIC:
      analyticsEventName = 'reviews_generic_review';
      break;
    default:
      analyticsEventName = 'reviews_senior_care_review';
      break;
  }

  const sourceName = getSourceName(domain, formType);

  const handleFormEvents = useCallback(
    (data, formInstanceId: string) => {
      const formStepInstanceId = uuidv4();
      const stepContent: stepContent[] = [];
      const stepSubmission: stepSubmission[] = [];
      const stepIndex = 1;
      const stepId = uuidv5(String(stepIndex), metadata['@id']);
      if (Object.keys(data).length > 0) {
        Object.entries(data).map((prompt, i) => {
          const field = prompt[0];
          const value = prompt[1] as string;

          stepContent.push({
            prompt_id: uuidv5(field, metadata['@id']),
            prompt_type: formFieldTypes[field] ?? '',
            prompt_instance_id: uuidv4(),
            prompt_index: i + 1,
            prompt_value: formFieldPrompts[field] ?? '',
            response_array: [
              {
                response_value: value,
                response_id: uuidv4()
              }
            ]
          });
        });
        stepSubmission.push({
          step_id: stepId,
          step_instance_id: formStepInstanceId,
          step_index: stepIndex
        });
      }

      formStepSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        form_type: FormType.REVIEW,
        step_id: stepId,
        step_instance_id: formStepInstanceId,
        step_index: 1,
        step_content: stepContent
      });
      formSubmission({
        form_template_id: formTemplateId,
        form_instance_id: formInstanceId,
        step_submissions: stepSubmission,
        form_type: FormType.REVIEW
      });
    },
    [metadata, formTemplateId, formStepSubmission, formSubmission]
  );

  const onSubmit = handleSubmit((data) => {
    const formInstanceId = uuidv4();
    mutate(
      {
        legacyResourceId: null,
        locationId: provider?.id || '',
        authorName: String(data.authorName),
        authorEmail: String(data.authorEmail),
        title: String(data.title),
        ratingOverall: Number(data.ratingOverall),
        ratingStaff: Number(data.ratingStaff) || null,
        ratingActivities: Number(data.ratingActivities) || null,
        ratingFood: Number(data.ratingFood) || null,
        ratingFacilities: Number(data.ratingFacilities) || null,
        ratingValue: Number(data.ratingValue) || null,
        sourceName,
        body: String(data.reviewBody)
      },
      {
        onSuccess: (response) => {
          trackAnalyticsEvent(analyticsEventName, {
            event_category: 'reviews'
          });

          handleFormEvents(
            { ...data, response: response.message },
            formInstanceId
          );
          toast({
            status: 'success',
            title: 'Review submitted',
            description: (
              <SuccessToastDescription
                domain={domain}
                message={successMessage}
              />
            ),
            duration: null,
            containerStyle: {
              maxWidth: 'unset'
            },
            position: 'top'
          });
          handleCancel();
        }
      }
    );
  });

  const handleCancel = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: 'full', lg: '5xl' }}>
      <ModalOverlay />
      <ModalContent width="full">
        <Show below="lg">
          <ModalHeader>
            <Text as="h1" fontWeight="bold" fontSize="xl">
              Share Your Thoughts
            </Text>
            <Text as="h1" fontWeight="bold" fontSize="xl">
              {providerName}
            </Text>
          </ModalHeader>
        </Show>
        <ModalCloseButton />
        <ModalBody px={{ base: '', lg: 2 }} pl={{ base: 2, lg: '' }} mb={10}>
          <Stack direction={{ base: 'column', lg: 'row' }} spacing={8} pt={6}>
            <Stack width="full" pl={{ base: 2, lg: 6 }}>
              <Show above="lg">
                <Text as="h1" fontWeight="bold" fontSize="xl" mb={6}>
                  Share Your Thoughts {providerName ? `- ${providerName}` : ''}
                </Text>
              </Show>
              <form
                onSubmit={onSubmit}
                onClick={(e) =>
                  handleFormFocus(
                    e,
                    formFocused,
                    setFormFocused,
                    formFieldFocused,
                    formTemplateId,
                    FormType.REVIEW
                  )
                }
                onBlur={(e) =>
                  handleFormBlur(e, formFocused, reviewForm, setFormFocused)
                }
                ref={reviewForm}
              >
                <ReviewForm
                  register={register}
                  setValue={setValue}
                  trigger={trigger}
                  watch={watch}
                  errors={errors}
                  setError={setError}
                  formType={formType}
                  reviewGuidelinesURL={reviewGuidelinesURL}
                  contactUsURL={contactUsURL}
                />

                <HStack
                  spacing={5}
                  width="full"
                  justify={{ base: 'flex-start', lg: 'flex-end' }}
                  align="flex-end"
                  mt={6}
                >
                  <Button
                    variant="outline"
                    size="md"
                    minWidth="112px"
                    width="auto"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    bg="primary.500"
                    _hover={{ bg: 'primary.600' }}
                    color="white"
                    size="md"
                    minWidth="112px"
                    width="auto"
                    isLoading={isLoading}
                  >
                    Submit
                  </Button>
                </HStack>
              </form>
            </Stack>
            <Show above="lg">
              <Stack
                borderLeft="1px"
                borderColor="caringBlue.50"
                pl={8}
                maxW="xs"
              >
                <ReviewGuidelines
                  reviewGuidelinesURL={reviewGuidelinesURL}
                  contactUsURL={contactUsURL}
                />
              </Stack>
            </Show>
          </Stack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ReviewSubmissionModal;
