import fetch from 'jest-fetch-mock';

import { REVIEW_TYPES } from '~/constants';
import Provider<PERSON>ontext, {
  Provider,
  ProviderService
} from '~/contexts/Provider';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import {
  EMAIL_FIELD,
  fillInInput,
  fireEvent,
  RELATIONSHIP_FIELD,
  render,
  REVIEW_TEXT_FIELD,
  screen,
  SCREEN_NAME_FIELD,
  SERVICE_FIELD,
  setDesktopScreen,
  submitForm,
  waitFor,
  waitForReviewApiToBeCalled
} from '~/utils/test-utils';

import ReviewSubmissionModal from './ReviewSubmissionModal';

const fakeProvider: Provider = {
  slug: 'foo-boo',
  services: [
    {
      id: '38853',
      costs: {
        currency: '',
        startingPriceCents: 0
      },
      category: {
        name: 'Assisted Living',
        description: '',
        imageURL: ''
      }
    }
  ] as ProviderService[]
} as Provider;

const fakeCaringSite: SiteContextType = {
  site: {
    path: 'caring.com',
    name: 'Caring.com',
    domains: ['caring.com'],
    segmentWriteKey: 'key',
    segmentCdnURL: 'cdn',
    partnerToken: 'token',
    publicFolder: 'folder'
  }
};

const fakeSeniorhomesSite: SiteContextType = {
  site: {
    path: 'seniorhomes.com',
    name: 'SeniorHomes.com',
    domains: ['seniorhomes.com'],
    segmentWriteKey: 'key',
    segmentCdnURL: 'cdn',
    partnerToken: 'token',
    publicFolder: 'folder'
  }
};

const fakeMetadata = {
  '@index': 7,
  '@name': '02',
  '@path':
    '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/provider/main/02',
  '@id': '0a03fc04-1a55-4abf-a07f-ae674a963dc0',
  '@nodeType': 'mgnl:component',
  'mgnl:created': '2023-09-12T13:53:13.448+02:00',
  'mgnl:template': 'spa-lm:components/reviews',
  'mgnl:lastModified': '2023-12-13T17:10:12.510+01:00',
  '@nodes': []
};

const Wrapper = ({ children }) => (
  <ProviderContext.Provider
    value={{ setProvider: () => {}, provider: fakeProvider }}
  >
    <SiteContext.Provider value={fakeCaringSite}>
      {children}
    </SiteContext.Provider>
  </ProviderContext.Provider>
);

describe('SeniorLivingReviewsModal', () => {
  beforeEach(() => {
    setDesktopScreen();
    fetch.resetMocks();
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });

  it('should render', async () => {
    render(
      <ReviewSubmissionModal
        formType={REVIEW_TYPES.SENIOR_LIVING}
        isOpen={true}
        onClose={jest.fn}
        providerName="Test Provider"
        metadata={fakeMetadata}
      />
    );

    expect(
      screen.getByRole('heading', {
        name: /share your thoughts - test/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByText(/how would you rate this community\?/i)
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', {
        name: /what should care seekers or potential residents know\?/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', { name: /screen name/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('textbox', { name: /email address/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('combobox', {
        name: /relationship to provider/i
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('combobox', {
        name: /care type or service being reviewed/i
      })
    ).toBeInTheDocument();
    expect(screen.getByText(/food \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/activities \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/staff \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/facility \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByText(/value \(optional\)/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    render(
      <ReviewSubmissionModal
        formType={REVIEW_TYPES.SENIOR_LIVING}
        isOpen={true}
        onClose={jest.fn}
        providerName="Test Provider"
        metadata={fakeMetadata}
      />
    );

    submitForm();

    await waitFor(() => {
      expect(screen.getByText(/rate is required/i)).toBeInTheDocument();
      expect(screen.getByText(/review text is required/i)).toBeInTheDocument();
      expect(screen.getByText(/the email/i)).toBeInTheDocument();
      expect(
        screen.getByText(/relationship to provider is required/i)
      ).toBeInTheDocument();
      expect(
        screen.getByText(/care type or service is required/i)
      ).toBeInTheDocument();
    });
  });

  it('submits a review including the site from context', async () => {
    fetch.mockResponseOnce(JSON.stringify({}));

    render(
      <Wrapper>
        <SiteContext.Provider value={fakeCaringSite}>
          <ReviewSubmissionModal
            formType={REVIEW_TYPES.SENIOR_LIVING}
            isOpen={true}
            onClose={jest.fn}
            providerName="Test Provider"
            metadata={fakeMetadata}
          />
        </SiteContext.Provider>
      </Wrapper>
    );

    fireEvent.click(screen.getAllByTestId('star-1')[0]);

    fillInInput(REVIEW_TEXT_FIELD, 'Test Review Content');
    fillInInput(SCREEN_NAME_FIELD, 'Test Name');
    fillInInput(EMAIL_FIELD, '<EMAIL>');
    fillInInput(RELATIONSHIP_FIELD, 'I visited this facility', 'combobox');
    fillInInput(SERVICE_FIELD, 'assisted_living_facilities', 'combobox');

    submitForm();

    await waitForReviewApiToBeCalled();

    const content = fetch.mock.lastCall?.[1];
    const body = JSON.parse(content?.body?.toString() || '');
    expect(body).toEqual({
      legacyResourceId: null,
      locationId: '',
      authorName: 'Test Name',
      authorEmail: '<EMAIL>',
      title: 'I visited this facility',
      ratingOverall: 1,
      ratingStaff: null,
      ratingActivities: null,
      ratingFood: null,
      ratingFacilities: null,
      ratingValue: null,
      sourceName: 'provider_page',
      body: 'Test Review Content'
    });
  });

  it('submits the right sourceName for seniorhomes', async () => {
    fetch.mockResponseOnce(JSON.stringify({}));

    render(
      <Wrapper>
        <SiteContext.Provider value={fakeSeniorhomesSite}>
          <ReviewSubmissionModal
            formType={REVIEW_TYPES.SENIOR_LIVING}
            isOpen={true}
            onClose={jest.fn}
            providerName="Test Provider"
            metadata={fakeMetadata}
          />
        </SiteContext.Provider>
      </Wrapper>
    );
    fireEvent.click(screen.getAllByTestId('star-1')[0]);

    fillInInput(REVIEW_TEXT_FIELD, 'Test Review Content');
    fillInInput(SCREEN_NAME_FIELD, 'Test Name');
    fillInInput(EMAIL_FIELD, '<EMAIL>');
    fillInInput(RELATIONSHIP_FIELD, 'I visited this facility', 'combobox');
    fillInInput(SERVICE_FIELD, 'assisted_living_facilities', 'combobox');

    submitForm();

    await waitForReviewApiToBeCalled();

    const content = fetch.mock.lastCall?.[1];
    const body = JSON.parse(content?.body?.toString() || '');
    expect(body).toEqual({
      legacyResourceId: null,
      locationId: '',
      authorName: 'Test Name',
      authorEmail: '<EMAIL>',
      title: 'I visited this facility',
      ratingOverall: 1,
      ratingStaff: null,
      ratingActivities: null,
      ratingFood: null,
      ratingFacilities: null,
      ratingValue: null,
      sourceName: 'seniorhomes.com',
      body: 'Test Review Content'
    });
  });
});
