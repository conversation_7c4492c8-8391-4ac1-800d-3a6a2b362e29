import { <PERSON><PERSON><PERSON><PERSON>ider } from '@chakra-ui/react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { REVIEW_TYPES } from '~/constants';

import ReviewForm from './ReviewForm';

jest.mock('@hooks/useDebounce', () => ({
  __esModule: true,
  default: (value: string) => value
}));

// Mock the new location autocomplete hook
jest.mock('@hooks/use-location-autocomplete', () => ({
  useLocationAutocompleteQuery: jest.fn()
}));

jest.mock('@choc-ui/chakra-autocomplete', () => ({
  AutoComplete: ({
    children,
    onChange
  }: {
    children: React.ReactNode;
    onChange: (value: string) => void;
  }) => (
    <div
      data-testid="autocomplete"
      onClick={(e: React.MouseEvent<HTMLDivElement>) =>
        onChange?.(e.currentTarget.getAttribute('data-value') || '')
      }
    >
      {children}
    </div>
  ),
  AutoCompleteInput: ({ onChange, isInvalid }: any) => (
    <input
      data-testid="autocomplete-input"
      onChange={onChange}
      aria-invalid={isInvalid}
    />
  ),
  AutoCompleteList: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="autocomplete-list">{children}</div>
  ),
  AutoCompleteGroup: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="autocomplete-group">{children}</div>
  ),
  AutoCompleteItem: ({ children, value, onClick }: any) => (
    <div
      data-testid={`autocomplete-item-${value}`}
      data-value={value}
      onClick={(e: React.MouseEvent<HTMLDivElement>) => {
        onClick?.();
        const target = e.target as HTMLDivElement;
        target.setAttribute('data-value', value);
        const autocomplete = target.closest('[data-testid="autocomplete"]');
        if (autocomplete) {
          (autocomplete as HTMLElement).click();
        }
      }}
    >
      {children}
    </div>
  )
}));

const mockLocations = [
  {
    id: 'location1',
    name: 'Senior Living Center',
    street: '123 Test St',
    city: 'Test City',
    state: 'California',
    zipCode: '12345',
    locationServices: {
      'Assisted Living': 'service-id-1',
      'Memory Care': 'service-id-2'
    },
    services: [
      { careTypeName: 'Assisted Living', serviceId: 'service-id-1' },
      { careTypeName: 'Memory Care', serviceId: 'service-id-2' }
    ],
    formattedAddress: '123 Test St, Test City, California 12345'
  },
  {
    id: 'location2',
    name: 'Care Home',
    street: '456 Care Ave',
    city: 'Another City',
    state: 'New York',
    zipCode: '67890',
    locationServices: {
      'Independent Living': 'service-id-3'
    },
    services: [
      { careTypeName: 'Independent Living', serviceId: 'service-id-3' }
    ],
    formattedAddress: '456 Care Ave, Another City, New York 67890'
  }
];

const createMockFormMethods = () => {
  const subjects = {
    watch: {
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
      next: jest.fn()
    },
    array: {
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
      next: jest.fn()
    },
    state: {
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
      next: jest.fn()
    }
  };

  return {
    register: jest.fn(),
    setValue: jest.fn(),
    setError: jest.fn(),
    trigger: jest.fn(),
    watch: jest.fn().mockReturnValue(''),
    control: {
      register: jest.fn(),
      unregister: jest.fn(),
      getFieldState: jest.fn(),
      _names: {
        mount: new Set(),
        unMount: new Set(),
        array: new Set(),
        watch: new Set()
      },
      _subjects: subjects,
      _getWatch: jest.fn(),
      _formValues: {},
      _defaultValues: {},
      _stateFlags: {
        mount: false,
        action: false,
        watch: false
      },
      _options: {
        mode: 'onSubmit',
        reValidateMode: 'onChange',
        shouldFocusError: true
      },
      _updateValid: jest.fn(),
      _removeUnmounted: jest.fn(),
      _fields: {},
      _formState: {
        isDirty: false,
        isValidating: false,
        dirtyFields: {},
        isSubmitted: false,
        submitCount: 0,
        touchedFields: {},
        isSubmitting: false,
        isSubmitSuccessful: false,
        isValid: false,
        errors: {}
      },
      _getFieldArray: jest.fn(),
      _proxyFormState: {
        isDirty: false,
        dirtyFields: false,
        touchedFields: false,
        isValidating: false,
        isValid: false,
        errors: false
      },
      _watchInternal: jest.fn(),
      _createWatcher: jest.fn(),
      _updateFieldArray: jest.fn(),
      _getValues: jest.fn(),
      _getDirty: jest.fn(),
      _updateFormState: jest.fn(),
      _updateFieldsState: jest.fn(),
      current: {
        subject: {
          subscribe: jest.fn(),
          unsubscribe: jest.fn(),
          next: jest.fn()
        }
      }
    }
  };
};

describe('ReviewForm', () => {
  let mockMethods: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockMethods = createMockFormMethods();

    const {
      useLocationAutocompleteQuery
    } = require('@hooks/use-location-autocomplete');
    useLocationAutocompleteQuery.mockReturnValue({
      data: { locations: mockLocations },
      isLoading: false,
      error: null
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <ChakraProvider>
        <ReviewForm
          register={mockMethods.register}
          setValue={mockMethods.setValue}
          setError={mockMethods.setError}
          trigger={mockMethods.trigger}
          watch={mockMethods.watch}
          errors={{}}
          control={mockMethods.control}
          formType={REVIEW_TYPES.GENERIC}
          {...props}
        />
      </ChakraProvider>
    );
  };

  describe('Provider AutoComplete', () => {
    it('renders provider search field only for generic review type', () => {
      const { rerender } = renderComponent();
      expect(screen.getByTestId('autocomplete')).toBeInTheDocument();

      rerender(
        <ChakraProvider>
          <ReviewForm
            register={mockMethods.register}
            setValue={mockMethods.setValue}
            setError={mockMethods.setError}
            trigger={mockMethods.trigger}
            watch={mockMethods.watch}
            errors={{}}
            control={mockMethods.control}
            formType={REVIEW_TYPES.SENIOR_CARE}
          />
        </ChakraProvider>
      );
      expect(screen.queryByTestId('autocomplete')).not.toBeInTheDocument();
    });

    it('shows validation error state when facilityName has error', () => {
      renderComponent({
        errors: {
          facilityName: {
            type: 'required',
            message: 'Provider is required'
          }
        }
      });

      const input = screen.getByTestId('autocomplete-input');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('displays location suggestions when typing', async () => {
      renderComponent();

      const input = screen.getByTestId('autocomplete-input');
      fireEvent.change(input, { target: { value: 'Senior' } });

      await waitFor(() => {
        expect(screen.getByText('Senior Living Center')).toBeInTheDocument();
        expect(
          screen.getByText('123 Test St, Test City, California 12345')
        ).toBeInTheDocument();
        expect(screen.getByText('Senior (New Provider)')).toBeInTheDocument();
      });
    });

    it('handles new provider selection', async () => {
      renderComponent();

      const input = screen.getByTestId('autocomplete-input');
      fireEvent.change(input, { target: { value: 'New Provider' } });

      const newProviderOption = await screen.findByTestId(
        'autocomplete-item-new-provider'
      );
      fireEvent.click(newProviderOption);

      await waitFor(() => {
        expect(mockMethods.setValue).toHaveBeenCalledWith('locationId', '');
        expect(mockMethods.setValue).toHaveBeenCalledWith('provider', '');
        expect(mockMethods.setValue).toHaveBeenCalledWith('careType', '');
        expect(mockMethods.setValue).toHaveBeenCalledWith(
          'legacyResourceId',
          ''
        );
        expect(mockMethods.setValue).toHaveBeenCalledWith(
          'facilityName',
          'New Provider'
        );
      });
    });

    it('disables city and state fields when existing provider is selected', async () => {
      const { rerender } = renderComponent();

      const input = screen.getByTestId('autocomplete-input');
      fireEvent.change(input, { target: { value: 'Senior' } });

      const locationOption = await screen.findByTestId(
        'autocomplete-item-location1'
      );
      fireEvent.click(locationOption);

      // Mock that a location is selected by making watch return the location ID
      mockMethods.watch.mockReturnValue('location1');

      rerender(
        <ChakraProvider>
          <ReviewForm
            register={mockMethods.register}
            setValue={mockMethods.setValue}
            setError={mockMethods.setError}
            trigger={mockMethods.trigger}
            watch={mockMethods.watch}
            errors={{}}
            control={mockMethods.control}
            formType={REVIEW_TYPES.GENERIC}
          />
        </ChakraProvider>
      );

      const cityInput = screen.getByLabelText('City');
      const stateSelect = screen.getByLabelText('State');

      expect(cityInput).toHaveAttribute('disabled');
      expect(stateSelect).toHaveAttribute('disabled');
    });
  });
});
