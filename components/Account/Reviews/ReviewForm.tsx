'use client';

import { SearchIcon } from '@chakra-ui/icons';
import {
  Di<PERSON>r,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  InputGroup,
  InputRightElement,
  Select,
  Show,
  Stack,
  Text,
  Textarea,
  VStack
} from '@chakra-ui/react';
import {
  DEFAULT_CARE_TYPES,
  RELATIONSHIPS_TO_PROVIDER,
  REVIEW_FIELD_LABELS,
  REVIEW_FIELD_PLACEHOLDERS,
  REVIEW_RATING_LABELS
} from '@components/Account/Reviews/contants';
import { useLocationAutocompleteQuery } from '@hooks/use-location-autocomplete';
import useDebounce from '@hooks/useDebounce';
import { LocationAutocompleteResult } from '@services/modular-monolith/types/location.type';
import dynamic from 'next/dynamic';
import { memo, useMemo, useState } from 'react';
import {
  Control,
  Controller,
  FieldErrorsImpl,
  UseFormRegister,
  UseFormSetError,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch
} from 'react-hook-form';

import { DEFAULT_HITS_PER_PAGE, REVIEW_TYPES } from '~/constants';
import { getStateAbbreviation, usStates } from '~/utils/UsStates';

import ContactUs from './ContactUs';
import ReviewGuidelines from './ReviewGuidelines';
import ReviewStarsInput from './ReviewStarsInput';

interface ReviewFormProps {
  reviewBody: string;
  authorName: string;
  authorEmail: string;
  title: string;
  careType: string;
  city: string;
  state: string;
  ratingOverall: string;
  ratingFood: string;
  ratingActivities: string;
  ratingStaff: string;
  ratingFacilities: string;
  ratingValue: string;
  provider: string;
  facilityName: string;
  locationId: string;
  legacyResourceId: string;
}

type Ratings =
  | 'ratingOverall'
  | 'ratingStaff'
  | 'ratingActivities'
  | 'ratingFood'
  | 'ratingFacilities'
  | 'ratingValue';

interface Props {
  register: UseFormRegister<ReviewFormProps>;
  setValue: UseFormSetValue<ReviewFormProps>;
  setError: UseFormSetError<ReviewFormProps>;
  trigger: UseFormTrigger<ReviewFormProps>;
  watch: UseFormWatch<ReviewFormProps>;
  errors: Partial<FieldErrorsImpl<ReviewFormProps>>;
  control?: Control<ReviewFormProps>;
  formType: REVIEW_TYPES;
  reviewGuidelinesURL?: string;
  contactUsURL?: string;
}

interface AutoCompleteWrapperProps {
  showOptionOfNewProvider: boolean;
  handleProviderChange: (value: string) => void;
  control: Control<ReviewFormProps>;
  handleProviderInputChange: (value: string) => void;
  errors: Partial<FieldErrorsImpl<ReviewFormProps>>;
  locations?: LocationAutocompleteResult[];
  keywords: string;
}

const DynamicAutoComplete = dynamic<AutoCompleteWrapperProps>(
  () =>
    import('@choc-ui/chakra-autocomplete').then((chakraAutocomplete) => {
      const Wrapper = memo<AutoCompleteWrapperProps>(
        ({
          showOptionOfNewProvider,
          handleProviderChange,
          control,
          handleProviderInputChange,
          errors,
          locations,
          keywords
        }) => (
          <chakraAutocomplete.AutoComplete
            openOnFocus={showOptionOfNewProvider}
            onChange={handleProviderChange}
            disableFilter
          >
            <InputGroup>
              <Controller
                control={control}
                render={({ field }) => (
                  <chakraAutocomplete.AutoCompleteInput
                    {...field}
                    variant="outline"
                    placeholder="Search"
                    onChange={(event) =>
                      handleProviderInputChange(event.target.value)
                    }
                    autoComplete="off"
                    isInvalid={Boolean(errors.facilityName)}
                  />
                )}
                name="facilityName"
              />
              <InputRightElement>
                <SearchIcon />
              </InputRightElement>
            </InputGroup>
            <chakraAutocomplete.AutoCompleteList mt={1} maxW="632px">
              {locations?.map((location, oid) => (
                <chakraAutocomplete.AutoCompleteGroup key={`option-${oid}`}>
                  <chakraAutocomplete.AutoCompleteItem
                    value={location.id}
                    textTransform="capitalize"
                    align="center"
                  >
                    <VStack align="start" w="full">
                      <Text fontSize={14} fontWeight="bold">
                        {location.name}
                      </Text>
                      <Text fontSize={14}>
                        {`${location.street}, ${location.city}, ${location.state} ${location.zipCode}`}
                      </Text>
                      <Divider />
                    </VStack>
                  </chakraAutocomplete.AutoCompleteItem>
                </chakraAutocomplete.AutoCompleteGroup>
              ))}
              {showOptionOfNewProvider && (
                <chakraAutocomplete.AutoCompleteGroup key="option-new-provider">
                  <chakraAutocomplete.AutoCompleteItem
                    value="new-provider"
                    textTransform="capitalize"
                    align="center"
                  >
                    <VStack align="start" w="full">
                      <Text fontSize={14} fontWeight="bold">
                        {keywords} (New Provider)
                      </Text>
                      <Divider />
                    </VStack>
                  </chakraAutocomplete.AutoCompleteItem>
                </chakraAutocomplete.AutoCompleteGroup>
              )}
            </chakraAutocomplete.AutoCompleteList>
          </chakraAutocomplete.AutoComplete>
        )
      );

      Wrapper.displayName = 'AutoCompleteWrapper';
      return Wrapper;
    }),
  { ssr: false }
);

const ReviewForm: React.FC<Props> = ({
  control,
  register,
  errors,
  setValue,
  setError,
  trigger,
  watch,
  formType = REVIEW_TYPES.SENIOR_LIVING,
  reviewGuidelinesURL,
  contactUsURL
}) => {
  const [keywords, setKeywords] = useState('');
  const debouncedKeywords = useDebounce(keywords, 500);

  const { data: locationData } = useLocationAutocompleteQuery({
    query: debouncedKeywords,
    pageSize: DEFAULT_HITS_PER_PAGE
  });
  const locations = useMemo(
    () =>
      locationData?.locations?.map((location) => ({
        ...location,
        services: location.locationServices
          ? Object.entries(location.locationServices).map(
              ([careTypeName, serviceId]) => ({
                careTypeName,
                serviceId
              })
            )
          : [],
        formattedAddress: `${location.street}, ${location.city}, ${location.state} ${location.zipCode}`
      })),
    [locationData?.locations]
  );

  const displaySpecialFields = formType !== REVIEW_TYPES.SENIOR_CARE;

  const selectedLocationId = watch('locationId');
  const isLocationSelected = Boolean(selectedLocationId);
  const showOptionOfNewProvider = Boolean(keywords);

  const handleChangeStars = (input: Ratings, value: string) => {
    setValue(input, value);
    setError(input, { type: 'custom', message: '' });
  };

  const handleProviderChange = (value: string) => {
    setValue('legacyResourceId', '');
    const location = locations?.find((location) => location.id === value);
    if (value === 'new-provider' || !location) {
      setValue('locationId', '');
      setValue('provider', '');
      setValue('careType', '');
    } else {
      setValue('locationId', location.id || '');
      setValue('facilityName', location.name || '');
      setKeywords(location.name || '');

      // Fill-up city & state, and remove any existing validation error
      setValue('provider', location.name || '');
      setValue('city', location.city || '');
      setValue('state', getStateAbbreviation(location.state || '') || '');

      if (location.services && location.services.length > 0) {
        const firstService = location.services[0];
        setValue('careType', firstService.careTypeName);
        setValue('legacyResourceId', firstService.serviceId || '');
      } else {
        setValue('careType', '');
        setValue('legacyResourceId', '');
      }

      trigger(['city', 'state', 'careType']);
    }
  };

  const handleProviderInputChange = (value: string) => {
    setValue('locationId', '');
    setValue('facilityName', value || '');
    setValue('careType', '');
    setValue('legacyResourceId', '');
    setValue('provider', '');
    setKeywords(value || '');
  };

  return (
    <VStack spacing="8" pr={{ base: 4, lg: 0 }}>
      {/* Hidden field to register legacyResourceId with react-hook-form */}
      <input type="hidden" {...register('legacyResourceId')} />
      {formType === REVIEW_TYPES.GENERIC && control && (
        <>
          <FormControl isInvalid={Boolean(errors.facilityName)} mt={8}>
            <FormLabel>Provider</FormLabel>
            <DynamicAutoComplete
              showOptionOfNewProvider={showOptionOfNewProvider}
              handleProviderChange={handleProviderChange}
              control={control}
              handleProviderInputChange={handleProviderInputChange}
              errors={errors}
              locations={locations}
              keywords={keywords}
            />
            <FormErrorMessage>
              {String(errors?.facilityName?.message)}
            </FormErrorMessage>
          </FormControl>
          <Stack
            spacing="8"
            direction={{ base: 'column', lg: 'row' }}
            width="full"
          >
            <FormControl isInvalid={Boolean(errors.city)}>
              <FormLabel fontSize="sm">City</FormLabel>
              <Input
                {...register('city', { required: true })}
                isDisabled={isLocationSelected}
              />
              <FormErrorMessage>
                {String(errors?.city?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.state)}>
              <FormLabel fontSize="sm">State</FormLabel>
              <Select
                {...register('state', { required: true })}
                isDisabled={isLocationSelected}
              >
                {usStates.map(({ label, value }) => (
                  <option key={`state-${value}`} value={value}>
                    {label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>
                {String(errors?.state?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
        </>
      )}

      <FormControl isInvalid={Boolean(errors.ratingOverall)}>
        <FormLabel fontSize="sm">{REVIEW_RATING_LABELS[formType]}</FormLabel>
        <ReviewStarsInput
          onChange={(value: number) => {
            handleChangeStars('ratingOverall', String(value));
          }}
        />
        <FormErrorMessage>
          {String(errors?.ratingOverall?.message)}
        </FormErrorMessage>
      </FormControl>
      <FormControl isInvalid={Boolean(errors.reviewBody)}>
        <FormLabel fontSize="sm">{REVIEW_FIELD_LABELS[formType]}</FormLabel>
        <Textarea
          {...register('reviewBody', { required: true })}
          placeholder={REVIEW_FIELD_PLACEHOLDERS[formType]}
          height="191px"
          resize="none"
        />
        <FormErrorMessage>
          {String(errors?.reviewBody?.message)}
        </FormErrorMessage>
      </FormControl>
      <Show below="lg">
        <ReviewGuidelines
          reviewGuidelinesURL={reviewGuidelinesURL}
          contactUsURL={contactUsURL}
        />
      </Show>
      <Stack spacing="8" direction={{ base: 'column', lg: 'row' }} width="full">
        <FormControl isInvalid={Boolean(errors.authorName)}>
          <FormLabel fontSize="sm">Screen Name</FormLabel>
          <Input {...register('authorName', { required: true })} />
          <FormErrorMessage>
            {String(errors?.authorName?.message)}
          </FormErrorMessage>
        </FormControl>
        <FormControl isInvalid={Boolean(errors.authorEmail)}>
          <FormLabel fontSize="sm">Email Address</FormLabel>
          <Input
            {...register('authorEmail', { required: true })}
            type="email"
          />
          <FormErrorMessage>
            {String(errors?.authorEmail?.message)}
          </FormErrorMessage>
        </FormControl>
      </Stack>
      <Stack spacing="8" direction={{ base: 'column', lg: 'row' }} width="full">
        <FormControl isInvalid={Boolean(errors.title)}>
          <FormLabel fontSize="sm">Relationship to provider</FormLabel>
          <Select {...register('title', { required: true })}>
            <option value={''}>Select</option>
            {RELATIONSHIPS_TO_PROVIDER[formType]?.map(({ name }) => (
              <option key={`title-${name}`} value={name}>
                {name}
              </option>
            ))}
          </Select>
          <FormErrorMessage>{String(errors?.title?.message)}</FormErrorMessage>
        </FormControl>
        <FormControl isInvalid={Boolean(errors.careType)}>
          <FormLabel fontSize="sm">
            Care type or service being reviewed
          </FormLabel>
          <Select {...register('careType', { required: true })}>
            <option value={''}>Select</option>
            {isLocationSelected
              ? locations
                  ?.find((loc) => loc.id === selectedLocationId)
                  ?.services?.map(({ careTypeName, serviceId }) => (
                    <option
                      key={`careType-${careTypeName}`}
                      value={careTypeName}
                    >
                      {careTypeName}
                    </option>
                  ))
              : Object.entries(DEFAULT_CARE_TYPES).map(([id, name]) => (
                  <option key={`careType-${id}`} value={id}>
                    {name}
                  </option>
                ))}
          </Select>
          <FormErrorMessage>
            {String(errors?.careType?.message)}
          </FormErrorMessage>
        </FormControl>
      </Stack>
      {displaySpecialFields && (
        <>
          <Stack
            spacing="8"
            direction={{ base: 'column', lg: 'row' }}
            width="full"
          >
            <FormControl isInvalid={Boolean(errors.ratingFood)}>
              <FormLabel fontSize="sm">Food (optional)</FormLabel>
              <ReviewStarsInput
                onChange={(value: number) => {
                  handleChangeStars('ratingFood', String(value));
                }}
              />
              <FormErrorMessage>
                {String(errors?.ratingFood?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.ratingActivities)}>
              <FormLabel fontSize="sm">Activities (optional)</FormLabel>
              <ReviewStarsInput
                onChange={(value: number) => {
                  handleChangeStars('ratingActivities', String(value));
                }}
              />
              <FormErrorMessage>
                {String(errors?.ratingActivities?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
          <Stack
            spacing="8"
            direction={{ base: 'column', lg: 'row' }}
            width="full"
          >
            <FormControl isInvalid={Boolean(errors.ratingStaff)}>
              <FormLabel fontSize="sm">Staff (optional)</FormLabel>
              <ReviewStarsInput
                onChange={(value: number) => {
                  handleChangeStars('ratingStaff', String(value));
                }}
              />
              <FormErrorMessage>
                {String(errors?.ratingStaff?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.ratingFacilities)}>
              <FormLabel fontSize="sm">Facility (optional)</FormLabel>
              <ReviewStarsInput
                onChange={(value: number) => {
                  handleChangeStars('ratingFacilities', String(value));
                }}
              />
              <FormErrorMessage>
                {String(errors?.ratingFacilities?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
          <Stack
            spacing="8"
            direction={{ base: 'column', lg: 'row' }}
            width="full"
          >
            <FormControl isInvalid={Boolean(errors.ratingValue)}>
              <FormLabel fontSize="sm">Value (optional)</FormLabel>
              <ReviewStarsInput
                onChange={(value: number) => {
                  handleChangeStars('ratingValue', String(value));
                }}
              />
              <FormErrorMessage>
                {String(errors?.ratingValue?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
        </>
      )}
      <Show below="lg">
        <HStack width="full" pb={8}>
          <ContactUs contactUsURL={contactUsURL} />
        </HStack>
      </Show>
    </VStack>
  );
};

export default ReviewForm;
