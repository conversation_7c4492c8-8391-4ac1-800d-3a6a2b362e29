import { fireEvent, render, screen, waitFor } from '~/utils/test-utils';

import ReviewStarsInput from './ReviewStarsInput';

describe('ReviewStarsInput', () => {
  it('should render', async () => {
    render(<ReviewStarsInput onChange={jest.fn()} />);

    expect(screen.getByTestId('star-1')).toBeVisible();
  });

  it('should change color on click', async () => {
    const mockChange = jest.fn();
    render(<ReviewStarsInput onChange={mockChange} />);

    fireEvent.click(screen.getByTestId('star-1'));

    await waitFor(() => expect(mockChange).toHaveBeenCalledWith(1));
  });
});
