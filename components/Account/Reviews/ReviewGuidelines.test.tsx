import {
  render,
  screen,
  setDesktopScreen,
  setMobileScreen
} from '~/utils/test-utils';

import ReviewGuidelines from './ReviewGuidelines';

describe('ReviewGuidelines', () => {
  it('should render', async () => {
    setDesktopScreen();
    render(
      <ReviewGuidelines reviewGuidelinesURL="/path/to/review_guidelines" />
    );

    expect(screen.getByText('Review Guidelines')).toBeVisible();
    expect(screen.getByText('View All Guidelines')).toBeVisible();
    expect(screen.getByText('View All Guidelines')).toHaveAttribute(
      'href',
      expect.stringContaining('/path/to/review_guidelines')
    );
  });

  it('should not render review guideline title in mobile', async () => {
    setMobileScreen();
    render(
      <ReviewGuidelines reviewGuidelinesURL="/path/to/review_guidelines" />
    );

    const title = screen.queryByText('Review Guidelines');
    expect(title).not.toBeInTheDocument();
  });
});
