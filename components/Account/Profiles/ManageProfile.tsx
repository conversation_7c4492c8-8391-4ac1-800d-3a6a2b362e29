import {
  Box,
  Divider,
  Heading,
  HStack,
  Show,
  Stack,
  useToast
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import capitalize from 'lodash/capitalize';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import Loading from '~/components/Loading/Loading';
import { ACCOUNT_VALIDATIONS } from '~/constants';
import {
  useCareRecipientProfile,
  useCreateCareRecipientProfileMutation,
  useEditCareRecipientProfileMutation,
  useRelationshipsQuery
} from '~/hooks/profiles';
import trackAnalyticsEvent from '~/utils/analytics';
import { customErrorMap } from '~/utils/zodCustomErrorMap';

import ActivitiesForm from './ActivitiesForm';
import NeedsForm from './NeedsForm';
import PersonalInformationForm from './PersonalInformationForm';
import ProfileActions from './ProfileActions';

const { valid_name_regex, invalid_field } = ACCOUNT_VALIDATIONS;

zod.setErrorMap(customErrorMap);
const schema = zod
  .object({
    firstName: zod.string().trim().nullable().optional(),
    lastName: zod.string().trim().nullable().optional(),
    relationshipType: zod
      .string({
        required_error: 'Relationship is required'
      })
      .min(1),
    city: zod.string().trim().nullable().optional(),
    state: zod.string().trim().nullable().optional(),
    needs: zod.array(zod.string()).nullable().optional(),
    activities: zod.array(zod.string()).nullable().optional()
  })
  .refine(
    (data) => !data.firstName || data.firstName?.match(valid_name_regex),
    {
      message: invalid_field,
      path: ['firstName']
    }
  )
  .refine((data) => !data.lastName || data.lastName?.match(valid_name_regex), {
    message: invalid_field,
    path: ['lastName']
  });

const ManageProfile: React.FC = () => {
  const router = useRouter();
  const toast = useToast();

  const careRecipientProfile = useCareRecipientProfile();
  const { mutate: createProfileMutate, isLoading: isLoadingCreateProfile } =
    useCreateCareRecipientProfileMutation();
  const { mutate: updateProfileMutate, isLoading: isLoadingUpdateProfile } =
    useEditCareRecipientProfileMutation();
  const { data: relationships, isLoading: isLoadingRelationships } =
    useRelationshipsQuery();

  const isCreation = !careRecipientProfile?.id;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    mode: 'onBlur',
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: careRecipientProfile?.firstName || '',
      lastName: careRecipientProfile?.lastName || '',
      city: careRecipientProfile?.city || '',
      state: careRecipientProfile?.state || '',
      relationshipType: capitalize(
        careRecipientProfile?.relationshipType ||
          relationships?.[0]?.name ||
          'Self'
      ),
      needs: [],
      activities: []
    }
  });

  const onSubmit = handleSubmit(async (data) => {
    const profile = {
      firstName: data.firstName || null,
      lastName: data.lastName || null,
      city: data.city || null,
      state: data.state || null,
      relationshipType: data.relationshipType,
      needs: data.needs?.map((needId) => ({ id: needId })) || null,
      activities:
        data.activities?.map((activitiesId) => ({
          id: activitiesId
        })) || null
    };

    if (isCreation) {
      createProfileMutate(profile, {
        onSuccess: () => {
          trackAnalyticsEvent('accounts_account_create_profile', {
            event_category: 'accounts',
            parameter_name: 'customer has created a profile'
          });
          toast({
            description: 'Profile created successfully.',
            status: 'success',
            duration: 3000,
            isClosable: true,
            position: 'top-right'
          });

          reset();
          router.push('/account/profiles');
        }
      });
    } else {
      updateProfileMutate(
        { ...profile, id: careRecipientProfile.id },
        {
          onSuccess: () => {
            toast({
              description: 'Profile updated successfully.',
              status: 'success',
              duration: 3000,
              isClosable: true,
              position: 'top-right'
            });

            router.push('/account/profiles');
          }
        }
      );
    }
  });

  if (isLoadingRelationships) {
    return <Loading />;
  }

  return (
    <Box width="full">
      <form onSubmit={onSubmit}>
        <HStack justify="space-between">
          <Heading as="h1" size="lg" py="6" pl="10">
            {isCreation
              ? 'Create Profile'
              : `${careRecipientProfile?.firstName || ''} ${
                  careRecipientProfile?.lastName || ''
                }`}
          </Heading>
          <Show above="lg">
            <HStack spacing={5}>
              <ProfileActions
                isLoadingProfile={
                  isLoadingCreateProfile || isLoadingUpdateProfile
                }
                reset={reset}
              />
            </HStack>
          </Show>
        </HStack>
        <Stack
          width="full"
          bgColor="gray.50"
          padding="6"
          spacing="6"
          pr={{ base: '6', lg: '9' }}
          divider={<Divider borderColor="gray.300" />}
        >
          <PersonalInformationForm register={register} errors={errors} />
          <NeedsForm register={register} errors={errors} />
          <ActivitiesForm register={register} errors={errors} />
        </Stack>
        <HStack mt={6} width="full" justifyContent="flex-end" spacing={5}>
          <ProfileActions
            isLoadingProfile={isLoadingCreateProfile || isLoadingUpdateProfile}
            reset={reset}
          />
        </HStack>
      </form>
    </Box>
  );
};

export default ManageProfile;
