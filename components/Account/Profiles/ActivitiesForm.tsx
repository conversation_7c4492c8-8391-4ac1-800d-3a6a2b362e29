import {
  Box,
  Checkbox,
  FormControl,
  FormErrorMessage,
  Heading,
  Stack,
  Text,
  VStack
} from '@chakra-ui/react';
import capitalize from 'lodash/capitalize';
import { FieldErrorsImpl, UseFormRegister } from 'react-hook-form';

import Loading from '~/components/Loading/Loading';
import {
  useActivitiesByProfileIdQuery,
  useActivitiesQuery
} from '~/hooks/profiles';
import { ActivityCategories } from '~/types/CareRecipientProfile';

interface ProfileFormRegister {
  firstName: string;
  lastName: string;
  city: string;
  state: string;
  relationshipType: string;
  needs: never[];
  activities: never[];
}

interface Props {
  register: UseFormRegister<ProfileFormRegister>;
  errors: Partial<FieldErrorsImpl<ProfileFormRegister>>;
}

const ActivitiesForm: React.FC<Props> = ({ register, errors }) => {
  const { data: activities, isLoading } = useActivitiesQuery();
  const { data: profileActivities, isLoading: isLoadingProfileActivities } =
    useActivitiesByProfileIdQuery();

  const renderOptions = (activity: ActivityCategories) => {
    return (
      <Checkbox
        {...register('activities')}
        key={activity.id}
        id={activity.id}
        value={activity.id}
        defaultChecked={Boolean(
          profileActivities?.find((profile) => profile.id === activity.id)
        )}
        name="activities"
        padding={2}
        pr={4}
        display="flex"
        flexDirection="row-reverse"
        justifyContent="space-between"
        border="1px solid #E2E8F0"
        borderRadius="6px"
        width="full"
        fontWeight="bold"
        size="sm"
        colorScheme="caringBlue"
        _checked={{
          borderColor: 'caringBlue.400'
        }}
      >
        {capitalize(activity.name || '')}
      </Checkbox>
    );
  };

  if (isLoading || isLoadingProfileActivities) {
    return <Loading />;
  }

  return (
    <Stack
      spacing="6"
      justify="space-between"
      direction={{ base: 'column', lg: 'row' }}
    >
      <VStack alignItems="start" spacing="1" width={{ base: '', lg: '600px' }}>
        <Heading as="h2" size="sm">
          What types of activities does this person enjoy?
        </Heading>
        <Text fontSize="sm" fontWeight="bold" pt={4}>
          Click all that apply
        </Text>
      </VStack>
      <Box
        maxW={{ base: '', lg: '735px' }}
        width="full"
        padding="5"
        bg="white"
        boxShadow="md"
        borderRadius="md"
      >
        <FormControl isInvalid={Boolean(errors.activities)}>
          <Stack spacing={4}>
            {activities?.map(renderOptions)}
            <FormErrorMessage>
              {String(errors?.activities?.message)}
            </FormErrorMessage>
          </Stack>
        </FormControl>
      </Box>
    </Stack>
  );
};

export default ActivitiesForm;
