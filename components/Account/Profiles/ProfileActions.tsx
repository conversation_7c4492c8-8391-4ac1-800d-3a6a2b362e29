import { Button, useDisclosure } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { UseFormReset } from 'react-hook-form';

import { useCareRecipientProfile } from '~/hooks/profiles';

import DeleteProfileModal from './DeleteProfileModal';

interface Props {
  isLoadingProfile: boolean;
  reset: UseFormReset<{
    firstName: string;
    lastName: string;
    city: string;
    state: string;
    relationshipType: string;
    needs: never[];
    activities: never[];
  }>;
}

const ProfileActions: React.FC<Props> = ({ isLoadingProfile, reset }) => {
  const router = useRouter();
  const careRecipientProfile = useCareRecipientProfile();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const isCreation = !careRecipientProfile;

  const handleCancel = () => {
    reset();
    router.push('/account/profiles');
  };

  return (
    <>
      {isCreation && (
        <Button
          variant="outline"
          size="md"
          minWidth="112px"
          width={{ base: 'full', lg: 'auto' }}
          onClick={handleCancel}
        >
          Cancel
        </Button>
      )}
      {!isCreation && (
        <>
          <Button
            variant="outline"
            size="md"
            minWidth="112px"
            width={{ base: 'full', lg: 'auto' }}
            onClick={onOpen}
          >
            Delete Profile
          </Button>
          <DeleteProfileModal
            profileId={String(careRecipientProfile?.id)}
            isOpen={isOpen}
            onClose={onClose}
          />
        </>
      )}
      <Button
        type="submit"
        colorScheme="caringGreen"
        bg="caringGreen.400"
        size="md"
        minWidth="112px"
        width={{ base: 'full', lg: 'auto' }}
        isLoading={isLoadingProfile}
      >
        {isCreation ? 'Create' : 'Save'}
      </Button>
    </>
  );
};

export default ProfileActions;
