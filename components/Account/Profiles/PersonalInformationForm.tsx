import {
  Box,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  VStack
} from '@chakra-ui/react';
import capitalize from 'lodash/capitalize';
import { FieldErrorsImpl, UseFormRegister } from 'react-hook-form';

import Loading from '~/components/Loading/Loading';
import { useRelationshipsQuery } from '~/hooks/profiles';
import { usStates } from '~/utils/UsStates';

interface ProfileFormRegister {
  firstName: string;
  lastName: string;
  city: string;
  state: string;
  relationshipType: string;
  needs: never[];
  activities: never[];
}
interface Props {
  register: UseFormRegister<ProfileFormRegister>;
  errors: Partial<FieldErrorsImpl<ProfileFormRegister>>;
}

const PersonalInformationForm: React.FC<Props> = ({ register, errors }) => {
  const { data: relationships, isLoading } = useRelationshipsQuery();

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Stack
      spacing="6"
      justify="space-between"
      direction={{ base: 'column', lg: 'row' }}
    >
      <VStack alignItems="start" spacing="1" width={{ base: '', lg: '600px' }}>
        <Heading as="h2" size="sm">
          Personal Information
        </Heading>
      </VStack>
      <Box
        maxW={{ base: '', lg: '735px' }}
        width="full"
        padding={{ base: '0', lg: '5' }}
        bg="white"
        boxShadow="md"
        borderRadius="md"
      >
        <Stack spacing="2" padding={{ base: '4', lg: '0' }}>
          <Stack spacing="4" direction={{ base: 'column', lg: 'row' }}>
            <FormControl isInvalid={Boolean(errors.firstName)}>
              <FormLabel>First Name</FormLabel>
              <Input {...register('firstName', { required: true })} />
              <FormErrorMessage>
                {String(errors?.firstName?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.lastName)}>
              <FormLabel>Last Name</FormLabel>
              <Input {...register('lastName', { required: true })} />
              <FormErrorMessage>
                {String(errors?.lastName?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
          <FormControl isInvalid={Boolean(errors.relationshipType)}>
            <FormLabel>Relationship</FormLabel>
            <Select {...register('relationshipType')}>
              {relationships?.map(({ name }) => (
                <option
                  key={`relationshipType-${name}`}
                  value={capitalize(name)}
                >
                  {capitalize(name)}
                </option>
              ))}
            </Select>
            <FormErrorMessage>
              {String(errors?.relationshipType?.message)}
            </FormErrorMessage>
          </FormControl>
          <Stack spacing="4" direction={{ base: 'column', lg: 'row' }}>
            <FormControl isInvalid={Boolean(errors.city)}>
              <FormLabel>City</FormLabel>
              <Input {...register('city')} />
              <FormErrorMessage>
                {String(errors?.city?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.state)}>
              <FormLabel>State</FormLabel>
              <Select {...register('state')}>
                {usStates.map(({ label, value }) => (
                  <option key={`state-${value}`} value={value}>
                    {label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>
                {String(errors?.state?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
};

export default PersonalInformationForm;
