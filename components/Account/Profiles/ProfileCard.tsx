import {
  Avatar,
  Box,
  Flex,
  Stack,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import React from 'react';

import { CareRecipientProfileDto } from '~/types/CareRecipientProfile';
import { toEllipsisText } from '~/utils/strings';

import DeleteProfileModal from './DeleteProfileModal';

interface Props {
  profile: Partial<CareRecipientProfileDto>;
}

const ProfileCard: React.FC<Props> = ({ profile }) => {
  const router = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const getProfileName = () => {
    const { firstName, lastName } = profile;
    const newName = toEllipsisText(
      `${firstName || ''} ${lastName || ''}`.trim()
    );
    return newName || null;
  };

  return (
    <Flex
      direction="column"
      rounded="lg"
      padding="8"
      position="relative"
      shadow="lg"
      bg="white"
      width="full"
    >
      <Box position="absolute" inset="0" height="20" roundedTop="inherit">
        <Flex
          position="absolute"
          left="90%"
          top="5%"
          color="gray.700"
          cursor="pointer"
          onClick={onOpen}
        >
          x
        </Flex>
        <DeleteProfileModal
          profileId={String(profile?.id)}
          isOpen={isOpen}
          onClose={onClose}
        />
      </Box>
      <Stack
        width="full"
        height="full"
        justifyContent="start"
        alignItems="center"
        _hover={{ cursor: 'pointer' }}
        onClick={() => router.push(`profiles/${profile?.id}`)}
      >
        <Avatar
          size="lg"
          bg="caringRed.700"
          color="white"
          name={`${getProfileName() || profile.relationshipType}`}
        />
        <Text mt="2" fontSize="xl" fontWeight="bold">
          {`${getProfileName() || ''}`}{' '}
          <Text as="span" fontSize="sm">{`(${profile.relationshipType})`}</Text>
        </Text>
      </Stack>
    </Flex>
  );
};

export default ProfileCard;
