import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
  Text,
  useToast
} from '@chakra-ui/react';
import { useRouter } from 'next/router';

import { useDeleteCareRecipientProfileMutation } from '~/hooks/profiles';

interface Props {
  profileId: string;
  isOpen: boolean;
  onClose: () => void;
}

const DeleteProfileModal: React.FC<Props> = ({
  profileId,
  isOpen,
  onClose
}) => {
  const toast = useToast();
  const router = useRouter();
  const { mutate, isLoading } = useDeleteCareRecipientProfileMutation();

  const handleDelete = () => {
    mutate(profileId, {
      onSuccess: () => {
        toast({
          description: 'Profile deleted successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top-right'
        });

        onClose();
        router.push('/account/profiles');
      }
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Delete profile</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text fontSize="sm">
            Are you sure? You can’t undo this action afterwards.
          </Text>
        </ModalBody>

        <ModalFooter>
          <Button variant="outline" size="md" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button
            colorScheme="caringRed"
            bg="caringRed.500"
            onClick={handleDelete}
            isLoading={isLoading}
          >
            Delete
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DeleteProfileModal;
