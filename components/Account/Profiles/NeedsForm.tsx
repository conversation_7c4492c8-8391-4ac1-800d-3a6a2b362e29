import {
  Box,
  Checkbox,
  FormControl,
  FormErrorMessage,
  Heading,
  Stack,
  Text,
  VStack
} from '@chakra-ui/react';
import capitalize from 'lodash/capitalize';
import { FieldErrorsImpl, UseFormRegister } from 'react-hook-form';

import Loading from '~/components/Loading/Loading';
import { useNeedsByProfileIdQuery, useNeedsQuery } from '~/hooks/profiles';
import { NeedCategories } from '~/types/CareRecipientProfile';

interface ProfileFormRegister {
  firstName: string;
  lastName: string;
  city: string;
  state: string;
  relationshipType: string;
  needs: never[];
  activities: never[];
}

interface Props {
  register: UseFormRegister<ProfileFormRegister>;
  errors: Partial<FieldErrorsImpl<ProfileFormRegister>>;
}

const NeedsForm: React.FC<Props> = ({ register, errors }) => {
  const { data: needs, isLoading } = useNeedsQuery();
  const { data: profileNeeds, isLoading: isLoadingProfileNeeds } =
    useNeedsByProfileIdQuery();

  const renderOptions = (need: NeedCategories) => {
    return (
      <Checkbox
        {...register('needs')}
        key={need.id}
        id={need.id}
        value={need.id}
        defaultChecked={Boolean(
          profileNeeds?.find((profile) => profile.id === need.id)
        )}
        padding={2}
        pr={4}
        display="flex"
        flexDirection="row-reverse"
        justifyContent="space-between"
        border="1px solid #E2E8F0"
        borderRadius="6px"
        width="full"
        fontWeight="bold"
        size="sm"
        colorScheme="caringBlue"
        _checked={{
          borderColor: 'caringBlue.400'
        }}
      >
        {capitalize(need.name || '')}
      </Checkbox>
    );
  };

  if (isLoading || isLoadingProfileNeeds) {
    return <Loading />;
  }

  return (
    <Stack
      spacing="6"
      justify="space-between"
      direction={{ base: 'column', lg: 'row' }}
    >
      <VStack alignItems="start" spacing="1" width={{ base: '', lg: '600px' }}>
        <Heading as="h2" size="sm">
          What type of care does this person require on a daily basis?
        </Heading>
        <Text fontSize="sm">
          Not all care providers have the services required, this will help us
          identify the appropriate care.
        </Text>
        <Text fontSize="sm" fontWeight="bold" pt={4}>
          Click all that apply
        </Text>
      </VStack>
      <Box
        maxW={{ base: '', lg: '735px' }}
        width="full"
        padding="5"
        bg="white"
        boxShadow="md"
        borderRadius="md"
      >
        <FormControl isInvalid={Boolean(errors.needs)}>
          <Stack spacing={4}>
            {needs?.map(renderOptions)}
            <FormErrorMessage>
              {String(errors?.needs?.message)}
            </FormErrorMessage>
          </Stack>
        </FormControl>
      </Box>
    </Stack>
  );
};

export default NeedsForm;
