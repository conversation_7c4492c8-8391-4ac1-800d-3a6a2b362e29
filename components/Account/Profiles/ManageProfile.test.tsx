import { CareRecipientProfileContext } from '~/contexts/CareRecipientProfileContext';
import {
  useActivitiesByProfileIdQuery,
  useActivitiesQuery,
  useCareRecipientProfile,
  useCreateCareRecipientProfileMutation,
  useEditCareRecipientProfileMutation,
  useNeedsByProfileIdQuery,
  useNeedsQuery,
  useRelationshipsQuery
} from '~/hooks/profiles';
import { render, screen } from '~/utils/test-utils';

import ManageProfile from './ManageProfile';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('~/hooks/profiles');

xdescribe('ManageProfile', () => {
  beforeAll(() => {
    (useCareRecipientProfile as jest.Mock).mockReturnValue(null);
    (useCreateCareRecipientProfileMutation as jest.Mock).mockReturnValue({
      mutate: null,
      isLoading: false
    });
    (useEditCareRecipientProfileMutation as jest.Mock).mockReturnValue({
      mutate: null,
      isLoading: false
    });
    (useRelationshipsQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
    (useNeedsByProfileIdQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
    (useNeedsQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
    (useActivitiesByProfileIdQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
    (useActivitiesQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
  });

  it('should render', async () => {
    render(
      <CareRecipientProfileContext.Provider value={null}>
        <ManageProfile />
      </CareRecipientProfileContext.Provider>
    );
    expect(
      await screen.findByRole('heading', { name: /create profile/i })
    ).toBeVisible();
    expect(
      await screen.findByRole('heading', { name: /personal information/i })
    ).toBeVisible();
    expect(
      await screen.findByRole('heading', {
        name: /what type of care does this person require on a daily basis\?/i
      })
    ).toBeVisible();
    expect(
      screen.getByRole('heading', {
        name: /what types of activities does this person enjoy\?/i
      })
    ).toBeVisible();
    expect(screen.getByRole('button', { name: /create/i })).toBeVisible();
  });

  describe('PersonalInformationForm', () => {
    it('should have all inputs', async () => {
      render(
        <CareRecipientProfileContext.Provider value={null}>
          <ManageProfile />
        </CareRecipientProfileContext.Provider>
      );
      expect(
        await screen.findByRole('textbox', { name: /first name/i })
      ).toBeVisible();
      expect(screen.getByRole('textbox', { name: /last name/i })).toBeVisible();
      expect(
        screen.getByRole('combobox', { name: /relationship/i })
      ).toBeVisible();
      expect(screen.getByRole('textbox', { name: /city/i })).toBeVisible();
      expect(screen.getByRole('combobox', { name: /state/i })).toBeVisible();
    });
  });
});
