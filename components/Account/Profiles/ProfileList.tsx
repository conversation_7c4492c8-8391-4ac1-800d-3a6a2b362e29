import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ta<PERSON>,
  Show,
  SimpleGrid,
  Stack,
  Text,
  VStack
} from '@chakra-ui/react';

import Loading from '~/components/Loading/Loading';
import { CareRecipientProfileContext } from '~/contexts/CareRecipientProfileContext';
import { useCareRecipientProfilesQuery } from '~/hooks/profiles';

import ProfileCard from './ProfileCard';

const redirectToCreateProfile = () => {
  // Note: Force window reload to clean the checkboxes state.
  // Is not working with the zod reset because the component is storing the state internally
  window.location.href = '/account/profiles/create';
};

const ProfileList: React.FC = () => {
  const { isLoading, data: profiles } = useCareRecipientProfilesQuery();

  if (isLoading) {
    return <Loading />;
  }
  return (
    <Box width="full">
      <HStack justify="space-between">
        <Heading as="h1" size="lg" py="6">
          Manage Profiles
        </Heading>
        <Show above="lg">
          <HStack spacing={5}>
            <Button
              type="submit"
              colorScheme="caringGreen"
              bg="caringGreen.400"
              size="md"
              minWidth="112px"
              width="auto"
              onClick={redirectToCreateProfile}
            >
              Create Profile
            </Button>
          </HStack>
        </Show>
      </HStack>
      <Stack
        width="full"
        bgColor="gray.50"
        padding="6"
        spacing="6"
        pr={{ base: '6', lg: '9' }}
      >
        <Stack
          spacing="6"
          justify="space-between"
          display="content"
          direction="row"
        >
          {Boolean(profiles?.length) && (
            <VStack alignItems="start" spacing="1">
              <Heading as="h2" size="sm">
                Profiles
              </Heading>
              <Text fontSize="sm">
                Edit your name, email, address or reset your password
              </Text>
              <SimpleGrid
                columns={{ base: 1, lg: 4 }}
                spacing="6"
                pt="8"
                width="full"
              >
                {profiles?.map((profile, index) => {
                  return (
                    <CareRecipientProfileContext.Provider
                      value={profile}
                      key={`profile-${index}`}
                    >
                      <ProfileCard profile={profile} />
                    </CareRecipientProfileContext.Provider>
                  );
                })}
              </SimpleGrid>
            </VStack>
          )}
          {!profiles?.length && (
            <Stack
              align="center"
              justify="center"
              spacing="1"
              width="full"
              minH="xs"
            >
              <Heading as="h2" size="md">
                No Profiles
              </Heading>
              <Text fontSize="sm">Add a person to get started</Text>
            </Stack>
          )}
        </Stack>
      </Stack>
      <Show below="lg">
        <HStack spacing={5} mt={6}>
          <Button
            type="submit"
            colorScheme="caringGreen"
            bg="caringGreen.400"
            size="md"
            width="full"
            onClick={redirectToCreateProfile}
          >
            Create Profile
          </Button>
        </HStack>
      </Show>
    </Box>
  );
};

export default ProfileList;
