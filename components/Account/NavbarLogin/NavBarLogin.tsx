import {
  <PERSON><PERSON>,
  H<PERSON>tack,
  Link,
  Show,
  useBreakpointValue
} from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { useRouter } from 'next/router';

import { useCurrentUser, useLogOutMutation } from '~/hooks/session';
import trackAnalyticsEvent from '~/utils/analytics';

import MobileAccountMenu from './MobileAccountMenu';

const NavBarLogin: React.FC = () => {
  const router = useRouter();
  const { data: user } = useCurrentUser();
  const { mutate: logout, isLoading } = useLogOutMutation();
  const avatarSize = useBreakpointValue({ base: 'sm', lg: 'md' });

  const redirectToSignUpPage = () => {
    trackAnalyticsEvent('accounts_account_signup', {
      event_category: 'accounts'
    });
    router.push('/account/sign-up');
  };

  const redirectToLoginPage = () => {
    router.push('/account/sign-in');
  };

  if (user) {
    return (
      <HStack spacing="6">
        <Show above="lg">
          <Button
            colorScheme="caringGreen"
            leftIcon="MdLogout"
            onClick={() => logout()}
            isLoading={isLoading}
            bg="caringGreen.400"
            elementAction={ElementActions.INTERNAL_LINK}
            elementName={ElementNames.HEADER_BUTTON}
            elementType={ElementTypes.LINK}
          >
            Log Out
          </Button>
          <Link href="/account/details">
            <Avatar
              name={`${user.firstName} ${user.lastName}`}
              size={avatarSize}
              bg="caringGreen.800"
              textColor="white"
              width={{ base: 8, lg: 10 }}
              height={{ base: 8, lg: 10 }}
            />
          </Link>
        </Show>
        <MobileAccountMenu>
          <Avatar
            name={`${user.firstName} ${user.lastName}`}
            size={avatarSize}
            bg="caringGreen.800"
            textColor="white"
            width={{ base: 8, lg: 10 }}
            height={{ base: 8, lg: 10 }}
          />
        </MobileAccountMenu>
      </HStack>
    );
  }

  return (
    <HStack spacing="6">
      <Show above="lg">
        <Button
          _hover={{
            textDecoration: 'none',
            color: 'caringGreen.500'
          }}
          variant="unstyled"
          onClick={redirectToSignUpPage}
          elementAction={ElementActions.INTERNAL_LINK}
          elementName={ElementNames.HEADER_BUTTON}
          elementType={ElementTypes.LINK}
        >
          Sign Up
        </Button>
        <Button
          colorScheme="caringGreen"
          bg="caringGreen.400"
          leftIcon="MdLogin"
          onClick={redirectToLoginPage}
          elementAction={ElementActions.INTERNAL_LINK}
          elementName={ElementNames.HEADER_BUTTON}
          elementType={ElementTypes.LINK}
        >
          Log In
        </Button>
      </Show>
    </HStack>
  );
};

export default NavBarLogin;
