import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  Show
} from '@chakra-ui/react';

import { useLogOutMutation } from '~/hooks/session';

type MobileAccountMenuProps = {
  children: React.ReactNode;
};
const MobileAccountMenu: React.FC<MobileAccountMenuProps> = (
  props: MobileAccountMenuProps
) => {
  const { mutate: logout } = useLogOutMutation();

  return (
    <Show below="lg">
      <Menu autoSelect={false}>
        <MenuButton>{props.children}</MenuButton>
        <MenuList>
          <MenuItem>
            <Link href="/account/details">Edit Profile</Link>
          </MenuItem>
          <MenuItem onClick={() => logout()}>Log Out</MenuItem>
        </MenuList>
      </Menu>
    </Show>
  );
};

export default MobileAccountMenu;
