import { screen } from '@testing-library/react';

import { UserAccount } from '~/types/user-account';
import { render, setDesktopScreen, setMobileScreen } from '~/utils/test-utils';

import NavBarLogin from './NavBarLogin';
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));
jest.mock('~/utils/analytics');
jest.mock('~/hooks/session', () => ({
  useCurrentUser: () => {
    return { data: { firstName: 'first', lastName: 'last' } as UserAccount };
  },
  useLogOutMutation: () => ''
}));

xdescribe('NavBarLogin', () => {
  test('renders user icon correctly on mobile', async () => {
    setMobileScreen();
    render(<NavBarLogin />);
    expect(
      screen.getByRole('button', {
        name: /first last/i
      })
    ).toBeInTheDocument();
  });

  test('renders user icon correctly on desktop', async () => {
    setDesktopScreen();
    render(<NavBarLogin />);
    expect(
      screen.getByRole('link', {
        name: /first last/i
      })
    ).toBeInTheDocument();
  });
});
