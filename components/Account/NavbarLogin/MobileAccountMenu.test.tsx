import { fireEvent, screen } from '@testing-library/react';

import { render, setMobileScreen } from '~/utils/test-utils';

import MobileAccountMenu from './MobileAccountMenu';

xdescribe('MobileAccountMenu', () => {
  test('renders children props', async () => {
    setMobileScreen();
    render(
      <MobileAccountMenu>
        <button data-testid="test">Test</button>
      </MobileAccountMenu>
    );
    expect(screen.getByTestId('test')).toBeInTheDocument();
  });

  test('renders edit profile button', async () => {
    setMobileScreen();
    render(
      <MobileAccountMenu>
        <button data-testid="test">Test</button>
      </MobileAccountMenu>
    );
    fireEvent.click(screen.getByTestId('test'));
    expect(
      await screen.findByRole('menuitem', { name: /Edit Profile/i })
    ).toBeInTheDocument();
  });

  test('renders log out button', async () => {
    setMobileScreen();
    render(
      <MobileAccountMenu>
        <button data-testid="test">Test</button>
      </MobileAccountMenu>
    );
    fireEvent.click(screen.getByTestId('test'));
    expect(
      await screen.findByRole('menuitem', { name: /Log Out/i })
    ).toBeInTheDocument();
  });
});
