import { useRouter } from 'next/router';

import { useCurrentUser, useLogInMutation } from '~/hooks/session';
import { fireEvent, render, screen, waitFor } from '~/utils/test-utils';

import SignIn from './SignIn';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {}
  }))
}));
jest.mock('~/hooks/session');

xdescribe('<SignIn />', () => {
  beforeAll(() => {
    (useCurrentUser as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false
    });
    (useLogInMutation as jest.Mock).mockReturnValue({
      mutate: jest.fn(),
      isLoading: false
    });
  });

  it('renders login page', async () => {
    render(<SignIn googleLoginUrl="" facebookLoginUrl="" />);

    expect(await screen.findByText('Log in to Caring')).toBeVisible();
    expect(screen.getByRole('button', { name: /Log in/i })).toBeVisible();
    expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/password/i)).toBeInTheDocument();
    expect(screen.getByText(/forgot password/i)).toBeVisible();
    expect(screen.getByText(/log in without password/i)).toBeVisible();
  });

  it('login button works', async () => {
    const mockRouter = { back: jest.fn(), push: jest.fn() };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    render(<SignIn googleLoginUrl="" facebookLoginUrl="" />);

    const signInButton = await screen.findByRole('button', { name: /Log in/i });
    const email = screen.getByPlaceholderText(/email/i);
    const password = screen.getByPlaceholderText(/password/i);

    expect(signInButton).toBeInTheDocument();
    fireEvent.click(signInButton);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(1);
    });

    fireEvent.change(email, {
      target: { value: '<EMAIL>' }
    });
    fireEvent.blur(email);
    expect(email).toHaveValue('<EMAIL>');

    fireEvent.change(password, {
      target: { value: '1adf3413' }
    });
    fireEvent.blur(password);
    expect(password).toHaveValue('1adf3413');

    fireEvent.click(signInButton);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(0);
    });
    expect(useLogInMutation).toHaveBeenCalled();
  });

  it('should check email validity', async () => {
    render(<SignIn googleLoginUrl="" facebookLoginUrl="" />);

    const email = await screen.findByPlaceholderText(/email/i);
    expect(email).toBeInTheDocument();

    fireEvent.change(email, {
      target: { value: 'testcaring.com' }
    });
    fireEvent.blur(email);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(1);
    });

    fireEvent.change(email, {
      target: { value: '1' }
    });
    fireEvent.blur(email);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(1);
    });

    fireEvent.change(email, {
      target: { value: '' }
    });
    fireEvent.blur(email);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(1);
    });

    fireEvent.change(email, {
      target: { value: '1abc@gmail' }
    });
    fireEvent.blur(email);
    await waitFor(() => {
      expect(
        screen.queryAllByText(
          /The email you have entered does not match the required format/
        ).length
      ).toBe(1);
    });
  });

  it('shows toast with error message', async () => {
    const mockRouter = {
      back: jest.fn(),
      push: jest.fn(),
      query: { error: 'error message' }
    };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    render(<SignIn googleLoginUrl="" facebookLoginUrl="" />);

    expect(await screen.findByText('error message')).toBeVisible();
  });
});
