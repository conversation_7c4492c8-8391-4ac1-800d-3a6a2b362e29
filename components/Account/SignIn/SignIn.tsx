import { Container, useToast, VStack } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

import { useCurrentUser } from '~/hooks/session';

import SignInForm from './SignInForm';
import SocialLogin, { SocialLoginProps } from './SocialLogin';

const SignIn: React.FC<SocialLoginProps> = ({
  facebookLoginUrl,
  googleLoginUrl
}) => {
  const router = useRouter();
  const toast = useToast();
  const { data: user, isLoading } = useCurrentUser();

  useEffect(() => {
    if (router.query?.error) {
      const id = 'signInError';
      if (!toast.isActive(id)) {
        toast({
          description: router.query.error,
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top-right',
          id
        });
      }
    }
  }, [router.query?.error, toast]);

  if (user) router.push('/account/details');

  const handleSignInSuccess = () => {
    router.push('/account/details');
  };

  if (isLoading) return null;

  return (
    <Container
      maxW="lg"
      py={{ base: '12', lg: '16' }}
      px={{ base: '0', lg: '8' }}
    >
      <VStack spacing={6}>
        <SignInForm onSuccess={handleSignInSuccess} />
        <SocialLogin
          facebookLoginUrl={facebookLoginUrl}
          googleLoginUrl={googleLoginUrl}
        />
      </VStack>
    </Container>
  );
};

export default SignIn;
