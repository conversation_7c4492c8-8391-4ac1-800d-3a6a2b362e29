import { render, screen } from '~/utils/test-utils';

import SocialButton from './SocialButton';

xdescribe('<SocialButton />', () => {
  it('renders social button for google', () => {
    render(<SocialButton href="" provider="google" />);
    expect(screen.getByText(/google/i)).toBeInTheDocument();
  });

  it('renders social button for facebook', () => {
    render(<SocialButton href="" provider="facebook" />);
    expect(screen.getByText(/facebook/i)).toBeInTheDocument();
  });
});
