import { Button, Text } from '@chakra-ui/react';
import React from 'react';
import { RiFacebookCircleFill, RiGoogleFill } from 'react-icons/ri';

interface Props {
  provider: 'google' | 'facebook';
  href: string;
}

const SocialButton: React.FC<Props> = ({ provider, href }) => {
  const Icon = provider === 'google' ? RiGoogleFill : RiFacebookCircleFill;
  return (
    <Button
      as="a"
      leftIcon={<Icon />}
      variant="outline"
      width="full"
      borderWidth="2px"
      borderColor="gray.400"
      textAlign="center"
      href={href}
    >
      <Text flex="1">
        Continue with {provider === 'facebook' ? 'Facebook' : 'Google'}
      </Text>
    </Button>
  );
};

export default SocialButton;
