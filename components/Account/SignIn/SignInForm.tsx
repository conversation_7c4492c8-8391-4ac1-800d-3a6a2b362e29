import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  Heading,
  HStack,
  Input,
  Link,
  Stack,
  Text,
  useBreakpointValue
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { customErrorMap } from '@utils/zodCustomErrorMap';
import Image from 'next/image';
import NextLink from 'next/link';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { LEGACY_BASE_URL } from '~/constants';
import { useLogInMutation } from '~/hooks/session';

zod.setErrorMap(customErrorMap);
const schema = zod.object({
  email: zod.string().email().trim(),
  password: zod.string().trim()
});

interface Props {
  onSuccess?: () => void;
  signUpAction?: () => void;
}

const SignInForm: React.FC<Props> = ({ onSuccess, signUpAction }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm({
    mode: 'onBlur',
    resolver: zodResolver(schema)
  });
  const { isLoading, mutate: login } = useLogInMutation();
  const headingSize = useBreakpointValue({ base: 'md', lg: 'lg' });

  const onSubmit = handleSubmit((data) => {
    login(
      { email: data.email, password: data.password },
      {
        onSuccess: () => {
          if (onSuccess) onSuccess();
        },
        onError: () => {
          setError('email', { message: 'Your credentials does not match' });
          setError('password', {
            message: `Password must have:
          - At least 8 number of characters
          - Capital and lowercase letters
          - Numbers 
          - At least one Special character
          `
          });
        }
      }
    );
  });

  return (
    <Box width="full">
      <Stack spacing={{ base: '2', md: '3' }} textAlign="center">
        <Link href={LEGACY_BASE_URL} title="Caring.com" isExternal>
          <Image
            src="/icons/caring-small.svg"
            width="80"
            height="80"
            alt="Caring Logo"
          />
        </Link>
        <Heading size={headingSize}>Log in to Caring</Heading>
        <Text fontSize="m" fontWeight="thin">
          Don`t have an account?{' '}
          {signUpAction ? (
            <Button
              onClick={signUpAction}
              variant="link"
              colorScheme="caringBlue"
            >
              Sign Up
            </Button>
          ) : (
            <NextLink href="/account/sign-up">
              <Link
                color={'caringBlue.500'}
                fontWeight="bold"
                data-testid="sign-up-link"
              >
                Sign Up
              </Link>
            </NextLink>
          )}
        </Text>
      </Stack>
      <form onSubmit={onSubmit}>
        <Stack spacing="6" padding="4">
          <Stack spacing="5">
            <FormControl isInvalid={Boolean(errors.email)}>
              <Input
                type="email"
                placeholder="Email"
                {...register('email', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.email?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.password)}>
              <Input
                type="password"
                placeholder="Password"
                {...register('password', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.password?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
          <Stack spacing={{ base: '3', lg: '6' }}>
            <Button
              type="submit"
              colorScheme="green"
              bg="caringGreen.400"
              isLoading={isLoading}
            >
              Log In
            </Button>
            <HStack fontSize="sm" justify="space-between">
              <NextLink href="/account/sign-in/one-time-link">
                <Link color="caringBlue.400" fontWeight="bold" float="right">
                  Log in without password
                </Link>
              </NextLink>
              <NextLink href="/account/forgot-password">
                <Link color="caringBlue.400" fontWeight="bold" float="right">
                  Forgot Password
                </Link>
              </NextLink>
            </HStack>
          </Stack>
        </Stack>
      </form>
    </Box>
  );
};

export default SignInForm;
