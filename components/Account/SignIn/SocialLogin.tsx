import { Divider, HStack, Text, VStack } from '@chakra-ui/react';
import React from 'react';

import SocialButton from './SocialButton';

export interface SocialLoginProps {
  facebookLoginUrl?: string;
  googleLoginUrl?: string;
}

const SocialLogin: React.FC<SocialLoginProps> = ({
  facebookLoginUrl,
  googleLoginUrl
}) => {
  return (
    <>
      <HStack px={4} width="full" justifyContent="space-between">
        <Divider borderColor="gray.400" />
        <Text fontSize="sm" fontWeight="thin">
          OR
        </Text>
        <Divider borderColor="gray.400" />
      </HStack>
      <VStack spacing={6} px={4} width="full">
        {facebookLoginUrl && (
          <SocialButton href={facebookLoginUrl} provider="facebook" />
        )}
        {googleLoginUrl && (
          <SocialButton href={googleLoginUrl} provider="google" />
        )}
      </VStack>
    </>
  );
};

export default SocialLogin;
