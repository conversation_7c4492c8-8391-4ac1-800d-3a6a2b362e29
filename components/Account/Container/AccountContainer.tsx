import {
  Container,
  <PERSON>,
  Show,
  <PERSON>ack,
  Tab,
  <PERSON>b<PERSON><PERSON>,
  TabPanel,
  TabPanels,
  Tabs
} from '@chakra-ui/react';
import { useRouter } from 'next/router';

import { useAccountPages } from '~/hooks/use-account-pages';

interface Props {
  currentPath: string;
}

const AccountContainer: React.FC<Props> = ({ currentPath }) => {
  const router = useRouter();
  const pages = useAccountPages();
  const tabIndex = pages.findIndex((page) => page.path === currentPath);
  const handleTabChange = (index: number) => {
    router.push(`${pages?.[index].path}`);
  };

  return (
    <Container maxWidth="container.xl" marginBottom={'20px'}>
      <Show above="lg">
        <Tabs
          orientation="vertical"
          borderColor="transparent"
          size="sm"
          onChange={handleTabChange}
          index={tabIndex}
        >
          <TabList
            ml="4"
            mr="10"
            mt="24"
            alignItems="start"
            minWidth="fit-content"
            maxWidth="180px"
          >
            {pages?.map(({ title, path, isHidden, isSubPage }) => (
              <Tab
                key={`tab-${path}`}
                fontWeight={isSubPage ? 'normal' : 'bold'}
                textAlign="start"
                mb={2}
                mt={isSubPage ? '-2' : ''}
                hidden={isHidden}
                _selected={{
                  color: 'caringBlue.400',
                  borderColor: 'caringBlue.400'
                }}
              >
                {title}
              </Tab>
            ))}
          </TabList>
          <TabPanels>
            {pages.map(({ component, path }) => (
              <TabPanel key={`panel-${path}`}>{component}</TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </Show>
      <Show below="lg">
        <Stack spacing="4" mt={6}>
          <Select
            variant="outline"
            focusBorderColor="caringGreen.400"
            iconColor="caringGreen.400"
            textColor="caringGreen.400"
            borderColor="caringGreen.400"
            onChange={(event) => handleTabChange(Number(event.target.value))}
            value={tabIndex}
          >
            {pages.map(({ path, title, isHidden }, index) => (
              <option key={`opt-${path}`} value={index} hidden={isHidden}>
                {title}
              </option>
            ))}
          </Select>
          {tabIndex >= 0 && pages[tabIndex].component}
        </Stack>
      </Show>
    </Container>
  );
};

export default AccountContainer;
