import { useAccountPages } from '~/hooks/use-account-pages';
import { render, screen, setDesktopScreen } from '~/utils/test-utils';

import AccountContainer from './AccountContainer';

jest.mock('next/router', () => require('next-router-mock'));
jest.mock('~/hooks/use-account-pages');

describe('AccountContainer', () => {
  beforeAll(() => {
    (useAccountPages as jest.Mock).mockReturnValue([
      {
        title: 'Account_Test',
        component: <p>test</p>,
        path: ''
      }
    ]);
  });

  it('should render for mobile', async () => {
    render(<AccountContainer currentPath={''} />);

    const selectOption = screen.getByRole('option', { name: 'Account_Test' });
    expect(selectOption).toBeVisible();
  });

  it('should render for desktop', async () => {
    setDesktopScreen();
    render(<AccountContainer currentPath={''} />);

    const selectOption = screen.queryByRole('option', { name: 'Account_Test' });
    expect(selectOption).not.toBeInTheDocument();

    const tabOption = screen.getByRole('tab', { name: 'Account_Test' });
    expect(tabOption).toBeInTheDocument();
  });
});
