import {
  useCurrentUser,
  useForgotPasswordMutation,
  useUpdateUserMutation
} from '~/hooks/session';
import { render, screen, setDesktopScreen } from '~/utils/test-utils';

import AccountDetails from './AccountDetails';

jest.mock('~/hooks/session');

xdescribe('AccountDetails', () => {
  (useCurrentUser as jest.Mock).mockReturnValue({ data: {} });
  (useUpdateUserMutation as jest.Mock).mockReturnValue({
    isLoading: true,
    mutate: jest.fn()
  });
  (useForgotPasswordMutation as jest.Mock).mockReturnValue({
    isLaoding: true,
    mutate: jest.fn()
  });

  it('should render', async () => {
    render(<AccountDetails />);

    expect(screen.getByText(/manage your personal information/i)).toBeVisible();
    expect(
      screen.getByRole('heading', {
        name: /reset password/i
      })
    ).toBeVisible();
  });

  describe('AccountForm', () => {
    it('should have all inputs', () => {
      render(<AccountDetails />);
      expect(
        screen.getByRole('textbox', {
          name: /first name/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /last name/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /email/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /phone/i
        })
      ).toBeVisible();
      expect(
        screen.getAllByRole('textbox', {
          name: /address/i
        })[0]
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /address 2/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /city/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('combobox', {
          name: /state/i
        })
      ).toBeVisible();
      expect(
        screen.getByRole('textbox', {
          name: /zip code/i
        })
      ).toBeVisible();
    });
  });

  describe('AccountDesktopHeader', () => {
    it('should render desktop Header', async () => {
      setDesktopScreen();
      render(<AccountDetails />);
      expect(screen.getAllByText('Account').length).toBe(2);
    });
  });
});
