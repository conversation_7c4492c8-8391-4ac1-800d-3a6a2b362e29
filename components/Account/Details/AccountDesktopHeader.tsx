import { But<PERSON>, <PERSON><PERSON>, HStack, Show } from '@chakra-ui/react';

interface Props {
  isLoading: boolean;
  handleCancel: () => void;
}

const AccountDesktopHeader: React.FC<Props> = ({ isLoading, handleCancel }) => {
  return (
    <Show above="lg">
      <HStack justify="space-between">
        <Heading as="h1" size="lg" py="6" pl="10">
          Account
        </Heading>
        <HStack spacing={5}>
          <Button
            variant="outline"
            size="md"
            minWidth="112px"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            colorScheme="caringGreen"
            bg="caringGreen.400"
            size="md"
            minWidth="112px"
            isLoading={isLoading}
          >
            Save
          </Button>
        </HStack>
      </HStack>
    </Show>
  );
};

export default AccountDesktopHeader;
