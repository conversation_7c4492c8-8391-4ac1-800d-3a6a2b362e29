import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Box,
  Button,
  Divider,
  Heading,
  HStack,
  Stack,
  Text,
  useBoolean,
  useToast,
  VStack
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { ACCOUNT_VALIDATIONS } from '~/constants';
import {
  useCurrentUser,
  useForgotPasswordMutation,
  useUpdateUserMutation
} from '~/hooks/session';
import { UserAccount } from '~/types/UserAccount';
import trackAnalyticsEvent from '~/utils/analytics';
import { getOnlyNumbers } from '~/utils/strings';
import { customErrorMap } from '~/utils/zodCustomErrorMap';

import AccountDesktopHeader from './AccountDesktopHeader';
import AccountForm from './AccountForm';

const { valid_name_regex, invalid_field, zip_code_regex } = ACCOUNT_VALIDATIONS;

zod.setErrorMap(customErrorMap);
const schema = zod
  .object({
    firstName: zod
      .string()
      .trim()
      .min(2)
      .regex(valid_name_regex, invalid_field),
    lastName: zod.string().trim().min(1).regex(valid_name_regex, invalid_field),
    email: zod.string().email().trim(),
    phoneNumber: zod.string().trim().min(10),
    addressLine: zod.string().trim().nullable().optional(),
    addressLine2: zod.string().trim().nullable().optional(),
    city: zod.string().trim().nullable().optional(),
    state: zod.string().trim().nullable().optional(),
    zipCode: zod.string().nullable().optional()
  })
  .refine(
    (data) => {
      const zipCode = getOnlyNumbers(data.zipCode || '');
      return (
        zipCode &&
        (!zipCode.length ||
          (typeof zipCode === 'string' &&
            zipCode.match(new RegExp(zip_code_regex))))
      );
    },
    {
      message: 'Please specify a valid US zip code',
      path: ['zipCode']
    }
  );

const AccountDetails: React.FC = () => {
  const [isPasswordResetSent, setIsPasswordResetSent] = useBoolean();
  const { data: user } = useCurrentUser();
  const { isLoading: isLoadingUpdateUser, mutate } = useUpdateUserMutation();
  const { isLoading: isLoadingResetPwd, mutate: forgotPassword } =
    useForgotPasswordMutation();
  const toast = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    mode: 'onBlur',
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phoneNumber: user?.phoneNumber || '',
      addressLine: user?.addressLine || '',
      addressLine2: user?.addressLine2 || '',
      city: user?.city || '',
      state: user?.state || '',
      zipCode: user?.zipCode || ''
    }
  });

  const onSubmit = handleSubmit(async (data) => {
    const account: UserAccount = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phoneNumber: data.phoneNumber,
      addressLine: data.addressLine,
      addressLine2: data.addressLine2,
      city: data.city,
      state: data.state,
      zipCode: getOnlyNumbers(data.zipCode)
    };

    mutate(account, {
      onSuccess: () => {
        toast({
          description: 'Account details updated',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top-right'
        });
        trackAnalyticsEvent('accounts_account_updated', {
          event_category: 'accounts',
          parameter_name: 'account updated method',
          dimension_name: [
            'First name',
            'Last name',
            'Email',
            'Phone number',
            'Address',
            'City',
            'state',
            'zip'
          ]
        });
      }
    });
  });

  const handleResetPassword = async () => {
    if (!user) return;
    forgotPassword(
      { email: user.email },
      {
        onSuccess: () => {
          setIsPasswordResetSent.on();
        }
      }
    );
  };

  const handleCancel = () => {
    reset({
      ...user,
      phoneNumber: user?.phoneNumber || '',
      zipCode: user?.zipCode || ''
    });
  };

  return (
    <Box width="full">
      <form onSubmit={onSubmit}>
        <AccountDesktopHeader
          isLoading={isLoadingUpdateUser}
          handleCancel={handleCancel}
        />
        <Stack
          width={{ base: 'full', lg: '96%' }}
          bgColor="gray.50"
          padding="6"
          spacing="6"
          pr={{ base: '6', lg: '9' }}
          divider={<Divider borderColor="gray.300" />}
        >
          <Stack
            spacing="6"
            justify="space-between"
            direction={{ base: 'column', lg: 'row' }}
          >
            <VStack
              alignItems="start"
              spacing="1"
              minWidth={{ base: '0', lg: 188 }}
            >
              <Heading as="h2" size="sm">
                Account
              </Heading>
              <Text fontSize="sm">Manage your personal information</Text>
            </VStack>
            <Box
              maxW="735px"
              width="full"
              padding={{ base: '0', lg: '5' }}
              bg="white"
              boxShadow="md"
              borderRadius="md"
            >
              <AccountForm register={register} errors={errors} />
            </Box>
          </Stack>
          <Stack
            spacing="6"
            justify="space-between"
            direction={{ base: 'column', lg: 'row' }}
          >
            <VStack
              alignItems="start"
              spacing="1"
              minWidth={{ base: '0', lg: 188 }}
            >
              <Heading as="h2" size="sm">
                Reset Password
              </Heading>
              <Text fontSize="sm">Manage your password</Text>
            </VStack>
            <Box
              maxW="735px"
              width="full"
              padding={{ base: '0', lg: '5' }}
              bg="white"
              boxShadow="md"
              borderRadius="md"
            >
              {!isPasswordResetSent && (
                <Button
                  variant="outline"
                  size="sm"
                  width={{ base: 'full', lg: 'auto' }}
                  isLoading={isLoadingResetPwd}
                  onClick={handleResetPassword}
                >
                  Reset Password
                </Button>
              )}
              {isPasswordResetSent && (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <Stack
                    spacing="2"
                    direction={{ base: 'column', lg: 'row' }}
                    justify="center"
                    align="center"
                  >
                    <AlertTitle minW="fit-content">
                      Reset Instruction sent
                    </AlertTitle>
                    <AlertDescription>
                      A message was sent to <b>{user?.email}</b> with a link to
                      reset your password. The link will expire in 24 hours.
                    </AlertDescription>
                  </Stack>
                </Alert>
              )}
            </Box>
          </Stack>
        </Stack>

        <HStack spacing={5} mt={6} width="full" justifyContent="flex-end">
          <Button
            variant="outline"
            size="md"
            minWidth="112px"
            width={{ base: 'full', lg: 'auto' }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            colorScheme="caringGreen"
            bg="caringGreen.400"
            size="md"
            minWidth="112px"
            width={{ base: 'full', lg: 'auto' }}
            isLoading={isLoadingUpdateUser}
          >
            Save
          </Button>
        </HStack>
      </form>
    </Box>
  );
};

export default AccountDetails;
