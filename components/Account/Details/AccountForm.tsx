import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Select,
  Stack
} from '@chakra-ui/react';
import {
  DeepRequired,
  FieldErrorsImpl,
  FieldValues,
  UseFormRegister
} from 'react-hook-form';
import InputMask from 'react-input-mask';

import { useCurrentUser } from '~/hooks/session';
import { usStates } from '~/utils/UsStates';

interface Props {
  register: UseFormRegister<{
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    addressLine: string;
    addressLine2: string;
    city: string;
    state: string;
    zipCode: string;
  }>;
  errors: FieldErrorsImpl<DeepRequired<FieldValues>>;
}

const AccountForm: React.FC<Props> = ({ register, errors }) => {
  const { data: user } = useCurrentUser();
  if (!user) return null;

  return (
    <Stack spacing="2" padding={{ base: '4', lg: '0' }}>
      <Stack spacing="4" direction={{ base: 'column', lg: 'row' }}>
        <FormControl isInvalid={Boolean(errors.firstName)}>
          <FormLabel>First Name</FormLabel>
          <Input {...register('firstName', { required: true })} />
          <FormErrorMessage>
            {String(errors?.firstName?.message)}
          </FormErrorMessage>
        </FormControl>
        <FormControl isInvalid={Boolean(errors.lastName)}>
          <FormLabel>Last Name</FormLabel>
          <Input {...register('lastName', { required: true })} />
          <FormErrorMessage>
            {String(errors?.lastName?.message)}
          </FormErrorMessage>
        </FormControl>
      </Stack>
      <FormControl isInvalid={Boolean(errors.email)}>
        <FormLabel>Email</FormLabel>
        <Input
          type="email"
          {...register('email', { required: true })}
          isDisabled={Boolean(user?.email)}
          _disabled={{
            bg: 'gray.100',
            color: 'blackAlpha.500'
          }}
        />
        <FormErrorMessage>{String(errors?.email?.message)}</FormErrorMessage>
      </FormControl>
      <FormControl isInvalid={Boolean(errors.phoneNumber)}>
        <FormLabel>Phone</FormLabel>
        <Input
          type="tel"
          as={InputMask}
          mask="+****************"
          {...register('phoneNumber', { required: true })}
        />
        <FormErrorMessage>
          {String(errors?.phoneNumber?.message)}
        </FormErrorMessage>
      </FormControl>
      <FormControl isInvalid={Boolean(errors.addressLine)}>
        <FormLabel>Address</FormLabel>
        <Input {...register('addressLine')} />
        <FormErrorMessage>
          {String(errors?.addressLine?.message)}
        </FormErrorMessage>
      </FormControl>
      <FormControl isInvalid={Boolean(errors.addressLine2)}>
        <FormLabel>Address 2</FormLabel>
        <Input {...register('addressLine2')} />
        <FormErrorMessage>
          {String(errors?.addressLine2?.message)}
        </FormErrorMessage>
      </FormControl>
      <Stack spacing="4" direction={{ base: 'column', lg: 'row' }}>
        <FormControl isInvalid={Boolean(errors.city)}>
          <FormLabel>City</FormLabel>
          <Input {...register('city')} />
          <FormErrorMessage>{String(errors?.city?.message)}</FormErrorMessage>
        </FormControl>
        <FormControl isInvalid={Boolean(errors.state)}>
          <FormLabel>State</FormLabel>
          <Select {...register('state')}>
            {usStates.map(({ label, value }) => (
              <option key={`state-${value}`} value={value}>
                {label}
              </option>
            ))}
          </Select>
          <FormErrorMessage>{String(errors?.state?.message)}</FormErrorMessage>
        </FormControl>
        <FormControl isInvalid={Boolean(errors.zipCode)}>
          <FormLabel>Zip Code</FormLabel>
          <Input {...register('zipCode')} as={InputMask} mask="99999" />
          <FormErrorMessage>
            {String(errors?.zipCode?.message)}
          </FormErrorMessage>
        </FormControl>
      </Stack>
    </Stack>
  );
};

export default AccountForm;
