import {
  Button,
  Checkbox,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  HStack,
  Input,
  Link,
  Stack,
  Text,
  useBreakpointValue
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import NextLink from 'next/link';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import {
  ACCOUNT_VALIDATIONS,
  CARING_CONTACT_BY_TELEPHONE_URL,
  CARING_PRIVACY_URL,
  CARING_TERMS_URL,
  LEGACY_BASE_URL
} from '~/constants';
import { useSignUpMutation } from '~/hooks/session';
import trackAnalyticsEvent from '~/utils/analytics';
import { customErrorMap } from '~/utils/zodCustomErrorMap';

const {
  password_strength_regex,
  password_strength_message,
  valid_name_regex,
  invalid_field
} = ACCOUNT_VALIDATIONS;

zod.setErrorMap(customErrorMap);
const schema = zod
  .object({
    firstName: zod
      .string()
      .trim()
      .min(2)
      .regex(valid_name_regex, invalid_field),
    lastName: zod.string().trim().min(1).regex(valid_name_regex, invalid_field),
    email: zod.string().email().trim(),
    password: zod
      .string()
      .trim()
      .min(8)
      .regex(password_strength_regex, password_strength_message),
    confirmPassword: zod
      .string()
      .trim()
      .min(8)
      .regex(password_strength_regex, password_strength_message),
    acceptedTermsAndConditions: zod.literal(true)
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword']
  });

interface Props {
  logInAction?: () => void;
  onSuccess?: () => void;
}

const SignUpForm: React.FC<Props> = ({ logInAction, onSuccess }) => {
  const { isLoading, mutate: signUp } = useSignUpMutation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm({ mode: 'onBlur', resolver: zodResolver(schema) });

  const onSubmit = handleSubmit(async (data) => {
    signUp(
      {
        firstName: data.firstName,
        lastName: data.lastName,
        password: data.password,
        confirmPassword: data.confirmPassword,
        email: data.email,
        acceptedTermsAndConditions: data.acceptedTermsAndConditions
      },
      {
        onSuccess: () => {
          trackAnalyticsEvent('accounts_account_created', {
            event_category: 'accounts',
            dimension_name: 'email'
          });
          if (onSuccess) onSuccess();
        },
        onError: () => {
          setError('email', { type: 'custom', message: 'Incorrect Email' });
          setError('password', {
            type: 'custom',
            message: 'Incorrect Password'
          });
          setError('confirmPassword', {
            type: 'custom',
            message: 'Incorrect Password'
          });
        }
      }
    );
  });

  return (
    <>
      <Stack spacing={{ base: '2', md: '3' }} textAlign="center">
        <Link
          href={LEGACY_BASE_URL}
          title="Caring create account Page"
          isExternal
        >
          <Image
            src="/icons/caring-small.svg"
            width="80"
            height="80"
            alt="Caring Logo"
          />
        </Link>
        <Heading size={useBreakpointValue({ base: 'md', lg: 'lg' })}>
          Create an account
        </Heading>
        <Text fontSize="m" fontWeight="thin">
          Already have an account?{' '}
          {logInAction ? (
            <Button
              onClick={logInAction}
              colorScheme="caringBlue"
              variant="link"
            >
              Log In
            </Button>
          ) : (
            <NextLink href="/account/sign-in">
              <Link color={'caringBlue.500'} fontWeight="bold">
                Log In
              </Link>
            </NextLink>
          )}
        </Text>
      </Stack>
      <form onSubmit={onSubmit}>
        <Stack spacing="6" padding="4">
          <Stack spacing="5">
            <FormControl isInvalid={Boolean(errors.firstName)}>
              <Input
                placeholder="First Name"
                {...register('firstName', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.firstName?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.lastName)}>
              <Input
                placeholder="Last Name"
                {...register('lastName', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.lastName?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.email)}>
              <Input
                type="email"
                placeholder="Email"
                {...register('email', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.email?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.password)}>
              <Input
                type="password"
                placeholder="Password"
                {...register('password', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.password?.message)}
              </FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors.confirmPassword)}>
              <FormLabel>Retype password</FormLabel>
              <Input
                type="password"
                placeholder="Password"
                {...register('confirmPassword', { required: true })}
              />
              <FormErrorMessage>
                {String(errors?.confirmPassword?.message)}
              </FormErrorMessage>
            </FormControl>
          </Stack>
          <Stack spacing="6">
            <Button
              type="submit"
              colorScheme="green"
              bg="caringGreen.400"
              isLoading={isLoading}
            >
              Create Account
            </Button>
          </Stack>
          <HStack justify="space-between">
            <FormControl isInvalid={Boolean(errors.acceptedTermsAndConditions)}>
              <Checkbox
                {...register('acceptedTermsAndConditions', {
                  required: true
                })}
              >
                I agree to the{' '}
                <Link
                  href={CARING_TERMS_URL}
                  isExternal
                  color={'caringBlue.400'}
                  fontWeight="bold"
                >
                  Terms of Service
                </Link>
                ,{' '}
                <Link
                  href={CARING_CONTACT_BY_TELEPHONE_URL}
                  isExternal
                  color={'caringBlue.400'}
                  fontWeight="bold"
                >
                  Agreement to be contacted
                </Link>
                , and{' '}
                <Link
                  href={CARING_PRIVACY_URL}
                  isExternal
                  color={'caringBlue.400'}
                  fontWeight="bold"
                >
                  Privacy Policy
                </Link>
              </Checkbox>
            </FormControl>
          </HStack>
        </Stack>
      </form>
    </>
  );
};

export default SignUpForm;
