import { useRouter } from 'next/router';

import { useSignUpMutation } from '~/hooks/session';
import { fireEvent, render, screen, waitFor } from '~/utils/test-utils';

import SignUp from './SignUp';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));
jest.mock('~/hooks/session');

xdescribe('SignUp', () => {
  beforeAll(() => {
    (useSignUpMutation as jest.Mock).mockReturnValue({
      mutate: jest.fn(),
      isLoading: false
    });
  });

  it('should render and display the fields properly', async () => {
    render(<SignUp />);

    expect(screen.getByText(/Create an account/)).toBeVisible();
    expect(screen.getByText(/Already have an account/)).toBeVisible();
    expect(screen.getByText(/I agree to/)).toBeVisible();
  });

  it('should validate required fields', async () => {
    render(<SignUp />);

    const submitButton = screen.getByRole('button', {
      name: /create account/i
    });

    expect(submitButton).toBeInTheDocument();
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.queryAllByText(/String must contain/).length).toBe(4);
    });
    expect(
      screen.queryAllByText(
        /The email you have entered does not match the required format/
      ).length
    ).toBe(1);
  });

  it('should submit', async () => {
    const mockRouter = { back: jest.fn(), push: jest.fn() };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    render(<SignUp />);

    const submitButton = screen.getByRole('button', {
      name: /create account/i
    });
    const firstNameInput = screen.getByPlaceholderText(/First Name/);
    const lastNameInput = screen.getByPlaceholderText(/Last Name/);
    const emailInput = screen.getByPlaceholderText(/Email/);
    const passwordInputs = screen.getAllByPlaceholderText(/Password/);

    expect(submitButton).toBeInTheDocument();
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.queryAllByText(/String must contain/).length).toBe(4);
    });

    fireEvent.change(firstNameInput, {
      target: { value: 'Test' }
    });
    fireEvent.blur(firstNameInput);
    expect(firstNameInput).toHaveValue('Test');

    fireEvent.change(lastNameInput, {
      target: { value: 'Test' }
    });
    fireEvent.blur(lastNameInput);

    fireEvent.change(emailInput, {
      target: { value: '<EMAIL>' }
    });
    fireEvent.blur(emailInput);

    fireEvent.change(passwordInputs[0], {
      target: { value: 'T$st3123' }
    });
    fireEvent.blur(passwordInputs[0]);

    fireEvent.change(passwordInputs[1], {
      target: { value: 'T$st3123' }
    });
    fireEvent.blur(passwordInputs[1]);

    fireEvent.click(screen.getByText(/I agree to/));

    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.queryAllByText(/String must contain/).length).toBe(0);
    });
    expect(useSignUpMutation).toHaveBeenCalled();
  });
});
