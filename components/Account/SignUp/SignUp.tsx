import { Container, VStack } from '@chakra-ui/react';
import { useRouter } from 'next/router';

import SocialLogin, { SocialLoginProps } from '../SignIn/SocialLogin';
import SignUpForm from './SignUpForm';

const SignUp: React.FC<SocialLoginProps> = ({
  googleLoginUrl,
  facebookLoginUrl
}) => {
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/account/details');
  };

  return (
    <Container
      maxW="lg"
      py={{ base: '12', lg: '16' }}
      px={{ base: '0', lg: '8' }}
    >
      <VStack spacing={6}>
        <SignUpForm onSuccess={handleSuccess} />
        <SocialLogin
          googleLoginUrl={googleLoginUrl}
          facebookLoginUrl={facebookLoginUrl}
        />
      </VStack>
    </Container>
  );
};

export default SignUp;
