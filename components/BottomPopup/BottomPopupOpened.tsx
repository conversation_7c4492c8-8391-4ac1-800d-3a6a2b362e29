import { Icon } from '@chakra-ui/icon';
import { Box, Heading, HStack } from '@chakra-ui/layout';
import { useContext } from 'react';
import { MdClose } from 'react-icons/md';

import { BottomPopupContext, ChildrenAsProps } from './BottomPopup';

export type BottomPopupOpenedProps = ChildrenAsProps & { title?: string };

const BottomPopupOpened: React.FC<BottomPopupOpenedProps> = ({
  children,
  title = ''
}) => {
  const { isOpen, toggle } = useContext(BottomPopupContext);

  if (!isOpen) return null;

  return (
    <Box
      bottom="0"
      pos="fixed"
      display="flex"
      flexDirection="column"
      gap={0}
      right={{ sm: '0', md: '20' }}
      w={['full', 'full', 'sm']}
      border="1px"
      borderColor="gray.100"
      zIndex="modal"
      maxH="100vh"
      overflowY="auto"
    >
      <HStack justifyContent="space-between" bgColor="gray.300" px={4} py={2}>
        <Heading size="sm">{title}</Heading>
        <Icon as={MdClose} boxSize="5" onClick={toggle} cursor="pointer" />
      </HStack>
      <Box p={4} bgColor="white" color="black">
        {children}
      </Box>
    </Box>
  );
};

export default BottomPopupOpened;
