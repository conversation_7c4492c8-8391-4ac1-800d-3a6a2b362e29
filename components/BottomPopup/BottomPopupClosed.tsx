import { Icon } from '@chakra-ui/icons';
import { Box, BoxProps, Heading, HStack } from '@chakra-ui/layout';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { ChatOverlayPopupType, Visibility } from '@components/Navigation';
import { STRING_TO_ICON_CLASS, StringToIconKeys } from '@components/RenderIcon';
import { SessionStorageKeys } from '@hooks/use-session-storage-value';
import sessionStore from '@utils/sessionStorage';
import { showOnDesktop, showOnMobile } from '@utils/visibility';
import { useContext } from 'react';
import { MdClose } from 'react-icons/md';

import { BottomPopupContext } from './BottomPopup';

export type BottomPopupClosedProps = {
  field: ChatOverlayPopupType;
  title?: string;
  icon?: StringToIconKeys;
  color?: string;
  size?: string;
  variant?: string;
  visibility?: Visibility;
};

const BottomPopupClosed: React.FC<BottomPopupClosedProps> = ({
  field: type,
  title = '',
  icon,
  color,
  size,
  variant,
  visibility = 'always'
}) => {
  const { isOpen, toggle } = useContext(BottomPopupContext);
  const elementClicked = useElementClicked();

  const onClickClose = () => {
    try {
      sessionStore.set(SessionStorageKeys.HIDE_BOTTOM_POPUP, true);
    } catch (err) {
      console.error('BottomPopupClosed: sessionStorage is not available');
    }
  };

  if (isOpen) return null;

  const wrapperCommonProps: BoxProps = {
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopRadius: { sm: '0', md: 'lg' },
    bottom: '0',
    display: {
      base: showOnMobile(visibility) ? 'flex' : 'none',
      md: showOnDesktop(visibility) ? 'flex' : 'none'
    },
    gap: '4',
    pos: 'fixed',
    px: '4',
    py: '3',
    right: { sm: '0', md: '20' },
    w: ['full', 'full', 'sm'],
    cursor: 'pointer',
    zIndex: 1001
  };

  return type === 'floatingCTA' ? (
    <Box {...wrapperCommonProps}>
      <Button
        colorScheme={color}
        leftIcon={icon}
        variant={variant}
        size={size}
        w="full"
        fontSize={16}
        elementType={ElementTypes.BUTTON}
        elementName={ElementNames.FLOATING_CTA}
        elementAction={ElementActions.OPEN_MODAL}
        onClick={toggle}
      >
        {title}
      </Button>
    </Box>
  ) : (
    <Box
      {...wrapperCommonProps}
      bgColor="primary.500"
      color="white"
      onClick={() => {
        toggle();
        elementClicked({
          element: {
            type: ElementTypes.BUTTON,
            action: ElementActions.OPEN_MODAL,
            name: ElementNames.INFO_REQUEST_SECTION,
            text: title,
            color: 'primary.500',
            textColor: 'white'
          }
        });
      }}
    >
      <HStack>
        {icon && <Icon as={STRING_TO_ICON_CLASS[icon]} boxSize="5" />}
        {title && (
          <Heading size="sm" color="white">
            {title}
          </Heading>
        )}
      </HStack>
      <Icon as={MdClose} boxSize="5" onClick={onClickClose} />
    </Box>
  );
};

export default BottomPopupClosed;
