'use client';

import useSessionStorageValue, {
  SessionStorageKeys
} from '@hooks/use-session-storage-value';
import { createContext, useCallback, useState } from 'react';

import BottomPopupClosed, { BottomPopupClosedProps } from './BottomPopupClosed';
import BottomPopupOpened, { BottomPopupOpenedProps } from './BottomPopupOpened';

export type ButtonPopupType = BottomPopupClosedProps;
export type ChildrenAsProps = {
  children?: React.ReactNode;
};

type BottomPopupContextType = {
  isOpen: boolean;
  toggle: () => void;
};

export const BottomPopupContext = createContext<BottomPopupContextType>({
  isOpen: false,
  toggle: () => {}
});

interface BottomPopupCompound {
  Closed: React.FC<BottomPopupClosedProps>;
  Opened: React.FC<BottomPopupOpenedProps>;
}

const BottomPopup: React.FC<ChildrenAsProps> & BottomPopupCompound = ({
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const toggle = useCallback(() => setIsOpen((state) => !state), []);
  const hideBottomPopup = useSessionStorageValue(
    SessionStorageKeys.HIDE_BOTTOM_POPUP
  );

  if (hideBottomPopup) return null;

  return (
    <BottomPopupContext.Provider value={{ isOpen, toggle }}>
      {children}
    </BottomPopupContext.Provider>
  );
};

BottomPopup.Closed = BottomPopupClosed;
BottomPopup.Opened = BottomPopupOpened;

export default BottomPopup;
