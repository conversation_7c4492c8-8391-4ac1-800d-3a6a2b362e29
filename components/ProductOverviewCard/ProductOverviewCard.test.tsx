import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';
import { createID } from '@utils/strings';
import {
  DAM_IMAGE_FIXTURE,
  fireEvent,
  render,
  screen
} from '@utils/test-utils';

import segmentEvents from '~/config/segment-events';
import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
import { MagnoliaImage } from '~/types/Magnolia';

import ProductOverviewCard, {
  ProductOverviewCardProps
} from './ProductOverviewCard';

describe('Product Overview Card', () => {
  const mockAnalytics = {
    track: jest.fn()
  };
  window.tracking = mockAnalytics;

  const mockSiteContext: SiteContextType = {
    site: {
      path: 'caring.com',
      name: 'Caring.com',
      domains: ['caring.com'],
      segmentWriteKey: 'key',
      segmentCdnURL: 'cdn',
      partnerToken: 'token',
      publicFolder: 'folder'
    }
  };

  const mockImage = {
    ...DAM_IMAGE_FIXTURE,
    '@name': 'bha_hear.com',
    '@path': '/Caring.com/MobileHelp.png',
    metadata: {
      fileName: 'MobileHelp.png',
      mimeType: 'image/png',
      caption: 'Mobile Help',
      title: 'Mobile Help',
      fileSize: 152869,
      height: 441,
      width: 413
    }
  } as MagnoliaImage;

  const mockBulletPoint = {
    '@name': 'bulletPoints0',
    bulletPoint: 'Starts at $19.95 per month',
    '@nodes': []
  };

  const card: ProductOverviewCardProps = {
    title: 'MobileHelp',
    headingElement: 'h3',
    headingSize: 'md',
    headingMarginTop: 20,
    image: {
      field: 'damChooser',
      image: mockImage
    },
    badge: 'badge',
    bulletPoints: {
      bulletPoints0: {
        ...mockBulletPoint,
        bulletPoint: 'Starts at $19.95 per month'
      },
      bulletPoints1: {
        ...mockBulletPoint,
        '@name': 'bulletPoints1',
        bulletPoint: 'In-Home and Mobile'
      },
      bulletPoints2: {
        ...mockBulletPoint,
        '@name': 'bulletPoints2',
        bulletPoint: 'No Landline Required'
      },
      bulletPoints3: {
        ...mockBulletPoint,
        '@name': 'bulletPoints3',
        bulletPoint: 'Starts at $24.95 per month'
      },
      '@nodes': [
        'bulletPoints0',
        'bulletPoints1',
        'bulletPoints2',
        'bulletPoints3'
      ]
    },
    readReviewText: 'Read Our Review',
    readReviewId: 'Most Affordable In-Home System',
    ctaText: 'See Packages',
    ctaState: 'solid',
    ctaWidth: 'fit-content',
    ctaSize: 'md',
    ctaUrl:
      'https://buy.mobilehelp.com/?PubID=CaringWeb&DNIS=**********&SubID=ma-cr-car160&CampaignID=1057',
    ctaBehavior: '_blank',
    ctaRel: ['nofollow'],
    ctaBgColor: 'orange',
    ctaTextColor: 'white',
    withContainer: true,
    productName: 'Mobile Help - Medical Alert',
    productCategory: 'devices',
    productCategoryId: undefined,
    productSubCategory: 'medical-alerts',
    productId: undefined,
    productProvider: 'Mobile Help',
    productProviderId: undefined,
    productMonetized: '1',
    productLocationId: '2'
  };

  it('render feature card', () => {
    const { getByText, getByRole, getByAltText } = render(
      <SiteContext.Provider value={mockSiteContext}>
        <ProductOverviewCard
          title={card.title}
          headingElement={card.headingElement}
          headingSize={card.headingSize}
          headingMarginTop={card.headingMarginTop}
          image={card.image}
          badge={card.badge}
          bulletPoints={card.bulletPoints}
          readReviewText={card.readReviewText}
          readReviewId={card.readReviewId}
          ctaText={card.ctaText}
          ctaState={card.ctaState}
          ctaWidth={card.ctaWidth}
          ctaSize={card.ctaSize}
          ctaUrl={card.ctaUrl}
          ctaBehavior={card.ctaBehavior}
          ctaRel={card.ctaRel}
          ctaBgColor={card.ctaBgColor}
          ctaTextColor={card.ctaTextColor}
          withContainer={card.withContainer}
          productName={card.productName}
          productCategory={card.productCategory}
          productCategoryId={card.productCategoryId}
          productSubCategory={card.productSubCategory}
          productId={card.productId}
          productProvider={card.productProvider}
          productProviderId={card.productProviderId}
          productMonetized={card.productMonetized}
          productLocationId={card.productLocationId}
        />
      </SiteContext.Provider>
    );

    expect(getByAltText(mockImage.metadata.caption));
    expect(getByText(card.title)).toBeInTheDocument();
    expect(getByText('Starts at $19.95 per month')).toBeInTheDocument();
    expect(getByText('In-Home and Mobile')).toBeInTheDocument();
    expect(getByText('No Landline Required')).toBeInTheDocument();
    expect(getByText('Starts at $24.95 per month')).toBeInTheDocument();
    const readReview = screen.getByRole('link', {
      name: `${card.readReviewText}`
    });
    expect(readReview).toHaveAttribute(
      'href',
      `#${createID(card.readReviewId)}`
    );

    expect(getByText(card.badge)).toBeInTheDocument();
    const seePackages = screen.getByRole('link', { name: `${card.ctaText}` });
    expect(seePackages).toHaveAttribute('href', `${card.ctaUrl}`);
    expect(seePackages).toHaveAttribute('target', `${card.ctaBehavior}`);
    expect(seePackages).toHaveAttribute('rel', `${card.ctaRel?.join(' ')}`);
    fireEvent.click(seePackages);
    expect(mockAnalytics.track).toHaveBeenCalled();
    expect(mockAnalytics.track).toHaveBeenCalledWith(
      segmentEvents.PRODUCT_CLICKED,
      {
        product_name: card.productName,
        product_category: card.productCategory,
        product_category_id: null,
        product_sub_category: card.productSubCategory,
        product_id: null,
        product_location_id: card.productLocationId,
        product_provider: card.productProvider,
        product_provider_id: null,
        product_monetized: card.productMonetized,
        product_url: 'https://buy.mobilehelp.com/',
        product_url_query: {
          CampaignID: '1057',
          DNIS: '**********',
          PubID: 'CaringWeb',
          SubID: 'ma-cr-car160'
        },
        color: card.ctaBgColor,
        name: ElementNames.PRODUCT_OVERVIEW_CARD,
        text: card.ctaText,
        textColor: card.ctaTextColor,
        action: ElementActions.EXTERNAL_LINK,
        type: ElementTypes.BUTTON,
        page_session_id: '',
        session_id: '',
        dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
      }
    );
  });
});
