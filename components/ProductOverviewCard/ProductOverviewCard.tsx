'use client';

import { Button, ButtonProps } from '@chakra-ui/button';
import { Box, Heading, SimpleGrid } from '@chakra-ui/layout';
import { Link, List, ListItem, useBreakpointValue } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { useProductClicked } from '@components/Analytics/events/ProductClicked';
import HtmlToReact from '@components/HtmlToReact';
import StoryImage from '@components/Image/StoryImage';
import { createID } from '@utils/strings';
import dynamic from 'next/dynamic';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import { MagnoliaImage } from '~/types/Magnolia';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

interface BulletPoint {
  '@name': string;
  bulletPoint: string;
}

interface BulletPoints {
  bulletPoints0?: BulletPoint;
  bulletPoints1?: BulletPoint;
  bulletPoints2?: BulletPoint;
  bulletPoints3?: BulletPoint;
  '@nodes': Array<string>;
}

export interface ProductOverviewCardProps {
  title: string;
  headingElement?: HeadingElements;
  headingSize?: HeadingSizes | HeadingSizes[];
  headingMarginTop?: 20 | 8;
  image: {
    field: 'damChooser' | 'externalSource';
    image?: MagnoliaImage;
    imageAlt?: string;
    imageUrl?: string;
  };
  badge: string;
  bulletPoints: BulletPoints;
  readReviewText: string;
  readReviewId: string;
  ctaText: string;
  ctaState: 'solid' | 'outline' | 'ghost';
  ctaWidth: 'fit-content' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '100%';
  ctaSize: ButtonProps['size'];
  ctaUrl: string;
  ctaBehavior: '_blank' | '_self' | '_parent' | '_top';
  ctaRel?: Array<
    'external' | 'nofollow' | 'noopener' | 'noreferrer' | 'opener'
  >;
  ctaBgColor: string;
  ctaTextColor: string;
  productName: string;
  productCategory: string;
  productCategoryId?: string | null;
  productSubCategory: string;
  productId?: string | null;
  productProvider: string;
  productProviderId?: string | null;
  productMonetized: string;
  productLocationId: string;
  withContainer?: boolean;
}

const ProductOverviewCard = ({
  title,
  headingElement,
  headingSize,
  headingMarginTop = 20,
  image,
  badge = '',
  bulletPoints,
  readReviewText = 'Read Our Review',
  readReviewId,
  ctaText = 'See Packages',
  ctaState,
  ctaWidth,
  ctaSize = 'md',
  ctaUrl,
  ctaBehavior,
  ctaRel,
  ctaBgColor = 'secondary',
  ctaTextColor,
  withContainer = true,
  productName,
  productCategory,
  productCategoryId = null,
  productSubCategory,
  productId = null,
  productProvider,
  productProviderId = null,
  productMonetized,
  productLocationId
}: ProductOverviewCardProps): JSX.Element => {
  const elementClicked = useElementClicked();
  const productClicked = useProductClicked({
    productName: productName,
    productCategory: productCategory,
    productCategoryId: productCategoryId ?? null,
    productSubCategory: productSubCategory,
    productId: productId ?? null,
    productLocationId: productLocationId,
    productProvider: productProvider,
    productProviderId: productProviderId ?? null,
    productMonetized: productMonetized,
    productUrl: ctaUrl,
    color: ctaBgColor,
    name: ElementNames.PRODUCT_OVERVIEW_CARD,
    text: ctaText,
    textColor: ctaTextColor
  });

  const numberOfColumns = useBreakpointValue({
    base: 'repeat(1, 1fr)',
    sm: 'repeat(5, 1fr)'
  });

  const marginTop = useBreakpointValue({
    base: 8,
    sm: 4,
    md: headingMarginTop
  });

  const grid = useBreakpointValue({
    base: 2,
    sm: 1,
    md: 2
  });

  const productOverview = () => (
    <SimpleGrid
      columns={grid}
      spacing={8}
      padding={4}
      border={'1px solid'}
      borderColor="gray.200"
      borderRadius={'md'}
    >
      <Box>
        {badge && (
          <Box
            textAlign="center"
            height="66px"
            display={{ base: 'none', sm: 'block' }}
          >
            <Button
              as="span"
              mb={2}
              alignContent={'center'}
              colorScheme={ctaBgColor}
              variant={ctaState}
              width={{ base: '100%', sm: 'auto' }}
              alignSelf={'center'}
              textColor={ctaTextColor}
              size="xs"
            >
              {badge}
            </Button>
          </Box>
        )}
        {image && (
          <StoryImage
            switchable={image}
            displayAsBackground={false}
            backgroundSize="contain"
            containerMarginBottom="0"
            withContainer={false}
          />
        )}
      </Box>
      <Box>
        {title && (
          <Heading
            as="h3"
            size="md"
            textAlign="left"
            style={{ scrollMarginTop: 48 }}
          >
            {HtmlToReact({ html: title })}
          </Heading>
        )}
        <List>
          {bulletPoints?.bulletPoints0 &&
            bulletPoints['@nodes'].map((key) => {
              const bullet = bulletPoints[key] as BulletPoint;
              return (
                <ListItem
                  key={key}
                  fontSize="16px"
                  fontStyle="normal"
                  fontWeight="400"
                  lineHeight="150%"
                >
                  {bullet.bulletPoint}
                </ListItem>
              );
            })}
        </List>
        {(ctaUrl || readReviewId) && (
          <SimpleGrid
            columns={grid}
            spacing={8}
            mt={8}
            textAlign={{ base: 'center', sm: 'center', md: 'left' }}
          >
            {ctaUrl && (
              <Button
                as="a"
                mb={4}
                href={`${ctaUrl}`}
                target={ctaBehavior || '_self'}
                rel={ctaRel?.join(' ') || ''}
                colorScheme={ctaBgColor}
                variant={ctaState}
                width={{ base: '100%', sm: '100%', md: ctaWidth }}
                textColor={ctaTextColor}
                size={ctaSize}
                onClick={() => {
                  productClicked();
                }}
              >
                {ctaText}
              </Button>
            )}
            {readReviewId && (
              <Link
                pt="2"
                display="inline-block"
                as="a"
                verticalAlign="top"
                lineHeight="24px"
                href={`#${createID(readReviewId)}`}
                fontWeight="400"
                color="primary.600"
                fontSize="md"
                textDecoration="underline"
                onClick={() => {
                  elementClicked({
                    element: {
                      type: ElementTypes.LINK,
                      action: ElementActions.JUMP_LINK,
                      name: ElementNames.PRODUCT_OVERVIEW_CARD,
                      text: ctaText,
                      color: 'primary.600',
                      textColor: ctaTextColor
                    },
                    destinationUrl: `#${createID(readReviewId)}`
                  });
                }}
              >
                {readReviewText}
              </Link>
            )}
            {/* </Box> */}
          </SimpleGrid>
        )}
      </Box>
    </SimpleGrid>
  );

  if (!withContainer) return productOverview();

  return <Container>{productOverview()}</Container>;
};

export default ProductOverviewCard;
