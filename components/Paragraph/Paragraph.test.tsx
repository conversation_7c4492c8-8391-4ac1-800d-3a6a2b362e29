import { mockParagraphTexts, render, screen } from '@utils/test-utils';
import parse from 'html-react-parser';

import Paragraph from './';

describe('Paragraph', () => {
  it('should not render component if text is empty', async () => {
    const { container } = render(<Paragraph text={null} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should not render component text is equal to DEFAULT_EMPTY_TEXT', async () => {
    const paragraphText = mockParagraphTexts.mock_default_empty_text;
    render(<Paragraph text={paragraphText} />);

    expect(parse(paragraphText)).toBeInTheDocument;
  });

  it('should not render component text is equal to default empty description', async () => {
    const paragraphText = mockParagraphTexts.mock_default_empty_description;
    render(<Paragraph text={paragraphText} />);

    expect(parse(paragraphText)).toBeInTheDocument;
  });

  it('should render component with a given text', async () => {
    const paragraphText = mockParagraphTexts.text;
    render(<Paragraph text={paragraphText} />);

    const text = screen.getByText(paragraphText);
    expect(text).toBeInTheDocument();
  });
});
