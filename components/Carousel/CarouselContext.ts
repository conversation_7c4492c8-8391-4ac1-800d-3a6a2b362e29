import { createContext, useContext } from 'react';

export type CarouselState = {
  currentSlide: number;
  slideCount: number;
  slickGoTo: (slide: number) => void;
};

const CarouselContext = createContext<CarouselState | null>(null);

export const CarouselProvider = CarouselContext.Provider;

export const useCarouselState = () => {
  const carouselState = useContext(CarouselContext);
  if (carouselState === null) {
    throw new Error('useCarouselState must be used within a CarouselProvider');
  }
  return carouselState;
};
