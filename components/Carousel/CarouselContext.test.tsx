import { render } from '@testing-library/react';

import { CarouselProvider, useCarouselState } from './CarouselContext';

const customRender = (ui, { providerProps, ...renderOptions }) => {
  return render(
    <CarouselProvider {...providerProps}>{ui}</CarouselProvider>,
    renderOptions
  );
};
const TestComponent = () => {
  const { currentSlide, slideCount, slickGoTo } = useCarouselState();
  const handleClick = () => slickGoTo(1);
  return (
    <div>
      <p>{currentSlide}</p>
      <p>{slideCount}</p>
      <button onClick={handleClick}>Go to slide</button>
    </div>
  );
};

describe('CarouselContext', () => {
  it('should provide the correct values', () => {
    const slickGoTo = jest.fn();
    const { getByText, getAllByRole } = customRender(<TestComponent />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo
        }
      }
    });

    expect(getByText('0')).toBeInTheDocument();
    expect(getByText('3')).toBeInTheDocument();
    getAllByRole('button')[0].click();
    expect(slickGoTo).toHaveBeenCalledWith(1);
  });

  describe('useCarouselState', () => {
    it('should throw an error if used outside of a CarouselProvider', () => {
      jest.spyOn(console, 'error').mockImplementation(() => {});
      expect(() => render(<TestComponent />)).toThrowError();
    });
  });
});
