// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dots should render correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 2px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: green.400;
}

.emotion-2 {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0px;
  overflow: hidden;
  white-space: nowrap;
  position: absolute;
}

.emotion-3 {
  width: 10px;
  height: 12px;
  display: inline-block;
  line-height: 1em;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  color: currentColor;
  vertical-align: middle;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: inherit;
}

<div>
  <nav
    aria-label="Pagination"
    class="emotion-0"
  >
    <button
      aria-current="true"
      class="emotion-1"
    >
      <span
        class="emotion-2"
      >
        1
      </span>
      <svg
        class="chakra-icon emotion-3"
        focusable="false"
        viewBox="0 0 2 2"
      >
        <circle
          cx="1"
          cy="1"
          fill="currentColor"
          r="1"
        />
      </svg>
    </button>
    <button
      aria-current="false"
      class="emotion-4"
    >
      <span
        class="emotion-2"
      >
        2
      </span>
      <svg
        class="chakra-icon emotion-3"
        focusable="false"
        viewBox="0 0 2 2"
      >
        <circle
          cx="1"
          cy="1"
          fill="currentColor"
          r="1"
        />
      </svg>
    </button>
    <button
      aria-current="false"
      class="emotion-4"
    >
      <span
        class="emotion-2"
      >
        3
      </span>
      <svg
        class="chakra-icon emotion-3"
        focusable="false"
        viewBox="0 0 2 2"
      >
        <circle
          cx="1"
          cy="1"
          fill="currentColor"
          r="1"
        />
      </svg>
    </button>
  </nav>
</div>
`;
