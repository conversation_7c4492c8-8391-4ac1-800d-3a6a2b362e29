'use client';

import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';

import { Box } from '@chakra-ui/layout';
import BackgroundOverlay from '@components/BackgroundOverlay';
import Container from '@components/LayoutStructure/Container';
import EditableArea from '@components/LayoutStructure/EditableArea';
import { EditorContextHelper } from '@magnolia/react-editor';
import { getColor } from '@utils/getColor';
import { getContainerSpacing } from '@utils/layout';
import cloneDeep from 'lodash/cloneDeep';
import { useCallback, useEffect, useState, useTransition } from 'react';
import Slider from 'react-slick';

import { MagnoliaImage, Metadata } from '~/types/Magnolia';
import { OverlayColors } from '~/types/OverlayColors';

import { CarouselProvider, CarouselState } from './CarouselContext';

const baseURL = process.env.NEXT_PUBLIC_MGNL_HOST;

type Switchable = {
  [key: string]: any;
  field?: string;
  bgColor?: string;
  bgColorRange?: string;
  image?: MagnoliaImage;
  bgImageOverlay?: OverlayColors;
  bgOverlayOpacity?: string;
  bgImagePosition?: string;
};

type Slide = {
  [key: string]: any;
};

interface Slides extends Omit<Metadata, 'dataName' | 'mgnl:template'> {
  [key: string]: string | Array<string> | Slide | undefined;
}

export type CarouselProps = {
  metadata?: Omit<Metadata, 'dataName'>;
  slides?: Slides;
  background?: {
    [key: string]: any;
    switchable?: Switchable;
  };
  autoplay?: boolean;
  autoplaySpeed?: number;
};

type CarouselBackgroundProps = {
  children?: React.ReactNode;
  switchable: Switchable;
};

const CarouselBackground: React.FC<CarouselBackgroundProps> = ({
  children,
  switchable
}) => {
  const {
    field,
    bgColor,
    bgColorRange,
    image,
    bgImageOverlay,
    bgOverlayOpacity,
    bgImagePosition
  } = switchable;

  switch (field) {
    case 'bgColorForm':
      return (
        <Box
          data-testid="carousel-background"
          bgColor={getColor(bgColor, bgColorRange)}
        >
          {children}
        </Box>
      );
    case 'bgImageForm':
      const imageURL = image
        ? `${baseURL}/dam/${image['@id']}${image['@path']}`
        : '';
      return (
        <BackgroundOverlay
          data-testid="carousel-background"
          image={imageURL}
          imagePosition={bgImagePosition}
          overlayMode={bgImageOverlay}
          overlayOpacity={parseInt(bgOverlayOpacity ?? '0') / 100}
        >
          {children}
        </BackgroundOverlay>
      );
    default:
      return <Box data-testid="carousel-background">{children}</Box>;
  }
};

const Carousel: React.FC<CarouselProps> = ({
  metadata,
  slides,
  autoplay = false,
  autoplaySpeed = 3000,
  background
}) => {
  const [isPending, startTransition] = useTransition();
  const { switchable } = background ?? {};
  const [carouselState, setCarouselState] = useState<CarouselState>({
    currentSlide: 0,
    slideCount: 1,
    slickGoTo: () => {}
  });
  const carouselRef = useCallback((node: Slider) => {
    if (node !== null) {
      setCarouselState({
        slickGoTo: (index) => node.slickGoTo(index),
        // @ts-ignore - state is not defined in the type definition
        slideCount: node.innerSlider?.state.slideCount,
        currentSlide:
          // @ts-ignore - state is not defined in the type definition
          node.innerSlider?.state.currentSlide
      });
    }
  }, []);
  const [showAsSlider, setShowAsSlider] = useState(true);
  const carouselSettings = {
    arrows: false,
    autoplay,
    autoplaySpeed,
    dots: false,
    infinite: true,
    pauseOnHover: true,
    slidesToScroll: 1,
    slidesToShow: 1,
    speed: 500,
    useTransform: false, // This is a workaround for a visual bug in the carousel: https://github.com/akiran/react-slick/issues/248#issuecomment-434229886
    beforeChange: (oldIndex: number, newIndex: number) =>
      startTransition(() =>
        setCarouselState((prevState) => ({
          ...prevState,
          currentSlide: newIndex
        }))
      )
  };

  useEffect(() => {
    EditorContextHelper.inEditorAsync().then((inEditor) => {
      setShowAsSlider(!inEditor);
    });
  }, []);

  // If the carousel doesn't have a valid configuration, don't render anything.
  if (!metadata || !slides || !switchable) return null;

  return (
    <CarouselProvider value={carouselState}>
      <Container ignoreMaxWidth>
        <CarouselBackground switchable={switchable}>
          {showAsSlider ? (
            <Slider ref={carouselRef} {...carouselSettings}>
              {slides['@nodes']?.map((slide) => {
                const content = cloneDeep(slides);
                content['@nodes'] = [slide];
                return (
                  <EditableArea
                    key={content?.[slide]?.['@id'] ?? slide}
                    content={content}
                    parentTemplateId={metadata['mgnl:template']}
                    // Remove spacing from the inner containers
                    {...getContainerSpacing('0px', '0px')}
                  />
                );
              })}
            </Slider>
          ) : (
            <EditableArea
              content={slides}
              parentTemplateId={metadata['mgnl:template']}
              // Remove spacing from the inner containers
              {...getContainerSpacing('0px', '0px')}
            />
          )}
        </CarouselBackground>
      </Container>
    </CarouselProvider>
  );
};

export default Carousel;
