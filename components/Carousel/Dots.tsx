'use client';

import { Icon } from '@chakra-ui/icon';
import { Box } from '@chakra-ui/layout';
import { VisuallyHidden } from '@chakra-ui/visually-hidden';

import { useCarouselState } from './CarouselContext';

type Props = {
  dotsColor?: string;
  dotsColorRange?: string;
};

const Dots: React.FC<Props> = ({
  dotsColor = 'green',
  dotsColorRange = '400'
}) => {
  const carouselState = useCarouselState();

  const dots = Array.from({
    length: carouselState.slideCount
  });

  const goToSlide = (slide: number) => {
    carouselState.slickGoTo(slide);
  };

  return (
    <Box
      as="nav"
      aria-label="Pagination"
      display="flex"
      justifyContent="center"
      gap="2"
      my="8"
    >
      {dots.map((_, i) => (
        <Box
          key={i}
          as="button"
          aria-current={carouselState.currentSlide === i}
          display="flex"
          onClick={() => goToSlide(i)}
          color={
            carouselState.currentSlide === i
              ? `${dotsColor}.${dotsColorRange}`
              : 'inherit'
          }
        >
          <VisuallyHidden>{i + 1}</VisuallyHidden>
          <Icon viewBox="0 0 2 2" width="10px" height="12px">
            <circle cx={1} cy={1} r={1} fill="currentColor" />
          </Icon>
        </Box>
      ))}
    </Box>
  );
};

export default Dots;
