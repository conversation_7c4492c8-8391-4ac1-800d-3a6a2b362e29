import { matchers } from '@emotion/jest';
import { render } from '@testing-library/react';

import { CarouselProvider } from './CarouselContext';
import Dots from './Dots';

expect.extend(matchers);

const customRender = (ui, { providerProps, ...renderOptions }) => {
  return render(
    <CarouselProvider {...providerProps}>{ui}</CarouselProvider>,
    renderOptions
  );
};

describe('Dots', () => {
  it('should render correctly', () => {
    const { container } = customRender(<Dots />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo: jest.fn()
        }
      }
    });
    expect(container).toMatchSnapshot();
  });

  it('should throw an error if used outside of Carousel', () => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
    expect(() => render(<Dots />)).toThrowError();
  });

  it('should render the correct number of dots', () => {
    const { getAllByRole } = customRender(<Dots />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo: jest.fn()
        }
      }
    });
    expect(getAllByRole('button')).toHaveLength(3);
  });

  it('should render the correct aria-current value', () => {
    const { getAllByRole } = customRender(<Dots />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo: jest.fn()
        }
      }
    });
    expect(getAllByRole('button')[0]).toHaveAttribute('aria-current', 'true');
    expect(getAllByRole('button')[1]).toHaveAttribute('aria-current', 'false');
    expect(getAllByRole('button')[2]).toHaveAttribute('aria-current', 'false');
  });

  it('should call slickGoTo when a dot is clicked', () => {
    const slickGoTo = jest.fn();
    const { getAllByRole } = customRender(<Dots />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo
        }
      }
    });
    getAllByRole('button')[1].click();
    expect(slickGoTo).toHaveBeenCalledWith(1);
  });

  it('should render the correct color for the current slide', () => {
    const { getAllByRole } = customRender(<Dots />, {
      providerProps: {
        value: {
          currentSlide: 0,
          slideCount: 3,
          slickGoTo: jest.fn()
        }
      }
    });
    expect(getAllByRole('button')[0]).toHaveStyleRule('color', 'green.400');
    expect(getAllByRole('button')[1]).toHaveStyleRule('color', 'inherit');
    expect(getAllByRole('button')[2]).toHaveStyleRule('color', 'inherit');
  });
});
