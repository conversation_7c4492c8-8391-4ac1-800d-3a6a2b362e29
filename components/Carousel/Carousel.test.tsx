import { matchers } from '@emotion/jest';
import { render } from '@utils/test-utils';
import { mockCarousel } from '@utils/test-utils/mocks/carousel';

import Carousel from './Carousel';

const defaultCarouselProps = mockCarousel;

expect.extend(matchers);

jest.mock('@magnolia/react-editor', () => ({
  EditableArea: () => 'mocked EditableArea',
  EditorContextHelper: {
    inEditorAsync: () => new Promise((resolve) => resolve(false))
  }
}));

describe('Carousel', () => {
  it('should render correctly', () => {
    const carouselProps = {
      ...defaultCarouselProps,
      background: {
        ...defaultCarouselProps.background,
        switchable: {
          ...defaultCarouselProps.background.switchable,
          field: 'bgColorForm',
          bgColor: 'red',
          bgColorRange: '500'
        }
      }
    };
    const { getByTestId } = render(<Carousel {...carouselProps} />);
    expect(getByTestId('carousel-background')).toBeInTheDocument();
  });

  it('should render the correct background color', () => {
    const carouselProps = {
      ...defaultCarouselProps,
      background: {
        ...defaultCarouselProps.background,
        switchable: {
          ...defaultCarouselProps.background.switchable,
          field: 'bgColorForm',
          bgColor: 'red',
          bgColorRange: '500'
        }
      }
    };
    const { getByTestId } = render(<Carousel {...carouselProps} />);
    expect(getByTestId('carousel-background')).toHaveStyleRule(
      'background-color',
      'var(--chakra-colors-red-500)'
    );
  });

  it('should render the correct background image', () => {
    const carouselProps = {
      ...defaultCarouselProps,
      background: {
        ...defaultCarouselProps.background,
        switchable: {
          ...defaultCarouselProps.background.switchable,
          bgImageOverlay: 'none' as const
        }
      }
    };
    const { getByTestId } = render(<Carousel {...carouselProps} />);
    expect(getByTestId('carousel-background')).toHaveStyleRule(
      'background-image',
      `linear-gradient(rgb(0 0 0 / 0.5), rgb(0 0 0 / 0.5)),url(${process.env.NEXT_PUBLIC_MGNL_HOST}/dam/jcr:cb7d301c-ddf5-4f23-b0c1-b21ed139939a/download.jpeg)`
    );
  });

  it('should render the correct background image overlay', () => {
    const carouselProps = {
      ...defaultCarouselProps,
      background: {
        ...defaultCarouselProps.background,
        switchable: {
          ...defaultCarouselProps.background.switchable,
          bgImageOverlay: 'dark' as const,
          bgOverlayOpacity: '50'
        }
      }
    };
    const { getByTestId } = render(<Carousel {...carouselProps} />);
    expect(getByTestId('carousel-background')).toHaveStyleRule(
      'background-image',
      `linear-gradient(rgb(0 0 0 / 0.5), rgb(0 0 0 / 0.5)),url(${process.env.NEXT_PUBLIC_MGNL_HOST}/dam/jcr:cb7d301c-ddf5-4f23-b0c1-b21ed139939a/download.jpeg)`
    );
  });
});
