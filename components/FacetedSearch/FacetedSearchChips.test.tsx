import { render, screen } from '@testing-library/react';
import { AmenityCategoryNode } from '@utils/faceted-search';

import FacetedSearchChips from './FacetedSearchChips';

describe('FacetedSearchChips', () => {
  const mockLinks: AmenityCategoryNode[] = [
    { name: 'Pet Friendly', value: 'pet-friendly', amenities: [] },
    { name: 'Physical Therapy', value: 'physical-therapy', amenities: [] },
    { name: 'Golf', value: 'golf', amenities: [] }
  ];

  const defaultProps = {
    basePath: '/senior-living/california',
    links: mockLinks,
    numberOfResults: 42
  };

  it('renders all chips correctly', () => {
    render(<FacetedSearchChips {...defaultProps} />);

    mockLinks.forEach((link) => {
      expect(screen.getByText(link.name)).toBeInTheDocument();
    });
  });

  it('shows number of results when a chip is selected', () => {
    render(
      <FacetedSearchChips {...defaultProps} initialValue="pet-friendly" />
    );

    expect(screen.getByText('Pet Friendly (42)')).toBeInTheDocument();
  });

  it('constructs correct URLs for chips', () => {
    render(<FacetedSearchChips {...defaultProps} />);

    const petFriendlyLink = screen.getByText('Pet Friendly').closest('a');
    expect(petFriendlyLink).toHaveAttribute(
      'href',
      '/senior-living/california/pet-friendly'
    );
  });

  it('disables chips when no basePath is provided', () => {
    render(<FacetedSearchChips links={mockLinks} />);

    const buttons = screen.getAllByRole('button');
    buttons.forEach((button) => {
      expect(button).toBeDisabled();
    });
  });

  it('shows total number of results for selected chip', () => {
    render(
      <FacetedSearchChips {...defaultProps} initialValue="pet-friendly" />
    );
    const selectedChip = screen.getByText('Pet Friendly (42)');
    expect(selectedChip).toBeInTheDocument();

    const nonSelectedChip = screen.getByText('Physical Therapy');
    expect(nonSelectedChip).not.toHaveTextContent(/\(\d+\)/);
  });
});
