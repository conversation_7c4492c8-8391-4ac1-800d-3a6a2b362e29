import { autocompleteSuggestionMapBoxAPI } from '@components/AutocompleteInput/AutocompleteInput';
import { DISPLAY_MODE } from '@components/FacetedSearch/constants';
import { SearchComponentDialogProps } from '@components/FacetedSearch/types';
import { OptimizedFacetedSearchProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/OptimizedFacetedSearch';
import { parseComponentConfig } from '@components/Search/EnhancedSearch/parser';
import { useRouter } from 'next/router';
import { useContext, useEffect, useMemo, useState } from 'react';

import SiteContext from '~/contexts/SiteContext';
import { tenantSwitcher } from '~/contexts/TenantFunctionsContext';
import { Domain } from '~/types/Domains';

import { getFacetedSearchParams } from './search-params';
import { FacetedSearchParsedProps } from './types';
import { getSourceForFacetedSearch, hasDistanceFilter } from './utils';

export const useFacetedSearchSetup = (
  props: OptimizedFacetedSearchProps | SearchComponentDialogProps
) => {
  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral>({
    lat: 0,
    lng: 0
  });
  const [prevKeyword, setPrevKeyword] = useState('');
  const router = useRouter();
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path as Domain;
  const parsedConfig = useMemo(() => parseComponentConfig(props), [props]);

  const searchParams = useMemo(
    () => getFacetedSearchParams(router.query),
    [router.query]
  );
  const distanceFilterEnabled = hasDistanceFilter(parsedConfig.searchSidebar);
  const isMapViewEnabled = parsedConfig.mapView.visible;
  const { getCountyForSearch } = tenantSwitcher(domain);
  const displayMode =
    parsedConfig.searchOptions.displayMode === DISPLAY_MODE.LIST
      ? DISPLAY_MODE.LIST
      : DISPLAY_MODE.SEARCH;
  const source = getSourceForFacetedSearch({
    amenityCategoryChips: parsedConfig.amenityCategoryChips,
    displayMode: displayMode,
    domain,
    searchParams
  });

  const [searchProps, setSearchProps] = useState<FacetedSearchParsedProps>({
    amenityCategory: '',
    careType: '',
    city: '',
    county: '',
    displayMode: displayMode,
    distanceFilterEnabled: false,
    domain: domain,
    latitude: '',
    longitude: '',
    resultsPerPage: 0,
    source: source,
    state: '',
    shouldShowOnlyIndexedProviders:
      props?.filterAndSort?.defaultValues?.shouldShowOnlyIndexedProviders ??
      false
  });

  const [searchLng, searchLat] = searchParams.latLng?.split(',') ?? [];
  const [latitude, setLatitude] = useState(() => {
    if (parsedConfig.searchOptions.latitude) {
      return String(parsedConfig.searchOptions.latitude);
    }
    return searchLat ?? '';
  });
  const [longitude, setLongitude] = useState(() => {
    if (parsedConfig.searchOptions.longitude) {
      return String(parsedConfig.searchOptions.longitude);
    }
    return searchLng ?? '';
  });

  useEffect(() => {
    if (!latitude || !longitude || searchParams.keyword !== prevKeyword) {
      autocompleteSuggestionMapBoxAPI(searchParams.keyword, (results) => {
        if (results.length > 0) {
          const [lng, lat] = results[0].center;
          searchParams.latLng = `${lat},${lng}`;
          setLatitude(lat);
          setLongitude(lng);
        }
      });
      setMapCenter({
        lat: Number(latitude),
        lng: Number(longitude)
      });
      setPrevKeyword(searchParams.keyword);
    }
  }, [latitude, longitude, searchParams, prevKeyword]);

  useEffect(() => {
    setSearchProps({
      amenityCategory: parsedConfig.searchOptions.amenityCategory ?? '',
      careType:
        displayMode === DISPLAY_MODE.LIST
          ? parsedConfig.searchOptions.careType ?? ''
          : searchParams.careType,
      city: parsedConfig.searchOptions.city ?? '',
      county: getCountyForSearch(parsedConfig.searchOptions.county ?? ''),
      displayMode: displayMode,
      distanceFilterEnabled,
      domain,
      latitude,
      longitude,
      resultsPerPage: parsedConfig.searchOptions.itemsPerPage,
      source,
      state: parsedConfig.searchOptions.state ?? '',
      shouldShowOnlyIndexedProviders:
        props?.filterAndSort?.defaultValues?.shouldShowOnlyIndexedProviders ??
        false
    });
  }, [
    latitude,
    longitude,
    distanceFilterEnabled,
    domain,
    getCountyForSearch,
    source,
    parsedConfig,
    searchParams,
    displayMode,
    props
  ]);

  return {
    mapCenter,
    searchProps,
    parsedConfig,
    searchParams,
    isMapViewEnabled,
    displayMode,
    domain,
    latitude,
    longitude
  };
};
