import { act, render, renderHook } from '@testing-library/react';
import { useRouter } from 'next/router';

import {
  FacetedSearchProvider,
  useFacetedSearchActions,
  useFacetedSearchQuery
} from './FacetedSearchContext';
import * as navigation from './navigation';
import { FacetedSearchParsedParams } from './search-params';
import { FacetedSearchParsedProps, FacetedSearchResponse } from './types';

// Mock next/router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

// Mock navigation functions
jest.mock('./navigation', () => ({
  navigateToInitialSearch: jest.fn(),
  navigateToSearchPage: jest.fn(),
  navigateToSearchQuery: jest.fn()
}));

// Mock useFacetedSearch hook
jest.mock('./useFacetedSearch', () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue({
    data: {
      currentPage: 0,
      outOfBounds: false
    },
    isError: false
  })
}));

describe('FacetedSearchContext', () => {
  const mockRouter = {
    push: jest.fn(),
    asPath: '/test-path'
  };

  const mockProps = {
    initialData: {
      resultCount: 10,
      results: []
    } as FacetedSearchResponse,
    query: {
      page: 0,
      careType: 'senior-living'
    } as FacetedSearchParsedParams,
    careTypes: [{ id: '1', name: 'Senior Living' }],
    componentProps: {
      domain: 'test.com'
    } as FacetedSearchParsedProps
  };

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    jest.clearAllMocks();
  });

  describe('FacetedSearchProvider', () => {
    it('renders children correctly', () => {
      const { getByText } = render(
        <FacetedSearchProvider {...mockProps}>
          <div>Test Child</div>
        </FacetedSearchProvider>
      );

      expect(getByText('Test Child')).toBeInTheDocument();
    });

    it('returns null when there is an error', () => {
      const useFacetedSearch = require('./useFacetedSearch').default;
      useFacetedSearch.mockReturnValueOnce({ isError: true });

      const { container } = render(
        <FacetedSearchProvider {...mockProps}>
          <div>Test Child</div>
        </FacetedSearchProvider>
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('Context Hooks', () => {
    const wrapper = ({ children }) => (
      <FacetedSearchProvider {...mockProps}>{children}</FacetedSearchProvider>
    );

    it('useFacetedSearchActions provides correct actions', () => {
      const { result } = renderHook(() => useFacetedSearchActions(), {
        wrapper
      });

      expect(result.current).toHaveProperty('changePage');
      expect(result.current).toHaveProperty('resetQuery');
      expect(result.current).toHaveProperty('updateQuery');

      act(() => {
        result.current.resetQuery();
      });
      expect(navigation.navigateToInitialSearch).toHaveBeenCalledWith(
        mockRouter
      );

      act(() => {
        result.current.changePage(1);
      });
      expect(navigation.navigateToSearchPage).toHaveBeenCalledWith(
        mockRouter,
        1
      );

      act(() => {
        result.current.updateQuery({
          key: 'careType',
          value: 'assisted-living'
        });
      });
      expect(navigation.navigateToSearchQuery).toHaveBeenCalledWith(
        mockRouter,
        'careType',
        'assisted-living'
      );
    });

    it('useFacetedSearchQuery provides correct query', () => {
      const { result } = renderHook(() => useFacetedSearchQuery(), { wrapper });
      expect(result.current).toEqual(mockProps.query);
    });
  });
});
