'use client';

import { Card, CardBody } from '@chakra-ui/card';

import {
  useFacetedSearchActions,
  useFacetedSearchQuery,
  useFacetedSearchResult
} from './FacetedSearchContext';
import ProviderList from './ProviderList';

const SearchResults = (props) => {
  const { isFetching, data } = useFacetedSearchResult();
  const { page } = useFacetedSearchQuery();
  const { changePage } = useFacetedSearchActions();

  const handlePageChange = (newPage: number) => {
    changePage(newPage);
  };
  return (
    <Card pt={0}>
      <CardBody>
        {
          <ProviderList
            search={{
              isLoading: isFetching,
              blurCosts: props.blurCosts,
              boxShadow: props.boxShadow,
              careType: props.careType,
              countOfTilesPerRow: props.countOfTilesPerRow,
              displayBadges: props.displayBadges,
              displayLearnMoreButton: props.displayLearnMoreButton,
              displayProviderPhoneNumber: props.displayProviderPhoneNumber,
              displayRequestInfoButton: props.displayRequestInfoButton,
              fullWidthTile: props.fullWidthTile,
              genericBlock1: props.genericBlock1,
              genericBlock2: props.genericBlock2,
              infoButtonInquiryId: props.infoButtonInquiryId,
              learnMoreButtonColorScheme: props.learnMoreButtonColorScheme,
              learnMoreButtonText: props.learnMoreButtonText,
              metadata: props.metadata,
              providerPhoneNumberSource: props.providerPhoneNumberSource,
              providerTitleColor: props.providerTitleColor,
              providerTitleColorRange: props.providerTitleColorRange,
              ratingStarsColor: props.ratingStarsColor,
              ratingStarsColorRange: props.ratingStarsColorRange,
              readMoreButton: props.readMoreButton,
              requestInfoButtonColorScheme: props.requestInfoButtonColorScheme,
              requestInfoButtonText: props.requestInfoButtonText,
              tileBorder: props.tileBorder,
              tileBorderColor: props.tileBorderColor,
              tileBorderColorRange: props.tileBorderColorRange,
              displayCompareOption: props.displayCompareOption,
              dontOpenInNewTab: props.dontOpenInNewTab,
              state: props.state,
              county: props.county,
              city: props.city,
              promotionColorScheme: props.promotionColorScheme
            }}
            result={data}
            options={{
              visibleItems: Number(props.visibleLimit),
              filterAndSort: props.filterAndSort
            }}
            pagination={{
              currentPage: page,
              totalPages: data?.resultCount
                ? Math.ceil(data.resultCount / data.resultsPerPage)
                : 0,
              setPage: (newPage) => handlePageChange(newPage)
            }}
          />
        }
      </CardBody>
    </Card>
  );
};

export default SearchResults;
