import { GetProvidersResponse } from '@services/modular-monolith/types/search.type';
import { getOrCreateDiceRollUuidClientSide } from '@utils/diceRollUuid';
import { getActivePromotion } from '@utils/getActivePromotion';

import segmentEvents from '~/config/segment-events';
import { SessionContextType } from '~/contexts/SessionContext';

import { FacetedSearchParsedParams, PARAM_NAMES } from './search-params';

interface Location {
  location_id: string;
  name?: string;
  min_price?: number;
  position: number;
  slug?: string;
  image_url?: string;
  promotion_active?: boolean;
}

interface Filter {
  type: string;
  value: unknown;
}

interface EventData {
  page_session_id: string;
  session_id: string;
  dice_roll_uuid: string;
  type: 'search' | 'geo';
  list_id: string;
  query_id: string;
  filters: Array<Filter>;
  sorts: Array<{
    type: string;
    value: 'asc' | 'desc';
  }>;
  location_count: number;
  locations: Array<Location>;
}

type ZeroIndexedPosition = number;

const mapProviderToLocation = (
  provider: GetProvidersResponse['results'][number],
  position: ZeroIndexedPosition
): Location => {
  const { id, name, slug, images } = provider;
  const activePromotion = getActivePromotion(provider.promotions ?? []);
  const location: Location = {
    location_id: id,
    name,
    // min_price
    position,
    slug,
    image_url: images?.[0] ?? '',
    promotion_active: provider.hasActivePromotion ?? !!activePromotion
  };

  return location;
};

const getLocations = (response: GetProvidersResponse): Location[] => {
  const results = response.results;
  return results.map(mapProviderToLocation);
};

const TRACKABLE_FILTERS = PARAM_NAMES.filter(
  (param) => !['page', 'keyword', 'sortBy'].includes(param)
);

const getFilters = (searchQuery: FacetedSearchParsedParams): Filter[] => {
  return TRACKABLE_FILTERS.map((key) => {
    const filterValue = searchQuery[key];
    return {
      type: key,
      value: filterValue
    };
  });
};

const getEventProperties = ({
  session,
  searchQuery,
  response
}: {
  session: SessionContextType;
  searchQuery: FacetedSearchParsedParams;
  response: GetProvidersResponse;
}): EventData => {
  const { pageSessionId, sessionId } = session;
  const filters = getFilters(searchQuery);
  const locations = getLocations(response);

  return {
    page_session_id: pageSessionId,
    session_id: sessionId,
    dice_roll_uuid: getOrCreateDiceRollUuidClientSide(),
    type: 'search',
    list_id: response.listId,
    query_id: response.queryId,
    filters,
    sorts: [
      { type: 'isEnhancedWithReviews', value: 'desc' },
      { type: 'isBasicWithReviews', value: 'desc' },
      { type: 'enhancedListing', value: 'desc' },
      { type: 'bayesian_average', value: 'desc' },
      { type: 'name', value: 'asc' }
    ],
    location_count: locations.length,
    locations
  };
};

export const trackProviderListFiltered = ({
  session,
  searchQuery,
  response
}: {
  session: SessionContextType;
  searchQuery: FacetedSearchParsedParams;
  response: GetProvidersResponse;
}) => {
  const eventName = segmentEvents.PROVIDER_LIST_FILTERED;
  const eventProperties = getEventProperties({
    session,
    searchQuery,
    response
  });

  window.tracking?.track(eventName, eventProperties);
};
