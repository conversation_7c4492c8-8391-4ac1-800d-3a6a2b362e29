import { getDefaultFacetedSearchParams } from './search-params';

describe('getDefaultFacetedSearchParams', () => {
  it('should return default faceted search parameters', () => {
    const result = getDefaultFacetedSearchParams();

    expect(result).toEqual({
      accessibility: [],
      awards: [],
      careType: '',
      dining: [],
      distanceInMiles: 30,
      healthServices: [],
      keyword: '',
      languages: [],
      latLng: '',
      lifestyle: [],
      matchAllFilters: true,
      ongoingPromotion: [],
      otherAmenities: [],
      page: 0,
      personalCare: [],
      priceRange: [1, 4],
      providersWith: [],
      reviews: [],
      roomAmenities: [],
      roomType: [],
      sortBy: 'recommended',
      staffQualifications: [],
      verified: []
    });
  });
});
