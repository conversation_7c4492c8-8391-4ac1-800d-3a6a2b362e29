'use client';

import { Box } from '@chakra-ui/layout';
import { FacetedSearchProvider } from '@components/FacetedSearch/FacetedSearchContext';
import FacetedSearchHeader from '@components/FacetedSearch/FacetedSearchHeader';
import FacetedSearchLayout from '@components/FacetedSearch/FacetedSearchLayout';
import FacetedSearchSummary from '@components/FacetedSearch/FacetedSearchSummary';
import MapViewButton from '@components/FacetedSearch/MapView/MapViewButton';
import MapViewCta from '@components/FacetedSearch/MapView/MapViewCta';
import MapViewModal from '@components/FacetedSearch/MapView/MapViewModal';
import SearchResults from '@components/FacetedSearch/SearchResults';
import Sidebar from '@components/FacetedSearch/Sidebar';
import SidebarLayout from '@components/FacetedSearch/Sidebar/SidebarLayout';
import {
  LayoutType,
  SearchComponentDialogProps
} from '@components/FacetedSearch/types';
import { useFacetedSearchSetup } from '@components/FacetedSearch/useFacetedSearchSetup';

import { OptimizedFacetedSearch } from './variants';

const FacetedSearch: React.FC<SearchComponentDialogProps> = (props) => {
  const {
    mapCenter,
    searchProps,
    parsedConfig,
    searchParams,
    isMapViewEnabled,
    displayMode,
    domain,
    latitude,
    longitude
  } = useFacetedSearchSetup(props);
  return (
    <Box id="faceted-search-tracker" scrollMarginTop={8}>
      <FacetedSearchProvider
        initialData={props.data}
        query={searchParams}
        componentProps={searchProps}
        careTypes={props.data.careTypes}
      >
        <FacetedSearchLayout
          header={
            <FacetedSearchHeader
              {...{
                ...props,
                domain,
                displayMode: displayMode,
                careType: parsedConfig.searchOptions.careType,
                latitude: latitude,
                longitude: longitude
              }}
            />
          }
          sidebar={
            <SidebarLayout>
              <SidebarLayout.Header>
                {isMapViewEnabled ? (
                  <MapViewCta display={{ base: 'none', lg: 'flex' }} />
                ) : null}
                <FacetedSearchSummary heading="Filters" />
              </SidebarLayout.Header>

              <SidebarLayout.Body>
                {isMapViewEnabled ? (
                  <MapViewButton
                    display={{ base: 'inline-flex', lg: 'none' }}
                    showMarker={true}
                  />
                ) : null}
                <Sidebar
                  searchOptions={parsedConfig.searchSidebar.filters}
                  searchDefaultValues={props.filterAndSort?.defaultValues ?? {}}
                  filterTooltips={props.filterAndSort?.filterTooltips ?? {}}
                />
              </SidebarLayout.Body>
            </SidebarLayout>
          }
          main={
            <SearchResults {...{ ...props, careType: searchProps.careType }} />
          }
        />

        {isMapViewEnabled ? (
          <MapViewModal
            searchOptions={parsedConfig.searchSidebar.filters}
            initialMapOptions={{
              defaultCenter: mapCenter
            }}
            filterTooltips={props.filterAndSort?.filterTooltips ?? {}}
          />
        ) : null}
      </FacetedSearchProvider>
    </Box>
  );
};

const FacetedSearchWrapper = (props) => {
  return props.data?.layoutType === LayoutType.Optimized ? (
    <OptimizedFacetedSearch {...props} />
  ) : (
    <FacetedSearch {...props} />
  );
};

export default FacetedSearchWrapper;
