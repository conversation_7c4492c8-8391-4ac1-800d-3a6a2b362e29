import { Icon } from '@chakra-ui/icon';
import { Box, Link, LinkBox, LinkOverlay, Text } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import ImageSlider from '@components/ProviderCard/ImageSlider';
import RatingStars from '@components/RatingStars';
import { formatRating } from '@components/RatingStars/utils';
import { useContext } from 'react';
import { MdDirections } from 'react-icons/md';

import SiteContext from '~/contexts/SiteContext';

import { useFacetedSearchResult } from '../FacetedSearchContext';
import { Provider } from './parser';

const GOOGLE_MAPS_URL = 'https://www.google.com/maps/';

interface ProviderCardProps {
  provider: Provider;
}

const getDirectionsUrl = (location: google.maps.LatLngLiteral): string => {
  const directionsUrl = new URL('dir/', GOOGLE_MAPS_URL);
  const searchParams = new URLSearchParams({
    api: '1',
    destination: `${location.lat},${location.lng}`
  });

  directionsUrl.search = searchParams.toString();

  return directionsUrl.toString();
};

const ProviderCard: React.FC<ProviderCardProps> = ({ provider }) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const { data } = useFacetedSearchResult();
  const {
    address,
    legacyId,
    location,
    name,
    photos,
    rating,
    reviewCount,
    url
  } = provider;
  const hasReviews = reviewCount > 0;

  return (
    <LinkBox
      as="article"
      background="white"
      className="map-provider-card" // Used to style the slider
      display="flex"
      flexDirection="column"
      gap="10px"
      w="172px"
      maxWidth="100%"
    >
      <ImageSlider
        domain={domain}
        images={photos}
        path={url}
        query={{
          locationId: legacyId,
          queryId: data?.queryId ?? '',
          listId: data?.listId ?? ''
        }}
        title={name}
        size="small"
      />

      <LinkOverlay href={url}>
        <Text
          color="primary.700"
          fontFamily="var(--font-montserrat)"
          fontSize="xl"
          fontWeight="700"
          lineHeight="6"
          textDecoration="underline"
        >
          {name}
        </Text>
      </LinkOverlay>

      <Text
        color="gray.700"
        fontFamily="var(--font-montserrat)"
        fontSize="sm"
        fontWeight="400"
      >
        {address}
      </Text>

      {hasReviews ? (
        <Box display="flex" flexDirection="row" alignItems="center" gap={0.5}>
          <RatingStars
            rating={rating}
            showLabel={false}
            spacing="-0.5"
            starColor="info.500"
            starSize="15px"
          />
          <Text
            color="info.500"
            fontFamily="var(--font-montserrat)"
            fontSize="sm"
            fontWeight="400"
          >
            {formatRating(rating)}
          </Text>
          <Text
            color="gray.700"
            fontFamily="var(--font-montserrat)"
            fontSize="xs"
            fontWeight="400"
          >
            ({reviewCount})
          </Text>
        </Box>
      ) : null}

      <Button
        as={Link}
        _hover={{ textDecoration: 'none' }}
        colorScheme="primary"
        elementAction={ElementActions.EXTERNAL_LINK}
        elementType={ElementTypes.LINK}
        fontFamily="var(--font-montserrat)"
        fontSize="sm"
        href={getDirectionsUrl(location)}
        isExternal
        leftIcon={<Icon as={MdDirections} boxSize={4} />}
        size="sm"
      >
        Directions
      </Button>
    </LinkBox>
  );
};

export default ProviderCard;
