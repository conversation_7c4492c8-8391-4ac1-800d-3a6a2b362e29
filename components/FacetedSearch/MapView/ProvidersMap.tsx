import { Box } from '@chakra-ui/layout';
import { DEFAULT_VALUES } from '@components/FacetedSearch/constants';
import {
  AdvancedMarker,
  InfoWindow,
  Map as GoogleMap,
  MapProps,
  useMap,
  useMapsLibrary
} from '@vis.gl/react-google-maps';
import { useEffect, useId, useState } from 'react';

import MarkerIcon from './MarkerIcon';
import { Provider } from './parser';
import ProviderCard from './ProviderCard';

const DEFAULT_MAP_BOUNDS_PADDING = 80;
const DEFAULT_MAP_ZOOM_LEVEL = 10;
const MAP_ID = process.env.NEXT_PUBLIC_GOOGLE_MAPS_MAP_VIEW_MAP_ID;
const ONE_MILE_IN_METERS = 1609.34;
const POPUP_HORIZONTAL_OFFSET = 0;
const POPUP_VERTICAL_OFFSET = -40;

interface ProvidersMapProps extends MapProps {
  defaultCenter: google.maps.LatLngLiteral;
  providers: Array<Provider>;
}

const milesToMeters = (miles: number) => miles * ONE_MILE_IN_METERS;

const fitToRadius = (options: {
  center: google.maps.LatLngLiteral;
  map: google.maps.Map;
  mapsLibrary: google.maps.MapsLibrary;
  radius: number;
}) => {
  const { center, map, mapsLibrary, radius } = options;
  const circle = new mapsLibrary.Circle({
    center,
    radius
  });
  const bounds = circle.getBounds();

  if (bounds) {
    map.fitBounds(bounds);
  }
};

const fitToProviders = (options: {
  coreLibrary: google.maps.CoreLibrary;
  map: google.maps.Map;
  providers: Array<Provider>;
}) => {
  const { coreLibrary, map, providers } = options;
  const bounds = new coreLibrary.LatLngBounds();

  providers.forEach((provider) => {
    const point: google.maps.LatLngLiteral = provider.location;
    bounds.extend(point);
  });

  map.fitBounds(bounds, DEFAULT_MAP_BOUNDS_PADDING);
};

const MarkersWithInfoWindow = ({
  providers
}: {
  providers: Array<Provider>;
}) => {
  const [activeProvider, setActiveProvider] = useState<Provider | null>(null);
  const displayInfoWindow =
    activeProvider && providers.includes(activeProvider);

  return (
    <>
      {providers.map((provider) => (
        <AdvancedMarker
          key={provider.id}
          position={provider.location}
          onClick={() => {
            setActiveProvider(provider);
          }}
        >
          <MarkerIcon
            backgroundColor="primary.600"
            height="35px"
            width="27px"
            // The `pointerEvents` and `userSelect` props are used to allow the click event to pass through the marker
            pointerEvents="none"
            userSelect="none"
          />
        </AdvancedMarker>
      ))}

      {displayInfoWindow && (
        <InfoWindow
          position={activeProvider.location}
          pixelOffset={[POPUP_HORIZONTAL_OFFSET, POPUP_VERTICAL_OFFSET]}
          onClose={() => {
            setActiveProvider(null);
          }}
        >
          <ProviderCard provider={activeProvider} />
        </InfoWindow>
      )}
    </>
  );
};

const ProvidersMap: React.FC<ProvidersMapProps> = ({
  defaultCenter,
  providers
}) => {
  const elementId = useId();
  const coreLibrary = useMapsLibrary('core');
  const mapsLibrary = useMapsLibrary('maps');
  const map = useMap(elementId);
  const [displayMap, setDisplayMap] = useState(false);

  // Ensure the map is displayed only in the client side.
  useEffect(() => {
    setDisplayMap(true);
  }, []);

  // Fit the map to the providers when the map and providers are loaded
  useEffect(() => {
    if (!map || !coreLibrary || !mapsLibrary) {
      return;
    }

    // If there are no providers, fit the map to the default center
    if (providers.length === 0) {
      fitToRadius({
        center: defaultCenter,
        map,
        mapsLibrary,
        radius: milesToMeters(DEFAULT_VALUES.RADIUS_FOR_SEARCH_IN_MILES)
      });
      return;
    }

    // If there is only one provider, fit the map to center on that provider
    if (providers.length === 1) {
      fitToRadius({
        center: providers[0].location,
        map,
        mapsLibrary,
        radius: DEFAULT_VALUES.RADIUS_FOR_SEARCH_IN_MILES * 1609.34
      });
      return;
    }

    // If there are multiple providers, fit the map to ensure all providers are visible
    fitToProviders({
      coreLibrary,
      map,
      providers
    });
  }, [map, coreLibrary, mapsLibrary, defaultCenter, providers]);

  return (
    <Box position="relative" flexGrow="1">
      {displayMap ? (
        <GoogleMap
          id={elementId}
          mapId={MAP_ID}
          defaultCenter={defaultCenter}
          defaultZoom={DEFAULT_MAP_ZOOM_LEVEL}
          reuseMaps
          style={{ position: 'absolute' }} // Required to ensure the map fills the container
        >
          <MarkersWithInfoWindow providers={providers} />
        </GoogleMap>
      ) : null}
    </Box>
  );
};

export default ProvidersMap;
