import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>lose<PERSON><PERSON>on,
  Drawer<PERSON>ontent,
  DrawerOverlay,
  DrawerProps
} from '@chakra-ui/modal';
import { useBreakpointValue } from '@chakra-ui/react';
import { Spinner } from '@chakra-ui/spinner';
import { SearchOption } from '@components/FacetedSearch/constants';
import { MODAL } from '@constants/modals';

import { useModal } from '~/contexts/ModalContext';
import { FilterTooltips } from '~/types/componentsConfig';

import { useFacetedSearchResult } from '../FacetedSearchContext';
import FacetedSearchSummary from '../FacetedSearchSummary';
import FilterList from '../Sidebar/FilterList';
import SidebarLayout from '../Sidebar/SidebarLayout';

interface MapViewSidebarProps {
  searchOptions: Array<SearchOption>;
  filterTooltips?: FilterTooltips;
}

const MapViewSidebar: React.FC<MapViewSidebarProps> = ({
  searchOptions,
  filterTooltips
}) => {
  const placement = useBreakpointValue<DrawerProps['placement']>({
    base: 'bottom',
    lg: 'left'
  });
  const { isFetching } = useFacetedSearchResult();
  const { visible, hide } = useModal(MODAL.MAP_VIEW_FILTERS);

  return (
    <Drawer isOpen={visible} placement={placement} onClose={() => hide()}>
      <DrawerOverlay
        zIndex="modal"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        {isFetching ? <Spinner color="white" /> : null}
      </DrawerOverlay>

      <DrawerContent
        background="white"
        borderTopRadius={{ base: '12px', lg: 0 }}
        maxH={{ base: 'calc(100vh - 96px)', lg: '100vh' }}
        maxW="412px"
      >
        <DrawerBody borderTopRadius={{ base: '12px', lg: 0 }} p={0}>
          <SidebarLayout px={8} py={6} variant="unstyled">
            <DrawerCloseButton right="22px" top={6} />
            <SidebarLayout.Header>
              <FacetedSearchSummary heading="Filters" />
            </SidebarLayout.Header>

            <SidebarLayout.Body>
              <FilterList
                searchOptions={searchOptions}
                filterTooltips={filterTooltips}
              />
            </SidebarLayout.Body>
          </SidebarLayout>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};

export default MapViewSidebar;
