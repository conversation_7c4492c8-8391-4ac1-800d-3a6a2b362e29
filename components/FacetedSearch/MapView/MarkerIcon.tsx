import { Icon, IconProps } from '@chakra-ui/icon';
import { ComponentWithAs } from '@chakra-ui/system';

interface MarkerIconProps {
  backgroundColor?: IconProps['fill'];
  glyphColor?: IconProps['fill'];
}

const MarkerIcon: ComponentWithAs<'svg', IconProps & MarkerIconProps> = ({
  backgroundColor = 'currentColor',
  glyphColor = 'white',
  ...rest
}) => {
  return (
    <Icon
      viewBox="0 0 26.5 34.5"
      sx={{
        circle: {
          fill: glyphColor
        },
        path: {
          fill: backgroundColor
        }
      }}
      {...rest}
    >
      <circle cx="13.125" cy="13.125" r="7.875" />
      <path
        clipRule="evenodd"
        d="M13.2363 33.8002C13.9278 33.8002 26.2851 20.5972 26.2851 13.3906C26.2851 6.18393 20.4429 0.341797 13.2363 0.341797C6.02962 0.341797 0.1875 6.18393 0.1875 13.3906C0.1875 20.5972 12.5447 33.8002 13.2363 33.8002ZM13.2363 19.9171C16.8901 19.9171 19.8521 16.9551 19.8521 13.3013C19.8521 9.64756 16.8901 6.6856 13.2363 6.6856C9.58253 6.6856 6.62056 9.64756 6.62056 13.3013C6.62056 16.9551 9.58253 19.9171 13.2363 19.9171Z"
        fillRule="evenodd"
      />
    </Icon>
  );
};

export default MarkerIcon;
