import { Icon } from '@chakra-ui/icons';
import { Modal, ModalContent } from '@chakra-ui/modal';
import {
  ElementActions,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { SearchOption } from '@components/FacetedSearch/constants';
import MapViewSidebar from '@components/FacetedSearch/MapView/MapViewSidebar';
import Container from '@components/LayoutStructure/Container';
import { MODAL } from '@constants/modals';
import { useMemo } from 'react';
import { MdArrowBack, MdTune } from 'react-icons/md';

import { useModal, useModalDispatch } from '~/contexts/ModalContext';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { FilterTooltips } from '~/types/componentsConfig';

import { useFacetedSearchResult } from '../FacetedSearchContext';
import { parseProvider, Provider } from './parser';
import ProvidersMap from './ProvidersMap';

const MapViewHeader: React.FC = () => {
  const { showModal, hideModal } = useModalDispatch();

  return (
    <Container
      background="white"
      gap="4"
      display="flex"
      py="3"
      justifyContent={{ base: 'center', lg: 'flex-start' }}
    >
      <Button
        colorScheme="primary"
        elementAction={ElementActions.CLOSE_MODAL}
        elementType={ElementTypes.BUTTON}
        fontSize={{ base: 'sm!', lg: 'md!' }}
        fontWeight="bold"
        leftIcon={<Icon as={MdArrowBack} boxSize="18px" />}
        size={{ base: 'sm', lg: 'lg' }}
        onClick={() => hideModal(MODAL.MAP_VIEW)}
      >
        Back to List
      </Button>

      <Button
        colorScheme="primary"
        elementAction={ElementActions.OPEN_MODAL}
        elementType={ElementTypes.BUTTON}
        fontSize={{ base: 'sm!', lg: 'md!' }}
        fontWeight="bold"
        leftIcon={<Icon as={MdTune} boxSize="18px" />}
        size={{ base: 'sm', lg: 'lg' }}
        variant="outline"
        onClick={() => showModal(MODAL.MAP_VIEW_FILTERS)}
      >
        Filters
      </Button>
    </Container>
  );
};

interface MapViewModalProps {
  initialMapOptions: {
    defaultCenter: google.maps.LatLngLiteral;
  };
  searchOptions: Array<SearchOption>;
  filterTooltips?: FilterTooltips;
}

const MapViewModal: React.FC<MapViewModalProps> = ({
  initialMapOptions,
  searchOptions,
  filterTooltips
}) => {
  const { data } = useFacetedSearchResult();
  const { getProviderDetailsPath } = useTenantFunctions();
  const { visible: isOpen, hide: closeModal } = useModal(MODAL.MAP_VIEW);
  const providersToDisplay = useMemo(() => {
    return (data?.results || [])
      .map((provider) =>
        parseProvider({
          ...provider,
          legacyId: provider.legacyId || provider.id,
          url: getProviderDetailsPath(provider)
        })
      )
      .filter(Boolean) as Array<Provider>;
  }, [data, getProviderDetailsPath]);

  return (
    <Modal isOpen={isOpen} size="full" onClose={closeModal}>
      <ModalContent>
        <MapViewHeader />

        <MapViewSidebar
          searchOptions={searchOptions}
          filterTooltips={filterTooltips}
        />

        <ProvidersMap
          defaultCenter={initialMapOptions.defaultCenter}
          providers={providersToDisplay}
        />
      </ModalContent>
    </Modal>
  );
};

export default MapViewModal;
