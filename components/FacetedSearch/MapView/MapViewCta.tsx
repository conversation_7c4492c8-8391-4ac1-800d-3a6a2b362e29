import { Card, CardBody, CardProps } from '@chakra-ui/card';
import { ComponentWithAs } from '@chakra-ui/system';
import map from 'assets/map/map.png';
import Image from 'next/image';

import MapViewButton from './MapViewButton';
import MarkerIcon from './MarkerIcon';

const MapViewCta: ComponentWithAs<'div', CardProps> = (props) => {
  return (
    <Card
      overflow="hidden"
      rounded="xl"
      size="lg"
      variant="outline"
      py="7"
      {...props}
    >
      {/* Background Image */}
      <Image
        alt=""
        fill
        placeholder="blur"
        quality={100}
        sizes="424px"
        src={map}
        style={{ objectFit: 'cover' }}
      />

      <CardBody
        display="flex"
        flexDirection="column"
        gap="2"
        justifyContent="center"
      >
        <MarkerIcon
          boxSize={20}
          color="secondary.500"
          margin="auto"
          position="relative"
        />

        <MapViewButton showMarker={false} />
      </CardBody>
    </Card>
  );
};

export default MapViewCta;
