import {
  ElementActions,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button, { ButtonProps } from '@components/Button';
import { MODAL } from '@constants/modals';

import { useModalDispatch } from '~/contexts/ModalContext';

import MarkerIcon from './MarkerIcon';

interface MapViewButtonProps extends Omit<ButtonProps, 'elementAction'> {
  showMarker?: boolean;
}

const MapViewButton: React.FC<MapViewButtonProps> = ({
  showMarker = true,
  ...rest
}) => {
  const { showModal } = useModalDispatch();

  return (
    <Button
      colorScheme="secondary"
      elementAction={ElementActions.OPEN_MODAL}
      elementType={ElementTypes.BUTTON}
      fontSize="md"
      leftIcon={showMarker ? <MarkerIcon glyphColor="none" /> : undefined}
      size="lg"
      onClick={() => showModal(MODAL.MAP_VIEW)}
      {...rest}
    >
      Map View
    </Button>
  );
};

export default MapViewButton;
