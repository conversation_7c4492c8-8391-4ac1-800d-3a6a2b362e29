import { coerceNumber } from '@utils/coercion';
import { z } from 'zod';

const ProviderSchema = z.object({
  id: z.string(),
  address: z.string(),
  legacyId: z.string(),
  location: z.object({
    lat: coerceNumber(z.number().min(-90).max(90)),
    lng: coerceNumber(z.number().min(-180).max(180))
  }),
  name: z.string(),
  photos: z.array(z.string()),
  rating: coerceNumber(z.number()),
  reviewCount: coerceNumber(z.number()),
  url: z.string()
});

const ProviderParser = z
  .record(z.string(), z.unknown())
  .transform((provider) => {
    return {
      id: provider.id,
      address: provider.address ? provider.address['formattedAddress'] : null,
      legacyId: provider.legacyId,
      location: {
        lat: provider.address ? provider.address['latitude'] : null,
        lng: provider.address ? provider.address['longitude'] : null
      },
      name: provider.name,
      photos: provider.images,
      rating: provider.averageRating,
      reviewCount: provider.reviewCount,
      url: provider.url
    };
  })
  .pipe(ProviderSchema);

export type Provider = z.infer<typeof ProviderParser>;

export const parseProvider = (provider: unknown): Provider | null => {
  const result = ProviderParser.safeParse(provider);

  if (result.success) {
    return result.data;
  }

  console.error('MapView: Invalid provider data: ', result.error.message);

  return null;
};
