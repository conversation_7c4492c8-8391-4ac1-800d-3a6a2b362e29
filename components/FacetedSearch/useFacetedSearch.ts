import { SearchStates } from '@components/FacetedSearch/constants';
import {
  getAmenityCategoriesFromResponse,
  isValidCareTypeForFacetedSearch
} from '@components/FacetedSearch/utils';
import { getFeatureOverrideFromSearchParamsClientSide } from '@lib/featureOverride';
import {
  fetchProvidersWithEnhancedSearch,
  fetchProvidersWithFacetedSearch,
  fetchProvidersWithFacetedSearchV2
} from '@services/faceted-search/api';
import { AlgoliaProvider } from '@services/modular-monolith/types/search.type';
import { useInfiniteQuery } from '@tanstack/react-query';
import { QueryKey } from '@utils/queryKey';
import { useRouter } from 'next/router';
import { useMemo } from 'react';

import {
  SessionContextType,
  useSessionContext
} from '~/contexts/SessionContext';
import { Source } from '~/types/json-api-response';
import { parseResultAmenities, parseResultContracts } from '~/utils/search';

import { trackProviderListFiltered } from './facetedSearchTracking';
import { FacetedSearchParsedParams } from './search-params';
import { FacetedSearchParsedProps, FacetedSearchResponse } from './types';

const fieldsToIgnore = new Set(['matchAllFilters']);

const baseSearchQuery = {
  accessibility: [],
  awards: [],
  careType: '',
  dining: [],
  distanceInMiles: 30,
  healthServices: [],
  keyword: '',
  latLng: '',
  languages: [],
  lifestyle: [],
  matchAllFilters: true,
  ongoingPromotion: [],
  otherAmenities: [],
  personalCare: [],
  priceRange: [1, 4],
  providersWith: [],
  reviews: [],
  roomAmenities: [],
  roomType: [],
  sortBy: 'recommended',
  staffQualifications: [],
  verified: []
};

export const fetchProvidersByFacets = async ({
  searchQuery,
  hasActiveFilters,
  page,
  session,
  componentProps,
  apiVersion,
  featureOverride,
  geoUrl
}: {
  searchQuery: FacetedSearchParsedParams;
  hasActiveFilters: boolean;
  page: number;
  session: SessionContextType;
  componentProps: FacetedSearchParsedProps;
  apiVersion?: string;
  featureOverride?: string;
  geoUrl?: string;
}): Promise<FacetedSearchResponse> => {
  const source = componentProps.source;

  const searchParams = {
    ...searchQuery,
    page
  };

  const data =
    source === Source.ALGOLIA || hasActiveFilters
      ? componentProps.displayMode === 'search'
        ? await fetchProvidersWithFacetedSearchV2({
            searchParams,
            componentProps
          })
        : await fetchProvidersWithFacetedSearch({
            searchParams,
            componentProps
          })
      : await fetchProvidersWithEnhancedSearch({
          searchParams,
          componentProps,
          apiVersion,
          featureOverride,
          geoUrl
        });

  const { totalItems, totalNearbyItems, totalPages, totalRegionItems } = data;

  const resultsWithAmenities = parseResultAmenities(
    source,
    data.results as AlgoliaProvider[]
  );

  const results = parseResultContracts(
    source,
    resultsWithAmenities as AlgoliaProvider[]
  );

  const careType = componentProps.careType;
  const isCareTypeValid = isValidCareTypeForFacetedSearch(careType);
  const amenityCategories = isCareTypeValid
    ? getAmenityCategoriesFromResponse(data, careType)
    : [];

  trackProviderListFiltered({
    session,
    searchQuery,
    response: data
  });

  return {
    amenityCategories,
    listId: data.listId,
    nearbyCount: totalNearbyItems,
    totalPages,
    queryId: data.queryId,
    regionCount: totalRegionItems,
    resultCount: totalItems,
    results,
    resultsPerPage: componentProps.resultsPerPage,
    searchState:
      totalItems === 0
        ? SearchStates.INPUT_WITH_NO_RESULTS
        : SearchStates.INPUT_WITH_RESULTS
  };
};

const useFacetedSearch = ({
  searchQuery,
  initialData,
  componentProps,
  geoUrl
}: {
  searchQuery: FacetedSearchParsedParams;
  initialData?: FacetedSearchResponse;
  componentProps: FacetedSearchParsedProps;
  geoUrl: string | undefined;
}) => {
  const initialResultCount = initialData?.resultCount ?? 0;
  const session = useSessionContext();
  const { query } = useRouter();

  const hasActiveFilters = useMemo(() => {
    return Object.entries(searchQuery).some(([key, currentValue]) => {
      if (fieldsToIgnore.has(key)) return false;

      const defaultValue = baseSearchQuery[key];

      if (Array.isArray(currentValue) && Array.isArray(defaultValue)) {
        if (currentValue.length !== defaultValue.length) return true;
        return JSON.stringify(currentValue) !== JSON.stringify(defaultValue);
      }
      return currentValue !== defaultValue;
    });
  }, [searchQuery]);

  return useInfiniteQuery({
    queryKey: [QueryKey.FacetedSearch, { searchQuery, query }],
    queryFn: ({ pageParam = 0 }) => {
      //To-do: To allow fetchProvidersByFacets call support for filters
      // if (!initialResultCount) {
      //   return Promise.resolve({
      //     amenityCategories: [],
      //     listId: '',
      //     nearbyCount: 0,
      //     totalPages: 0,
      //     queryId: '',
      //     regionCount: 0,
      //     resultCount: 0,
      //     results: [],
      //     resultsPerPage: 0,
      //     searchState: SearchStates.INPUT_WITH_NO_RESULTS
      //   });
      // }

      return fetchProvidersByFacets({
        searchQuery,
        hasActiveFilters,
        page: pageParam,
        session,
        componentProps,
        apiVersion: String(query.geo),
        featureOverride: getFeatureOverrideFromSearchParamsClientSide(query),
        geoUrl
      });
    },
    getNextPageParam: (lastPage, allPages) => {
      const hasMore =
        lastPage.resultCount > allPages.flatMap((p) => p.results).length;
      return hasMore ? allPages.length : undefined;
    },
    initialData: initialData
      ? {
          pages: [initialData],
          pageParams: [0]
        }
      : undefined,
    keepPreviousData: true,
    cacheTime: 0,
    enabled: initialResultCount <= 0 || !initialData
  });
};

export default useFacetedSearch;
