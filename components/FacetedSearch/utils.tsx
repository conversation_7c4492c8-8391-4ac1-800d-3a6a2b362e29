'use client';

import { Grid, GridItem, Heading, Link, Text } from '@chakra-ui/layout';
import {
  Community,
  CommunityComparisonBanner,
  ComparisonErrorDialog
} from '@components/CommunityComparison/GeosCommunityComparison';
import {
  AmenityCategory,
  DISPLAY_MODE,
  DisplayMode,
  SEARCH_OPTION,
  SearchStates
} from '@components/FacetedSearch/constants';
import SearchResult from '@components/FacetedSearch/SearchResult';
import {
  CareTypeObject,
  FacetedSearchParsedProps,
  renderResultProps
} from '@components/FacetedSearch/types';
import { getDynamicLayout } from '@components/Layouts/layoutConstants';
import { EnhancedSearchParsedConfig } from '@components/Search/EnhancedSearch/parser';
import { EditableArea } from '@magnolia/react-editor';
import {
  CareType as MagnoliaCareType,
  getDataByDomain
} from '@services/magnolia/getDataByDomain';
import { ModularMonolithClient } from '@services/modular-monolith/client';
import {
  FacetedSearchRequestQuery,
  GetProvidersResponse
} from '@services/modular-monolith/types/search.type';
import {
  AMENITY_CATEGORIES,
  AMENITY_CATEGORIES_BY_CARE_TYPE,
  AmenityCategoryNode,
  CareType,
  isAmenityCategory
} from '@utils/faceted-search';
import { isTrue } from '@utils/isTrue';
import mergeWith from 'lodash/mergeWith';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { Amenities, LegacyAmenities, Provider } from '~/contexts/Provider';
import { Domain, Domains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';
import { calculateProviderMinPrice } from '~/utils/providers';

import {
  FacetedSearchParsedParams,
  hasSearchParamsChanged
} from './search-params';
const getScrollMargins = (target: HTMLElement) => {
  const computedStyle = getComputedStyle(target);
  const top = parseFloat(computedStyle.scrollMarginTop) || 0;
  const right = parseFloat(computedStyle.scrollMarginRight) || 0;
  const bottom = parseFloat(computedStyle.scrollMarginBottom) || 0;
  const left = parseFloat(computedStyle.scrollMarginLeft) || 0;

  return { top, right, bottom, left };
};

export const scrollToElement = (
  element: HTMLElement | null,
  behavior: ScrollBehavior = 'smooth',
  offset: number = parseInt(getDynamicLayout().HEADER_DESKTOP_HEIGHT)
) => {
  if (!element) {
    return;
  }

  const scrollMargin = getScrollMargins(element).top;

  const top = element.offsetTop - offset - scrollMargin;

  window.scrollTo({
    top,
    behavior
  });
};

export const createEnhancedSearchQuery = ({
  searchParams,
  componentProps
}: {
  searchParams: Omit<FacetedSearchParsedParams, 'searchParams'>;
  componentProps: FacetedSearchParsedProps;
}) => {
  const query = {
    careType: componentProps.careType,
    city: componentProps.city,
    county: componentProps.county,
    hitsPerPage: componentProps.resultsPerPage,
    keyword: searchParams.keyword,
    latitude: componentProps.latitude,
    longitude: componentProps.longitude,
    page: searchParams.page,
    state: componentProps.state,
    source: componentProps.source,
    radiusForSearch: searchParams.distanceInMiles,
    domain: componentProps.domain,
    shouldShowOnlyIndexedProviders:
      componentProps?.shouldShowOnlyIndexedProviders || false
  };
  return query;
};

const createLocationParams = (
  componentProps: FacetedSearchParsedProps
): {
  latitude?: string | undefined;
  longitude?: string | undefined;
  keyword?: string | undefined;
  city?: string | undefined;
  county?: string | undefined;
  state?: string | undefined;
} => {
  return componentProps.distanceFilterEnabled
    ? {
        latitude: componentProps.latitude,
        longitude: componentProps.longitude
        // keyword: '',
      }
    : {
        city: componentProps.city,
        county: componentProps.county,
        state: componentProps.state
      };
};

const createCriteria = (
  searchParams: FacetedSearchParsedParams,
  componentProps: FacetedSearchParsedProps,
  geoParams:
    | object
    | {
        latitude?: string | undefined;
        longitude?: string | undefined;
        keyword?: string | undefined;
        city?: string | undefined;
        county?: string | undefined;
        state?: string | undefined;
      }
) => {
  const [lat, lng] = searchParams.latLng?.split(',') || [];
  const criteria = {
    accessibility: searchParams.accessibility,
    amenityCategory: componentProps.amenityCategory
      ? [componentProps.amenityCategory]
      : [],
    awards: searchParams.awards,
    careType:
      componentProps.displayMode === DISPLAY_MODE.LIST &&
      componentProps.careType !== ''
        ? componentProps.careType
        : searchParams.careType,
    dining: searchParams.dining,
    distanceInMiles: searchParams.distanceInMiles,
    domain: componentProps.domain,
    healthServices: searchParams.healthServices,
    keyword: searchParams.keyword,
    languages: searchParams.languages,
    lifestyle: searchParams.lifestyle,
    ongoingPromotion: searchParams.ongoingPromotion,
    otherAmenities: searchParams.otherAmenities,
    personalCare: searchParams.personalCare,
    priceRange: searchParams.priceRange,
    providersWith: searchParams.providersWith,
    reviews: searchParams.reviews,
    roomAmenities: searchParams.roomAmenities,
    roomType: searchParams.roomType,
    staffQualifications: searchParams.staffQualifications,
    verified: searchParams.verified,
    ...geoParams
  };

  if (lat) {
    criteria.latitude = lat;
  }

  if (lng) {
    criteria.longitude = lng;
  }

  return criteria;
};

export const createFacetedSearchQuery = ({
  componentProps,
  searchParams
}: {
  componentProps: FacetedSearchParsedProps;
  searchParams: FacetedSearchParsedParams;
}) => {
  const geoParams = createLocationParams(componentProps);
  const criteria = createCriteria(searchParams, componentProps, geoParams);

  const shouldShowOnlyIndexedProvidersFilter = isTrue(
    componentProps?.shouldShowOnlyIndexedProviders
  )
    ? {
        shouldShowOnlyIndexedProviders: true
      }
    : {};

  return {
    hitsPerPage: componentProps.resultsPerPage,
    matchAllFilters: searchParams.hasOwnProperty('matchAllFilters')
      ? searchParams.matchAllFilters
      : false,
    ...shouldShowOnlyIndexedProvidersFilter,
    page: searchParams.page,
    sortBy: searchParams.sortBy,
    criteria
  };
};

export const isAmenityCategoryPage = (
  amenityCategoryChips: EnhancedSearchParsedConfig['amenityCategoryChips']
): Boolean => {
  if (
    amenityCategoryChips.visible &&
    isAmenityCategory(amenityCategoryChips.amenityCategory || '')
  ) {
    return true;
  }

  return false;
};

export const getSourceForFacetedSearch = ({
  displayMode,
  domain,
  amenityCategoryChips,
  searchParams
}: {
  displayMode: DisplayMode;
  domain: Domain;
  amenityCategoryChips: EnhancedSearchParsedConfig['amenityCategoryChips'];
  searchParams: FacetedSearchParsedParams;
}): Source => {
  const useAlgolia =
    domain !== Domains.CaringDomains.LIVE ||
    displayMode === DISPLAY_MODE.SEARCH ||
    isAmenityCategoryPage(amenityCategoryChips) ||
    hasSearchParamsChanged(searchParams);

  return useAlgolia ? Source.ALGOLIA : Source.LEGACY;
};

export const hasDistanceFilter = (
  searchSidebar: EnhancedSearchParsedConfig['searchSidebar']
) => {
  return searchSidebar.filters.includes(SEARCH_OPTION.DISTANCE);
};

export const getCareTypeId = (
  slug: string,
  careTypes: MagnoliaCareType[]
): string => {
  const matchingCareType = careTypes.find((e) => e.slug === slug);
  if (matchingCareType) return matchingCareType.id;
  else return slug;
};

export const getDefaultKeywords = ({
  state,
  city,
  county,
  queryKeyword = ''
}) => {
  let keyword = '';

  const shouldRenderSearchBar = !!(state || city || county);

  if (!shouldRenderSearchBar && !queryKeyword) {
    return '';
  }

  city && !county && (keyword += `${city} `);
  state && (keyword += `${state} `);
  county && (keyword += `${county} `);

  if (queryKeyword) keyword += queryKeyword;

  return keyword;
};

export const getContent = (
  fullWidthTile,
  result: Provider,
  index,
  showPrice = true,
  displayBadges = false,
  getProviderDetailsPath,
  getProviderDescription,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton,
  requestInfoButtonText,
  readMoreButton,
  inquiryId,
  showMoreLimit,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor,
  ratingStarsColorRange,
  providerTitleColor,
  providerTitleColorRange,
  boxShadow,
  tileBorder,
  tileBorderColor,
  tileBorderColorRange,
  displayProviderPhoneNumber,
  providerPhoneNumberSource,
  dontOpenInNewTab,
  careType?: string,
  queryId?: string,
  listId?: string,
  isChecked?: boolean,
  displayCompareOption?: boolean,
  onSelectProvider?: (community: Community) => void,
  promotionColorScheme?: string
) => {
  const {
    legacyId,
    address,
    name,
    awards,
    images,
    averageRating,
    reviewCount,
    id,
    promotions,
    lastReviewSnippet,
    amenities,
    isIndexed
  } = result;

  const minPrice = calculateProviderMinPrice({ result, careType });
  const path = getProviderDetailsPath(result);
  const isHidden = showMoreLimit === 0 ? false : index > showMoreLimit;

  return (
    <SearchResult
      key={index}
      id={id}
      legacyId={legacyId ?? id}
      title={name}
      isIndexed={isIndexed}
      address={address?.formattedAddress ?? ''}
      images={images ?? []}
      averageRating={averageRating}
      reviewCount={reviewCount}
      description={getProviderDescription(result)}
      price={minPrice}
      path={path}
      isChecked={isChecked}
      handleCompare={() => {
        if (onSelectProvider) {
          onSelectProvider({ index, ...result });
        }
      }}
      caringStars={awards || []}
      displayBadges={displayBadges}
      displayLearnMoreButton={displayLearnMoreButton}
      learnMoreButtonText={learnMoreButtonText}
      displayRequestInfoButton={displayRequestInfoButton}
      requestInfoButtonText={requestInfoButtonText}
      readMoreButton={readMoreButton}
      modalId={inquiryId}
      isHidden={isHidden}
      requestInfoButtonColorScheme={requestInfoButtonColorScheme}
      learnMoreButtonColorScheme={learnMoreButtonColorScheme}
      ratingStarsColor={ratingStarsColor}
      ratingStarsColorRange={ratingStarsColorRange}
      providerTitleColor={providerTitleColor}
      providerTitleColorRange={providerTitleColorRange}
      boxShadow={boxShadow}
      border={tileBorder}
      borderColor={tileBorderColor}
      borderColorRange={tileBorderColorRange}
      displayProviderPhoneNumber={displayProviderPhoneNumber}
      providerPhoneNumberSource={providerPhoneNumberSource}
      phoneNumber={result.phoneNumber}
      promotions={promotions}
      legacyResourceId={result.services?.[0]?.legacyResourceId}
      lastReviewSnippet={lastReviewSnippet}
      queryId={queryId}
      listId={listId}
      displayCompareOption={displayCompareOption}
      amenities={amenities as Amenities | LegacyAmenities}
      dontOpenInNewTab={dontOpenInNewTab}
      promotionColorScheme={promotionColorScheme}
    />
  );
};

export const RenderResults = ({
  results,
  searchState,
  itemsPerRow,
  providerTitleColor,
  providerTitleColorRange,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor,
  ratingStarsColorRange,
  boxShadow,
  tileBorder,
  tileBorderColor,
  tileBorderColorRange,
  fullWidthTile,
  displayBadges,
  showPrice,
  genericBlock1,
  genericBlock2,
  metadata,
  currentPage,
  getProviderDetailsPath,
  getProviderDescription,
  showMoreLimit,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton,
  requestInfoButtonText,
  readMoreButton,
  inquiryId,
  displayProviderPhoneNumber,
  providerPhoneNumberSource,
  careType,
  filterAndSort,
  queryId,
  listId,
  displayCompareOption = false,
  dontOpenInNewTab,
  promotionColorScheme
}: renderResultProps) => {
  const router = useRouter();

  const selectedCommunities = router.query?.['selectedCommunities'];
  const [isCompareModalOpen, setIsCompareModalOpen] = useState<Boolean>(
    router.query?.['isModalOpen'] === 'true'
  );

  const [providersList, setProvidersList] = useState<Community[]>([]);
  const [displayComparisonErrorDialog, setDisplayComparisonErrorDialog] =
    useState<boolean>(false);

  const genericBlock =
    currentPage !== undefined && currentPage % 2 === 0
      ? genericBlock1
      : genericBlock2;

  const gridSetup = fullWidthTile
    ? '1fr'
    : {
        base: 'repeat(3, 1fr)',
        sm: 'repeat(6, 1fr)',
        md: `repeat(${itemsPerRow}, 1fr)`
      };
  const genericBlockFrequency = 6;

  useEffect(() => {
    if (selectedCommunities) {
      const selectedItems = (selectedCommunities as string).split(',');
      const filteredProviders = results
        .map((provider, index) => {
          if (selectedItems.includes(String(provider.id))) {
            return {
              ...provider,
              index
            } as Community;
          }
          return null;
        })
        .filter((provider): provider is Community => provider !== null);

      setProvidersList(filteredProviders);
    }
  }, [selectedCommunities, results]);

  useEffect(() => {
    if (router?.query?.hasOwnProperty('isModalOpen')) {
      setIsCompareModalOpen(router.query?.isModalOpen === 'true');
    }
  }, [router.query]);

  const handleCompareProvider = (community: Community) => {
    const isProviderPresent = isProviderInList(community, providersList);

    if (providersList.length > 3 && !isProviderPresent) {
      return setDisplayComparisonErrorDialog(true);
    }

    if (!isProviderPresent) {
      setProvidersList([...providersList, community]);
    } else {
      const filteredProviders = providersList.filter(
        (existingProvider) => existingProvider.id !== community.id
      );
      setProvidersList(filteredProviders);
    }
  };

  switch (searchState) {
    case SearchStates.INPUT_WITH_RESULTS:
      const resultsToRenderFirst = results.slice(0, genericBlockFrequency);
      const resultsToRenderAfter = results.slice(
        genericBlockFrequency,
        results.length
      );
      return (
        <>
          <Grid templateColumns={gridSetup} gap={6}>
            {resultsToRenderFirst.map((result, index) => {
              return getContent(
                fullWidthTile,
                result,
                index,
                showPrice,
                displayBadges,
                getProviderDetailsPath,
                getProviderDescription,
                displayLearnMoreButton,
                learnMoreButtonText,
                displayRequestInfoButton,
                requestInfoButtonText,
                readMoreButton,
                inquiryId,
                showMoreLimit,
                requestInfoButtonColorScheme,
                learnMoreButtonColorScheme,
                ratingStarsColor,
                ratingStarsColorRange,
                providerTitleColor,
                providerTitleColorRange,
                boxShadow,
                tileBorder,
                tileBorderColor,
                tileBorderColorRange,
                displayProviderPhoneNumber,
                providerPhoneNumberSource,
                dontOpenInNewTab,
                careType,
                queryId,
                listId,
                isProviderInList(result, providersList),
                displayCompareOption,
                handleCompareProvider,
                promotionColorScheme
              );
            })}
          </Grid>
          {genericBlock && (
            <EditableArea
              content={genericBlock}
              parentTemplateId={metadata?.['mgnl:template']}
            />
          )}
          <Grid templateColumns={gridSetup} gap={6}>
            {resultsToRenderAfter.map((result, index) => {
              const showMoreLimitAfter =
                showMoreLimit && showMoreLimit > 0
                  ? showMoreLimit - genericBlockFrequency
                  : 0;
              return getContent(
                fullWidthTile,
                result,
                index,
                showPrice,
                displayBadges,
                getProviderDetailsPath,
                getProviderDescription,
                displayLearnMoreButton,
                learnMoreButtonText,
                displayRequestInfoButton,
                requestInfoButtonText,
                readMoreButton,
                inquiryId,
                showMoreLimitAfter,
                requestInfoButtonColorScheme,
                learnMoreButtonColorScheme,
                ratingStarsColor,
                ratingStarsColorRange,
                providerTitleColor,
                providerTitleColorRange,
                boxShadow,
                tileBorder,
                tileBorderColor,
                tileBorderColorRange,
                displayProviderPhoneNumber,
                providerPhoneNumberSource,
                dontOpenInNewTab,
                careType,
                queryId,
                listId,
                isProviderInList(result, providersList),
                displayCompareOption,
                handleCompareProvider
              );
            })}
          </Grid>
          {providersList.length > 0 && (
            <CommunityComparisonBanner
              listId={listId}
              router={router}
              queryId={queryId}
              modalId={inquiryId}
              providers={providersList}
              displayBadges={displayBadges}
              onCancel={handleCompareProvider}
              displayViewCommunityButton={true}
              ratingStarsColor={ratingStarsColor}
              providerTitleColor={providerTitleColor}
              viewCommunityButtonColorScheme="secondary"
              ratingStarsColorRange={ratingStarsColorRange}
              openCompareModal={
                Boolean(selectedCommunities) && Boolean(isCompareModalOpen)
              }
              providerTitleColorRange={providerTitleColorRange}
              displayRequestInfoButton={displayRequestInfoButton}
            />
          )}
          {displayComparisonErrorDialog && (
            <ComparisonErrorDialog
              isOpen={displayComparisonErrorDialog}
              onClose={() => setDisplayComparisonErrorDialog(false)}
            />
          )}
        </>
      );
    case SearchStates.NO_SEARCH_INPUT:
      return (
        <GridItem
          display="flex"
          flexDirection={'column'}
          justifyContent="center"
          textAlign="center"
          height={500}
          colSpan={12}
        >
          <Heading size="lg">No Results</Heading>
          <Heading size="md">
            Please change your filters to produce results
          </Heading>
        </GridItem>
      );
    case SearchStates.INPUT_WITH_NO_RESULTS:
      return (
        <Text fontSize="lg" textAlign="center" fontWeight="normal">
          There aren&apos;t any providers that match your preferences. If
          you&apos;ve selected multiple filters, try expanding your search with
          fewer filters.
          <br />
          <br />
          {filterAndSort?.phoneNumber ? (
            <>
              If you&apos;re still unable to find a provider that meets your
              needs, our expert{' '}
              <Text as="span" fontWeight="bold">
                Family Advisor team can help.
              </Text>{' '}
              <Text as="span" fontWeight="bold" color="accent.500">
                Call us at{' '}
                <Link href={`tel:${filterAndSort.phoneNumber.number}`}>
                  {filterAndSort.phoneNumber.label}
                </Link>
                .
              </Text>
            </>
          ) : null}
        </Text>
      );
    default:
      return <>No results</>;
  }
};

export const isProviderInList = (provider, providersList) => {
  return providersList.some(
    (existingProvider) => existingProvider.id === provider.id
  );
};

export const fetchCareTypes = async (
  domain: Domain
): Promise<CareTypeObject[]> => {
  const domainData = await getDataByDomain({ domain });
  if (domainData.careTypes && domainData.careTypes.length > 0) {
    const filteredCareTypes = domainData.careTypes.filter(
      (careType) => careType.name
    );
    return filteredCareTypes;
  }
  return [];
};

export const isValidCareTypeForFacetedSearch = (
  careType: string
): careType is CareType => {
  return Object.values(CareType).includes(careType as CareType);
};

export const getAmenitiesByCategory = (amenityCategory: string): string[] => {
  if (isAmenityCategory(amenityCategory)) {
    return AMENITY_CATEGORIES[amenityCategory].amenities;
  }

  return [];
};

const addOptionalNumbers = (objValue?: number, srcValue?: number) => {
  return (objValue ?? 0) + (srcValue ?? 0);
};

const mergeAmenitiesCount = (
  inRegionCount: Record<string, number>,
  nearbyCount: Record<string, number>
): Record<string, number> => {
  const mergedAmenitiesCount = {};
  return mergeWith(
    mergedAmenitiesCount,
    inRegionCount,
    nearbyCount,
    addOptionalNumbers
  );
};

const getAmenityCounts = (
  response: GetProvidersResponse
): Record<string, number> => {
  const inRegionFacets =
    response.inRegionFacets?.['amenities.amenityType.name'] ?? {};
  const nearbyFacets =
    response.nearbyFacets?.['amenities.amenityType.name'] ?? {};
  return mergeAmenitiesCount(inRegionFacets, nearbyFacets);
};

const getAmenityCategoriesByCareType = (
  careType: string
): AmenityCategory[] => {
  if (isValidCareTypeForFacetedSearch(careType)) {
    return AMENITY_CATEGORIES_BY_CARE_TYPE[careType];
  }

  return [];
};

export const getMaxAmenityCount = (
  amenities: string[],
  amenitiesCount: Record<string, number>
): number => {
  let count = 0;
  for (const amenity of amenities) {
    const currentCount = amenitiesCount[amenity] ?? 0;
    // Get the max count of all amenities in the category
    // e.g. if there are 2 amenities in the category, one with 5 and one with 10, the count should be 10
    // This is because one provider can have multiple amenities in the same category
    // e.g. a provider can have both 'Swimming Pool' and 'Hot Tub Spa' amenities
    // and both of these amenities are in the 'Pool / Hot Tub' category
    // so the count for the 'Pool / Hot Tub' category should be 1
    count = Math.max(count, currentCount);
  }
  return count;
};

export interface AmenityCategoryNodeWithCount extends AmenityCategoryNode {
  count: number;
}

const mergeAmenityCategoriesWithCounts = (
  amenityCategories: AmenityCategory[],
  amenitiesCount: Record<string, number>
): AmenityCategoryNodeWithCount[] => {
  return amenityCategories.map((amenityCategorySlug) => {
    const amenityCategory = AMENITY_CATEGORIES[amenityCategorySlug];
    const amenities = amenityCategory.amenities;
    const count = getMaxAmenityCount(amenities, amenitiesCount);

    return {
      ...amenityCategory,
      count
    };
  });
};

export const getAmenityCategoriesFromResponse = (
  response: GetProvidersResponse,
  careType: string
): AmenityCategoryNodeWithCount[] => {
  const amenitiesCount = getAmenityCounts(response);
  const amenityCategories = getAmenityCategoriesByCareType(careType);
  const amenityCategoriesWithCounts = mergeAmenityCategoriesWithCounts(
    amenityCategories,
    amenitiesCount
  );

  return amenityCategoriesWithCounts.filter(
    (amenityCategory) => amenityCategory.count > 0
  );
};
export const fetchAmenityCategoryCounts = async (
  searchParams: FacetedSearchRequestQuery
): Promise<AmenityCategoryNodeWithCount[]> => {
  const { domain } = searchParams.criteria;
  const careType = searchParams.criteria.careType ?? '';
  const hasInvalidCareType = !isValidCareTypeForFacetedSearch(careType);

  if (hasInvalidCareType) {
    return [];
  }

  // Remove amenities from searchParams to get the count of amenities categories
  const updatedSearchParams: FacetedSearchRequestQuery = {
    ...searchParams,
    criteria: {
      ...searchParams.criteria,
      amenityCategory: []
    },
    page: 0
  };

  const modularMonolithClient = new ModularMonolithClient(domain as Domain);
  const providers = await modularMonolithClient.searchProvidersByFacets(
    updatedSearchParams
  );

  return getAmenityCategoriesFromResponse(providers, careType);
};
