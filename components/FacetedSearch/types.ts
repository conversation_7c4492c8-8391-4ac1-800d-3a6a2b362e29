import { LinkBoxProps } from '@chakra-ui/react';
import { As } from '@chakra-ui/system';
import {
  DisplayMode,
  SearchOption,
  SearchStates
} from '@components/FacetedSearch/constants';
import { SortingBy } from '@services/modular-monolith/types/search.type';
import { AmenityCategoryNode } from '@utils/faceted-search';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import {
  Amenities,
  Award,
  LegacyAmenities,
  Provider,
  ProviderService
} from '~/contexts/Provider';
import {
  DefaultFilterValues,
  FilterTooltips,
  PhoneNumberConfig
} from '~/types/componentsConfig';
import { Domain } from '~/types/Domains';
import { Source } from '~/types/json-api-response';
import { EditableAreaType, Metadata } from '~/types/Magnolia';

// A type that represents a nested key path in an object.
// For example, for an object { a: { b: { c: 1 } } }, a valid NestedKeyPath would be 'a.b.c'.
export type NestedKeyPath<T, K = keyof T> = K extends keyof T & string
  ?
      | `${K}`
      | (T[K] extends Record<string, unknown>
          ? `${K}.${NestedKeyPath<T[K]>}`
          : never)
  : never;

// A type that represents the value type of a nested key in an object.
// For example, for an object { a: { b: { c: 1 } } }, the NestedValueType of 'a.b.c' would be number.
export type NestedValueType<T, K extends string> = K extends keyof T
  ? T[K]
  : K extends `${infer F}.${infer R}`
  ? F extends keyof T
    ? NestedValueType<T[F], R>
    : never
  : never;

export interface FacetedSearchQuery {
  accessibility: string[];
  amenityCategory: string[];
  awards: string[];
  dining: string[];
  distanceInMiles: number;
  healthServices: string[];
  languages: string[];
  lifestyle: string[];
  matchAllFilters: boolean;
  shouldShowOnlyIndexedProviders: boolean;
  ongoingPromotion: string[];
  otherAmenities: string[];
  personalCare: string[];
  priceRange: [number, number];
  providersWith: string[];
  reviews: string[];
  roomAmenities: string[];
  roomType: string[];
  sortBy: SortingBy;
  staffQualifications: string[];
  verified: string[];
  keyword: string;
  page: ZeroIndexedPage;
}

export interface FacetedSearchResult {
  data: FacetedSearchResponse | undefined;
  isFetching: boolean;
  isFetchingNextPage?: boolean;
  hasNextPage?: boolean;
}

export interface FacetedSearchResponse extends Omit<SearchData, 'careTypes'> {}

export type QueryKeyPath = NestedKeyPath<FacetedSearchQuery>;

export type QueryKeyValue<K extends QueryKeyPath> = NestedValueType<
  FacetedSearchQuery,
  K
>;

export interface FacetedSearchParsedProps {
  amenityCategory?: string;
  careType: string;
  city: string;
  county: string;
  displayMode: DisplayMode;
  distanceFilterEnabled: boolean;
  domain: Domain;
  latitude: string;
  longitude: string;
  resultsPerPage: number;
  source: Source;
  state: string;
  shouldShowOnlyIndexedProviders: boolean;
}

export type CareTypeObject = {
  id: string;
  slug: string;
  name: string;
  rollUpType: string;
};

export type CareTypeString = string;

export type CareType = CareTypeObject | CareTypeString;

export interface SearchComponentDialogProps {
  countOfTiles?: string;
  countOfTilesPerRow?: string;
  radiusForSearch?: number;
  title?: string;
  headingElement: HeadingElements;
  titleSize: HeadingSizes;
  desktopTitleSize: HeadingSizes;
  state?: string;
  city?: string;
  county?: string;
  latitude?: string;
  longitude?: string;
  careType?: CareType;
  data: SearchData;
  displayGuidedSearchBanner?: boolean;
  guidedSearchInquiryId?: string;
  fullWidthTile?: boolean;
  displayCompareOption?: boolean;
  displayBadges?: boolean;
  excludeNearby?: boolean;
  displayToggleMap?: boolean;
  hideSearchButton?: boolean;
  searchBarBgColor: string;
  searchBarBgColorRange: string;
  searchBarButtonColorScheme: string;
  displaySearchBar?: boolean;
  displayTotal?: boolean;
  blurCosts?: boolean;
  readMoreButton?: string;
  genericBlock1: EditableAreaType;
  genericBlock2: EditableAreaType;
  metadata: Metadata;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  infoButtonInquiryId?: string;
  requestInfoButtonText?: string;
  requestInfoButtonColorScheme: string;
  learnMoreButtonColorScheme: string;
  ratingStarsColor: string;
  ratingStarsColorRange: string;
  providerTitleColor: string;
  providerTitleColorRange: string;
  visibleLimit?: string;
  bgColor: string;
  bgColorRange: string;
  bgBorderRadius: number;
  displayLocationFilter: boolean;
  readOnlyLocationInput: boolean;
  displayCareTypeFilter: boolean;
  displayProviderPhoneNumber: boolean;
  providerPhoneNumberSource?: {
    field: string;
    providerPhoneNumber?: string;
  };
  readOnlyLocationInputPlaceholder: string;
  enablePredictiveSearch: boolean;
  boxShadow: string;
  tileBorder: string;
  tileBorderColor: string;
  tileBorderColorRange: string;
  titleAlignment?: 'left' | 'center' | 'right';
  facetedSearch?: {
    enabled?: boolean;
    amenityCategory?: string;
    basePath?: string;
  };
  maxWidth?: number;
  filterAndSort?: {
    filters?: Array<SearchOption>;
    phoneNumber?: PhoneNumberConfig;
    defaultValues?: DefaultFilterValues;
    filterTooltips?: FilterTooltips;
  };
  mapView?: {
    enabled?: boolean;
  };
  displayMode?: DisplayMode;
  groupSearchCareTypeOptions?: boolean;
  preFillWithPageValues?: boolean;
  dontOpenInNewTab: boolean;
  promotionColorScheme?: string;
}

export type ZeroIndexedPage = number;

export enum LayoutType {
  Normal = 'normal',
  Optimized = 'optimized'
}

export interface SearchData {
  amenityCategories: AmenityCategoryNode[];
  careTypes: CareTypeObject[];
  currentPage?: number;
  listId?: string;
  nearbyCount: number;
  layoutType?: LayoutType;
  outOfBounds?: boolean;
  queryId?: string;
  regionCount: number;
  resultCount: number;
  results: Provider[];
  resultsPerPage: ZeroIndexedPage;
  searchState: SearchStates;
  totalPages?: number;
}

export interface renderResultProps {
  results: Provider[];
  searchState: SearchStates;
  itemsPerRow: number;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  requestInfoButtonColorScheme?: string;
  learnMoreButtonColorScheme?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  boxShadow?: string;
  tileBorder?: string;
  tileBorderColor?: string;
  tileBorderColorRange?: string;
  fullWidthTile?: boolean;
  displayCompareOption?: boolean;
  displayBadges?: boolean;
  showPrice?: boolean;
  genericBlock1?: EditableAreaType;
  genericBlock2?: EditableAreaType;
  metadata?: Metadata;
  currentPage?: number;
  getProviderDetailsPath?: (provider: Provider) => string;
  getProviderDescription?: (provider: Provider) => string;
  showMoreLimit?: number;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  readMoreButton?: string;
  inquiryId?: string;
  displayProviderPhoneNumber?: boolean;
  providerPhoneNumberSource?: {
    field: string;
    providerPhoneNumber?: string;
  };
  careType?: string;
  filterAndSort?: {
    phoneNumber?: PhoneNumberConfig;
  };
  queryId?: string;
  listId?: string;
  dontOpenInNewTab: boolean;
  promotionColorScheme?: string;
}

export interface renderResultProps {
  results: Provider[];
  searchState: SearchStates;
  itemsPerRow: number;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  requestInfoButtonColorScheme?: string;
  learnMoreButtonColorScheme?: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  boxShadow?: string;
  tileBorder?: string;
  tileBorderColor?: string;
  tileBorderColorRange?: string;
  fullWidthTile?: boolean;
  displayCompareOption?: boolean;
  displayBadges?: boolean;
  showPrice?: boolean;
  genericBlock1?: EditableAreaType;
  genericBlock2?: EditableAreaType;
  metadata?: Metadata;
  currentPage?: number;
  getProviderDetailsPath?: (provider: Provider) => string;
  getProviderDescription?: (provider: Provider) => string;
  showMoreLimit?: number;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  readMoreButton?: string;
  inquiryId?: string;
  displayProviderPhoneNumber?: boolean;
  providerPhoneNumberSource?: {
    field: string;
    providerPhoneNumber?: string;
  };
  careType?: string;
  filterAndSort?: {
    phoneNumber?: PhoneNumberConfig;
  };
  queryId?: string;
  listId?: string;
  dontOpenInNewTab: boolean;
  promotionColorScheme?: string;
}
export interface SearchResultProps extends LinkBoxProps {
  id?: string;
  legacyId?: string;
  images: Array<string>;
  title: string;
  address: string;
  reviewCount: number;
  averageRating: number;
  price: number;
  showPrice?: boolean;
  element?: As;
  path: string;
  ratingStarsColor?: string;
  ratingStarsColorRange?: string;
  providerTitleColor?: string;
  providerTitleColorRange?: string;
  boxShadow?: string;
  border?: string;
  borderColor?: string;
  borderColorRange?: string;
  caringStars?: Award[];
  displayBadges?: boolean;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  requestInfoButtonText?: string;
  readMoreButton?: string;
  modalId?: string;
  requestInfoButtonColorScheme?: string;
  learnMoreButtonColorScheme?: string;
  showVerifiedBadge?: boolean;
  queryId?: string;
  listId?: string;
  description?: string;
  isChecked?: boolean;
  displayCompareOption?: boolean;
  handleCompare?: (id: string) => void;
  dontOpenInNewTab: boolean;
}

export interface SearchResultFullWidthProps extends SearchResultProps {
  id: string;
  legacyId: string;
  description: string;
  caringStars?: Award[];
  isCaringStar?: boolean;
  displayBadges?: boolean;
  displayLearnMoreButton?: boolean;
  learnMoreButtonText?: string;
  displayRequestInfoButton?: boolean;
  modalId?: string;
  requestInfoButtonText?: string;
  readMoreButton?: string;
  isHidden?: boolean;
  requestInfoButtonColorScheme: string;
  learnMoreButtonColorScheme: string;
  ratingStarsColor: string;
  ratingStarsColorRange: string;
  providerTitleColor: string;
  providerTitleColorRange: string;
  boxShadow: string;
  border: string;
  borderColor: string;
  borderColorRange: string;
  displayProviderPhoneNumber: boolean;
  providerPhoneNumberSource?: {
    field: string;
    providerPhoneNumber?: string;
  };
  phoneNumber?: string;
  promotions?: Array<Promotion>;
  hasActivePromotion?: boolean;
  careTypes?: string[];
  legacyResourceId?: string;
  amenityIds?: number[];
  lastReviewSnippet?: string;
  amenitiesLegacy?: LegacyAmenities;
  queryId?: string;
  listId?: string;
  amenities?: Amenities | LegacyAmenities;
  isChecked?: boolean;
  displayCompareOption?: boolean;
  handleCompare: (id: string) => void;
  promotionColorScheme?: string;
  isIndexed?: boolean;
  isFirstProvider?: boolean;
  shouldDisplayCareTypes?: boolean;
  services?: ProviderService[];
  shouldHideDescription?: boolean;
  shouldUseContainmentContext?: boolean;
}

export interface Promotion {
  startsAt?: string | null;
  endsAt?: string | null;
  externalPromotionText?: string;
  visibleOnlyToFa: boolean;
}

interface ServiceCost {
  currency: string;
  startingPriceCents: number;
}
export interface ServiceInterface {
  costs: ServiceCost | [];
  category: {
    name: string;
  };
  contract: { type: string; isSuppressed: boolean };
  legacyResourceId?: string;
  isClaimed: boolean;
}
