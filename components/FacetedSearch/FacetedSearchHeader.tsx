'use client';

import { Card, CardHeader } from '@chakra-ui/card';
import { VStack } from '@chakra-ui/layout';
import { DISPLAY_MODE } from '@components/FacetedSearch/constants';
import { useFacetedSearchResult } from '@components/FacetedSearch/FacetedSearchContext';
import { navigateToQuery } from '@components/FacetedSearch/navigation';
import SearchBar from '@components/FacetedSearch/SearchBar';
import Heading from '@components/Heading';
import { SEARCH_PARAM } from '@constants/search-params';
import useSearch from '@hooks/useSearch';
import { CareType } from '@utils/faceted-search';
import { useRouter } from 'next/router';

import { Domains } from '~/types/Domains';

const FacetedSearchHeader = (props) => {
  const { isFetching, data } = useFacetedSearchResult();
  const { domain, displayMode } = props;
  const router = useRouter();
  const { buildSearchUrl } = useSearch();

  return (
    <Card pb={0} backgroundColor="transparent">
      <CardHeader>
        <VStack alignItems="left" spacing="5">
          {props.title && (
            <Heading
              as={props.headingElement}
              size={{ base: props.titleSize, md: props.desktopTitleSize }}
              textAlign={props.titleAlignment || 'left'}
              title={props.title || ''}
              withContainer={false}
            />
          )}
          {props?.displaySearchBar && (
            <SearchBar
              amenityCategories={data?.amenityCategories ?? []}
              careTypes={props?.data?.careTypes}
              groupSearchCareTypeOptions={props.groupSearchCareTypeOptions}
              displayCareTypeFilter={props.displayCareTypeFilter}
              displayLocationFilter={props.displayLocationInput}
              displayToggleMap={props.displayToggleMap}
              displayTotal={props.displayTotal}
              facetedSearch={props.facetedSearch}
              hideSearchButton={props.hideSearchButton}
              isLoading={isFetching}
              maxWidth={props.maxWidth}
              readOnlyLocationInput={props.readOnlyLocationInput}
              readOnlyLocationInputPlaceholder={
                props.readOnlyLocationInputPlaceholder
              }
              enablePredictiveSearch={props.enablePredictiveSearch}
              searchBarBgColor={props.searchBarBgColor}
              searchBarBgColorRange={props.searchBarBgColorRange}
              searchBarButtonColorScheme={props.searchBarButtonColorScheme}
              searchBarId={props.metadata?.['@id']}
              totalResults={data?.resultCount || 0}
              careType={props.careType}
              city={props.city}
              state={props.state}
              preFillWithPageValues={props.preFillWithPageValues}
              latitude={props.latitude}
              longitude={props.longitude}
              onSubmit={(formData) => {
                const careType = formData.careType ?? CareType.AssistedLiving;
                const keyword = formData.keyword ?? '';

                if (
                  domain === Domains.CaringDomains.LIVE &&
                  displayMode === DISPLAY_MODE.LIST
                ) {
                  const searchPageUrl = buildSearchUrl({ careType, keyword });
                  location.assign(searchPageUrl);
                } else {
                  const searchParams = new URLSearchParams({
                    [SEARCH_PARAM.CARE_TYPE]: careType,
                    [SEARCH_PARAM.KEYWORD]: keyword
                  });
                  navigateToQuery(router, searchParams);
                }
              }}
            />
          )}
        </VStack>
      </CardHeader>
    </Card>
  );
};

export default FacetedSearchHeader;
