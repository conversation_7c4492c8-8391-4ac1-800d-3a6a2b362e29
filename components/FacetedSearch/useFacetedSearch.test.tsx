import * as api from '@services/faceted-search/api';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';

import { useSessionContext } from '~/contexts/SessionContext';
import { CaringDomains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import { DISPLAY_MODE, SearchStates } from './constants';
import useFacetedSearch, { fetchProvidersByFacets } from './useFacetedSearch';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

jest.mock('~/contexts/SessionContext', () => ({
  useSessionContext: jest.fn()
}));

jest.mock('@services/faceted-search/api', () => ({
  fetchProvidersWithEnhancedSearch: jest.fn(),
  fetchProvidersWithFacetedSearch: jest.fn(),
  fetchProvidersWithFacetedSearchV2: jest.fn()
}));

jest.mock('@services/faceted-search/api', () => ({
  fetchProvidersWithEnhancedSearch: jest.fn(),
  fetchProvidersWithFacetedSearch: jest.fn(),
  fetchProvidersWithFacetedSearchV2: jest.fn().mockResolvedValue({
    totalItems: 1,
    totalNearbyItems: 0,
    totalPages: 1,
    totalRegionItems: 0,
    results: [],
    listId: 'list-1',
    queryId: 'query-1'
  })
}));
jest.mock('@components/FacetedSearch/utils', () => ({
  getAmenityCategoriesFromResponse: jest.fn().mockReturnValue([]),
  isValidCareTypeForFacetedSearch: jest.fn().mockReturnValue(true)
}));
jest.mock('@components/Search/EnhancedSearch/ServerComponent', () => ({
  parseResultAmenities: jest.fn((_, results) => results),
  parseResultContracts: jest.fn((_, results) => results)
}));
jest.mock('./facetedSearchTracking', () => ({
  trackProviderListFiltered: jest.fn()
}));

describe('fetchProvidersByFacets', () => {
  it('calls fetchProvidersWithFacetedSearchV2 when source is ALGOLIA and displayMode is search', async () => {
    const {
      fetchProvidersWithFacetedSearchV2
    } = require('@services/faceted-search/api');
    const searchQuery = {
      accessibility: [],
      awards: [],
      careType: 'assisted-living',
      dining: [],
      distanceInMiles: 30,
      healthServices: [],
      keyword: '',
      latLng: '33.4484,-112.0740',
      languages: [],
      lifestyle: [],
      matchAllFilters: true,
      ongoingPromotion: [],
      otherAmenities: [],
      personalCare: [],
      priceRange: [1, 4] as [number, number],
      providersWith: [],
      reviews: [],
      roomAmenities: [],
      roomType: [],
      sortBy: 'distance' as
        | 'distance'
        | 'best-activities'
        | 'best-meals-dining'
        | 'best-rated'
        | 'best-staff'
        | 'most-reviews',
      staffQualifications: [],
      verified: [],
      page: 0 // Added required 'page' property
    };
    const hasActiveFilters = false;
    const page = 0;
    const session = { sessionId: 'test-session', page };
    const componentProps = {
      careType: 'independent-living',
      source: Source.ALGOLIA,
      resultsPerPage: 10,
      displayMode: DISPLAY_MODE.SEARCH,
      city: 'Phoenix',
      county: 'Maricopa',
      distanceFilterEnabled: true,
      domain: CaringDomains.LIVE,
      geoType: 'city',
      regionId: '123',
      state: 'AZ',
      latitude: '33.4484',
      longitude: '-112.0740',
      shouldShowOnlyIndexedProviders: false
    };

    const apiVersion = undefined;
    const featureOverride = undefined;
    const geoUrl = undefined;

    await fetchProvidersByFacets({
      searchQuery,
      hasActiveFilters,
      page,
      session,
      componentProps,
      apiVersion,
      featureOverride,
      geoUrl
    });

    expect(fetchProvidersWithFacetedSearchV2).toHaveBeenCalledWith({
      searchParams: { ...searchQuery, page },
      componentProps
    });
  });
});

describe('useFacetedSearch', () => {
  const mockSession = { sessionId: 'test-session' };
  const mockRouter = { query: { geo: '1' } };
  const mockComponentProps = {
    careType: 'assisted-living',
    source: Source.ALGOLIA,
    resultsPerPage: 10,
    displayMode: DISPLAY_MODE.SEARCH,
    city: 'Phoenix',
    county: 'Maricopa',
    distanceFilterEnabled: true,
    domain: CaringDomains.LIVE,
    geoType: 'city',
    regionId: '123',
    state: 'AZ',
    latitude: '33.4484',
    longitude: '-112.0740',
    shouldShowOnlyIndexedProviders: false
  };

  const mockSearchQuery = {
    accessibility: [],
    awards: [],
    careType: 'assisted-living',
    dining: [],
    distanceInMiles: 30,
    healthServices: [],
    keyword: '',
    latLng: '33.4484,-112.0740',
    languages: [],
    lifestyle: [],
    matchAllFilters: true,
    ongoingPromotion: [],
    otherAmenities: [],
    personalCare: [],
    priceRange: [1, 4] as [number, number],
    providersWith: [],
    reviews: [],
    roomAmenities: [],
    roomType: [],
    sortBy: 'distance' as
      | 'distance'
      | 'best-activities'
      | 'best-meals-dining'
      | 'best-rated'
      | 'best-staff'
      | 'most-reviews',
    staffQualifications: [],
    verified: [],
    page: 0 // Added required 'page' property
  };

  const mockResponse = {
    amenityCategories: [],
    listId: 'test-list-id',
    nearbyCount: 5,
    totalPages: 2,
    queryId: 'test-query-id',
    regionCount: 10,
    resultCount: 15,
    results: [
      {
        id: '1',
        name: 'Test Provider',
        averageRating: 4.5,
        description: 'A test provider description',
        phoneNumber: '************',
        reviewCount: 10,
        slug: 'test-provider'
      }
    ],
    resultsPerPage: 10,
    searchState: SearchStates.INPUT_WITH_RESULTS
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSessionContext as jest.Mock).mockReturnValue(mockSession);
    (api.fetchProvidersWithFacetedSearchV2 as jest.Mock).mockResolvedValue({
      totalItems: 15,
      totalNearbyItems: 5,
      totalPages: 2,
      totalRegionItems: 10,
      results: [{ id: '1', name: 'Test Provider' }],
      listId: 'test-list-id',
      queryId: 'test-query-id'
    });
  });

  const createWrapper = () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false
        }
      }
    });
    const Wrapper = ({ children }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    );
    Wrapper.displayName = 'QueryClientProviderWrapper';
    return Wrapper;
  };

  it('should use initialData when provided', async () => {
    const { result } = renderHook(
      () =>
        useFacetedSearch({
          searchQuery: mockSearchQuery,
          initialData: mockResponse,
          componentProps: mockComponentProps,
          geoUrl: '/arizona/phoenix'
        }),
      { wrapper: createWrapper() }
    );

    expect(result.current.data?.pages[0]).toEqual(mockResponse);
  });

  it('should handle empty results correctly', async () => {
    (api.fetchProvidersWithFacetedSearchV2 as jest.Mock).mockResolvedValue({
      totalItems: 0,
      totalNearbyItems: 0,
      totalPages: 0,
      totalRegionItems: 0,
      results: [],
      listId: 'test-list-id',
      queryId: 'test-query-id'
    });

    const { result } = renderHook(
      () =>
        useFacetedSearch({
          searchQuery: mockSearchQuery,
          componentProps: mockComponentProps,
          geoUrl: '/arizona/phoenix'
        }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.data?.pages[0].searchState).toBe(
        SearchStates.INPUT_WITH_NO_RESULTS
      );
    });
  });
});
