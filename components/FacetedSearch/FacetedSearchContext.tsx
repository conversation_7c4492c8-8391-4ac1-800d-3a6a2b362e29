import { CareType } from '@services/magnolia/getDataByDomain';
import { AmenityCategoryNode } from '@utils/faceted-search';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import { useRouter } from 'next/router';
import { createContext, useContext, useMemo, useState } from 'react';

import { SearchStates } from './constants';
import {
  navigateToInitialSearch,
  navigateToSearchPage,
  navigateToSearchQuery
} from './navigation';
import {
  FacetedSearchParsedParams,
  FILTER_KEY_MAP,
  getDefaultFacetedSearchParams,
  QUERY_KEY_MAP
} from './search-params';
import {
  FacetedSearchParsedProps,
  FacetedSearchResponse,
  FacetedSearchResult,
  QueryKeyPath,
  QueryKeyValue
} from './types';
import useFacetedSearch from './useFacetedSearch';
import { getCareTypeId, scrollToElement } from './utils';

const FacetedSearchActionsContext = createContext<FacetedSearchActions | null>(
  null
);
const FacetedSearchQueryContext =
  createContext<FacetedSearchParsedParams | null>(null);
const FacetedSearchResultContext = createContext<FacetedSearchResult | null>(
  null
);

export type FacetedSearchActions = {
  showMoreResults: () => void;
  changePage: (page: number) => void;
  resetQuery: () => void;
  updateQuery: <K extends QueryKeyPath>(query: {
    key: K;
    value: QueryKeyValue<K>;
  }) => void;
};

interface FacetedSearchProviderProps {
  children: React.ReactNode;
  initialData: FacetedSearchResponse | undefined;
  query: FacetedSearchParsedParams;
  careTypes: CareType[];
  componentProps: FacetedSearchParsedProps;
}

export const FacetedSearchProvider: React.FC<FacetedSearchProviderProps> = ({
  children,
  initialData,
  query,
  careTypes,
  componentProps
}) => {
  const [initialQueryFromServer] = useState(query);
  const router = useRouter();
  const searchQuery = cloneDeep(query);

  if (searchQuery?.careType) {
    searchQuery.careType = getCareTypeId(searchQuery.careType, careTypes);
  }

  const initialDataForCurrentQuery = isEqual(initialQueryFromServer, query)
    ? initialData
    : undefined;

  const {
    data,
    isError,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useFacetedSearch({
    searchQuery,
    initialData: initialDataForCurrentQuery,
    componentProps,
    geoUrl: router.asPath
  });

  const allPages = data?.pages ?? [];
  const allResults = allPages.flatMap((page) => page.results);
  const latestPage = allPages[allPages.length - 1] ?? null;
  const queryId = latestPage?.queryId ?? '';
  const listId = latestPage?.listId ?? '';

  const actions: FacetedSearchActions = useMemo(() => {
    const resetQuery = () => {
      navigateToInitialSearch(router);
    };

    const updateQuery = ({ key, value }) => {
      navigateToSearchQuery(router, key, value);
    };

    const changePage = (page: number) => {
      scrollToElement(document.getElementById('faceted-search-tracker'));
      navigateToSearchPage(router, page);
    };

    const showMoreResults = () => {
      fetchNextPage();
    };

    return { changePage, resetQuery, showMoreResults, updateQuery };
  }, [router, fetchNextPage]);

  if (isError) {
    return null;
  }

  return (
    <FacetedSearchActionsContext.Provider value={actions}>
      <FacetedSearchQueryContext.Provider value={query}>
        <FacetedSearchResultContext.Provider
          value={{
            data: {
              results: allResults,
              amenityCategories: data?.pages?.[0]
                ?.amenityCategories as AmenityCategoryNode[],
              queryId,
              searchState: data?.pages?.[0]?.searchState as SearchStates,
              listId,
              resultCount: data?.pages?.[0]?.resultCount ?? 0
            } as any,
            isFetching: isFetching && !isFetchingNextPage,
            isFetchingNextPage,
            hasNextPage
          }}
        >
          {children}
        </FacetedSearchResultContext.Provider>
      </FacetedSearchQueryContext.Provider>
    </FacetedSearchActionsContext.Provider>
  );
};

export const useFacetedSearchActions = () => {
  const actions = useContext(FacetedSearchActionsContext);

  if (!actions) {
    throw new Error(
      'useFacetedSearchActions must be used within a FacetedSearchProvider'
    );
  }

  return actions;
};

export const useFacetedSearchResult = () => {
  const result = useContext(FacetedSearchResultContext);

  if (!result) {
    throw new Error(
      'useFacetedSearchResult must be used within a FacetedSearchProvider'
    );
  }

  return result;
};

export const useFacetedSearchQuery = () => {
  const query = useContext(FacetedSearchQueryContext);

  if (!query) {
    throw new Error(
      'useFacetedSearchQuery must be used within a FacetedSearchProvider'
    );
  }

  return query;
};

// This should be how this is declared in the other file, why is it a function.
const defaultParams = getDefaultFacetedSearchParams();

// This is possibly one of multiple near-identical functions, with very slight degrees of separation...
export const useFilterAndSortStats = () => {
  const router = useRouter();

  const filterAndSortStats = useMemo(() => {
    let isFilteringMap: Record<string, boolean> = {};
    let isSorting = false;
    let isFiltering = false;

    if (!router.query) {
      return { isFilteringMap, isFiltering, isSorting };
    }

    const queryStringKeys = Object.values(FILTER_KEY_MAP);
    for (const key of queryStringKeys) {
      const isFilteringForKey = Boolean(router.query[key]);
      if (isFilteringForKey) {
        isFiltering = true;
      }
      isFilteringMap[key] = isFilteringForKey;
    }

    // special snowflake query keys, annoying
    const maybeDistanceInMiles = router.query[QUERY_KEY_MAP.distanceInMiles];
    if (maybeDistanceInMiles) {
      const defaultDistanceInMiles = `${defaultParams.distanceInMiles}`;
      if (maybeDistanceInMiles !== defaultDistanceInMiles) {
        isFiltering = true;
        isFilteringMap[QUERY_KEY_MAP.distanceInMiles] = true;
      }
    }

    const maybePriceRange = router.query[QUERY_KEY_MAP.priceRange];
    if (maybePriceRange) {
      const defaultPriceRange = `${defaultParams.priceRange[0]},${defaultParams.priceRange[1]}`;
      if (maybePriceRange !== defaultPriceRange) {
        isFiltering = true;
        isFilteringMap[QUERY_KEY_MAP.priceRange] = true;
      }
    }

    const maybeSort = router.query[QUERY_KEY_MAP.sortBy];
    if (maybeSort) {
      const defaultSort = defaultParams.sortBy;
      if (maybeSort !== defaultSort) {
        isSorting = true;
      }
    }

    return { isFiltering, isFilteringMap, isSorting };
  }, [router.query]);

  return filterAndSortStats;
};
