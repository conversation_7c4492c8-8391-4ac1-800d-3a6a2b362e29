import {
  AMENITY_CATEGORY,
  DISPLAY_MODE
} from '@components/FacetedSearch/constants';
import { getDefaultComponentConfig } from '@components/Search/EnhancedSearch/parser';
import {
  FacetedSearchRequestQuery,
  GetProvidersResponse
} from '@services/modular-monolith/types/search.type';
import { AMENITY_CATEGORIES, CareType } from '@utils/faceted-search';

import { Domain, Domains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import { getDefaultFacetedSearchParams } from './search-params';
import {
  AmenityCategoryNodeWithCount,
  fetchAmenityCategoryCounts,
  getAmenitiesByCategory,
  getAmenityCategoriesFromResponse,
  getMaxAmenityCount,
  getSourceForFacetedSearch
} from './utils';

jest.mock('@services/modular-monolith/client', () => ({
  ModularMonolithClient: jest.fn().mockImplementation(() => ({
    searchProvidersByFacets: jest.fn().mockImplementation(() =>
      Promise.resolve({
        inRegionFacets: {
          'amenities.amenityType.name': {
            Pets: 36,
            'Occupational Therapy/Rehabilitation': 0,
            'Physical Therapy/Rehabilitation': 0
          }
        }
      })
    )
  }))
}));

const defaultSearchParams = getDefaultFacetedSearchParams();
const defaultAmenityCategoryChips =
  getDefaultComponentConfig().amenityCategoryChips;

describe('getSourceForFacetedSearch', () => {
  it('should return ALGOLIA if domain is not Caring', () => {
    const params = {
      displayMode: DISPLAY_MODE.LIST,
      domain: Domains.SeniorHomesDomains.LIVE,
      amenityCategoryChips: defaultAmenityCategoryChips,
      searchParams: defaultSearchParams
    };

    const result = getSourceForFacetedSearch(params);

    expect(result).toBe(Source.ALGOLIA);
  });

  it('should return ALGOLIA if displayMode is SEARCH', () => {
    const params = {
      displayMode: DISPLAY_MODE.SEARCH,
      domain: Domains.CaringDomains.LIVE,
      amenityCategoryChips: defaultAmenityCategoryChips,
      searchParams: defaultSearchParams
    };

    const result = getSourceForFacetedSearch(params);

    expect(result).toBe(Source.ALGOLIA);
  });

  it('should return ALGOLIA if is an amenity category page', () => {
    const params = {
      displayMode: DISPLAY_MODE.LIST,
      domain: Domains.CaringDomains.LIVE,
      amenityCategoryChips: {
        ...defaultAmenityCategoryChips,
        amenityCategory: AMENITY_CATEGORY.GOLF,
        visible: true
      },
      searchParams: defaultSearchParams
    };

    const result = getSourceForFacetedSearch(params);

    expect(result).toBe(Source.ALGOLIA);
  });

  it('should return ALGOLIA if searchParams have changed', () => {
    const params = {
      displayMode: DISPLAY_MODE.LIST,
      domain: Domains.CaringDomains.LIVE,
      amenityCategoryChips: defaultAmenityCategoryChips,
      searchParams: { ...defaultSearchParams, keyword: 'new keyword' }
    };

    const result = getSourceForFacetedSearch(params);

    expect(result).toBe(Source.ALGOLIA);
  });

  it('should return LEGACY if none of the conditions for ALGOLIA are met', () => {
    const params = {
      displayMode: DISPLAY_MODE.LIST,
      domain: Domains.CaringDomains.LIVE,
      amenityCategoryChips: defaultAmenityCategoryChips,
      searchParams: defaultSearchParams
    };

    const result = getSourceForFacetedSearch(params);

    expect(result).toBe(Source.LEGACY);
  });
});

describe('FacetedSearch utils', () => {
  describe('getAmenitiesByCategory', () => {
    it('should return amenities for a valid category', () => {
      const amenities = getAmenitiesByCategory(AMENITY_CATEGORY.GOLF);
      expect(amenities).toEqual(
        AMENITY_CATEGORIES[AMENITY_CATEGORY.GOLF].amenities
      );
    });

    it('should return an empty array for an invalid category', () => {
      const amenities = getAmenitiesByCategory('invalid-category');
      expect(amenities).toEqual([]);
    });
  });

  describe('getMaxAmenityCount', () => {
    it('should return 0 if amenities array is empty', () => {
      const amenities: string[] = [];
      const amenitiesCount: Record<string, number> = {};
      const count = getMaxAmenityCount(amenities, amenitiesCount);
      expect(count).toEqual(0);
    });

    it('should return 0 if amenitiesCount object is empty', () => {
      const amenities: string[] = ['Golf'];
      const amenitiesCount: Record<string, number> = {};
      const count = getMaxAmenityCount(amenities, amenitiesCount);
      expect(count).toEqual(0);
    });

    it('should return the maximum count among all amenities', () => {
      const amenities: string[] = ['Golf', 'Meal Preparation', 'Pets'];
      const amenitiesCount: Record<string, number> = {
        Golf: 5,
        'Meal Preparation': 10,
        Pets: 7
      };
      const count = getMaxAmenityCount(amenities, amenitiesCount);
      expect(count).toEqual(10);
    });

    it('should ignore amenities that are not in the amenitiesCount object', () => {
      const amenities: string[] = ['Golf', 'Meal Preparation', 'Pets'];
      const amenitiesCount: Record<string, number> = {
        Golf: 5,
        'Meal Preparation': 10
      };
      const count = getMaxAmenityCount(amenities, amenitiesCount);
      expect(count).toEqual(10);
    });
  });

  describe('fetchAmenityCategoryCounts', () => {
    it('should return amenity categories with counts greater than 0', async () => {
      const careType = CareType.AssistedLiving;
      const domain: Domain = Domains.CaringDomains.LIVE;
      const searchParams: FacetedSearchRequestQuery = {
        criteria: {
          accessibility: [],
          amenityCategory: [],
          verified: [],
          awards: [],
          careType,
          dining: [],
          distanceInMiles: 2,
          domain,
          healthServices: [],
          keyword: '',
          languages: [],
          latitude: '0',
          lifestyle: [],
          longitude: '0',
          ongoingPromotion: [],
          otherAmenities: [],
          personalCare: [],
          priceRange: [1, 4],
          providersWith: [],
          reviews: [],
          roomAmenities: [],
          roomType: [],
          staffQualifications: []
        },
        sortBy: 'distance',
        hitsPerPage: 20,
        matchAllFilters: false,
        shouldShowOnlyIndexedProviders: false,
        page: 0
      };
      const amenityCategories: AmenityCategoryNodeWithCount[] =
        await fetchAmenityCategoryCounts(searchParams);
      expect(amenityCategories).toEqual([
        {
          ...AMENITY_CATEGORIES[AMENITY_CATEGORY.PETS_ALLOWED],
          count: 36
        }
      ]);
    });

    it('should return an empty array if careType is not valid', async () => {
      const careType = 'continuing-care-retirement-communities';
      const domain: Domain = Domains.CaringDomains.LIVE;
      const searchParams: FacetedSearchRequestQuery = {
        criteria: {
          accessibility: [],
          amenityCategory: [],
          verified: [],
          awards: [],
          careType,
          dining: [],
          distanceInMiles: 2,
          domain,
          healthServices: [],
          keyword: '',
          languages: [],
          latitude: '0',
          lifestyle: [],
          longitude: '0',
          ongoingPromotion: [],
          otherAmenities: [],
          personalCare: [],
          priceRange: [1, 4],
          providersWith: [],
          reviews: [],
          roomAmenities: [],
          roomType: [],
          staffQualifications: []
        },
        sortBy: 'distance',
        hitsPerPage: 20,
        matchAllFilters: false,
        shouldShowOnlyIndexedProviders: false,
        page: 0
      };
      const amenityCategories: AmenityCategoryNodeWithCount[] =
        await fetchAmenityCategoryCounts(searchParams);
      expect(amenityCategories).toEqual([]);
    });
  });

  describe('getAmenityCategoriesFromResponse', () => {
    it('should return amenity categories that belong to the care type with counts greater than 0', () => {
      const response: GetProvidersResponse = {
        inRegionFacets: {
          'amenities.amenityType.name': {
            Golf: 5,
            'Meal Preparation': 10,
            Pets: 7,
            'Pool / Hot Tub': 0
          }
        }
      } as unknown as GetProvidersResponse;
      const careType = CareType.AssistedLiving;
      const amenityCategories: AmenityCategoryNodeWithCount[] =
        getAmenityCategoriesFromResponse(response, careType);
      expect(amenityCategories).toEqual([
        {
          amenities: ['Pets'],
          count: 7,
          name: 'Pets Allowed',
          value: 'pets-allowed'
        }
      ]);
    });

    it('should return an empty array if there are no amenities with count greater than 0', () => {
      const response: GetProvidersResponse = {
        inRegionFacets: {
          'amenities.amenityType.name': {
            Golf: 0,
            'Meal Preparation': 0,
            Pets: 0
          }
        }
      } as unknown as GetProvidersResponse;
      const careType = CareType.AssistedLiving;
      const amenityCategories: AmenityCategoryNodeWithCount[] =
        getAmenityCategoriesFromResponse(response, careType);
      expect(amenityCategories).toEqual([]);
    });

    it('should ignore amenities that do not belong to the care type', () => {
      const response: GetProvidersResponse = {
        inRegionFacets: {
          'amenities.amenityType.name': {
            Golf: 5,
            'Meal Preparation': 10,
            Pets: 7
          }
        }
      } as unknown as GetProvidersResponse;
      const careType = CareType.NursingHomes;
      const amenityCategories: AmenityCategoryNodeWithCount[] =
        getAmenityCategoriesFromResponse(response, careType);
      expect(amenityCategories).toEqual([]);
    });
  });
});
