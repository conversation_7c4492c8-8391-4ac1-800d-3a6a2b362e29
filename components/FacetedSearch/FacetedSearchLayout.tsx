import { Card, CardBody } from '@chakra-ui/card';
import { Box } from '@chakra-ui/layout';
import LAYOUT from '@components/Layouts/layoutConstants';
import Container from '@components/LayoutStructure/Container';

interface FacetedSearchLayoutProps {
  header?: React.ReactNode;
  main?: React.ReactNode;
  sidebar?: React.ReactNode;
}

const FacetedSearchLayout: React.FC<FacetedSearchLayoutProps> = ({
  header,
  main,
  sidebar
}) => {
  return (
    <Container px={{ base: 0, lg: LAYOUT.CONTAINER_HORIZONTAL_PADDING }}>
      <Card p="0" mx="auto">
        <CardBody
          display="grid"
          gridTemplateAreas={{
            base: `
                "header"
                "sidebar"
                "main"
              `,
            lg: `
                "sidebar header"
                "sidebar main"
              `
          }}
          gridTemplateColumns={{
            // minmax() is used to prevent the sidebar from shrinking
            base: 'minmax(0,1fr) ',
            lg: 'minmax(0,1fr) minmax(0,2fr)'
          }}
          gridTemplateRows={{
            base: 'minmax(0, auto)',
            lg: 'minmax(0, auto) minmax(0,1fr)'
          }}
          columnGap={5}
          rowGap={8}
        >
          <Box gridArea="header">{header}</Box>
          <Box gridArea="sidebar">{sidebar}</Box>
          <Box gridArea="main">{main}</Box>
        </CardBody>
      </Card>
    </Container>
  );
};

export default FacetedSearchLayout;
