import { ChakraProvider } from '@chakra-ui/react';
import { SEARCH_OPTION } from '@components/FacetedSearch/constants';
import { FacetedSearchProvider } from '@components/FacetedSearch/FacetedSearchContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';

import { CaringDomains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import FilterList from './FilterList';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0
    }
  }
});

const defaultProps = {
  searchOptions: [SEARCH_OPTION.VERIFIED, SEARCH_OPTION.AWARDS],
  filterTooltips: {
    verifiedInfo: 'Custom verified tooltip',
    awardsInfo: 'Custom awards tooltip'
  }
};

const customTooltips = {
  verifiedInfo: 'Custom verified tooltip',
  awardsInfo: 'Custom awards tooltip',
  priceInfo: 'Custom price tooltip'
};

describe('FilterList', () => {
  const renderFilterList = (props = {}) => {
    return render(
      <ChakraProvider>
        <QueryClientProvider client={queryClient}>
          <FacetedSearchProvider
            initialData={undefined}
            query={{
              accessibility: [],
              awards: [],
              dining: [],
              distanceInMiles: 0,
              healthServices: [],
              languages: [],
              lifestyle: [],
              matchAllFilters: false,
              ongoingPromotion: [],
              otherAmenities: [],
              personalCare: [],
              priceRange: [0, 0],
              providersWith: [],
              reviews: [],
              roomAmenities: [],
              roomType: [],
              sortBy: 'best-rated',
              staffQualifications: [],
              verified: [],
              keyword: '',
              page: 0,
              careType: '',
              latLng: ''
            }}
            careTypes={[]}
            componentProps={{
              amenityCategory: '',
              careType: '',
              city: '',
              county: '',
              displayMode: 'list',
              distanceFilterEnabled: false,
              domain: CaringDomains.LIVE,
              latitude: '0',
              longitude: '0',
              resultsPerPage: 10,
              source: Source.ALGOLIA,
              state: ''
            }}
          >
            <FilterList {...defaultProps} {...props} />
          </FacetedSearchProvider>
        </QueryClientProvider>
      </ChakraProvider>
    );
  };

  beforeEach(() => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      query: {},
      pathname: '/',
      push: jest.fn(),
      replace: jest.fn(),
      asPath: '/',
      events: {
        on: jest.fn(),
        off: jest.fn()
      }
    }));
  });

  describe('Filter Sections', () => {
    it('renders verified partners filter when included in searchOptions', () => {
      renderFilterList();
      expect(screen.getByText('Verified Partners')).toBeInTheDocument();
    });

    it('renders caring awards filter when included in searchOptions', () => {
      renderFilterList();
      expect(screen.getByText('Caring Awards')).toBeInTheDocument();
    });

    it('renders match all filters checkbox when option is included', () => {
      renderFilterList({
        searchOptions: [...defaultProps.searchOptions, 'matchAllFilters']
      });
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
    });

    it('does not render match all filters when option is not included', () => {
      renderFilterList();
      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
    });
  });

  describe('Filter Tooltips', () => {
    it('renders info icon for verified partners', () => {
      renderFilterList();

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      expect(verifiedButton).toBeInTheDocument();

      const infoIcon = verifiedButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders info icon for awards', () => {
      renderFilterList();

      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      expect(awardsButton).toBeInTheDocument();

      const infoIcon = awardsButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders info icons when custom tooltips are provided', () => {
      renderFilterList({ filterTooltips: customTooltips });

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      expect(verifiedButton).toBeInTheDocument();

      const verifiedInfoIcon = verifiedButton.querySelector(
        '[role="presentation"]'
      );
      expect(verifiedInfoIcon).toBeInTheDocument();

      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      expect(awardsButton).toBeInTheDocument();

      const awardsInfoIcon = awardsButton.querySelector(
        '[role="presentation"]'
      );
      expect(awardsInfoIcon).toBeInTheDocument();
    });

    it('renders price tooltip with dynamic range values', () => {
      renderFilterList({
        searchOptions: [...defaultProps.searchOptions, SEARCH_OPTION.PROMOTIONS]
      });

      const priceButton = screen.getByRole('button', { name: /price/i });
      expect(priceButton).toBeInTheDocument();

      const infoIcon = priceButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders price section with custom tooltip', () => {
      renderFilterList({
        searchOptions: [
          ...defaultProps.searchOptions,
          SEARCH_OPTION.PROMOTIONS
        ],
        filterTooltips: customTooltips
      });

      const priceButton = screen.getByRole('button', { name: /price/i });
      expect(priceButton).toBeInTheDocument();

      const infoIcon = priceButton.querySelector('[role="presentation"]');
      expect(infoIcon).toBeInTheDocument();
    });

    it('renders correct number of info icons based on searchOptions', () => {
      renderFilterList({
        searchOptions: [SEARCH_OPTION.VERIFIED],
        filterTooltips: customTooltips
      });

      const infoIcons = screen.queryAllByRole('presentation');
      expect(infoIcons).toHaveLength(1);
    });

    it('does not render price section when not included in searchOptions', () => {
      renderFilterList({
        searchOptions: [SEARCH_OPTION.VERIFIED],
        filterTooltips: customTooltips
      });

      const priceButton = screen.queryByRole('button', { name: /price/i });
      expect(priceButton).not.toBeInTheDocument();
    });

    it('renders all sections when all options are provided', () => {
      renderFilterList({
        searchOptions: [
          SEARCH_OPTION.VERIFIED,
          SEARCH_OPTION.AWARDS,
          SEARCH_OPTION.PROMOTIONS
        ],
        filterTooltips: customTooltips
      });

      const verifiedButton = screen.getByRole('button', {
        name: /verified partners/i
      });
      const awardsButton = screen.getByRole('button', {
        name: /caring awards/i
      });
      const priceButton = screen.getByRole('button', { name: /price/i });

      expect(verifiedButton).toBeInTheDocument();
      expect(awardsButton).toBeInTheDocument();
      expect(priceButton).toBeInTheDocument();

      const infoIcons = screen.getAllByRole('presentation');
      expect(infoIcons).toHaveLength(3);
    });
  });
});
