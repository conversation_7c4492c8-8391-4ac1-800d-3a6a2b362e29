import {
  Card,
  CardBody,
  CardBodyProps,
  CardHeader,
  CardHeaderProps,
  CardProps
} from '@chakra-ui/card';
import { ComponentWithAs } from '@chakra-ui/system';

interface CompoundComponents {
  Header: typeof SidebarHeader;
  Body: typeof SidebarBody;
}

const SidebarHeader: ComponentWithAs<'div', CardHeaderProps> = ({
  children,
  ...rest
}) => {
  return (
    <CardHeader display="flex" flexDirection="column" gap={8} {...rest}>
      {children}
    </CardHeader>
  );
};

const SidebarBody: ComponentWithAs<'div', CardBodyProps> = ({
  children,
  ...rest
}) => {
  return (
    <CardBody display="flex" flexDirection="column" gap={2} {...rest}>
      {children}
    </CardBody>
  );
};

const SidebarLayout: ComponentWithAs<'div', CardProps> & CompoundComponents = ({
  children,
  ...rest
}) => {
  return (
    <Card py={{ base: 0, lg: 8 }} background="transparent" {...rest}>
      {children}
    </Card>
  );
};

SidebarLayout.Header = SidebarHeader;
SidebarLayout.Body = SidebarBody;

export default SidebarLayout;
