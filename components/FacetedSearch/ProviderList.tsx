import { Box, Grid, GridItem, VStack } from '@chakra-ui/layout';
import { SearchStates } from '@components/FacetedSearch/constants';
import { RenderResults } from '@components/FacetedSearch/utils';
import { PageContext } from '@components/LayoutStructure/Contexts';
import SearchPagination from '@components/Search/SearchPagination';
import SearchResultSkeleton from '@components/Search/SearchResultSkeleton';
import SearchSeoCarousel from '@components/Search/SearchSeoCarousel';
import ShowMore from '@components/Search/ShowMore';
import { LISTING_PAGE } from '@constants/search-params';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { useContext, useState } from 'react';

import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { PhoneNumberConfig } from '~/types/componentsConfig';
import { EditableAreaType, Metadata } from '~/types/Magnolia';

import { FacetedSearchResponse } from './types';

interface ProviderListProps {
  pagination: {
    currentPage: number;
    totalPages: number;
    setPage: (page: number) => void;
  };
  options: {
    visibleItems: number;
    filterAndSort?: {
      phoneNumber?: PhoneNumberConfig;
    };
  };
  result: FacetedSearchResponse | undefined;
  search: {
    // TODO: refactor the following props. These were copied from the Enhanced search component.
    blurCosts?: boolean;
    boxShadow?: string;
    careType: string;
    countOfTilesPerRow?: string;
    displayBadges?: boolean;
    displayLearnMoreButton?: boolean;
    displayProviderPhoneNumber: boolean;
    displayRequestInfoButton?: boolean;
    fullWidthTile?: boolean;
    genericBlock1: EditableAreaType;
    genericBlock2: EditableAreaType;
    infoButtonInquiryId?: string;
    isLoading: boolean;
    learnMoreButtonColorScheme: string;
    learnMoreButtonText?: string;
    metadata: Metadata;
    providerPhoneNumberSource?: { field: string; providerPhoneNumber?: string };
    providerTitleColor: string;
    providerTitleColorRange: string;
    ratingStarsColor: string;
    ratingStarsColorRange: string;
    readMoreButton?: string;
    requestInfoButtonColorScheme: string;
    requestInfoButtonText?: string;
    tileBorder: string;
    tileBorderColor: string;
    tileBorderColorRange: string;
    displayCompareOption?: boolean;
    dontOpenInNewTab: boolean;
    state: string;
    county?: string;
    city?: string;
    promotionColorScheme: string;
  };
}

const ProviderList: React.FC<ProviderListProps> = ({
  pagination,
  options,
  result,
  search
}) => {
  const {
    careType,
    fullWidthTile = false,
    displayBadges = false,
    readMoreButton,
    genericBlock1,
    genericBlock2,
    metadata,
    providerTitleColor,
    providerTitleColorRange,
    displayLearnMoreButton,
    learnMoreButtonText,
    displayRequestInfoButton,
    requestInfoButtonColorScheme,
    learnMoreButtonColorScheme,
    ratingStarsColor,
    ratingStarsColorRange,
    infoButtonInquiryId,
    requestInfoButtonText,
    boxShadow = 'lg',
    tileBorder = 'none',
    tileBorderColor = 'none',
    tileBorderColorRange = 'none',
    displayCompareOption = false,
    displayProviderPhoneNumber = false,
    providerPhoneNumberSource,
    countOfTilesPerRow,
    blurCosts,
    dontOpenInNewTab,
    state,
    city,
    county,
    promotionColorScheme
  } = search;
  const pageProps = useContext(PageContext);
  const inquiryFormHasSubmitted = useInquiryFormSubmitted();
  const { getProviderDetailsPath, getProviderDescription } =
    useTenantFunctions();
  const [showMoreLimit, setShowMoreLimit] = useState<number>(
    options.visibleItems
  );
  const showPrice = blurCosts ? inquiryFormHasSubmitted : true;
  const itemsPerRow = Number(countOfTilesPerRow) * 3 ?? 12;

  const handleShowMore = (showMoreLimit: number) => {
    setShowMoreLimit(showMoreLimit);
  };
  return (
    <Box>
      <SearchSeoCarousel
        results={result?.results ?? []}
        totalCount={result?.resultCount as number}
        state={state}
        city={city}
        county={county}
        careType={careType}
      />
      <VStack alignItems={'left'} spacing="5">
        {search.isLoading ? (
          <Grid
            templateColumns={
              fullWidthTile
                ? '1fr'
                : {
                    base: 'repeat(3, 1fr)',
                    sm: 'repeat(6, 1fr)',
                    md: `repeat(${itemsPerRow}, 1fr)`
                  }
            }
            gap={6}
          >
            {new Array(6).fill(0).map((value, key) => (
              <GridItem colSpan={3} key={`${key}-${value}`}>
                <SearchResultSkeleton fullWidth={fullWidthTile} />
              </GridItem>
            ))}
          </Grid>
        ) : (
          <RenderResults
            results={result?.results ?? []}
            searchState={
              result?.searchState ?? SearchStates.INPUT_WITH_NO_RESULTS
            }
            itemsPerRow={itemsPerRow}
            providerTitleColor={providerTitleColor}
            providerTitleColorRange={providerTitleColorRange}
            requestInfoButtonColorScheme={requestInfoButtonColorScheme}
            learnMoreButtonColorScheme={learnMoreButtonColorScheme}
            ratingStarsColor={ratingStarsColor}
            ratingStarsColorRange={ratingStarsColorRange}
            boxShadow={boxShadow}
            tileBorder={tileBorder}
            tileBorderColor={tileBorderColor}
            tileBorderColorRange={tileBorderColorRange}
            fullWidthTile={fullWidthTile}
            displayBadges={displayBadges}
            showPrice={showPrice}
            genericBlock1={genericBlock1}
            genericBlock2={genericBlock2}
            metadata={metadata}
            currentPage={pagination?.currentPage}
            getProviderDetailsPath={getProviderDetailsPath}
            getProviderDescription={getProviderDescription}
            showMoreLimit={pagination?.totalPages > 1 ? 0 : showMoreLimit}
            displayLearnMoreButton={displayLearnMoreButton}
            learnMoreButtonText={learnMoreButtonText}
            displayRequestInfoButton={displayRequestInfoButton}
            requestInfoButtonText={requestInfoButtonText}
            readMoreButton={readMoreButton}
            inquiryId={infoButtonInquiryId}
            displayProviderPhoneNumber={displayProviderPhoneNumber}
            providerPhoneNumberSource={providerPhoneNumberSource}
            careType={careType}
            filterAndSort={options.filterAndSort}
            displayCompareOption={displayCompareOption}
            dontOpenInNewTab={dontOpenInNewTab}
            promotionColorScheme={promotionColorScheme}
          />
        )}

        {options.visibleItems > 0 && pagination?.totalPages <= 1 && (
          <ShowMore
            limit={options.visibleItems}
            currentLimit={showMoreLimit}
            setShowMore={handleShowMore}
            resultCount={result?.resultCount ?? 0}
          />
        )}

        <SearchPagination
          baseUrl={pageProps?.page.canonical || ''}
          currentPage={pagination?.currentPage}
          queryParamKey={LISTING_PAGE}
          totalPages={pagination?.totalPages}
          setPage={pagination?.setPage}
        />
      </VStack>
    </Box>
  );
};

export default ProviderList;
