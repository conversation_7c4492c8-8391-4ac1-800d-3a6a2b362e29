import { render, screen } from '@utils/test-utils';

import MarketingCTA from './MarketingCTA';

describe('MarketingCTA', () => {
  const defaultProps = {
    phoneNumber: { number: '+18559483865', label: '(*************' }
  };

  describe('Rendering', () => {
    it('renders the component with title and description', () => {
      render(<MarketingCTA {...defaultProps} />);

      expect(
        screen.getByText('Not finding what you’re looking for?')
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "Caring's Family Advisors are here to help you with questions about senior living and care options."
        )
      ).toBeInTheDocument();
    });

    it('renders both CTA buttons', () => {
      render(<MarketingCTA {...defaultProps} />);

      expect(screen.getByText('Get Help Now')).toBeInTheDocument();
      expect(screen.getByText('Call us at (*************')).toBeInTheDocument();
    });

    it('renders the heading as h2', () => {
      render(<MarketingCTA {...defaultProps} />);

      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Not finding what you’re looking for?');
    });
    it('includes phone icon in the phone CTA button', () => {
      render(<MarketingCTA {...defaultProps} />);

      const phoneButton = screen.getByText('Call us at (*************');
      const icon = phoneButton.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });
  });
});
