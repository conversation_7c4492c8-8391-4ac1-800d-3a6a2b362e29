import { Flex, Text } from '@chakra-ui/layout';
import Heading from '@components/Heading';
import { NewCallToAction } from '@components/NewCallToAction';
interface MarketingCTAProps {
  phoneNumber?: {
    label?: string;
    number?: string;
  };
}

const MarketingCTA: React.FC<MarketingCTAProps> = ({ phoneNumber }) => {
  return (
    <Flex
      direction="column"
      padding={14}
      justifyContent="center"
      textAlign="center"
    >
      <Heading
        headingElement="h2"
        headingSize="lg"
        textAlign="center"
        title="Not finding what you’re looking for?"
        withContainer={false}
      />
      <Text paddingBottom={8}>
        Caring&#39;s Family Advisors are here to help you with questions about
        senior living and care options.
      </Text>
      <Flex
        gap={{ base: 2, md: 4 }}
        direction={{ base: 'column', sm: 'row' }}
        justifyContent="center"
      >
        <NewCallToAction
          cta1={{
            text: 'Get Help Now',
            isInquiry: true,
            textColor: 'white',
            bgColor: 'secondary',
            state: 'solid',
            behavior: '_self',
            inquiryId: 'enhancedSearchMarketingCTAInquiry'
          }}
          cta2={{
            text: `Call us at ${phoneNumber?.label}`,
            isInquiry: false,
            textColor: 'white',
            bgColor: 'secondary',
            state: 'outline',
            icon: 'MdPhone',
            behavior: '_self',
            type: 'tel',
            url: phoneNumber?.number
          }}
          onlyCTA={{
            enabled: true
          }}
          metadata={{ '@id': '00000000-0000-0000-0000-000000000000' }}
        />
      </Flex>
    </Flex>
  );
};

export default MarketingCTA;
