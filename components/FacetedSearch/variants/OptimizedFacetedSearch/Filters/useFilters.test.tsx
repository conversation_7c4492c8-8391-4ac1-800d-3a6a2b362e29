import {
  useFacetedSearchActions,
  useFacetedSearchQuery
} from '@components/FacetedSearch/FacetedSearchContext';
import { renderHook } from '@testing-library/react';

import useFilters from './useFilters';

jest.mock('@components/FacetedSearch/FacetedSearchContext', () => ({
  useFacetedSearchActions: jest.fn(),
  useFacetedSearchQuery: jest.fn()
}));

describe('useFilters', () => {
  const mockUpdateQuery = jest.fn();
  const mockQuery = {
    verified: [],
    awards: [],
    distanceInMiles: 0,
    reviews: [],
    providersWith: [],
    roomType: [],
    languages: [],
    priceRange: [0, 10000],
    ongoingPromotion: [],
    staffQualifications: [],
    roomAmenities: [],
    personalCare: [],
    dining: [],
    healthServices: [],
    otherAmenities: []
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      updateQuery: mockUpdateQuery
    });
    (useFacetedSearchQuery as jest.Mock).mockReturnValue(mockQuery);
  });

  it('should return correct accordion item properties', () => {
    const { result } = renderHook(() => useFilters({}));
    const ratingsAndAwardsFilter = result.current.accordionItems.find(
      (item) => item.id === 'reviews'
    );

    expect(ratingsAndAwardsFilter).toMatchObject({
      id: 'reviews',
      label: 'Ratings & Awards'
    });
    expect(ratingsAndAwardsFilter?.children).toBeDefined();
  });

  it('should use custom tooltips when provided', () => {
    const customTooltips = {
      awardsInfo: 'Custom awards tooltip',
      priceInfo: 'Custom price tooltip'
    };

    const { result } = renderHook(() =>
      useFilters({ filterTooltips: customTooltips })
    );

    const ratingsAndAwardsFilter = result.current.accordionItems.find(
      (item) => item.id === 'reviews'
    );
    const budgetFilter = result.current.accordionItems.find(
      (item) => item.id === 'promotions'
    );
    expect(ratingsAndAwardsFilter?.children).toBeDefined();
    expect(budgetFilter?.infoText).toBe(customTooltips.priceInfo);
  });

  it('should handle input changes correctly', () => {
    const { result } = renderHook(() => useFilters({}));
    const testValue = ['test'];

    result.current.updateQuery({ key: 'verified', value: testValue });

    expect(mockUpdateQuery).toHaveBeenCalledWith({
      key: 'verified',
      value: testValue
    });
  });

  it('should handle matchAllFilters changes correctly', () => {
    const { result } = renderHook(() => useFilters({}));

    result.current.handleMatchAllFiltersChange(true);

    expect(mockUpdateQuery).toHaveBeenCalledWith({
      key: 'matchAllFilters',
      value: true
    });
  });

  it('should return the current query state', () => {
    const { result } = renderHook(() => useFilters({}));
    expect(result.current.query).toEqual(mockQuery);
  });

  it('should memoize accordionItems correctly', () => {
    const { result, rerender } = renderHook(() => useFilters({}));
    const firstRenderItems = result.current.accordionItems;

    rerender();
    const secondRenderItems = result.current.accordionItems;

    expect(firstRenderItems).toBe(secondRenderItems);
  });

  it('should update memoized accordionItems when query changes', () => {
    const { result, rerender } = renderHook(() => useFilters({}));
    const firstRenderItems = result.current.accordionItems;

    const newQuery = { ...mockQuery, verified: ['new-value'] };
    (useFacetedSearchQuery as jest.Mock).mockReturnValue(newQuery);

    rerender();
    const secondRenderItems = result.current.accordionItems;

    expect(firstRenderItems).not.toBe(secondRenderItems);
  });
});
