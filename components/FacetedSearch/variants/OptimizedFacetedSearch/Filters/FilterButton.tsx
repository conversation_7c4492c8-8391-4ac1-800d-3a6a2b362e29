import { ButtonProps } from '@chakra-ui/button';
import SeeMoreButton from '@components/SeeMoreButton/SeeMoreButton';
import { ReactNode } from 'react';

interface FilterButtonProps extends ButtonProps {
  isActive: boolean;
  onClick: () => void;
  children: ReactNode;
}

const FilterButton = ({
  isActive,
  onClick,
  children,
  leftIcon,
  minWidth = '60px'
}: FilterButtonProps) => {
  return (
    <SeeMoreButton
      open={isActive}
      onClick={onClick}
      colorScheme={isActive ? 'accent' : 'gray'}
      backgroundColor={isActive ? 'primary.100' : 'white'}
      variant="outline"
      height="32px"
      fontSize="xs"
      iconSpacing={1}
      paddingX={3}
      borderWidth="2px"
      flex="1"
      aria-expanded={isActive}
      minWidth={minWidth}
      leftIcon={leftIcon}
      {...(leftIcon && { rightIcon: undefined })}
    >
      {children}
    </SeeMoreButton>
  );
};

export default FilterButton;
