import AccessibilityFilter from '@components/Accessibility';
import AwardsFilter from '@components/AwardsFilter';
import DiningFilter from '@components/DiningFilter';
import DistanceSlider from '@components/DistanceSlider';
import {
  AWARDS_TOOLTIP,
  VERIFIED_TOOLTIP
} from '@components/FacetedSearch/constants';
import {
  useFacetedSearchActions,
  useFacetedSearchQuery
} from '@components/FacetedSearch/FacetedSearchContext';
import { QueryKeyPath } from '@components/FacetedSearch/types';
import { AccordionItem } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import HealthServiceFilter from '@components/HealthServicesFilter';
import LanguagesFilter from '@components/LanguagesFilter';
import LifestyleFilter from '@components/LifestyleFilter';
import OtherAmenitiesFilter from '@components/OtherAmenitiesFilter';
import PersonalCareFilter from '@components/PersonalCareFilter';
import PriceSlider, { valueToSymbol } from '@components/PriceSlider';
import ProvidersWithFilter from '@components/ProvidersWithFilter';
import ReviewsFilter from '@components/ReviewsFilter';
import RoomAmenitiesFilter from '@components/RoomAmenitiesFilter';
import RoomsFilter from '@components/RoomsFilter';
import StaffQualificationFilter from '@components/StaffQualificationFilter';
import VerifiedPartnersFilter from '@components/VerifiedPartnersFilter';
import { useCallback, useMemo } from 'react';
import { MdOutlineHelpOutline } from 'react-icons/md';

import { FilterTooltips } from '~/types/componentsConfig';

interface UseFiltersProps {
  filterTooltips?: FilterTooltips;
}

const useFilters = (
  props?: UseFiltersProps
): {
  accordionItems: AccordionItem[];
  handleMatchAllFiltersChange: (value: boolean) => void;
  query: any;
  updateQuery: (args: { key: QueryKeyPath; value: any }) => void;
} => {
  const { updateQuery } = useFacetedSearchActions();
  const query = useFacetedSearchQuery();
  const filterTooltips = props?.filterTooltips;

  const handleInputChange = useCallback(
    (name: string, value: any) => {
      updateQuery({ key: name as QueryKeyPath, value });
    },
    [updateQuery]
  );

  const handleMatchAllFiltersChange = useCallback(
    (value: boolean) => {
      updateQuery({ key: 'matchAllFilters', value });
    },
    [updateQuery]
  );

  const accordionItems: AccordionItem[] = useMemo(
    () => [
      {
        id: 'reviews',
        label: 'Ratings & Awards',
        children: (
          <>
            <ReviewsFilter onChange={handleInputChange} value={query.reviews} />
            <AwardsFilter
              onChange={handleInputChange}
              value={query.awards}
              tooltipText={filterTooltips?.awardsInfo || AWARDS_TOOLTIP}
              tooltipIcon={MdOutlineHelpOutline}
            />
            <VerifiedPartnersFilter
              onChange={handleInputChange}
              value={query.verified}
              tooltipText={filterTooltips?.verifiedInfo || VERIFIED_TOOLTIP}
              tooltipIcon={MdOutlineHelpOutline}
            />
          </>
        )
      },
      {
        id: 'distance',
        label: 'Distance',
        children: (
          <DistanceSlider
            onChange={handleInputChange}
            value={query.distanceInMiles}
          />
        )
      },
      {
        id: 'promotions',
        label: 'Budget',
        children: (
          <PriceSlider
            onChange={handleInputChange}
            priceRange={query.priceRange}
            ongoingPromotion={query.ongoingPromotion}
          />
        ),
        infoText:
          filterTooltips?.priceInfo ||
          `Providers in this area range from ${valueToSymbol(
            query.priceRange[0]
          )} to ${valueToSymbol(query.priceRange[1])}`
      },
      {
        id: 'fivePlusPhotos',
        label: 'Photos',
        children: (
          <ProvidersWithFilter
            onChange={handleInputChange}
            value={query.providersWith}
          />
        )
      },
      {
        id: 'roomType',
        label: 'Room Type',
        children: (
          <RoomsFilter onChange={handleInputChange} value={query.roomType} />
        )
      },
      {
        id: 'roomAmenities',
        label: 'Room Amenities',
        children: (
          <RoomAmenitiesFilter
            onChange={handleInputChange}
            value={query.roomAmenities}
          />
        )
      },
      {
        id: 'otherAmenities',
        label: 'Community Amenities',
        children: (
          <>
            <LifestyleFilter
              onChange={handleInputChange}
              value={query.lifestyle}
            />
            <OtherAmenitiesFilter
              onChange={handleInputChange}
              value={query.otherAmenities}
            />
          </>
        )
      },
      {
        id: 'dining',
        label: 'Dining',
        children: (
          <DiningFilter onChange={handleInputChange} value={query.dining} />
        )
      },
      {
        id: 'healthServices',
        label: 'Health Services',
        children: (
          <>
            <HealthServiceFilter
              onChange={handleInputChange}
              value={query.healthServices}
            />
            <AccessibilityFilter
              onChange={handleInputChange}
              value={query.accessibility}
            />
          </>
        )
      },
      {
        id: 'personalCare',
        label: 'Personal Care',
        children: (
          <PersonalCareFilter
            onChange={handleInputChange}
            value={query.personalCare}
          />
        )
      },
      {
        id: 'staffQualifications',
        label: 'Staff Qualifications',
        children: (
          <StaffQualificationFilter
            onChange={handleInputChange}
            value={query.staffQualifications}
          />
        )
      },
      {
        id: 'languages',
        label: 'Languages',
        children: (
          <LanguagesFilter
            onChange={handleInputChange}
            value={query.languages}
          />
        )
      }
    ],
    [query, handleInputChange, filterTooltips]
  );

  return {
    accordionItems,
    handleMatchAllFiltersChange,
    query,
    updateQuery
  };
};

export default useFilters;
