import Accordion from '@components/Accordion';
import { SearchOption } from '@components/FacetedSearch/constants';

import { FilterTooltips } from '~/types/componentsConfig';

import useFilters from './useFilters';

interface FiltersProps {
  searchOptions: Array<SearchOption>;
  filterTooltips?: FilterTooltips;
}

const Filters: React.FC<FiltersProps> = ({ searchOptions, filterTooltips }) => {
  const { accordionItems } = useFilters({
    filterTooltips
  });

  return (
    <Accordion
      openIndexes={[]}
      items={accordionItems.filter((item) => searchOptions.includes(item.id))}
    />
  );
};

export default Filters;
