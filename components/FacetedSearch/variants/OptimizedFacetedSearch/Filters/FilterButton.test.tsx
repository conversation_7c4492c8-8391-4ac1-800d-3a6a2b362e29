import { fireEvent, render, screen } from '@testing-library/react';
import { MdTune } from 'react-icons/md';

import FilterButton from './FilterButton';

describe('FilterButton', () => {
  const defaultProps = {
    isActive: false,
    onClick: jest.fn(),
    children: 'Test Button'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders button with correct text', () => {
    render(<FilterButton {...defaultProps} />);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', async () => {
    render(<FilterButton {...defaultProps} />);
    fireEvent.click(screen.getByText('Test Button'));
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  describe('Icon behavior', () => {
    it('shows default right icon (chevron) when no leftIcon is provided', () => {
      render(<FilterButton {...defaultProps} />);
      const button = screen.getByRole('button');

      expect(
        button.querySelector('[data-testid="chevron-icon-down"]')
      ).toBeInTheDocument();
    });

    it('shows the up chevron icon when isActive is true', () => {
      render(<FilterButton {...defaultProps} isActive={true} />);
      const button = screen.getByRole('button');
      expect(
        button.querySelector('[data-testid="chevron-icon-up"]')
      ).toBeInTheDocument();
    });

    it('hides right icon when leftIcon is provided', () => {
      render(
        <FilterButton
          {...defaultProps}
          leftIcon={<MdTune data-testid="tune-icon" />}
        />
      );
      const button = screen.getByRole('button');
      expect(screen.getByTestId('tune-icon')).toBeInTheDocument();
      expect(
        button.querySelector('[data-testid="chevron-icon-down"]')
      ).not.toBeInTheDocument();
    });
  });

  describe('Width behavior', () => {
    it('applies custom minWidth when provided', () => {
      render(<FilterButton {...defaultProps} minWidth="100px" />);
      const button = screen.getByRole('button');
      expect(button).toHaveStyle({ minWidth: '100px' });
    });

    it('applies default minWidth when not provided', () => {
      render(<FilterButton {...defaultProps} />);
      const button = screen.getByRole('button');
      expect(button).toHaveStyle({ minWidth: '60px' });
    });
  });

  describe('Active state styling', () => {
    it('applies active styles when isActive is true', () => {
      render(<FilterButton {...defaultProps} isActive={true} />);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-expanded', 'true');
      expect(screen.getByTestId('chevron-icon-up')).toBeInTheDocument();
      expect(screen.queryByTestId('chevron-icon-down')).not.toBeInTheDocument();
    });

    it('applies inactive styles when isActive is false', () => {
      render(<FilterButton {...defaultProps} isActive={false} />);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-expanded', 'false');
      expect(screen.queryByTestId('chevron-icon-up')).not.toBeInTheDocument();
      expect(screen.getByTestId('chevron-icon-down')).toBeInTheDocument();
    });
  });
});
