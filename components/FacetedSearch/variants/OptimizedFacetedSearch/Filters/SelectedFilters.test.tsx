import {
  useFacetedSearchActions,
  useFacetedSearchQuery,
  useFacetedSearchResult
} from '@components/FacetedSearch/FacetedSearchContext';
import { reviewsFilterItems } from '@components/ReviewsFilter/ReviewsFilter';
import { verifiedPartnersFilterItems } from '@components/VerifiedPartnersFilter/VerifiedPartnersFilter';
import { fireEvent, render, screen } from '@testing-library/react';

import SelectedFilters from './SelectedFilters';

// Mock the hooks
jest.mock('@components/FacetedSearch/FacetedSearchContext', () => ({
  useFacetedSearchActions: jest.fn(),
  useFacetedSearchQuery: jest.fn(),
  useFacetedSearchResult: jest.fn().mockReturnValue({
    data: { resultCount: 0 }
  })
}));

describe('SelectedFilters', () => {
  const mockUpdateQuery = jest.fn();
  const mockResetQuery = jest.fn();
  const mockAnalytics = {
    track: jest.fn()
  };
  const originalWindowAnalytics = window.tracking;

  beforeEach(() => {
    jest.clearAllMocks();
    window.tracking = mockAnalytics;
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      updateQuery: mockUpdateQuery,
      resetQuery: mockResetQuery
    });
  });

  afterAll(() => {
    window.tracking = originalWindowAnalytics;
  });

  it('renders just the provider result count when no filters are selected', () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({});
    render(<SelectedFilters />);
    expect(screen.getByText('0 Providers')).toBeInTheDocument();
  });

  it('renders selected filters as chips', () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      verified: [verifiedPartnersFilterItems[0].value]
    });

    render(<SelectedFilters />);

    expect(
      screen.getByText(verifiedPartnersFilterItems[0].label)
    ).toBeInTheDocument();
  });

  it('calls updateQuery when removing a single filter', async () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      verified: [verifiedPartnersFilterItems[0].value]
    });

    render(<SelectedFilters />);

    const verifiedChip = screen.getByText(verifiedPartnersFilterItems[0].label);
    await fireEvent.click(verifiedChip);

    expect(mockUpdateQuery).toHaveBeenCalledWith({
      key: 'verified',
      value: []
    });
  });

  it('calls resetQuery when clicking Clear All', async () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      verified: [verifiedPartnersFilterItems[0].value]
    });

    render(<SelectedFilters />);

    const clearAllButton = screen.getByText('Clear All');
    await fireEvent.click(clearAllButton);

    expect(mockResetQuery).toHaveBeenCalledTimes(1);
  });

  it('renders component-based filters correctly', () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      reviews: [reviewsFilterItems[0].value]
    });

    render(<SelectedFilters />);
    expect(screen.getByText('4+')).toBeInTheDocument();
  });

  it('handles empty array values', () => {
    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      verified: []
    });

    render(<SelectedFilters />);
    expect(screen.getByText('0 Providers')).toBeInTheDocument();
  });

  describe('Loading States', () => {
    beforeEach(() => {
      (useFacetedSearchResult as jest.Mock).mockReturnValue({
        data: { resultCount: 100 },
        isFetching: true
      });
    });

    it('shows spinners when fetching', () => {
      (useFacetedSearchQuery as jest.Mock).mockReturnValue({
        verified: [verifiedPartnersFilterItems[0].value]
      });

      render(<SelectedFilters showResultCount={true} />);

      const spinners = screen.getAllByTestId('loading-spinner');
      expect(spinners).toHaveLength(1); // One for filters, one for result count
    });
  });
});
