import { Icon } from '@chakra-ui/icons';
import { Box, BoxProps, Text } from '@chakra-ui/react';
import { accessibilityFilterItems } from '@components/Accessibility/Accessibility';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { awardsFilterItems } from '@components/AwardsFilter/AwardsFilter';
import Button from '@components/Button';
import { diningFilterItems } from '@components/DiningFilter/DiningFilter';
import {
  useFacetedSearchActions,
  useFacetedSearchQuery,
  useFacetedSearchResult
} from '@components/FacetedSearch/FacetedSearchContext';
import { QueryKeyPath } from '@components/FacetedSearch/types';
import ResultCount from '@components/FacetedSearch/variants/OptimizedFacetedSearch/ResultCount';
import { healthServicesFilterItems } from '@components/HealthServicesFilter/HealthServicesFilter';
import { languageFilterItems } from '@components/LanguagesFilter/LanguagesFilter';
import { lifestyleFilterItems } from '@components/LifestyleFilter/LifestyleFilter';
import { otherAmenitiesFilterItems } from '@components/OtherAmenitiesFilter/OtherAmenitiesFilter';
import { personalCareFilterItems } from '@components/PersonalCareFilter/PersonalCareFilter';
import { providersWithFilterItems } from '@components/ProvidersWithFilter/ProvidersWithFilter';
import { reviewsFilterItems } from '@components/ReviewsFilter/ReviewsFilter';
import { roomAmenitiesFilterItems } from '@components/RoomAmenitiesFilter/RoomAmenitiesFilter';
import { roomFilterItems } from '@components/RoomsFilter/RoomsFilter';
import { staffQualificationFilterItems } from '@components/StaffQualificationFilter/StaffQualificationFilter';
import { verifiedPartnersFilterItems } from '@components/VerifiedPartnersFilter/VerifiedPartnersFilter';
import { filterNonReactAttributes } from '@utils/filterNonReactAttributes';
import { useMemo } from 'react';
import { MdClose } from 'react-icons/md';

const ITEMS_FOR_VALUES_BY_SEARCH_OPTION = {
  verified: verifiedPartnersFilterItems,
  awards: awardsFilterItems,
  reviews: reviewsFilterItems,
  providersWith: providersWithFilterItems,
  roomType: roomFilterItems,
  languages: languageFilterItems,
  lifestyle: lifestyleFilterItems,
  staffQualifications: staffQualificationFilterItems,
  roomAmenities: roomAmenitiesFilterItems,
  accessibility: accessibilityFilterItems,
  personalCare: personalCareFilterItems,
  dining: diningFilterItems,
  healthServices: healthServicesFilterItems,
  otherAmenities: otherAmenitiesFilterItems
};

interface SelectedFiltersProps extends BoxProps {
  showClearButton?: boolean;
  showResultCount?: boolean;
}

const SelectedFilters = ({
  showClearButton = true,
  showResultCount = true,
  ...props
}: SelectedFiltersProps) => {
  const { updateQuery, resetQuery } = useFacetedSearchActions();
  const query = useFacetedSearchQuery();
  const { data, isFetching } = useFacetedSearchResult();
  const resultCount = data?.resultCount ?? 0;
  const boxProps = filterNonReactAttributes(props);

  const chipButtons = useMemo(() => {
    return Object.entries(query)
      .filter(([key]) => ITEMS_FOR_VALUES_BY_SEARCH_OPTION[key])
      .flatMap(([name, values]) =>
        ITEMS_FOR_VALUES_BY_SEARCH_OPTION[name]
          .filter((item) =>
            (values as (string | number)[]).includes(item.value)
          )
          .map((item) => (
            <Button
              key={`${item.value}-${item.label}`}
              type="button"
              size="sm"
              colorScheme="accent"
              variant="outline"
              sx={{ backgroundColor: 'primary.100' }}
              onClick={() => {
                const newValue = query[name].filter(
                  (value) => value !== item.value
                );
                updateQuery({ key: name as QueryKeyPath, value: newValue });
              }}
              elementAction={ElementActions.FILTERS}
              elementName={ElementNames.REMOVE_FILTER_CHIP}
              elementType={ElementTypes.BUTTON}
            >
              <Icon as={MdClose} boxSize="4" color="gray.800" sx={{ mr: 1 }} />
              {item.label ?? item.component}
            </Button>
          ))
      );
  }, [query, updateQuery]);

  const hasSelectedFilters = chipButtons.length > 0;

  return (
    <Box {...boxProps}>
      {hasSelectedFilters && (
        <Box padding={showResultCount ? 0 : 4}>
          <Box
            marginBottom={4}
            marginTop={showResultCount ? 6 : 0}
            justifyContent="space-between"
            display="flex"
          >
            <Text as="span" textAlign="left" fontWeight="bold">
              Your Selected Filters
            </Text>
          </Box>
          <Box display="flex" flexWrap="wrap" gap={2} alignItems="center">
            {chipButtons}
            {showClearButton && (
              <Button
                type="button"
                size="sm"
                variant="ghost"
                color="gray.800"
                onClick={() => resetQuery()}
                _hover={{ backgroundColor: 'none' }}
                elementAction={ElementActions.CLEAR_ALL_FILTERS}
                elementName={ElementNames.CLEAR_ALL_FILTERS}
                elementType={ElementTypes.BUTTON}
                isDisabled={isFetching}
              >
                <Text borderBottom="1px solid">Clear All</Text>
              </Button>
            )}
          </Box>
        </Box>
      )}
      {showResultCount && (
        <ResultCount resultCount={resultCount} isFetching={isFetching} />
      )}
    </Box>
  );
};

export default SelectedFilters;
