import { AccordionItemProps } from '@components/Accordion/Accordion';
import { SearchOption } from '@components/FacetedSearch/constants';

import { FilterTooltips } from '~/types/componentsConfig';

export type ActiveFilter = 'sort' | 'rating' | 'map' | 'all' | null;

export interface FilterProps {
  filterTooltips: FilterTooltips;
  searchOptions: Array<SearchOption>;
  isMapViewEnabled?: boolean;
}

export interface AccordionItem extends AccordionItemProps {
  id: SearchOption;
}
