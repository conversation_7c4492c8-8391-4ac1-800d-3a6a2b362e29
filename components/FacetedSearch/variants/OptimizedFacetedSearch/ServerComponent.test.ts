import { OptimizedFacetedSearchProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/OptimizedFacetedSearch';
import { SEARCH_PARAM } from '@constants/search-params';
import { ModularMonolithClient } from '@services/modular-monolith/client';
import { FacetedSearchResponse } from '@services/modular-monolith/types/search.type';
import { GetServerSidePropsContext } from 'next';

import { getLatLongMapBoxAPI } from '~/utils/search';

import { getServerSideComponentProps } from './ServerComponent';

const mockedComponentConfig = {
  bgBorderRadius: 0,
  bgColor: 'green',
  bgColorRange: '500',
  boxShadow: 'none',
  desktopTitleSize: 'md',
  displayCareTypeFilter: true,
  displayLocationFilter: true,
  displayProviderPhoneNumber: true,
  genericBlock1: {
    '@id': 'block-1',
    '@name': 'Generic Block 1',
    '@nodeType': 'mgnl:component',
    '@nodes': [],
    '@path': '/generic-block-1',
    'mgnl:created': '2023-01-01T00:00:00.000Z',
    'mgnl:lastModified': '2023-01-01T00:00:00.000Z',
    'mgnl:template': 'generic-block',
    content: null,
    visible: false
  },
  genericBlock2: {
    '@id': 'block-2',
    '@name': 'Generic Block 2',
    '@nodeType': 'mgnl:component',
    '@nodes': [],
    '@path': '/generic-block-2',
    'mgnl:created': '2023-01-01T00:00:00.000Z',
    'mgnl:lastModified': '2023-01-01T00:00:00.000Z',
    'mgnl:template': 'generic-block',
    content: null,
    visible: false
  },
  filterAndSort: {
    '@name': 'filterAndSort',
    '@path':
      '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/filterAndSort',
    '@id': '839b2ebd-89e5-4bb6-9a49-9e16abebeb81',
    '@nodeType': 'mgnl:contentNode',
    filters: [
      'healthServices',
      'languages',
      'distance',
      'accessibility',
      'dining',
      'verified',
      'roomAmenities',
      'otherAmenities',
      'staffQualifications',
      'lifestyle',
      'promotions',
      'reviews',
      'awards',
      'sortBy',
      'personalCare',
      'roomType',
      'fivePlusPhotos'
    ],
    'mgnl:lastModified': '2025-02-27T18:57:28.592Z',
    'mgnl:created': '2025-02-26T14:01:06.576Z',
    phoneNumber: {
      '@name': 'phoneNumber',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/filterAndSort/phoneNumber',
      '@id': '73043d81-701c-4042-9a45-cb930293229a',
      '@nodeType': 'mgnl:contentNode',
      label: '(*************',
      'mgnl:lastModified': '2025-02-26T14:01:06.583Z',
      number: '+18777521885',
      'mgnl:created': '2025-02-26T14:01:06.583Z',
      '@nodes': []
    },
    defaultValues: {
      '@name': 'defaultValues',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/filterAndSort/defaultValues',
      '@id': 'f6c98baa-e62b-473e-8271-5323f2fdf7e8',
      '@nodeType': 'mgnl:contentNode',
      'mgnl:created': '2024-05-21T21:07:40.869Z',
      matchAllFilters: true,
      'mgnl:lastModified': '2025-05-20T22:10:59.457Z',
      shouldShowOnlyIndexedProviders: true,
      '@nodes': []
    },
    '@nodes': ['phoneNumber', 'defaultValues']
  },
  headingElement: 'h2',
  learnMoreButtonColorScheme: 'primary',
  metadata: {
    '@id': 'meta-1',
    '@name': 'Metadata',
    '@nodeType': 'mgnl:metadata',
    '@nodes': [],
    '@path': '/metadata',
    'mgnl:created': '2023-01-01T00:00:00.000Z',
    'mgnl:lastModified': '2023-01-01T00:00:00.000Z',
    'mgnl:template': 'metadata'
  },
  providerTitleColor: 'green',
  providerTitleColorRange: '500',
  ratingStarsColor: 'blue',
  ratingStarsColorRange: '500',
  readOnlyLocationInput: false,
  readOnlyLocationInputPlaceholder: 'Enter a location',
  requestInfoButtonColorScheme: 'primary',
  searchBarBgColor: 'green',
  searchBarBgColorRange: '500',
  searchBarButtonColorScheme: 'primary',
  tileBorder: 'none',
  tileBorderColor: 'green',
  tileBorderColorRange: '500',
  titleSize: 'md',
  searchOptions: {
    displayMode: 'LIST',
    itemsPerPage: 10,
    amenityCategory: '',
    careType: '',
    city: ''
  },
  searchBar: {
    careTypeSelect: {
      visible: true
    }
  },
  searchSidebar: {
    filters: []
  },
  amenityCategoryChips: {
    visible: true
  },
  enablePredictiveSearch: false,
  dontOpenInNewTab: false,
  optimizedFacetedSearch: {
    '@name': 'optimizedFacetedSearch',
    '@path':
      '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch',
    '@id': '6cb52d13-49d2-4cd9-bb0c-4f3dc1f41b67',
    '@nodeType': 'mgnl:contentNode',
    displayCompareOption: false,
    countOfTiles: '10',
    careType: 'assisted-living',
    infoButtonInquiryId: 'ProviderInfoCostsForm',
    readMoreButton: 'redirect_to_provider_page',
    'mgnl:created': '2025-02-26T14:01:06.574Z',
    titleSize: 'md',
    titleAlignment: 'left',
    requestInfoButtonText: 'Request Info',
    visibleLimit: '0',
    longitude: '-80.31612085652348',
    headingElement: 'h2',
    county: 'Miami Dade County',
    state: 'FL',
    latitude: '25.75744192514186',
    desktopTitleSize: 'xl',
    city: 'Miami',
    'mgnl:lastModified': '2025-02-28T16:54:24.601Z',
    providerPhoneNumberSource: {
      '@name': 'providerPhoneNumberSource',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/providerPhoneNumberSource',
      '@id': '29db5cf1-f671-4efd-8cb7-d0bee10cfbf6',
      '@nodeType': 'mgnl:contentNode',
      'mgnl:lastModified': '2025-02-26T14:01:06.575Z',
      providerPhoneNumber: '18005580653',
      field: 'globalCatalog',
      'mgnl:created': '2025-02-26T14:01:06.575Z',
      '@nodes': []
    },
    mapView: {
      '@name': 'mapView',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/mapView',
      '@id': '35cd9266-a43c-4086-8336-e43cec68e3b7',
      '@nodeType': 'mgnl:contentNode',
      enabled: true,
      'mgnl:lastModified': '2025-02-26T14:01:06.575Z',
      'mgnl:created': '2025-02-26T14:01:06.575Z',
      '@nodes': []
    },
    filterAndSort: {
      '@name': 'filterAndSort',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/filterAndSort',
      '@id': '839b2ebd-89e5-4bb6-9a49-9e16abebeb81',
      '@nodeType': 'mgnl:contentNode',
      filters: [
        'healthServices',
        'languages',
        'distance',
        'accessibility',
        'dining',
        'verified',
        'roomAmenities',
        'otherAmenities',
        'staffQualifications',
        'lifestyle',
        'promotions',
        'reviews',
        'awards',
        'sortBy',
        'personalCare',
        'roomType',
        'fivePlusPhotos'
      ],
      'mgnl:lastModified': '2025-02-27T18:57:28.592Z',
      'mgnl:created': '2025-02-26T14:01:06.576Z',
      phoneNumber: {
        '@name': 'phoneNumber',
        '@path':
          '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/filterAndSort/phoneNumber',
        '@id': '73043d81-701c-4042-9a45-cb930293229a',
        '@nodeType': 'mgnl:contentNode',
        label: '(*************',
        'mgnl:lastModified': '2025-02-26T14:01:06.583Z',
        number: '+18777521885',
        'mgnl:created': '2025-02-26T14:01:06.583Z',
        '@nodes': []
      },
      defaultValues: {
        '@name': 'defaultValues',
        '@path':
          '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/filterAndSort/defaultValues',
        '@id': 'f6c98baa-e62b-473e-8271-5323f2fdf7e8',
        '@nodeType': 'mgnl:contentNode',
        'mgnl:created': '2024-05-21T21:07:40.869Z',
        matchAllFilters: true,
        'mgnl:lastModified': '2025-05-20T22:10:59.457Z',
        shouldShowOnlyIndexedProviders: true,
        '@nodes': []
      },
      '@nodes': ['phoneNumber', 'defaultValues']
    },
    facetedSearch: {
      '@name': 'facetedSearch',
      '@path':
        '/caring.com/senior-living/careTypeOrState/stateOrCountyOrCity/city/main/0/optimizedFacetedSearch/facetedSearch',
      '@id': 'cb7dda67-78fe-4149-860f-4a3e5307a2ff',
      '@nodeType': 'mgnl:contentNode',
      'mgnl:lastModified': '2025-02-28T16:54:24.602Z',
      basePath: '/senior-living/assisted-living/florida/miami',
      'mgnl:created': '2025-02-28T16:54:24.602Z',
      '@nodes': []
    },
    '@nodes': [
      'providerPhoneNumberSource',
      'mapView',
      'filterAndSort',
      'facetedSearch'
    ]
  } as unknown as OptimizedFacetedSearchProps
} as const;

const mockedContext = {
  req: {
    headers: {
      host: 'caring.com'
    }
  },
  query: {}
} as unknown as GetServerSidePropsContext;

const mockedFacetedSearchResponse: FacetedSearchResponse = {
  totalRegionItems: 0,
  totalNearbyItems: 0,
  totalItems: 0,
  totalPages: 0,
  results: [],
  queryId: '',
  listId: ''
};

jest.mock('@services/enhanced-search/api', () => ({
  getProviders: jest.fn()
}));

jest.mock('@services/magnolia/getDataByDomain', () => ({
  getDataByDomain: jest.fn().mockReturnValue({ careTypes: [] })
}));

global.fetch = jest.fn();

jest.mock('@services/modular-monolith/client');

jest.mock('~/utils/search', () => ({
  getLatLongMapBoxAPI: jest.fn(),
  parseResultAmenities: jest
    .fn()
    .mockImplementation((_source, results) => results),
  parseResultContracts: jest
    .fn()
    .mockImplementation((_source, results) => results)
}));

describe('getServerSideComponentProps', () => {
  it('calls modMonClient.getProviders', async () => {
    const searchProvidersByFacetsMock = jest.fn().mockResolvedValue({
      results: [],
      listId: 'id',
      totalNearbyItems: 0,
      totalPages: 1,
      queryId: 'qid',
      totalRegionItems: 0,
      totalItems: 0
    });
    (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
      searchProvidersByFacets: searchProvidersByFacetsMock
    }));

    await getServerSideComponentProps(
      {
        filterAndSort: { defaultValues: {} },
        optimizedFacetedSearch: {}
      } as any,
      {
        query: {},
        resolvedUrl: '/search'
      } as any
    );

    expect(searchProvidersByFacetsMock).toHaveBeenCalled();
  });
});

describe('OptimizedFacetedSearch ServerComponent', () => {
  beforeEach(() => {
    (global.fetch as jest.Mock).mockClear();
  });

  describe('getServerSideComponentProps', () => {
    it('should correctly read the current page from the query string', async () => {
      const searchProvidersByFacetsMock = jest
        .fn()
        .mockResolvedValue(mockedFacetedSearchResponse);
      (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
        searchProvidersByFacets: searchProvidersByFacetsMock
      }));

      const data = await getServerSideComponentProps(mockedComponentConfig, {
        ...mockedContext,
        query: { [SEARCH_PARAM.PAGE]: '2' }
      });

      expect(data).toHaveProperty('currentPage', 1); // Page is zero-indexed
      expect(searchProvidersByFacetsMock).toHaveBeenCalled();
    });

    it('should return outOfBounds as true if the current page is out of bounds', async () => {
      const searchProvidersByFacetsMock = jest.fn().mockResolvedValue({
        ...mockedFacetedSearchResponse,
        totalPages: 3
      });
      (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
        searchProvidersByFacets: searchProvidersByFacetsMock
      }));

      const data = await getServerSideComponentProps(mockedComponentConfig, {
        ...mockedContext,
        query: { [SEARCH_PARAM.PAGE]: '4' }
      });

      expect(data).toHaveProperty('outOfBounds', true);
      expect(searchProvidersByFacetsMock).toHaveBeenCalled();
    });

    it('should call Mapbox API when latLng is empty and keyword is provided', async () => {
      const mockMapboxResponse = [-122.4194, 37.7749]; // [longitude, latitude] for San Francisco
      (getLatLongMapBoxAPI as jest.Mock).mockResolvedValue(mockMapboxResponse);

      const searchProvidersByFacetsMock = jest
        .fn()
        .mockResolvedValue(mockedFacetedSearchResponse);
      (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
        searchProvidersByFacets: searchProvidersByFacetsMock
      }));

      const data = await getServerSideComponentProps(mockedComponentConfig, {
        ...mockedContext,
        query: {
          [SEARCH_PARAM.KEYWORD]: 'San Francisco',
          'lat-lng': '' // Empty latLng
        }
      });

      expect(getLatLongMapBoxAPI).toHaveBeenCalledWith('San Francisco');
      expect(searchProvidersByFacetsMock).toHaveBeenCalledWith(
        expect.objectContaining({
          criteria: expect.objectContaining({
            latitude: '37.7749', // latitude from Mapbox response
            longitude: '-122.4194' // longitude from Mapbox response
          })
        })
      );
      expect(data).toBeDefined();
    });

    it('should not call Mapbox API when latLng is already provided', async () => {
      const searchProvidersByFacetsMock = jest
        .fn()
        .mockResolvedValue(mockedFacetedSearchResponse);
      (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
        searchProvidersByFacets: searchProvidersByFacetsMock
      }));

      await getServerSideComponentProps(mockedComponentConfig, {
        ...mockedContext,
        query: {
          [SEARCH_PARAM.KEYWORD]: 'San Francisco',
          'lat-lng': '37.7749,-122.4194' // Already has latLng
        }
      });

      expect(getLatLongMapBoxAPI).not.toHaveBeenCalled();
    });

    it('should handle Mapbox API returning null gracefully', async () => {
      (getLatLongMapBoxAPI as jest.Mock).mockResolvedValue(null);

      const searchProvidersByFacetsMock = jest
        .fn()
        .mockResolvedValue(mockedFacetedSearchResponse);
      (ModularMonolithClient as jest.Mock).mockImplementation(() => ({
        searchProvidersByFacets: searchProvidersByFacetsMock
      }));

      const data = await getServerSideComponentProps(mockedComponentConfig, {
        ...mockedContext,
        query: {
          [SEARCH_PARAM.KEYWORD]: 'Invalid Location',
          'lat-lng': ''
        }
      });

      expect(getLatLongMapBoxAPI).toHaveBeenCalledWith('Invalid Location');
      expect(searchProvidersByFacetsMock).toHaveBeenCalledWith(
        expect.objectContaining({
          criteria: expect.not.objectContaining({
            latitude: expect.any(Number),
            longitude: expect.any(Number)
          })
        })
      );
      expect(data).toBeDefined();
    });
  });
});
