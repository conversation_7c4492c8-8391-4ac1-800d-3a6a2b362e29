import { Box } from '@chakra-ui/layout';
import LAYOUT from '@components/Layouts/layoutConstants';
import Container from '@components/LayoutStructure/Container';
import useDebounce from '@hooks/useDebounce';
import { useCallback, useEffect, useRef, useState } from 'react';

interface FacetedSearchLayoutProps {
  header?: React.ReactNode;
  main?: React.ReactNode;
  sidebar?: React.ReactNode;
  isFormFocused?: boolean;
}

const FacetedSearchLayout: React.FC<FacetedSearchLayoutProps> = ({
  header,
  main,
  sidebar,
  isFormFocused
}) => {
  const [isSticky, setIsSticky] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const debouncedSetIsSticky = useDebounce(isSticky, 100);

  const handleIntersection = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry) {
        requestAnimationFrame(() => {
          setIsSticky(entry.intersectionRatio < 1);
        });
      }
    },
    []
  );

  useEffect(() => {
    if (!headerRef.current) return;

    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold: [1],
      rootMargin: '-54px 0px 0px 0px'
    });

    observerRef.current.observe(headerRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersection]);

  return (
    <Container px={{ base: 0, lg: LAYOUT.CONTAINER_HORIZONTAL_PADDING }}>
      <Box
        p="0"
        mx="auto"
        backgroundColor="background.50"
        display="grid"
        gridTemplateAreas={{
          base: `
                'header'
                'sidebar'
                'main'
              `,
          lg: `
                'sidebar header'
                'sidebar main'
              `
        }}
        gridTemplateColumns={{
          base: 'minmax(0,1fr)',
          lg: 'minmax(0,1fr) minmax(0,2fr)'
        }}
        gridTemplateRows={{
          base: 'minmax(0, auto)',
          lg: 'minmax(0, auto) minmax(0,1fr)'
        }}
        rowGap={{ base: 0, lg: 8 }}
      >
        <Box
          gridArea="header"
          ref={headerRef}
          position={{ base: 'sticky', lg: 'relative' }}
          top={{ base: '50px', lg: '0' }}
          zIndex={{ base: 3, lg: 'initial' }}
          backgroundColor={{
            base: debouncedSetIsSticky ? 'white' : 'background.50',
            lg: 'background.50'
          }}
        >
          {header}
        </Box>
        <Box
          gridArea="sidebar"
          position={{ base: 'sticky', lg: 'relative' }}
          top={{ base: isFormFocused ? '162px' : '114px', lg: '0' }}
          zIndex={{ base: 2, lg: 'initial' }}
          backgroundColor={{
            base: debouncedSetIsSticky ? 'white' : 'background.50',
            lg: 'background.50'
          }}
          boxShadow={{ base: debouncedSetIsSticky ? 'md' : 'none', lg: 'none' }}
        >
          {sidebar}
        </Box>

        <Box gridArea="main">{main}</Box>
      </Box>
    </Container>
  );
};

export default FacetedSearchLayout;
