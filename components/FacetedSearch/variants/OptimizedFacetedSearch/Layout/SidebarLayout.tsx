import { BoxProps, Flex, FlexProps } from '@chakra-ui/layout';
import { ComponentWithAs } from '@chakra-ui/system';

interface CompoundComponents {
  Header: typeof SidebarHeader;
  Body: typeof SidebarBody;
}

const SidebarHeader: ComponentWithAs<'div', FlexProps> = ({
  children,
  ...rest
}) => {
  return (
    <Flex flexDirection="column" gap={8} {...rest}>
      {children}
    </Flex>
  );
};

const SidebarBody: ComponentWithAs<'div', FlexProps> = ({
  children,
  ...rest
}) => {
  return (
    <Flex flexDirection="column" gap={2} {...rest}>
      {children}
    </Flex>
  );
};

const SidebarLayout: ComponentWithAs<'div', BoxProps> & CompoundComponents = ({
  children,
  ...rest
}) => {
  return (
    <Flex flexDirection="column" py={{ base: 0, lg: 8 }} {...rest}>
      {children}
    </Flex>
  );
};

SidebarLayout.Header = SidebarHeader;
SidebarLayout.Body = SidebarBody;

export default SidebarLayout;
