import { SearchStates } from '@components/FacetedSearch/constants';
import { getFacetedSearchParams } from '@components/FacetedSearch/search-params';
import { LayoutType, SearchData } from '@components/FacetedSearch/types';
import {
  createFacetedSearchQuery,
  fetchCareTypes,
  getCareTypeId,
  getSourceForFacetedSearch
} from '@components/FacetedSearch/utils';
import {
  FilterAndSortDefaultProps,
  OptimizedFacetedSearchProps
} from '@components/FacetedSearch/variants/OptimizedFacetedSearch/OptimizedFacetedSearch';
import { parseComponentConfig } from '@components/Search/EnhancedSearch/parser';
import { ModularMonolithClient } from '@services/modular-monolith/client';
import { AlgoliaProvider } from '@services/modular-monolith/types/search.type';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';
import { tenantSwitcher } from '~/contexts/TenantFunctionsContext';
import {
  getLatLongMapBoxAPI,
  parseResultAmenities,
  parseResultContracts
} from '~/utils/search';

export const getServerSideComponentProps = async (
  componentConfig: {
    filterAndSort: FilterAndSortDefaultProps;
    optimizedFacetedSearch: OptimizedFacetedSearchProps;
  },
  context: GetServerSidePropsContext
): Promise<SearchData> => {
  const parsedConfig = parseComponentConfig(
    componentConfig.optimizedFacetedSearch
  );
  const site = findSiteForContext(context);
  const domain = site.path;
  const { getCountyForSearch } = tenantSwitcher(domain);
  const careTypeResult = await fetchCareTypes(domain);

  if (context.query['care-type']) {
    context.query['care-type'] = getCareTypeId(
      context.query['care-type'] as string,
      careTypeResult
    );
  }

  const searchParams = getFacetedSearchParams(context.query);
  const source = getSourceForFacetedSearch({
    displayMode: parsedConfig.searchOptions.displayMode,
    domain,
    amenityCategoryChips: parsedConfig.amenityCategoryChips,
    searchParams
  });

  const careType = parsedConfig.searchOptions.careType || searchParams.careType;
  const componentProps = {
    careType: careType,
    city: parsedConfig.searchOptions.city ?? '',
    county: getCountyForSearch(parsedConfig.searchOptions.county ?? ''),
    displayMode: parsedConfig.searchOptions.displayMode,
    distanceFilterEnabled: true,
    domain,
    latitude: String(parsedConfig.searchOptions.latitude),
    longitude: String(parsedConfig.searchOptions.longitude),
    resultsPerPage: parsedConfig.searchOptions.itemsPerPage,
    source,
    state: parsedConfig.searchOptions.state ?? '',
    shouldShowOnlyIndexedProviders:
      componentConfig?.filterAndSort?.defaultValues
        ?.shouldShowOnlyIndexedProviders ?? false
  };

  const filters = createFacetedSearchQuery({
    searchParams,
    componentProps
  });

  if (!searchParams.latLng && searchParams.keyword) {
    const results = await getLatLongMapBoxAPI(searchParams.keyword);
    if (results) {
      filters.criteria.latitude = String(results[1]);
      filters.criteria.longitude = String(results[0]);
    }
  }

  const modMonClient = new ModularMonolithClient(domain);
  const data = await modMonClient.searchProvidersByFacets(filters);

  const careTypes = careTypeResult;

  const parsedResultsAmenities = parseResultAmenities(
    source,
    data.results as AlgoliaProvider[]
  );

  const parsedResults = parseResultContracts(
    source,
    parsedResultsAmenities as AlgoliaProvider[]
  );

  const amenityCategories = [];

  return {
    amenityCategories,
    careTypes,
    currentPage: searchParams.page,
    listId: data.listId,
    nearbyCount: data.totalNearbyItems,
    layoutType: LayoutType.Optimized,
    outOfBounds: searchParams.page + 1 > data.totalPages,
    queryId: data.queryId,
    regionCount: data.totalRegionItems,
    resultCount: data.totalItems,
    results: parsedResults,
    resultsPerPage: parsedConfig.searchOptions.itemsPerPage,
    searchState:
      data.totalItems === 0
        ? SearchStates.INPUT_WITH_NO_RESULTS
        : SearchStates.INPUT_WITH_RESULTS
  };
};

export const dataName = 'enhancedSearch';
