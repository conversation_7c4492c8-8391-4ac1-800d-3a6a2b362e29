import useSearch from '@hooks/useSearch';
import { render, screen } from '@utils/test-utils';
import React from 'react';

import Header, { HeaderProps } from './Header';

jest.mock('@hooks/useSearch', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    buildSearchUrl: jest.fn(() => '/mocked/search/url')
  }))
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {}
  }))
}));

describe('Header', () => {
  const defaultProps: HeaderProps = {
    title: 'Search Senior Living',
    headingElement: 'h1' as const,
    titleSize: 'xl',
    desktopTitleSize: '2xl',
    titleAlignment: 'left' as const,
    latitude: '47.6062',
    longitude: '-122.3321',
    careType: 'assisted-living',
    metadata: {
      '@id': 'test-search-bar-id'
    },
    careTypes: [],
    displayCompareOption: false,
    countOfTiles: '12',
    mapView: { enabled: false },
    infoButtonInquiryId: '',
    requestInfoButtonText: '',
    readMoreButton: 'Read More',
    linkText: 'View Details',
    visibleLimit: '10',
    filterAndSort: {
      phoneNumber: {}
    },
    providerPhoneNumberSource: {
      field: '',
      providerPhoneNumber: ''
    }
  };

  const mockBuildSearchUrl = jest.fn();
  const mockAssign = jest.fn();

  beforeEach(() => {
    (useSearch as jest.Mock).mockReturnValue({
      buildSearchUrl: mockBuildSearchUrl
    });
    // Mock window.location.assign
    Object.defineProperty(window, 'location', {
      value: { assign: mockAssign },
      writable: true
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the title with correct props', () => {
      render(<Header {...defaultProps} />);

      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('Search Senior Living');
    });

    it('renders without title when not provided', () => {
      const propsWithoutTitle = { ...defaultProps, title: undefined };
      render(<Header {...propsWithoutTitle} />);

      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
    });

    it('renders SearchBar with correct props', () => {
      render(<Header {...defaultProps} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('renders with city and state', () => {
      const propsWithLocation = {
        ...defaultProps,
        city: 'Seattle',
        state: 'Wa'
      };
      render(<Header {...propsWithLocation} />);

      const searchForm = screen.getByRole('textbox');
      expect(searchForm).toHaveAttribute('value', 'Seattle, Wa');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('handles invalid coordinates gracefully', () => {
      const propsWithInvalidCoords = {
        ...defaultProps,
        latitude: 'invalid',
        longitude: 'invalid'
      };
      render(<Header {...propsWithInvalidCoords} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('handles missing metadata gracefully', () => {
      const propsWithoutMetadata = {
        ...defaultProps,
        metadata: undefined
      };
      render(<Header {...propsWithoutMetadata} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('handles empty filterAndSort object gracefully', () => {
      const propsWithEmptyFilters = {
        ...defaultProps,
        filterAndSort: {}
      };
      render(<Header {...propsWithEmptyFilters} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('handles missing providerPhoneNumberSource gracefully', () => {
      const propsWithoutPhoneSource = {
        ...defaultProps,
        providerPhoneNumberSource: undefined
      };
      render(<Header {...propsWithoutPhoneSource} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('handles invalid visibleLimit gracefully', () => {
      const propsWithInvalidLimit = {
        ...defaultProps,
        visibleLimit: 'invalid'
      };
      render(<Header {...propsWithInvalidLimit} />);

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });
});
