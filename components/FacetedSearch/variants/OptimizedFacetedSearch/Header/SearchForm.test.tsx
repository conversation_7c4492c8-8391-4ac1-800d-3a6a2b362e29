import { MODAL } from '@constants/modals';
import { act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { fireEvent, render, screen, waitFor } from '@utils/test-utils';

import { useModalDispatch } from '~/contexts/ModalContext';

import SearchForm from './SearchForm';

jest.mock('@components/AutocompleteInput/AutocompleteInput', () => {
  return {
    __esModule: true,
    default: ({
      'aria-label': ariaLabel,
      defaultValue,
      setKeyword,
      setLatLng
    }) => (
      <input
        aria-label={ariaLabel}
        defaultValue={defaultValue}
        onChange={(e) => {
          setKeyword?.(e.target.value);
          setLatLng?.('40.7128,-74.0060');
        }}
        data-testid="autocomplete-input"
      />
    )
  };
});

beforeEach(() => {
  const mockAnalytics = {
    track: jest.fn()
  };
  window.tracking = mockAnalytics;
});

jest.mock('~/contexts/ModalContext', () => ({
  useModalDispatch: jest.fn(() => ({
    showModal: jest.fn(),
    hideModal: jest.fn()
  }))
}));

describe('SearchForm', () => {
  const mockOnSubmit = jest.fn();
  const defaultProps = {
    careTypeSelect: {
      options: [
        {
          id: 'assisted-living',
          slug: 'assisted-living',
          name: 'Assisted Living',
          rollUpType: 'Senior Living'
        },
        {
          id: 'memory-care',
          slug: 'memory-care',
          name: 'Memory Care',
          rollUpType: 'Senior Care'
        }
      ],
      placeholder: 'Select care type',
      visible: true
    },
    locationInput: {
      placeholder: 'Enter city or zip code'
    },
    defaultValues: {
      careType: 'assisted-living',
      keyword: 'Seattle',
      latLng: '47.6062,-122.3321'
    },
    onSubmit: mockOnSubmit
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default values', () => {
    render(<SearchForm {...defaultProps} />);

    const locationInput = screen.getByTestId('autocomplete-input');
    expect(locationInput).toBeInTheDocument();
    expect(locationInput).toHaveValue('Seattle');
  });

  it('renders with pre-filled city', () => {
    render(
      <SearchForm
        {...defaultProps}
        city="Seattle"
        state="Wa"
        careType="memory-care"
      />
    );

    const locationInput = screen.getByLabelText('search-location');
    expect(locationInput).toHaveValue('Seattle, Wa');
  });

  it('updates latLng when location is selected from autocomplete', async () => {
    render(<SearchForm {...defaultProps} />);

    const locationInput = screen.getByTestId('autocomplete-input');

    await userEvent.clear(locationInput);
    await userEvent.type(locationInput, 'New York');

    const form = screen.getByTestId('search-bar-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          keyword: 'New York',
          latLng: '40.7128,-74.0060'
        }),
        expect.anything()
      );
    });
  });

  it('validates required fields before submission', async () => {
    render(
      <SearchForm
        {...defaultProps}
        defaultValues={{
          careType: '',
          keyword: '',
          latLng: ''
        }}
      />
    );

    const form = screen.getByTestId('search-bar-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  it('submits form with valid data', async () => {
    render(<SearchForm {...defaultProps} />);

    const form = screen.getByTestId('search-bar-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        {
          careType: 'assisted-living',
          keyword: 'Seattle',
          latLng: '47.6062,-122.3321'
        },
        expect.anything()
      );
    });
  });

  it('updates form values when AutocompleteInput changes', async () => {
    render(<SearchForm {...defaultProps} />);

    const locationInput = screen.getByLabelText('search-location');
    await userEvent.clear(locationInput);
    await userEvent.type(locationInput, 'Los Angeles');

    expect(locationInput).toHaveValue('Los Angeles');
  });

  it('requires keyword when locationInput is visible', async () => {
    render(
      <SearchForm
        {...defaultProps}
        defaultValues={{
          careType: 'assisted-living',
          keyword: '',
          latLng: ''
        }}
      />
    );

    const form = screen.getByTestId('search-bar-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  describe('Map/List View Button', () => {
    it('renders Map button by default', () => {
      render(<SearchForm {...defaultProps} />);
      expect(screen.getByText('Map')).toBeInTheDocument();
    });

    it('renders List button when isMapViewEnabled is true', () => {
      render(<SearchForm {...defaultProps} isMapViewEnabled={true} />);
      expect(screen.getByText('List')).toBeInTheDocument();
    });

    it('toggles between Map and List view when clicked', async () => {
      const mockShowModal = jest.fn();
      const mockHideModal = jest.fn();

      (useModalDispatch as jest.Mock).mockImplementation(() => ({
        showModal: mockShowModal,
        hideModal: mockHideModal
      }));

      render(<SearchForm {...defaultProps} />);

      const mapButton = screen.getByText('Map');
      fireEvent.click(mapButton);

      await waitFor(() => {
        expect(mockShowModal).toHaveBeenCalledWith(MODAL.MAP_VIEW);
      });

      render(<SearchForm {...defaultProps} isMapViewEnabled={true} />);
      const listButton = screen.getByText('List');
      fireEvent.click(listButton);

      await waitFor(() => {
        expect(mockHideModal).toHaveBeenCalledWith(MODAL.MAP_VIEW);
      });
    });
  });

  it('calls onFocusChange with debounced behavior', async () => {
    jest.useFakeTimers();
    const mockOnFocusChange = jest.fn();

    render(<SearchForm {...defaultProps} onFocusChange={mockOnFocusChange} />);

    const form = screen.getByTestId('search-bar-form');
    const input = screen.getByTestId('autocomplete-input');

    // Simulate focus events
    act(() => {
      fireEvent.focus(form);
      fireEvent.focus(input);
      jest.advanceTimersByTime(150);
    });

    expect(mockOnFocusChange).toHaveBeenCalledWith(true);

    // Simulate blur event
    act(() => {
      fireEvent.blur(form, {
        relatedTarget: null
      });
      jest.advanceTimersByTime(150);
    });

    expect(mockOnFocusChange).toHaveBeenCalledWith(false);

    jest.useRealTimers();
  });

  it('cancels pending debounced calls on unmount', () => {
    jest.useFakeTimers();
    const mockOnFocusChange = jest.fn();

    const { unmount } = render(
      <SearchForm {...defaultProps} onFocusChange={mockOnFocusChange} />
    );

    const form = screen.getByTestId('search-bar-form');

    // Simulate focus event
    fireEvent.focus(form);
    unmount();

    jest.advanceTimersByTime(150);
    expect(mockOnFocusChange).not.toHaveBeenCalled();

    jest.useRealTimers();
  });
});
