import useSearchFormSubmission from '@components/SearchBar/useSearchFormSubmission';
import { fireEvent, render, screen, waitFor } from '@utils/test-utils';
import { useSearchParams } from 'next/navigation';

import SearchBar from './SearchBar';

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn()
}));

jest.mock('@components/SearchBar/useSearchFormSubmission', () => ({
  __esModule: true,
  default: jest.fn(() => jest.fn())
}));

jest.mock('./SearchForm', () => {
  return {
    __esModule: true,
    default: ({ onSubmit, defaultValues }) => (
      <form
        data-testid="search-form"
        onSubmit={(e) => {
          e.preventDefault();
          onSubmit(defaultValues);
        }}
      >
        <input type="submit" />
      </form>
    )
  };
});

beforeEach(() => {
  const mockAnalytics = {
    track: jest.fn()
  };
  window.tracking = mockAnalytics;
});

describe('SearchBar', () => {
  const mockOnSubmit = jest.fn();
  const mockTrackFormSubmission = jest.fn();

  const defaultProps = {
    displayLocationFilter: true,
    displayCareTypeFilter: true,
    searchBarId: 'test-search-bar',
    careTypes: [
      {
        id: 'assisted-living',
        slug: 'assisted-living',
        name: 'Assisted Living',
        rollUpType: 'Senior Living'
      },
      {
        id: 'memory-care',
        slug: 'memory-care',
        name: 'Memory Care',
        rollUpType: 'Senior Care'
      }
    ],
    onSubmit: mockOnSubmit,
    careType: 'assisted-living',
    city: 'Seattle',
    state: 'Washington',
    latitude: 47.6062,
    longitude: -122.3321,
    isMapViewEnabled: false
  };

  const mockSearchParams = new URLSearchParams({
    'care-type': 'assisted-living',
    keyword: 'Seattle, WA'
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    (useSearchFormSubmission as jest.Mock).mockReturnValue(
      mockTrackFormSubmission
    );
  });

  it('handles form submission correctly', async () => {
    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(mockTrackFormSubmission).toHaveBeenCalled();
      expect(mockOnSubmit).toHaveBeenCalledWith({
        keyword: 'Seattle, WA',
        careType: 'assisted-living',
        latLng: '47.6062,-122.3321'
      });
    });
  });

  it('passes correct default values to SearchForm', () => {
    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    expect(form).toBeInTheDocument();
    fireEvent.submit(form);
    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: 'Seattle, WA',
      careType: 'assisted-living',
      latLng: '47.6062,-122.3321'
    });
  });

  it('handles missing latitude and longitude', () => {
    const propsWithoutLatLng = {
      ...defaultProps,
      latitude: undefined,
      longitude: undefined
    };

    render(<SearchBar {...propsWithoutLatLng} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: 'Seattle, WA',
      careType: 'assisted-living',
      latLng: ''
    });
  });

  it('uses search params for default values when available', () => {
    const customSearchParams = new URLSearchParams({
      'care-type': 'memory-care',
      keyword: 'New York'
    });
    (useSearchParams as jest.Mock).mockReturnValue(customSearchParams);

    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: 'New York',
      careType: 'memory-care',
      latLng: '47.6062,-122.3321'
    });
  });

  it('handles empty search params gracefully', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());

    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: '',
      careType: '',
      latLng: '47.6062,-122.3321'
    });
  });

  it('handles null search params', () => {
    (useSearchParams as jest.Mock).mockReturnValue(null);

    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: '',
      careType: '',
      latLng: '47.6062,-122.3321'
    });
  });

  it('handles invalid latitude/longitude format', () => {
    const propsWithInvalidLatLng = {
      ...defaultProps,
      latitude: NaN,
      longitude: NaN
    };

    render(<SearchBar {...propsWithInvalidLatLng} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      keyword: 'Seattle, WA',
      careType: 'assisted-living',
      latLng: ''
    });
  });

  it('handles empty careTypes array', () => {
    const propsWithoutCareTypes = {
      ...defaultProps,
      careTypes: []
    };

    render(<SearchBar {...propsWithoutCareTypes} />);

    const form = screen.getByTestId('search-form');
    expect(form).toBeInTheDocument();
  });

  it('tracks form submission with correct parameters', () => {
    render(<SearchBar {...defaultProps} />);

    const form = screen.getByTestId('search-form');
    fireEvent.submit(form);

    expect(mockTrackFormSubmission).toHaveBeenCalledWith({
      keyword: 'Seattle, WA',
      careType: 'assisted-living',
      latLng: '47.6062,-122.3321'
    });
    expect(mockTrackFormSubmission).toHaveBeenCalledTimes(1);
  });
});
