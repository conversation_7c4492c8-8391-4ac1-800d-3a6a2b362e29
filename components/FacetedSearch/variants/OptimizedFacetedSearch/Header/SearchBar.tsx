import { Flex } from '@chakra-ui/layout';
import {
  CareTypeObject,
  CareTypeString
} from '@components/FacetedSearch/types';
import SearchForm, {
  SearchFormData
} from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/SearchForm';
import useSearchFormSubmission from '@components/SearchBar/useSearchFormSubmission';
import { SEARCH_PARAM } from '@constants/search-params';
import { useSearchParams } from 'next/navigation';
import { SubmitHandler } from 'react-hook-form';

interface SearchBarProps {
  searchBarId: string;
  careTypes: CareTypeObject[];
  onSubmit: (data: SearchFormData) => void;
  careType?: CareTypeString;
  city?: string;
  state?: string;
  latitude?: number;
  longitude?: number;
  isMapViewEnabled?: boolean;
  onFocusChange?: (focused: boolean) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  searchBarId,
  careTypes,
  onSubmit,
  careType,
  city,
  state,
  latitude,
  longitude,
  isMapViewEnabled,
  onFocusChange
}) => {
  const searchParams = useSearchParams();
  const searchParamsCareType = searchParams?.get(SEARCH_PARAM.CARE_TYPE) ?? '';
  const keyword = searchParams?.get(SEARCH_PARAM.KEYWORD) ?? '';
  const latLng =
    (latitude ?? undefined) && (longitude ?? undefined)
      ? `${latitude},${longitude}`
      : '';
  const locationPlaceholder = 'Enter City, State or Zip';
  const selectCarePlaceholder = 'Select Care level';

  const trackFormSubmission = useSearchFormSubmission({
    searchBarId,
    careTypeSelect: {
      placeholder: selectCarePlaceholder,
      visible: false
    },
    locationInput: {
      placeholder: locationPlaceholder,
      visible: true
    }
  });

  const handleSubmit: SubmitHandler<SearchFormData> = (data) => {
    trackFormSubmission(data);
    onSubmit(data);
  };

  return (
    <Flex direction={{ base: 'column', md: 'row' }} gap={2}>
      <SearchForm
        defaultValues={{
          keyword: keyword,
          careType: searchParamsCareType,
          latLng: latLng
        }}
        locationInput={{
          placeholder: locationPlaceholder
        }}
        careTypeSelect={{
          options: careTypes,
          placeholder: selectCarePlaceholder,
          visible: true
        }}
        onSubmit={handleSubmit}
        careType={careType}
        city={city}
        state={state}
        isMapViewEnabled={isMapViewEnabled}
        onFocusChange={onFocusChange}
      />
    </Flex>
  );
};

export default SearchBar;
