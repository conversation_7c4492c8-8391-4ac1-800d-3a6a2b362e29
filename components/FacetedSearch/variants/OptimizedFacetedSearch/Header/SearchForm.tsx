import { InputGroup, InputRightElement } from '@chakra-ui/input';
import { Box, Flex } from '@chakra-ui/layout';
import { Select } from '@chakra-ui/select';
import {
  ElementActions,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import AutocompleteInput from '@components/AutocompleteInput/AutocompleteInput';
import Button from '@components/Button';
import {
  CareTypeObject,
  CareTypeString
} from '@components/FacetedSearch/types';
import {
  getInitialKeyword,
  SelectCareType
} from '@components/SearchBar/SearchForm';
import { MODAL } from '@constants/modals';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { MdFmdGood, MdFormatListBulleted } from 'react-icons/md';
import { RiSearchLine } from 'react-icons/ri';
import { z as zod } from 'zod';

import { useModalDispatch } from '~/contexts/ModalContext';

const formSchema = zod.object({
  careType: zod.string().trim().min(1),
  keyword: zod.string().trim().min(1),
  latLng: zod.string().trim().min(1).catch('')
});

type DefaultFormData = zod.infer<typeof formSchema>;

export type SearchFormData = Partial<zod.infer<typeof formSchema>>;

interface SearchFormProps {
  careTypeSelect: {
    options: Array<CareTypeObject>;
    placeholder: string;
    visible: boolean;
  };
  defaultValues?: DefaultFormData;
  locationInput: {
    placeholder: string;
  };
  onSubmit: SubmitHandler<SearchFormData>;
  careType?: CareTypeString;
  city?: string;
  state?: string;
  isMapViewEnabled?: boolean;
  onFocusChange?: (focused: boolean) => void;
}

const SearchForm: React.FC<SearchFormProps> = ({
  careTypeSelect,
  defaultValues,
  locationInput,
  onSubmit,
  careType,
  city,
  state,
  isMapViewEnabled = false,
  onFocusChange
}) => {
  const { hideModal, showModal } = useModalDispatch();
  const [latLng, setLatLng] = useState(defaultValues?.latLng || '');
  const [isFormFocused, setIsFormFocused] = useState(false);

  const handleFocusChange = useCallback(
    (focused: boolean) => {
      setIsFormFocused(focused);
      onFocusChange?.(focused);
    },
    [setIsFormFocused, onFocusChange]
  );

  const debouncedFocusChange = useMemo(
    () => debounce(handleFocusChange, 150),
    [handleFocusChange]
  );

  useEffect(() => {
    return () => {
      debouncedFocusChange.cancel();
    };
  }, [debouncedFocusChange]);

  const handleFocusOut = (e: React.FocusEvent<HTMLElement>) => {
    const currentTarget = e.currentTarget;
    const relatedTarget = e.relatedTarget as Node | null;

    // If relatedTarget is null or not contained within the form, consider it a blur
    if (
      currentTarget &&
      (!relatedTarget || !currentTarget.contains(relatedTarget))
    ) {
      debouncedFocusChange(false);
    }
  };

  const initialKeyword = getInitialKeyword({
    preFillWithPageValues: true,
    city,
    state,
    defaultKeyword: defaultValues?.keyword
  });

  const { handleSubmit, register, setValue, watch } = useForm<SearchFormData>({
    defaultValues: {
      careType: careType || defaultValues?.careType,
      keyword: initialKeyword || defaultValues?.keyword,
      latLng: (defaultValues?.latLng || latLng || '') as string
    },
    mode: 'onBlur'
  });
  const selectedOption = watch('careType');
  useEffect(() => {
    if (latLng !== undefined && latLng !== null && latLng !== '') {
      setValue('latLng', latLng as string);
    }
  }, [latLng, setValue]);

  const setKeyword = (value: string) => setValue('keyword', value);

  return (
    <Flex
      as="form"
      gap={2}
      data-testid="search-bar-form"
      onSubmit={handleSubmit(onSubmit)}
      onFocus={() => debouncedFocusChange(true)}
      onBlur={handleFocusOut}
      width="100%"
      direction={{ base: 'column', lg: 'row' }}
    >
      <Flex
        flex={1}
        width="100%"
        position="relative"
        paddingBottom={{ base: isFormFocused ? 0 : 2, lg: 0 }}
      >
        <InputGroup alignItems="center" display="flex" zIndex={2}>
          <AutocompleteInput
            aria-label="search-location"
            background="white"
            defaultValue={initialKeyword}
            onFocus={(e) => {
              // Prevents the autocomplete list from opening when switching from list view to map view
              e.stopPropagation();
              debouncedFocusChange(true);
            }}
            isRequired={true}
            placeholder={locationInput.placeholder}
            setKeyword={setKeyword}
            setLatLng={setLatLng}
            height={{ base: '40px', lg: '48px' }}
            fontWeight="700"
            borderWidth="2px"
            minWidth="280px"
          />
          <InputRightElement height="100%">
            <RiSearchLine />
          </InputRightElement>
        </InputGroup>
      </Flex>
      <Flex
        width="100%"
        flex={isFormFocused ? 2 : 'unset'}
        gap={2}
        position={{ base: 'relative', lg: 'static' }}
        height={{ base: '56px', lg: '50px' }}
        display={{ base: isFormFocused ? 'flex' : 'none', lg: 'flex' }}
        paddingBottom={isFormFocused ? 2 : 0}
        zIndex={1}
      >
        <Select
          aria-label="search-care-type"
          background="white"
          {...register('careType')}
          color={selectedOption == '' ? 'gray.500' : 'primary'}
          textOverflow="ellipsis"
          fontWeight="700"
          borderWidth="2px"
          height={{ base: '40px', lg: '48px' }}
          width={{ base: '100%', lg: 'auto' }}
          onBlur={(e) => {
            if (window.innerWidth <= 768) {
              e.stopPropagation();
              debouncedFocusChange(true);
            }
          }}
        >
          <option disabled hidden value="">
            Select Care Type
          </option>
          {SelectCareType({
            careTypes: careTypeSelect.options as Array<{
              id: string;
              name: string;
              slug: string;
              rollUpType: string;
            }>,
            areCareTypesGrouped: true
          })}
        </Select>
        <Button
          aria-label="search-submit"
          colorScheme="secondary"
          elementAction={ElementActions.OPEN_MODAL}
          elementType={ElementTypes.BUTTON}
          name="search-button"
          textAlign="center"
          type="submit"
          height={{ base: '40px', lg: '48px' }}
          width={{ base: '90px', lg: 'auto' }}
          minWidth="66px"
        >
          <Box display={{ base: 'none', lg: 'block' }}>
            <RiSearchLine />
          </Box>
          <Box display={{ base: 'block', lg: 'none' }}>Search</Box>
        </Button>
        <Button
          aria-label="map-view-toggle"
          colorScheme="gray"
          name="map-button"
          backgroundColor="white"
          elementAction={ElementActions.OPEN_MODAL}
          elementType={ElementTypes.BUTTON}
          variant="outline"
          fontSize="lg"
          leftIcon={isMapViewEnabled ? <MdFormatListBulleted /> : <MdFmdGood />}
          iconSpacing={1}
          onClick={(e) => {
            e.stopPropagation();
            setTimeout(() => {
              isMapViewEnabled
                ? hideModal(MODAL.MAP_VIEW)
                : showModal(MODAL.MAP_VIEW);
            }, 0);
          }}
          borderWidth="2px"
          maxWidth="108px"
          paddingX={8}
          height={{ base: '40px', lg: '48px' }}
          display={{ base: 'none', lg: 'flex' }}
        >
          {isMapViewEnabled ? 'List' : 'Map'}
        </Button>
      </Flex>
    </Flex>
  );
};

export default SearchForm;
