'use client';

import { Card, CardHeader } from '@chakra-ui/card';
import { VStack } from '@chakra-ui/layout';
import { navigateToQuery } from '@components/FacetedSearch/navigation';
import { CareTypeObject } from '@components/FacetedSearch/types';
import SearchBar from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/SearchBar';
import { SearchFormData } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/SearchForm';
import { OptimizedFacetedSearchProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/OptimizedFacetedSearch';
import Heading from '@components/Heading';
import { SEARCH_PARAM } from '@constants/search-params';
import useSearch from '@hooks/useSearch';
import { CareType } from '@utils/faceted-search';
import { useRouter } from 'next/router';

import { Metadata } from '~/types/Magnolia';

export interface HeaderProps extends OptimizedFacetedSearchProps {
  latitude: string;
  longitude: string;
  careType: string;
  city?: string;
  state?: string;
  metadata: Metadata;
  careTypes: CareTypeObject[];
  isMapViewEnabled?: boolean;
  onFocusChange?: (focused: boolean) => void;
}

const Header = (props: HeaderProps) => {
  const {
    title,
    headingElement,
    titleSize,
    desktopTitleSize,
    titleAlignment,
    latitude,
    longitude,
    careType,
    careTypes,
    isMapViewEnabled,
    onFocusChange
  } = props;
  const { buildSearchUrl } = useSearch();
  const router = useRouter();

  const handleSubmit = async (formData: SearchFormData) => {
    try {
      const careType = formData.careType ?? CareType.AssistedLiving;
      const keyword = formData.keyword ?? '';

      if (props?.['@path']?.includes('local/search')) {
        const searchParams = new URLSearchParams({
          [SEARCH_PARAM.CARE_TYPE]: careType,
          [SEARCH_PARAM.KEYWORD]: keyword
        });
        await navigateToQuery(router, searchParams);
        return;
      }
      const searchPageUrl = buildSearchUrl({ careType, keyword });
      window.location.assign(searchPageUrl);
    } catch (error) {
      console.error('Search submission error:', error);
      window.location.assign('/local/search');
    }
  };

  return (
    <Card
      paddingBottom={0}
      paddingX={{ base: 4, md: 8 }}
      paddingTop={{ base: 4, lg: 8 }}
      background="transparent"
      data-testid="faceted-search-header"
    >
      <CardHeader>
        <VStack alignItems="left" spacing="5">
          {title && (
            <Heading
              as={headingElement}
              size={{ base: titleSize, md: desktopTitleSize }}
              textAlign={titleAlignment || 'left'}
              title={title}
              withContainer={false}
            />
          )}
          <SearchBar
            careTypes={careTypes}
            searchBarId={props.metadata?.['@id']}
            careType={careType}
            city={props.city}
            state={props.state}
            latitude={Number.isFinite(Number(latitude)) ? Number(latitude) : 0}
            longitude={
              Number.isFinite(Number(longitude)) ? Number(longitude) : 0
            }
            isMapViewEnabled={isMapViewEnabled}
            onFocusChange={onFocusChange}
            onSubmit={handleSubmit}
          />
        </VStack>
      </CardHeader>
    </Card>
  );
};

export default Header;
