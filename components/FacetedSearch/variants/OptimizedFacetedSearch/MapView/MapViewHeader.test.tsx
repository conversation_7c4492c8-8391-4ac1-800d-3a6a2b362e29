import { ChakraProvider } from '@chakra-ui/react';
import { FacetedSearchProvider } from '@components/FacetedSearch/FacetedSearchContext';
import { MODAL } from '@constants/modals';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';

import { HeadingSizes } from '~/@types/heading';
import { useModalDispatch } from '~/contexts/ModalContext';
import { CaringDomains } from '~/types/Domains';
import { Source } from '~/types/json-api-response';

import MapViewHeader from './MapViewHeader';

jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

jest.mock('~/contexts/ModalContext', () => ({
  useModalDispatch: jest.fn()
}));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0
    }
  }
});

describe('MapViewHeader', () => {
  const mockShowModal = jest.fn();
  const mockHideModal = jest.fn();

  const defaultProps = {
    searchOptions: [],
    filterTooltips: {},
    facetedSearchHeaderProps: {
      title: 'Test Title',
      headingElement: 'h1' as const,
      titleSize: 'xl' as HeadingSizes,
      desktopTitleSize: '2xl' as HeadingSizes,
      titleAlignment: 'left' as const,
      latitude: '47.6062',
      longitude: '-122.3321',
      careType: 'assisted-living',
      metadata: {
        '@id': 'test-search-bar-id'
      },
      careTypes: [],
      displayCompareOption: false,
      countOfTiles: '12',
      mapView: { enabled: false },
      infoButtonInquiryId: '',
      requestInfoButtonText: '',
      readMoreButton: 'Read More',
      linkText: 'View Details',
      visibleLimit: '10',
      filterAndSort: {
        phoneNumber: {}
      },
      providerPhoneNumberSource: {
        field: '',
        providerPhoneNumber: ''
      }
    },
    isMapViewEnabled: true
  };

  const renderMapViewHeader = (props = {}) => {
    return render(
      <ChakraProvider>
        <QueryClientProvider client={queryClient}>
          <FacetedSearchProvider
            initialData={undefined}
            query={{
              accessibility: [],
              awards: [],
              dining: [],
              distanceInMiles: 0,
              healthServices: [],
              languages: [],
              lifestyle: [],
              matchAllFilters: false,
              ongoingPromotion: [],
              otherAmenities: [],
              personalCare: [],
              priceRange: [0, 0],
              providersWith: [],
              reviews: [],
              roomAmenities: [],
              roomType: [],
              sortBy: 'best-rated',
              staffQualifications: [],
              verified: [],
              keyword: '',
              page: 0,
              careType: '',
              latLng: ''
            }}
            careTypes={[]}
            componentProps={{
              amenityCategory: '',
              careType: '',
              city: '',
              county: '',
              displayMode: 'list',
              distanceFilterEnabled: false,
              domain: CaringDomains.LIVE,
              latitude: '0',
              longitude: '0',
              resultsPerPage: 10,
              source: Source.ALGOLIA,
              state: '',
              shouldShowOnlyIndexedProviders: false
            }}
          >
            <MapViewHeader {...defaultProps} {...props} />
          </FacetedSearchProvider>
        </QueryClientProvider>
      </ChakraProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      query: {},
      pathname: '/',
      push: jest.fn(),
      replace: jest.fn(),
      asPath: '/',
      events: {
        on: jest.fn(),
        off: jest.fn()
      }
    }));
    (useModalDispatch as jest.Mock).mockReturnValue({
      showModal: mockShowModal,
      hideModal: mockHideModal
    });
  });

  it('renders mobile view with FacetedSearchHeader and Sidebar', () => {
    renderMapViewHeader();

    expect(
      screen.getByRole('heading', { name: 'Test Title' })
    ).toBeInTheDocument();

    const mobileContainer = screen.getByTestId('mobile-container');
    expect(mobileContainer).toHaveStyle({ display: 'block' });
  });

  it('renders desktop view with Back to List and Filters buttons', () => {
    renderMapViewHeader();

    const backButton = screen.getByText('Back to List');
    const filtersButton = screen.getByText('Filters');

    expect(backButton).toBeInTheDocument();
    expect(filtersButton).toBeInTheDocument();
  });

  it('calls hideModal when Back to List button is clicked', () => {
    renderMapViewHeader();

    const backButton = screen.getByText('Back to List');
    fireEvent.click(backButton);

    expect(mockHideModal).toHaveBeenCalledWith(MODAL.MAP_VIEW);
  });

  it('calls showModal when Filters button is clicked', () => {
    renderMapViewHeader();

    const filtersButton = screen.getByText('Filters');
    fireEvent.click(filtersButton);

    expect(mockShowModal).toHaveBeenCalledWith(MODAL.MAP_VIEW_FILTERS);
  });
  it('applies correct responsive styles to containers', () => {
    renderMapViewHeader();

    const mobileContainer = screen.getByTestId('mobile-container');
    const desktopContainer = screen.getByTestId('desktop-container');

    expect(mobileContainer).toHaveStyle({
      display: 'block'
    });

    expect(desktopContainer).toHaveStyle({
      display: 'none'
    });
  });

  it('renders buttons with correct icons', () => {
    renderMapViewHeader();

    const backIcon = screen.getByTestId('back-icon');
    const filterIcon = screen.getByTestId('filter-icon');

    expect(backIcon).toBeInTheDocument();
    expect(filterIcon).toBeInTheDocument();
  });
});
