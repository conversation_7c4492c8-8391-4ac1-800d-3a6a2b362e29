import { Box } from '@chakra-ui/layout';
import { Modal, ModalContent } from '@chakra-ui/modal';
import { SearchOption } from '@components/FacetedSearch/constants';
import { useFacetedSearchResult } from '@components/FacetedSearch/FacetedSearchContext';
import MapViewSidebar from '@components/FacetedSearch/MapView/MapViewSidebar';
import {
  parseProvider,
  Provider
} from '@components/FacetedSearch/MapView/parser';
import ProvidersMap from '@components/FacetedSearch/MapView/ProvidersMap';
import { HeaderProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/Header';
import MapViewHeader from '@components/FacetedSearch/variants/OptimizedFacetedSearch/MapView/MapViewHeader';
import { MODAL } from '@constants/modals';
import { useMemo } from 'react';

import { useModal } from '~/contexts/ModalContext';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { FilterTooltips } from '~/types/componentsConfig';

interface MapViewModalProps {
  initialMapOptions: {
    defaultCenter: google.maps.LatLngLiteral;
  };
  searchOptions: Array<SearchOption>;
  filterTooltips: FilterTooltips;
  facetedSearchHeaderProps: HeaderProps;
  isMapViewEnabled: boolean;
}

const MapViewModal: React.FC<MapViewModalProps> = ({
  initialMapOptions,
  searchOptions,
  filterTooltips,
  facetedSearchHeaderProps,
  isMapViewEnabled
}) => {
  const { data } = useFacetedSearchResult();
  const { getProviderDetailsPath } = useTenantFunctions();
  const { visible: isOpen, hide: closeModal } = useModal(MODAL.MAP_VIEW);

  const providersToDisplay = useMemo(() => {
    return (data?.results || [])
      .map((provider) =>
        parseProvider({
          ...provider,
          legacyId: provider.legacyId || provider.id,
          url: getProviderDetailsPath(provider)
        })
      )
      .filter(Boolean) as Array<Provider>;
  }, [data, getProviderDetailsPath]);

  return (
    <Modal isOpen={isOpen} size="full" onClose={closeModal}>
      <ModalContent>
        <MapViewHeader
          filterTooltips={filterTooltips}
          searchOptions={searchOptions}
          facetedSearchHeaderProps={facetedSearchHeaderProps}
          isMapViewEnabled={isMapViewEnabled}
        />
        <Box display={{ base: 'none', lg: 'block' }}>
          <MapViewSidebar
            searchOptions={searchOptions}
            filterTooltips={filterTooltips}
          />
        </Box>
        <ProvidersMap
          defaultCenter={initialMapOptions.defaultCenter}
          providers={providersToDisplay}
        />
      </ModalContent>
    </Modal>
  );
};

export default MapViewModal;
