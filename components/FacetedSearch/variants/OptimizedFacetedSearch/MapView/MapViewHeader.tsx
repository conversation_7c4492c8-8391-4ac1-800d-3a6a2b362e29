import { Icon } from '@chakra-ui/icons';
import { Box } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { SearchOption } from '@components/FacetedSearch/constants';
import FacetedSearchHeader, {
  HeaderProps
} from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/Header';
import Sidebar from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/Sidebar';
import Container from '@components/LayoutStructure/Container';
import { MODAL } from '@constants/modals';
import { MdArrowBack, MdTune } from 'react-icons/md';

import { useModalDispatch } from '~/contexts/ModalContext';
import { FilterTooltips } from '~/types/componentsConfig';

const MapViewHeader: React.FC<{
  searchOptions: Array<SearchOption>;
  filterTooltips: FilterTooltips;
  facetedSearchHeaderProps: HeaderProps;
  isMapViewEnabled: boolean;
}> = ({
  searchOptions,
  filterTooltips,
  facetedSearchHeaderProps,
  isMapViewEnabled
}) => {
  const { showModal, hideModal } = useModalDispatch();

  return (
    <>
      <Box
        data-testid="mobile-container"
        display={{ base: 'block', lg: 'none' }}
      >
        <FacetedSearchHeader
          {...{
            ...facetedSearchHeaderProps,
            isMapViewEnabled: isMapViewEnabled
          }}
        />
        <Box paddingX={4} paddingY={2}>
          <Sidebar
            searchOptions={searchOptions}
            filterTooltips={filterTooltips}
            isMapViewEnabled={isMapViewEnabled}
            data-testid="sidebar"
          />
        </Box>
      </Box>
      <Box
        data-testid="desktop-container"
        display={{ base: 'none', lg: 'block' }}
      >
        <Container
          background="white"
          gap="4"
          display="flex"
          py="3"
          justifyContent={{ base: 'center', lg: 'flex-start' }}
        >
          <Button
            colorScheme="primary"
            elementAction={ElementActions.CLOSE_MODAL}
            elementType={ElementTypes.BUTTON}
            fontSize={{ base: 'sm', lg: 'md' }}
            fontWeight="bold"
            leftIcon={
              <Icon data-testid="back-icon" as={MdArrowBack} boxSize="18px" />
            }
            size={{ base: 'sm', lg: 'lg' }}
            onClick={() => hideModal(MODAL.MAP_VIEW)}
          >
            Back to List
          </Button>
          <Button
            colorScheme="primary"
            elementAction={ElementActions.OPEN_MODAL}
            elementType={ElementTypes.BUTTON}
            fontSize={{ base: 'sm', lg: 'md' }}
            fontWeight="bold"
            leftIcon={
              <Icon data-testid="filter-icon" as={MdTune} boxSize="18px" />
            }
            size={{ base: 'sm', lg: 'lg' }}
            variant="outline"
            onClick={() => showModal(MODAL.MAP_VIEW_FILTERS)}
          >
            Filters
          </Button>
        </Container>
      </Box>
    </>
  );
};

export default MapViewHeader;
