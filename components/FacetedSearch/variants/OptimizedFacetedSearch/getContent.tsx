import { Community } from '@components/CommunityComparison/GeosCommunityComparison';
import ProviderCard from '@components/ProviderCard/V2';

import { Provider } from '~/contexts/Provider';
import { calculateProviderMinPrice } from '~/utils/providers';
import {
  contractIsSubscription,
  providerIsEnhancedAndNotSuppressed
} from '~/utils/providers';

export const getContent = (
  result: Provider,
  index,
  showPrice = true,
  displayBadges = true,
  getProviderDetailsPath,
  getProviderDescription,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton,
  requestInfoButtonText,
  readMoreButton,
  inquiryId,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor,
  ratingStarsColorRange,
  providerTitleColor,
  providerTitleColorRange,
  boxShadow,
  tileBorder,
  tileBorderColor,
  tileBorderColorRange,
  displayProviderPhoneNumber,
  providerPhoneNumberSource,
  dontOpenInNewTab,
  careType?: string,
  queryId?: string,
  listId?: string,
  isChecked?: boolean,
  displayCompareOption?: boolean,
  onSelectProvider?: (community: Community) => void,
  promotionColorScheme?: string
) => {
  const {
    legacyId,
    address,
    name,
    awards,
    isCaringStar,
    images,
    averageRating,
    reviewCount,
    id,
    promotions,
    hasActivePromotion,
    startPriceInCents,
    isVerified,
    lastReviewSnippet,
    isIndexed
  } = result;

  const minPrice = calculateProviderMinPrice({ result });
  const path = getProviderDetailsPath(result);
  const isSubscription = contractIsSubscription(result);
  const isEnhanced = providerIsEnhancedAndNotSuppressed(result);

  return (
    <ProviderCard
      key={index}
      id={id}
      isIndexed={isIndexed}
      legacyId={legacyId ?? id}
      title={name}
      address={address?.formattedAddress ?? ''}
      images={images ?? []}
      averageRating={averageRating}
      reviewCount={reviewCount}
      description={getProviderDescription(result)}
      price={startPriceInCents ?? minPrice}
      showPrice={showPrice}
      path={path}
      isChecked={isChecked}
      handleCompare={() => {
        if (onSelectProvider) {
          onSelectProvider({ index, ...result });
        }
      }}
      isFirstProvider={index === 0}
      caringStars={awards || []}
      isCaringStar={isCaringStar}
      displayBadges={displayBadges}
      displayLearnMoreButton={displayLearnMoreButton}
      learnMoreButtonText={learnMoreButtonText}
      displayRequestInfoButton={displayRequestInfoButton}
      requestInfoButtonText={requestInfoButtonText}
      readMoreButton={readMoreButton}
      modalId={inquiryId}
      requestInfoButtonColorScheme={requestInfoButtonColorScheme}
      learnMoreButtonColorScheme={learnMoreButtonColorScheme}
      ratingStarsColor={ratingStarsColor}
      ratingStarsColorRange={ratingStarsColorRange}
      providerTitleColor={providerTitleColor}
      providerTitleColorRange={providerTitleColorRange}
      boxShadow={boxShadow}
      border={tileBorder}
      borderColor={tileBorderColor}
      borderColorRange={tileBorderColorRange}
      displayProviderPhoneNumber={displayProviderPhoneNumber}
      providerPhoneNumberSource={providerPhoneNumberSource}
      phoneNumber={result.phoneNumber}
      promotions={promotions}
      hasActivePromotion={hasActivePromotion}
      legacyResourceId={
        result?.legacyResourceId ?? result.services?.[0]?.legacyResourceId
      }
      lastReviewSnippet={lastReviewSnippet}
      queryId={queryId}
      listId={listId}
      displayCompareOption={displayCompareOption}
      dontOpenInNewTab={dontOpenInNewTab}
      promotionColorScheme={promotionColorScheme}
      showVerifiedBadge={isVerified ?? (isSubscription || isEnhanced)}
      careTypes={result?.careTypes}
    />
  );
};
