import { Box, TextProps } from '@chakra-ui/layout';
import { Spinner } from '@chakra-ui/react';
import { filterNonReactAttributes } from '@utils/filterNonReactAttributes';

interface ResultCountProps extends TextProps {
  resultCount: number;
  isFetching: boolean;
}

const ResultCount = ({
  resultCount,
  isFetching,
  ...props
}: ResultCountProps) => {
  const textProps = filterNonReactAttributes(props);
  return (
    <Box
      paddingY={6}
      fontSize="sm"
      textAlign="left"
      fontWeight="400"
      borderBottom="1px solid"
      borderColor=" gray.300"
      {...textProps}
    >
      {isFetching ? (
        <Spinner size="xs" color="gray.500" data-testid="loading-spinner" />
      ) : (
        `${resultCount || 0} ${resultCount === 1 ? 'Provider' : 'Providers'}`
      )}
    </Box>
  );
};

export default ResultCount;
