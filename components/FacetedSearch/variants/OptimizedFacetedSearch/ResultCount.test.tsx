import { render, screen } from '@testing-library/react';

import ResultCount from './ResultCount';

describe('ResultCount', () => {
  const defaultProps = {
    resultCount: 0,
    isFetching: false
  };

  it('renders loading spinner when fetching', () => {
    render(<ResultCount {...defaultProps} isFetching={true} />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders singular form when result count is 1', () => {
    render(<ResultCount {...defaultProps} resultCount={1} />);
    expect(screen.getByText('1 Provider')).toBeInTheDocument();
  });

  it('handles null or undefined result count gracefully', () => {
    // @ts-expect-error Testing invalid prop type
    render(<ResultCount {...defaultProps} resultCount={null} />);
    expect(screen.getByText('0 Providers')).toBeInTheDocument();
  });

  it('renders plural form when result count is not 1', () => {
    render(<ResultCount {...defaultProps} resultCount={5} />);
    expect(screen.getByText('5 Providers')).toBeInTheDocument();
  });

  it('renders zero results correctly', () => {
    render(<ResultCount {...defaultProps} resultCount={0} />);
    expect(screen.getByText('0 Providers')).toBeInTheDocument();
  });
});
