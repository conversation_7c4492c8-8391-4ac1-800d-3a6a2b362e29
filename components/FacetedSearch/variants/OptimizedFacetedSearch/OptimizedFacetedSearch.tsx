'use client';

import { Box, Heading } from '@chakra-ui/layout';
import { SearchOption } from '@components/FacetedSearch/constants';
import { FacetedSearchProvider } from '@components/FacetedSearch/FacetedSearchContext';
import { SearchData } from '@components/FacetedSearch/types';
import { useFacetedSearchSetup } from '@components/FacetedSearch/useFacetedSearchSetup';
import FacetedSearchHeader from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Header/Header';
import FacetedSearchLayout from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Layout/FacetedSearchLayout';
import SidebarLayout from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Layout/SidebarLayout';
import MapViewModal from '@components/FacetedSearch/variants/OptimizedFacetedSearch/MapView/MapViewModal';
import ResultsList from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Results/Results';
import Sidebar from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/Sidebar';
import { useState } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import { PHONE_NUMBER } from '~/constants';
import { FilterTooltips, PhoneNumberConfig } from '~/types/componentsConfig';
import { Metadata } from '~/types/Magnolia';

export interface FilterAndSortDefaultProps {
  defaultValues?: {
    matchAllFilters?: boolean;
    shouldShowOnlyIndexedProviders?: boolean;
  };
}

export interface OptimizedFacetedSearchProps {
  displayCompareOption: boolean;
  countOfTiles: string;
  mapView: {
    enabled: boolean;
  };
  infoButtonInquiryId: string;
  readMoreButton: string;
  headingElement: HeadingElements;
  titleSize: HeadingSizes;
  title?: string;
  titleAlignment: 'left' | 'right' | 'center' | 'justify' | undefined;
  requestInfoButtonText: string;
  desktopTitleSize: string;
  linkText: string;
  visibleLimit: string;
  facetedSearch?: {
    amenityCategory: string;
    basePath: string;
  };
  filterAndSort: {
    filters?: Array<SearchOption>;
    phoneNumber: PhoneNumberConfig;
    filterTooltips?: FilterTooltips;
    defaultValues?: {
      matchAllFilters?: boolean;
      shouldShowOnlyIndexedProviders?: boolean;
    };
  };
  providerPhoneNumberSource: {
    field: string;
    providerPhoneNumber: string;
  };
}

const OptimizedFacetedSearch: React.FC<{
  optimizedFacetedSearch: OptimizedFacetedSearchProps;
  isMapViewEnabled?: boolean;
  data: SearchData;
  metadata: Metadata;
  filterAndSort: {
    defaultValues?: {
      matchAllFilters?: boolean;
      shouldShowOnlyIndexedProviders?: boolean;
    };
  };
}> = (props) => {
  if (props.optimizedFacetedSearch) {
    props.optimizedFacetedSearch.filterAndSort.phoneNumber = {
      label:
        props.optimizedFacetedSearch.filterAndSort?.phoneNumber?.label ??
        PHONE_NUMBER.label,
      number:
        props.optimizedFacetedSearch.filterAndSort?.phoneNumber?.number ??
        PHONE_NUMBER.number
    };
    props.optimizedFacetedSearch.filterAndSort.defaultValues = {
      matchAllFilters:
        props?.filterAndSort?.defaultValues?.matchAllFilters ?? false,
      shouldShowOnlyIndexedProviders:
        props?.filterAndSort?.defaultValues?.shouldShowOnlyIndexedProviders ??
        false
    };
  }

  const {
    mapCenter,
    searchProps,
    parsedConfig,
    searchParams,
    isMapViewEnabled,
    displayMode,
    domain,
    latitude,
    longitude
  } = useFacetedSearchSetup(props.optimizedFacetedSearch);
  const facetedSearchHeaderProps = {
    ...props.optimizedFacetedSearch,
    domain,
    displayMode: displayMode,
    careType: parsedConfig.searchOptions.careType ?? '',
    latitude: latitude,
    longitude: longitude,
    data: props.data,
    metadata: props.metadata,
    careTypes: props.data.careTypes
  };

  const [isFormFocused, setIsFormFocused] = useState(false);

  return (
    <Box
      id="faceted-search-tracker"
      scrollMarginTop={8}
      css={`
        container-type: inline-size;
      `}
    >
      <FacetedSearchProvider
        initialData={props.data}
        query={searchParams}
        componentProps={searchProps}
        careTypes={props.data.careTypes}
      >
        <FacetedSearchLayout
          header={
            <FacetedSearchHeader
              {...facetedSearchHeaderProps}
              onFocusChange={setIsFormFocused}
            />
          }
          isFormFocused={isFormFocused}
          sidebar={
            <SidebarLayout paddingX={4}>
              <SidebarLayout.Header
                display={{ base: 'none', lg: 'block' }}
                gap={0}
              >
                <Heading
                  as="h3"
                  size="lg"
                  paddingBottom={8}
                  display={{ base: 'none', lg: 'block' }}
                  borderBottom="1px solid"
                  borderColor=" gray.300"
                >
                  Filters
                </Heading>
              </SidebarLayout.Header>
              <SidebarLayout.Body gap={0}>
                <Sidebar
                  searchOptions={parsedConfig.searchSidebar.filters}
                  filterTooltips={
                    props.optimizedFacetedSearch?.filterAndSort
                      ?.filterTooltips ?? {}
                  }
                  isMapViewEnabled={props.isMapViewEnabled ?? false}
                />
              </SidebarLayout.Body>
            </SidebarLayout>
          }
          main={
            <ResultsList
              {...{
                ...props.optimizedFacetedSearch,
                careType: searchProps.careType,
                facetedSearch: parsedConfig.amenityCategoryChips
              }}
            />
          }
        />

        {isMapViewEnabled ? (
          <MapViewModal
            searchOptions={parsedConfig.searchSidebar.filters}
            initialMapOptions={{
              defaultCenter: mapCenter
            }}
            filterTooltips={
              props.optimizedFacetedSearch?.filterAndSort?.filterTooltips ?? {}
            }
            facetedSearchHeaderProps={facetedSearchHeaderProps}
            isMapViewEnabled={isMapViewEnabled}
          />
        ) : null}
      </FacetedSearchProvider>
    </Box>
  );
};

export default OptimizedFacetedSearch;
