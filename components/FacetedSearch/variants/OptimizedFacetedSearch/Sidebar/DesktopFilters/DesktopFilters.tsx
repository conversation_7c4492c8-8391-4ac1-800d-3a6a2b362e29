import SelectedFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/SelectedFilters';
import useFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters';
import { AccordionFilters } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/DesktopFilters/AccordionFilters';
import { FilterProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import SortBy from '@components/SortBy';
import { SortingBy } from '@services/modular-monolith/types/search.type';

const DesktopFilters: React.FC<FilterProps> = ({
  filterTooltips,
  searchOptions
}) => {
  const { accordionItems, query, updateQuery } = useFilters({
    filterTooltips: filterTooltips
  });
  const handleSortByChange = (value: SortingBy) => {
    updateQuery({ key: 'sortBy', value });
  };

  return (
    <>
      <SelectedFilters />
      <SortBy
        onChange={handleSortByChange}
        value={query.sortBy}
        textProps={{ as: 'p' }}
      />
      <AccordionFilters
        accordionItems={accordionItems}
        searchOptions={searchOptions}
      />
    </>
  );
};

export default DesktopFilters;
