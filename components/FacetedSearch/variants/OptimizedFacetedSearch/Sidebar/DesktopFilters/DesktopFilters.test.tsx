import { SearchOption } from '@components/FacetedSearch/constants';
import useFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters';
import { fireEvent, render, screen } from '@testing-library/react';

import DesktopFilters from './DesktopFilters';

jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters'
);
jest.mock('@components/SortBy', () => ({
  __esModule: true,
  default: ({
    onChange,
    value
  }: {
    onChange: (value: string) => void;
    value: string;
  }) => (
    <select
      data-testid="sort-by"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      <option value="best-rated">Best Rated</option>
      <option value="distance">Distance</option>
    </select>
  )
}));
jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/SelectedFilters',
  () => ({
    __esModule: true,
    default: () => <div data-testid="selected-filters">Selected Filters</div>
  })
);
jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/DesktopFilters/AccordionFilters',
  () => ({
    AccordionFilters: ({
      accordionItems,
      searchOptions
    }: {
      accordionItems: any[];
      searchOptions: string[];
    }) => (
      <div data-testid="accordion-filters">
        Accordion Filters: {accordionItems.length} items, {searchOptions.length}{' '}
        options
      </div>
    )
  })
);

describe('DesktopFilters', () => {
  const mockUpdateQuery = jest.fn();
  const defaultProps = {
    filterTooltips: {
      verifiedInfo: 'Verified tooltip',
      awardsInfo: 'Awards tooltip'
    },
    searchOptions: ['verified', 'awards'] as SearchOption[]
  };

  const mockUseFilters = {
    accordionItems: [
      { id: 'verified', label: 'Verified' },
      { id: 'awards', label: 'Awards' }
    ],
    query: {
      sortBy: 'best-rated'
    },
    updateQuery: mockUpdateQuery
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useFilters as jest.Mock).mockReturnValue(mockUseFilters);
  });

  it('renders all components correctly', () => {
    render(<DesktopFilters {...defaultProps} />);

    expect(screen.getByTestId('selected-filters')).toBeInTheDocument();
    expect(screen.getByTestId('sort-by')).toBeInTheDocument();
    expect(screen.getByTestId('accordion-filters')).toBeInTheDocument();
  });

  it('initializes useFilters with correct props', () => {
    render(<DesktopFilters {...defaultProps} />);

    expect(useFilters).toHaveBeenCalledWith({
      filterTooltips: defaultProps.filterTooltips
    });
  });

  it('passes correct props to AccordionFilters', () => {
    render(<DesktopFilters {...defaultProps} />);

    const accordionFilters = screen.getByTestId('accordion-filters');
    expect(accordionFilters).toHaveTextContent(
      `Accordion Filters: ${mockUseFilters.accordionItems.length} items, ${defaultProps.searchOptions.length} options`
    );
  });

  it('handles sort-by changes correctly', () => {
    render(<DesktopFilters {...defaultProps} />);

    const sortBy = screen.getByTestId('sort-by');
    fireEvent.change(sortBy, { target: { value: 'distance' } });

    expect(mockUpdateQuery).toHaveBeenCalledWith({
      key: 'sortBy',
      value: 'distance'
    });
  });

  it('renders with empty searchOptions', () => {
    render(<DesktopFilters {...defaultProps} searchOptions={[]} />);

    const accordionFilters = screen.getByTestId('accordion-filters');
    expect(accordionFilters).toHaveTextContent(
      'Accordion Filters: 2 items, 0 options'
    );
  });

  it('renders with empty filterTooltips', () => {
    render(<DesktopFilters {...defaultProps} filterTooltips={{}} />);

    expect(useFilters).toHaveBeenCalledWith({
      filterTooltips: {}
    });
  });
});
