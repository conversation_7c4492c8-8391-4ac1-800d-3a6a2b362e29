import { Box } from '@chakra-ui/react';
import Accordion from '@components/Accordion';
import { SearchOption } from '@components/FacetedSearch/constants';
import { AccordionItem } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';

export const AccordionFilters: React.FC<{
  accordionItems: AccordionItem[];
  searchOptions: Array<SearchOption>;
}> = ({ accordionItems, searchOptions }) => {
  return (
    <Box flex="1" overflow="auto" width="100%">
      <Accordion
        openIndexes={[]}
        items={accordionItems.filter((item) => searchOptions.includes(item.id))}
        accordionButtonProps={{ paddingY: 6, paddingX: 0 }}
      />
    </Box>
  );
};
