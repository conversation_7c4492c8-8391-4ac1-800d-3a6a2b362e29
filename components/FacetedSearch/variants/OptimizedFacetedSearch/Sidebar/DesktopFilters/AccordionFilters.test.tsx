import { SearchOption } from '@components/FacetedSearch/constants';
import { render, screen } from '@testing-library/react';

import { AccordionFilters } from './AccordionFilters';

jest.mock('@components/Accordion', () => {
  return function MockAccordion({ items }: { items: any[] }) {
    return <div data-testid="mock-accordion">{items.length} items</div>;
  };
});

describe('AccordionFilters', () => {
  const mockAccordionItems = [
    {
      id: 'verified' as SearchOption,
      label: 'Verified Partners',
      children: <div data-testid="verified-filter" />
    },
    {
      id: 'lifestyle' as SearchOption,
      label: 'Lifestyle',
      children: <div data-testid="lifestyle-filter" />
    },
    {
      id: 'reviews' as SearchOption,
      label: 'Reviews',
      children: <div data-testid="reviews-filter" />
    },
    {
      id: 'roomAmenities' as SearchOption,
      label: 'Room Amenities',
      children: <div data-testid="room-amenities-filter" />
    }
  ];

  const defaultSearchOptions = [
    'verified',
    'lifestyle',
    'reviews',
    'roomAmenities'
  ] as SearchOption[];

  it('renders all filters when all search options are provided', () => {
    render(
      <AccordionFilters
        accordionItems={mockAccordionItems}
        searchOptions={defaultSearchOptions}
      />
    );
    expect(screen.getByTestId('mock-accordion')).toBeInTheDocument();
    expect(screen.getByText('4 items')).toBeInTheDocument();
  });

  it('filters out accordion items not included in searchOptions', () => {
    const limitedSearchOptions = ['verified', 'lifestyle'] as SearchOption[];
    render(
      <AccordionFilters
        accordionItems={mockAccordionItems}
        searchOptions={limitedSearchOptions}
      />
    );
    expect(screen.getByText('2 items')).toBeInTheDocument();
  });

  it('renders no filters when searchOptions is empty', () => {
    render(
      <AccordionFilters
        accordionItems={mockAccordionItems}
        searchOptions={[]}
      />
    );
    expect(screen.getByText('0 items')).toBeInTheDocument();
  });

  it('handles non-matching search options gracefully', () => {
    const nonMatchingOptions = ['nonexistent' as unknown as SearchOption];
    render(
      <AccordionFilters
        accordionItems={mockAccordionItems}
        searchOptions={nonMatchingOptions}
      />
    );
    expect(screen.getByText('0 items')).toBeInTheDocument();
  });

  it('handles empty accordionItems array', () => {
    render(
      <AccordionFilters
        accordionItems={[]}
        searchOptions={defaultSearchOptions}
      />
    );
    expect(screen.getByText('0 items')).toBeInTheDocument();
  });
});
