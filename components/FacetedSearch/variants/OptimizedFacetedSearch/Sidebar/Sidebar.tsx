import { useMediaQuery } from '@chakra-ui/react';
import { SearchOption } from '@components/FacetedSearch/constants';
import DesktopFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/DesktopFilters';
import MobileFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters';

import { FilterTooltips } from '~/types/componentsConfig';

interface SidebarProps {
  searchOptions: Array<SearchOption>;
  filterTooltips: FilterTooltips;
  isMapViewEnabled?: boolean;
}

const Sidebar: React.FC<SidebarProps> = (props) => {
  const [isDesktopView] = useMediaQuery('(min-width: 992px)');

  return isDesktopView ? (
    <DesktopFilters
      filterTooltips={props.filterTooltips}
      searchOptions={props.searchOptions}
    />
  ) : (
    <MobileFilters
      filterTooltips={props.filterTooltips}
      searchOptions={props.searchOptions}
      isMapViewEnabled={props.isMapViewEnabled}
    />
  );
};

export default Sidebar;
