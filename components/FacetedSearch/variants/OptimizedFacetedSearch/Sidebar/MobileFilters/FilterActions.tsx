import { Flex } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { useFacetedSearchActions } from '@components/FacetedSearch/FacetedSearchContext';

const FilterActions: React.FC<{
  closeAllFilters: () => void;
}> = ({ closeAllFilters }) => {
  const { resetQuery } = useFacetedSearchActions();
  const handleClearFilters = () => {
    closeAllFilters();
    resetQuery();
  };
  return (
    <Flex
      position="sticky"
      bottom={0}
      bg="white"
      gap={4}
      width="100%"
      paddingBottom={4}
    >
      <Button
        colorScheme="primary"
        variant="outline"
        height="32px"
        width="full"
        fontSize="12px"
        onClick={() => {
          handleClearFilters();
        }}
        elementAction={ElementActions.CLEAR_ALL_FILTERS}
        elementName={ElementNames.CLEAR_ALL_FILTERS}
        elementType={ElementTypes.BUTTON}
      >
        Clear
      </Button>
      <Button
        colorScheme="primary"
        height="32px"
        width="full"
        fontSize="12px"
        onClick={closeAllFilters}
        elementAction={ElementActions.APPLY_FILTERS}
        elementName={ElementNames.APPLY_FILTERS}
        elementType={ElementTypes.BUTTON}
      >
        Apply
      </Button>
    </Flex>
  );
};

export default FilterActions;
