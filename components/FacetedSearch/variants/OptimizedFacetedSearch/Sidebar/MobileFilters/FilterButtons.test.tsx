// We don't need MODAL import anymore as we're testing the handleFilterClick call
// import { MODAL } from '@constants/modals';
import { fireEvent, render, screen } from '@testing-library/react';

import { useModalDispatch } from '~/contexts/ModalContext';

import { FilterButtons } from './FilterButtons';

jest.mock('~/contexts/ModalContext', () => ({
  useModalDispatch: jest.fn()
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {}
  }))
}));

describe('FilterButtons', () => {
  const defaultProps = {
    activeFilter: null as any,
    isMapViewEnabled: false,
    handleFilterClick: jest.fn()
  };

  const mockModalDispatch = {
    showModal: jest.fn(),
    hideModal: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useModalDispatch as jest.Mock).mockReturnValue(mockModalDispatch);

    // Mock document.activeElement and its blur method
    Object.defineProperty(document, 'activeElement', {
      writable: true,
      value: {
        blur: jest.fn(),
        tagName: 'INPUT'
      }
    });
  });

  afterEach(() => {
    // Reset document.activeElement mock
    jest.restoreAllMocks();
  });

  describe('Rendering', () => {
    it('renders all filter buttons', () => {
      render(<FilterButtons {...defaultProps} />);

      expect(screen.getByText('Rating')).toBeInTheDocument();
      expect(screen.getByText('All Filters')).toBeInTheDocument();
      expect(screen.getByText('Sort')).toBeInTheDocument();
      expect(screen.getByText('Map')).toBeInTheDocument();
    });

    it('applies correct min-width to buttons', () => {
      render(<FilterButtons {...defaultProps} />);

      const ratingButton = screen.getByText('Rating').closest('button');
      const allFiltersButton = screen
        .getByText('All Filters')
        .closest('button');
      const sortButton = screen.getByText('Sort').closest('button');
      const mapButton = screen.getByText('Map').closest('button');

      expect(ratingButton).toHaveStyle({ minWidth: '65px' });
      expect(allFiltersButton).toHaveStyle({ minWidth: '90px' });
      expect(sortButton).toHaveStyle({ minWidth: '55px' });
      expect(mapButton).toHaveStyle({ minWidth: '30px' });
    });

    it('renders correct icons for each button', () => {
      render(<FilterButtons {...defaultProps} />);

      const allFiltersButton = screen.getByText('All Filters').parentElement;
      expect(allFiltersButton?.querySelector('svg')).toBeInTheDocument();

      const sortButton = screen.getByText('Sort').parentElement;
      expect(sortButton?.querySelector('svg')).toBeInTheDocument();

      const mapButton = screen.getByText('Map').parentElement;
      expect(mapButton?.querySelector('svg')).toBeInTheDocument();
    });
  });

  describe('Active States', () => {
    it('highlights active rating filter', () => {
      render(<FilterButtons {...defaultProps} activeFilter="rating" />);
      const ratingButton = screen.getByText('Rating').closest('button');
      expect(ratingButton).toHaveAttribute('aria-expanded', 'true');
    });

    it('highlights active all filters', () => {
      render(<FilterButtons {...defaultProps} activeFilter="all" />);
      const allFiltersButton = screen
        .getByText('All Filters')
        .closest('button');
      expect(allFiltersButton).toHaveAttribute('aria-expanded', 'true');
    });

    it('highlights active sort filter', () => {
      render(<FilterButtons {...defaultProps} activeFilter="sort" />);
      const sortButton = screen.getByText('Sort').closest('button');
      expect(sortButton).toHaveAttribute('aria-expanded', 'true');
    });

    it('highlights active map filter', () => {
      render(<FilterButtons {...defaultProps} activeFilter="map" />);
      const mapButton = screen.getByText('Map').closest('button');
      expect(mapButton).toHaveAttribute('aria-expanded', 'true');
    });
  });

  describe('Click Handlers', () => {
    it('calls handleFilterClick with "rating" when rating button is clicked', () => {
      render(<FilterButtons {...defaultProps} />);
      fireEvent.click(screen.getByText('Rating'));
      expect(defaultProps.handleFilterClick).toHaveBeenCalledWith('rating');
    });

    it('calls handleFilterClick with "all" when all filters button is clicked', () => {
      render(<FilterButtons {...defaultProps} />);
      fireEvent.click(screen.getByText('All Filters'));
      expect(defaultProps.handleFilterClick).toHaveBeenCalledWith('all');
    });

    it('calls handleFilterClick with "sort" when sort button is clicked', () => {
      render(<FilterButtons {...defaultProps} />);
      fireEvent.click(screen.getByText('Sort'));
      expect(defaultProps.handleFilterClick).toHaveBeenCalledWith('sort');
    });
  });

  describe('Map View Toggle', () => {
    it('shows "Map" text and MdFmdGood icon when isMapViewEnabled is false', () => {
      render(<FilterButtons {...defaultProps} isMapViewEnabled={false} />);
      expect(screen.getByText('Map')).toBeInTheDocument();
    });

    it('shows "List" text and MdFormatListBulleted icon when isMapViewEnabled is true', () => {
      render(<FilterButtons {...defaultProps} isMapViewEnabled={true} />);
      expect(screen.getByText('List')).toBeInTheDocument();
    });

    it('calls showModal when map button is clicked and isMapViewEnabled is false', async () => {
      jest.useFakeTimers();
      render(<FilterButtons {...defaultProps} isMapViewEnabled={false} />);
      fireEvent.click(screen.getByText('Map'));
      jest.runAllTimers();

      expect(defaultProps.handleFilterClick).toHaveBeenCalledWith('map');
      jest.useRealTimers();
    });

    it('calls hideModal when map button is clicked and isMapViewEnabled is true', async () => {
      jest.useFakeTimers();
      render(<FilterButtons {...defaultProps} isMapViewEnabled={true} />);
      fireEvent.click(screen.getByText('List'));
      jest.runAllTimers();

      expect(defaultProps.handleFilterClick).toHaveBeenCalledWith('map');
      jest.useRealTimers();
    });
  });
});
