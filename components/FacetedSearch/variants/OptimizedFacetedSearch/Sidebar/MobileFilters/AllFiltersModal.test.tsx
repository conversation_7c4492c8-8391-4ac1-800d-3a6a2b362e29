import { SearchOption } from '@components/FacetedSearch/constants';
import {
  AccordionItem,
  ActiveFilter
} from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import { fireEvent, render, screen } from '@testing-library/react';

import { AllFiltersModal } from './AllFiltersModal';

jest.mock('@components/Accordion', () => ({
  __esModule: true,
  default: ({ items }: { items: AccordionItem[] }) => (
    <div data-testid="mock-accordion">
      {items.map((item) => (
        <div key={item.id} data-testid={`accordion-item-${item.id}`}>
          {item.label}
        </div>
      ))}
    </div>
  )
}));

jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/SelectedFilters',
  () => ({
    __esModule: true,
    default: () => <div data-testid="selected-filters">Selected Filters</div>
  })
);

jest.mock('./FilterActions', () => ({
  __esModule: true,
  default: ({ closeAllFilters }: { closeAllFilters: () => void }) => (
    <div data-testid="filter-actions">
      <button onClick={closeAllFilters}>Mock Filter Actions</button>
    </div>
  )
}));

describe('AllFiltersModal', () => {
  const mockAccordionItems: AccordionItem[] = [
    {
      id: 'awards' as SearchOption,
      label: 'Awards',
      children: <div>Awards Content</div>
    },
    {
      id: 'rating' as SearchOption,
      label: 'Rating',
      children: <div>Rating Content</div>
    },
    {
      id: 'amenities' as SearchOption,
      label: 'Amenities',
      children: <div>Amenities Content</div>
    }
  ];

  const defaultProps = {
    activeFilter: 'all' as ActiveFilter,
    accordionItems: mockAccordionItems,
    searchOptions: ['awards', 'rating', 'amenities'] as SearchOption[],
    closeAllFilters: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the modal when activeFilter is "all"', () => {
      render(<AllFiltersModal {...defaultProps} />);

      expect(screen.getByText('All Filters')).toBeInTheDocument();
      expect(screen.getByTestId('mock-accordion')).toBeInTheDocument();
      expect(screen.getByTestId('selected-filters')).toBeInTheDocument();
      expect(screen.getByTestId('filter-actions')).toBeInTheDocument();
    });

    it('does not render the modal when activeFilter is not "all"', () => {
      render(<AllFiltersModal {...defaultProps} activeFilter="rating" />);

      expect(screen.queryByText('All Filters')).not.toBeInTheDocument();
    });

    it('filters accordion items based on searchOptions', () => {
      render(
        <AllFiltersModal
          {...defaultProps}
          searchOptions={['awards', 'rating'] as SearchOption[]}
        />
      );

      expect(screen.getByTestId('accordion-item-awards')).toBeInTheDocument();
      expect(screen.getByTestId('accordion-item-rating')).toBeInTheDocument();
      expect(
        screen.queryByTestId('accordion-item-amenities')
      ).not.toBeInTheDocument();
    });
  });

  describe('Modal Header', () => {
    it('renders with correct styling', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const header = screen.getByText('All Filters').closest('header');
      expect(header).toHaveStyle({
        position: 'sticky',
        top: '0',
        zIndex: 'sticky',
        backgroundColor: 'white'
      });
    });

    it('includes close button', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const closeButton = screen.getByRole('button', { name: /close/i });
      expect(closeButton).toBeInTheDocument();
    });

    it('renders SelectedFilters with correct props', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const selectedFilters = screen.getByTestId('selected-filters');
      expect(selectedFilters).toBeInTheDocument();
      // Verify the parent element has the correct styles
      const parentElement = selectedFilters.parentElement;
      expect(parentElement).toHaveStyle({
        position: 'sticky',
        top: '0',
        backgroundColor: 'white'
      });
    });
  });

  describe('Modal Footer', () => {
    it('includes FilterActions component', () => {
      render(<AllFiltersModal {...defaultProps} />);

      expect(screen.getByTestId('filter-actions')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls closeAllFilters when modal close button is clicked', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);
      expect(defaultProps.closeAllFilters).toHaveBeenCalledTimes(1);
    });

    it('calls closeAllFilters through FilterActions', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const filterActionsButton = screen.getByText('Mock Filter Actions');
      fireEvent.click(filterActionsButton);
      expect(defaultProps.closeAllFilters).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('has correct ARIA attributes', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
    });

    it('maintains focus trap within modal', () => {
      render(<AllFiltersModal {...defaultProps} />);

      const closeButton = screen.getByRole('button', { name: /close/i });
      const filterActionsButton = screen.getByText('Mock Filter Actions');

      closeButton.focus();
      expect(closeButton).toHaveFocus();

      filterActionsButton.focus();
      expect(filterActionsButton).toHaveFocus();
    });

    it('closes on Escape key press', () => {
      render(<AllFiltersModal {...defaultProps} />);

      fireEvent.keyDown(screen.getByRole('dialog'), {
        key: 'Escape',
        code: 'Escape'
      });
      expect(defaultProps.closeAllFilters).toHaveBeenCalledTimes(1);
    });
  });
});
