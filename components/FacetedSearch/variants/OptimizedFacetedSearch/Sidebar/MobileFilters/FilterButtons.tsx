import { Flex } from '@chakra-ui/react';
import { useFilterAndSortStats } from '@components/FacetedSearch/FacetedSearchContext';
import FilterButton from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/FilterButton';
import { ActiveFilter } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import { useCallback } from 'react';
import {
  MdFmdGood,
  MdFormatListBulleted,
  MdSyncAlt,
  MdTune
} from 'react-icons/md';

interface FilterButtonsProps {
  activeFilter: ActiveFilter;
  isMapViewEnabled?: boolean;
  handleFilterClick: (filter: ActiveFilter) => void;
}

export const FilterButtons: React.FC<FilterButtonsProps> = ({
  activeFilter,
  isMapViewEnabled,
  handleFilterClick
}) => {
  const { isFiltering, isFilteringMap, isSorting } = useFilterAndSortStats();

  const onRatingClick = useCallback(
    () => handleFilterClick('rating'),
    [handleFilterClick]
  );
  const onAllFiltersClick = useCallback(
    () => handleFilterClick('all'),
    [handleFilterClick]
  );

  const onSortClick = useCallback(
    () => handleFilterClick('sort'),
    [handleFilterClick]
  );
  const onMapClick = useCallback(
    () => handleFilterClick('map'),
    [handleFilterClick]
  );

  return (
    <Flex
      gap={2}
      flexWrap="wrap"
      paddingX={{ base: 0, md: 4 }}
      paddingBottom={{ base: 2, lg: 0 }}
    >
      <FilterButton
        isActive={activeFilter === 'rating' || isFilteringMap.reviews}
        onClick={onRatingClick}
        minWidth="65px"
      >
        Rating
      </FilterButton>
      <FilterButton
        isActive={activeFilter === 'all' || isFiltering}
        onClick={onAllFiltersClick}
        leftIcon={<MdTune />}
        minWidth="90px"
      >
        All Filters
      </FilterButton>
      <FilterButton
        isActive={activeFilter === 'sort' || isSorting}
        onClick={onSortClick}
        leftIcon={<MdSyncAlt style={{ transform: 'rotate(90deg)' }} />}
        minWidth="55px"
      >
        Sort
      </FilterButton>
      <FilterButton
        isActive={activeFilter === 'map'}
        leftIcon={isMapViewEnabled ? <MdFormatListBulleted /> : <MdFmdGood />}
        onClick={onMapClick}
        minWidth="30px"
      >
        {isMapViewEnabled ? 'List' : 'Map'}
      </FilterButton>
    </Flex>
  );
};

export default FilterButtons;
