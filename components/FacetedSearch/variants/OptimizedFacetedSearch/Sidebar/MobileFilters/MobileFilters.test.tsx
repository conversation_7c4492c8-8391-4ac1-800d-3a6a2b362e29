import useFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters';
import { FilterProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import { fireEvent, render, screen } from '@testing-library/react';

import MobileFilters from './MobileFilters';

jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters'
);
jest.mock('./FilterButtons', () => ({
  FilterButtons: ({
    activeFilter,
    handleFilterClick,
    isMapViewEnabled
  }: any) => (
    <div data-testid="filter-buttons">
      <button
        data-testid="rating-button"
        onClick={() => handleFilterClick('rating')}
      >
        Rating
      </button>
      <button
        data-testid="sort-button"
        onClick={() => handleFilterClick('sort')}
      >
        Sort
      </button>
      <button
        data-testid="all-filters-button"
        onClick={() => handleFilterClick('all')}
      >
        All Filters
      </button>
      <button data-testid="map-button" onClick={() => handleFilterClick('map')}>
        Map
      </button>
    </div>
  )
}));

jest.mock('./SortPanel', () => ({
  SortPanel: ({ query, handleSortByChange, closeAllFilters }: any) => (
    <div data-testid="sort-panel">
      <select
        data-testid="sort-select"
        value={query.sortBy}
        onChange={(e) => handleSortByChange(e.target.value)}
      >
        <option value="distance">Distance</option>
        <option value="best-rated">Best Rated</option>
      </select>
      <button onClick={closeAllFilters}>Close</button>
    </div>
  )
}));

jest.mock('./RatingPanel', () => ({
  RatingPanel: ({ query, handleInputChange, closeAllFilters }: any) => (
    <div data-testid="rating-panel">
      <select
        data-testid="rating-select"
        value={query.reviews}
        onChange={(e) => handleInputChange('reviews', e.target.value)}
      >
        <option value="">All</option>
        <option value="4">4+</option>
      </select>
      <button onClick={closeAllFilters}>Close</button>
    </div>
  )
}));

jest.mock('./AllFiltersModal', () => ({
  AllFiltersModal: ({
    activeFilter,
    accordionItems,
    searchOptions,
    closeAllFilters
  }: any) => (
    <div data-testid="all-filters-modal">
      {activeFilter === 'all' && (
        <div>
          <span>Filters Modal</span>
          <button onClick={closeAllFilters}>Close</button>
        </div>
      )}
    </div>
  )
}));

describe('MobileFilters', () => {
  const mockUseFilters = {
    accordionItems: [],
    query: {
      sortBy: 'distance',
      reviews: ''
    },
    updateQuery: jest.fn()
  };

  const defaultProps: FilterProps = {
    filterTooltips: {},
    searchOptions: [],
    isMapViewEnabled: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useFilters as jest.Mock).mockReturnValue(mockUseFilters);
  });

  it('renders without crashing', () => {
    render(<MobileFilters {...defaultProps} />);
    expect(screen.getByTestId('filter-buttons')).toBeInTheDocument();
  });

  it('initializes with no active filter', () => {
    render(<MobileFilters {...defaultProps} />);
    expect(screen.queryByTestId('sort-panel')).not.toBeInTheDocument();
    expect(screen.queryByTestId('rating-panel')).not.toBeInTheDocument();
  });

  describe('Filter Button Interactions', () => {
    it('toggles rating panel when rating button is clicked', () => {
      render(<MobileFilters {...defaultProps} />);

      const ratingButton = screen.getByTestId('rating-button');
      fireEvent.click(ratingButton);
      expect(screen.getByTestId('rating-panel')).toBeInTheDocument();

      fireEvent.click(ratingButton);
      expect(screen.queryByTestId('rating-panel')).not.toBeInTheDocument();
    });

    it('toggles sort panel when sort button is clicked', () => {
      render(<MobileFilters {...defaultProps} />);

      const sortButton = screen.getByTestId('sort-button');
      fireEvent.click(sortButton);
      expect(screen.getByTestId('sort-panel')).toBeInTheDocument();

      fireEvent.click(sortButton);
      expect(screen.queryByTestId('sort-panel')).not.toBeInTheDocument();
    });

    it('toggles all filters modal when all filters button is clicked', () => {
      render(<MobileFilters {...defaultProps} />);

      const allFiltersButton = screen.getByTestId('all-filters-button');
      fireEvent.click(allFiltersButton);
      expect(screen.getByText('Filters Modal')).toBeInTheDocument();

      fireEvent.click(allFiltersButton);
      expect(screen.queryByText('Filters Modal')).not.toBeInTheDocument();
    });
  });

  describe('Panel Interactions', () => {
    it('handles sort changes correctly', async () => {
      render(<MobileFilters {...defaultProps} />);

      fireEvent.click(screen.getByTestId('sort-button'));
      const sortSelect = screen.getByTestId('sort-select');

      fireEvent.change(sortSelect, { target: { value: 'best-rated' } });

      expect(mockUseFilters.updateQuery).toHaveBeenCalledWith({
        key: 'sortBy',
        value: 'best-rated'
      });
    });

    it('handles rating changes correctly', async () => {
      render(<MobileFilters {...defaultProps} />);

      fireEvent.click(screen.getByTestId('rating-button'));
      const ratingSelect = screen.getByTestId('rating-select');

      fireEvent.change(ratingSelect, { target: { value: '4' } });

      expect(mockUseFilters.updateQuery).toHaveBeenCalledWith({
        key: 'reviews',
        value: '4'
      });
    });

    it('closes active panel when close button is clicked', async () => {
      render(<MobileFilters {...defaultProps} />);

      fireEvent.click(screen.getByTestId('sort-button'));
      expect(screen.getByTestId('sort-panel')).toBeInTheDocument();

      fireEvent.click(screen.getByText('Close'));
      expect(screen.queryByTestId('sort-panel')).not.toBeInTheDocument();
    });
  });

  describe('Layout and Styling', () => {
    it('maintains sticky positioning', () => {
      const { container } = render(<MobileFilters {...defaultProps} />);
      const boxElement = container.firstChild;
      expect(boxElement).toHaveStyle({
        position: 'sticky',
        top: '0'
      });
    });
  });

  describe('Map View Integration', () => {
    it('renders map button when isMapViewEnabled is true', () => {
      render(<MobileFilters {...defaultProps} isMapViewEnabled={true} />);
      expect(screen.getByTestId('map-button')).toBeInTheDocument();
    });

    it('handles map view toggle correctly', () => {
      render(<MobileFilters {...defaultProps} isMapViewEnabled={true} />);

      const mapButton = screen.getByTestId('map-button');
      fireEvent.click(mapButton);
      expect(screen.queryByTestId('sort-panel')).not.toBeInTheDocument();
      expect(screen.queryByTestId('rating-panel')).not.toBeInTheDocument();
    });
  });
});
