import { SortingBy } from '@services/modular-monolith/types/search.type';
import { fireEvent, render, screen } from '@testing-library/react';

import { SortPanel } from './SortPanel';

jest.mock('@components/SortBy', () => ({
  __esModule: true,
  default: ({
    onChange,
    value
  }: {
    onChange: (value: string) => void;
    value: string;
  }) => (
    <select
      data-testid="sort-by"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      <option value="distance">Distance</option>
      <option value="best-rated">Best Rated</option>
      <option value="best-staff">Best Staff</option>
    </select>
  )
}));

jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters/FilterActions',
  () => ({
    __esModule: true,
    default: ({ closeAllFilters }: { closeAllFilters: () => void }) => (
      <button data-testid="filter-actions" onClick={closeAllFilters}>
        Close Filters
      </button>
    )
  })
);

describe('SortPanel', () => {
  const defaultProps = {
    query: {
      sortBy: 'distance' as SortingBy
    },
    handleSortByChange: jest.fn(),
    closeAllFilters: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<SortPanel {...defaultProps} />);
    expect(screen.getByTestId('sort-by')).toBeInTheDocument();
    expect(screen.getByTestId('filter-actions')).toBeInTheDocument();
  });

  it('passes correct props to SortBy component', () => {
    render(<SortPanel {...defaultProps} />);
    const sortBySelect = screen.getByTestId('sort-by') as HTMLSelectElement;
    expect(sortBySelect.value).toBe('distance');
  });

  it('handles sort change correctly', () => {
    render(<SortPanel {...defaultProps} />);
    const sortBySelect = screen.getByTestId('sort-by');

    fireEvent.change(sortBySelect, { target: { value: 'best-rated' } });

    expect(defaultProps.handleSortByChange).toHaveBeenCalledWith('best-rated');
    expect(defaultProps.handleSortByChange).toHaveBeenCalledTimes(1);
  });

  it('handles close filters correctly', () => {
    render(<SortPanel {...defaultProps} />);
    const closeButton = screen.getByTestId('filter-actions');

    fireEvent.click(closeButton);

    expect(defaultProps.closeAllFilters).toHaveBeenCalledTimes(1);
  });

  it('renders with custom text props for SortBy', () => {
    render(<SortPanel {...defaultProps} />);
    const sortByComponent = screen.getByTestId('sort-by');
    expect(sortByComponent).toBeInTheDocument();
    expect(sortByComponent.parentElement).toHaveStyle({ display: 'flex' });
  });

  it('handles undefined query.sortBy gracefully', () => {
    const propsWithoutSortBy = {
      ...defaultProps,
      query: {}
    };

    render(<SortPanel {...propsWithoutSortBy} />);
    const sortBySelect = screen.getByTestId('sort-by') as HTMLSelectElement;
    expect(sortBySelect.value).toBe('distance'); // Assuming 'distance' is the default value
  });
});
