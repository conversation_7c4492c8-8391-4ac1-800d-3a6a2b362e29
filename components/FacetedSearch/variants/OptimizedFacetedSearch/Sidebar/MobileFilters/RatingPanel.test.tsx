import { fireEvent, render, screen } from '@testing-library/react';

import { RatingPanel } from './RatingPanel';

jest.mock('@components/ReviewsFilter', () => ({
  __esModule: true,
  default: ({
    onChange,
    value
  }: {
    onChange: (name: string, value: any) => void;
    value: string;
  }) => (
    <div data-testid="reviews-filter">
      <select
        value={value}
        onChange={(e) => onChange('reviews', e.target.value)}
        data-testid="reviews-select"
      >
        <option value="">All ratings</option>
        <option value="4">4+ stars</option>
        <option value="3">3+ stars</option>
      </select>
    </div>
  )
}));

jest.mock(
  '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters/FilterActions',
  () => ({
    __esModule: true,
    default: ({ closeAllFilters }: { closeAllFilters: () => void }) => (
      <button data-testid="filter-actions" onClick={closeAllFilters}>
        Close Filters
      </button>
    )
  })
);

describe('RatingPanel', () => {
  const defaultProps = {
    query: {
      reviews: '4'
    },
    handleInputChange: jest.fn(),
    closeAllFilters: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<RatingPanel {...defaultProps} />);
    expect(screen.getByTestId('reviews-filter')).toBeInTheDocument();
    expect(screen.getByTestId('filter-actions')).toBeInTheDocument();
  });

  it('passes correct props to ReviewsFilter component', () => {
    render(<RatingPanel {...defaultProps} />);
    const reviewsSelect = screen.getByTestId(
      'reviews-select'
    ) as HTMLSelectElement;
    expect(reviewsSelect.value).toBe('4');
  });

  it('handles rating change correctly', () => {
    render(<RatingPanel {...defaultProps} />);
    const reviewsSelect = screen.getByTestId('reviews-select');

    fireEvent.change(reviewsSelect, { target: { value: '3' } });

    expect(defaultProps.handleInputChange).toHaveBeenCalledWith('reviews', '3');
    expect(defaultProps.handleInputChange).toHaveBeenCalledTimes(1);
  });

  it('handles close filters correctly', () => {
    render(<RatingPanel {...defaultProps} />);
    const closeButton = screen.getByTestId('filter-actions');

    fireEvent.click(closeButton);

    expect(defaultProps.closeAllFilters).toHaveBeenCalledTimes(1);
  });

  it('handles undefined query.reviews gracefully', () => {
    const propsWithoutReviews = {
      ...defaultProps,
      query: {}
    };

    render(<RatingPanel {...propsWithoutReviews} />);
    const reviewsSelect = screen.getByTestId(
      'reviews-select'
    ) as HTMLSelectElement;
    expect(reviewsSelect.value).toBe('');
  });

  it('renders ReviewsFilter with correct onChange handler', () => {
    render(<RatingPanel {...defaultProps} />);
    const reviewsSelect = screen.getByTestId('reviews-select');

    fireEvent.change(reviewsSelect, { target: { value: '4' } });
    expect(defaultProps.handleInputChange).toHaveBeenCalledWith('reviews', '4');

    fireEvent.change(reviewsSelect, { target: { value: '3' } });
    expect(defaultProps.handleInputChange).toHaveBeenCalledWith('reviews', '3');

    expect(defaultProps.handleInputChange).toHaveBeenCalledTimes(2);
  });

  it('renders both ReviewsFilter and FilterActions components', () => {
    render(<RatingPanel {...defaultProps} />);

    expect(screen.getByTestId('reviews-filter')).toBeInTheDocument();
    expect(screen.getByTestId('filter-actions')).toBeInTheDocument();
  });
});
