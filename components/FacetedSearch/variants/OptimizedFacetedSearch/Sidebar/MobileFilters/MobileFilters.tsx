import { Box } from '@chakra-ui/react';
import useFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/useFilters';
import { FilterProps } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import { ActiveFilter } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';
import { MODAL } from '@constants/modals';
import { SortingBy } from '@services/modular-monolith/types/search.type';
import { useState } from 'react';

import { useModalDispatch } from '~/contexts/ModalContext';

import { AllFiltersModal } from './AllFiltersModal';
import { FilterButtons } from './FilterButtons';
import { RatingPanel } from './RatingPanel';
import { SortPanel } from './SortPanel';

const MobileFilters: React.FC<FilterProps> = ({
  filterTooltips,
  searchOptions,
  isMapViewEnabled
}) => {
  const { hideModal, showModal } = useModalDispatch();

  const [activeFilter, setActiveFilter] = useState<ActiveFilter>(null);
  const { accordionItems, query, updateQuery } = useFilters({
    filterTooltips: filterTooltips
  });

  const closeAllFilters = () => {
    setActiveFilter(null);
  };

  const handleFilterClick = (filter: ActiveFilter) => {
    if (filter === 'map') {
      if (isMapViewEnabled) {
        hideModal(MODAL.MAP_VIEW);
      } else {
        showModal(MODAL.MAP_VIEW);
      }
    }

    if (activeFilter === filter) {
      setActiveFilter(null);
    } else {
      setActiveFilter(filter);
    }
  };
  const handleInputChange = (name: string, value: any) => {
    updateQuery({ key: name as any, value });
  };
  const handleSortByChange = (value: SortingBy) => {
    updateQuery({ key: 'sortBy', value });
  };

  return (
    <Box position="sticky" top={0}>
      <FilterButtons
        activeFilter={activeFilter}
        isMapViewEnabled={isMapViewEnabled}
        handleFilterClick={handleFilterClick}
      />

      {activeFilter === 'sort' && (
        <SortPanel
          query={query}
          handleSortByChange={handleSortByChange}
          closeAllFilters={closeAllFilters}
        />
      )}

      {activeFilter === 'rating' && (
        <RatingPanel
          query={query}
          handleInputChange={handleInputChange}
          closeAllFilters={closeAllFilters}
        />
      )}

      <AllFiltersModal
        activeFilter={activeFilter}
        accordionItems={accordionItems}
        searchOptions={searchOptions}
        closeAllFilters={closeAllFilters}
      />
    </Box>
  );
};

export default MobileFilters;
