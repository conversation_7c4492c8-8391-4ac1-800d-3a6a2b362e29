import { Flex } from '@chakra-ui/react';
import FilterActions from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters/FilterActions';
import SortBy from '@components/SortBy';
import { SortingBy } from '@services/modular-monolith/types/search.type';

interface SortPanelProps {
  query: any;
  handleSortByChange: (value: SortingBy) => void;
  closeAllFilters: () => void;
}

export const SortPanel: React.FC<SortPanelProps> = ({
  query,
  handleSortByChange,
  closeAllFilters
}) => {
  return (
    <Flex direction="column" gap={5} paddingTop={6}>
      <SortBy
        onChange={handleSortByChange}
        value={query.sortBy}
        textProps={{ display: 'none' }}
      />
      <FilterActions closeAllFilters={closeAllFilters} />
    </Flex>
  );
};
