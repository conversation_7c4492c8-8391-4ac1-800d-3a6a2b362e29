import { useFacetedSearchActions } from '@components/FacetedSearch/FacetedSearchContext';
import { fireEvent, render, screen } from '@testing-library/react';

import FilterActions from './FilterActions';

jest.mock('@components/FacetedSearch/FacetedSearchContext', () => ({
  useFacetedSearchActions: jest.fn()
}));

describe('FilterActions', () => {
  const mockCloseAllFilters = jest.fn();
  const mockResetQuery = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      resetQuery: mockResetQuery
    });
  });

  describe('Rendering', () => {
    it('renders Clear and Apply buttons', () => {
      render(<FilterActions closeAllFilters={mockCloseAllFilters} />);

      expect(screen.getByText('Clear')).toBeInTheDocument();
      expect(screen.getByText('Apply')).toBeInTheDocument();
    });

    it('applies correct styles to container', () => {
      const { container } = render(
        <FilterActions closeAllFilters={mockCloseAllFilters} />
      );
      const flexContainer = container.firstChild;

      expect(flexContainer).toHaveStyle({
        position: 'sticky',
        bottom: '0',
        width: '100%'
      });
    });

    it('applies correct button styles', () => {
      render(<FilterActions closeAllFilters={mockCloseAllFilters} />);

      const clearButton = screen.getByText('Clear').closest('button');
      const applyButton = screen.getByText('Apply').closest('button');

      expect(clearButton).toHaveStyle({
        height: '32px'
      });

      expect(applyButton).toHaveStyle({
        height: '32px'
      });
    });
  });

  describe('Interactions', () => {
    it('calls closeAllFilters when Apply button is clicked', () => {
      render(<FilterActions closeAllFilters={mockCloseAllFilters} />);

      fireEvent.click(screen.getByText('Apply'));
      expect(mockCloseAllFilters).toHaveBeenCalledTimes(1);
    });

    it('calls both resetQuery and closeAllFilters when Clear button is clicked', () => {
      render(<FilterActions closeAllFilters={mockCloseAllFilters} />);

      fireEvent.click(screen.getByText('Clear'));
      expect(mockResetQuery).toHaveBeenCalledTimes(1);
      expect(mockCloseAllFilters).toHaveBeenCalledTimes(1);
    });

    it('executes Clear button actions in correct order', () => {
      const executionOrder: string[] = [];
      const mockCloseAllFilters = jest.fn(() => executionOrder.push('close'));
      const mockResetQuery = jest.fn(() => executionOrder.push('reset'));

      (useFacetedSearchActions as jest.Mock).mockReturnValue({
        resetQuery: mockResetQuery
      });

      render(<FilterActions closeAllFilters={mockCloseAllFilters} />);
      fireEvent.click(screen.getByText('Clear'));

      expect(executionOrder).toEqual(['close', 'reset']);
    });
  });
});
