import {
  Box,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
  ModalOverlay
} from '@chakra-ui/react';
import Accordion from '@components/Accordion';
import { SearchOption } from '@components/FacetedSearch/constants';
import SelectedFilters from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Filters/SelectedFilters';
import FilterActions from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters/FilterActions';
import {
  AccordionItem,
  ActiveFilter
} from '@components/FacetedSearch/variants/OptimizedFacetedSearch/types';

interface AllFiltersModalProps {
  activeFilter: ActiveFilter;
  accordionItems: AccordionItem[];
  searchOptions: Array<SearchOption>;
  closeAllFilters: () => void;
}

export const AllFiltersModal: React.FC<AllFiltersModalProps> = ({
  activeFilter,
  accordionItems,
  searchOptions,
  closeAllFilters
}) => {
  return (
    <Modal
      isOpen={activeFilter === 'all'}
      onClose={closeAllFilters}
      size="full"
    >
      <ModalOverlay />
      <ModalContent margin={0}>
        <ModalHeader
          zIndex="sticky"
          position="sticky"
          top={0}
          boxShadow="md"
          borderColor="gray.200"
          px={4}
          py={3}
          textAlign="center"
          fontSize="md"
          backgroundColor="white"
        >
          All Filters
          <ModalCloseButton />
          <SelectedFilters
            showClearButton={false}
            showResultCount={false}
            position="sticky"
            top={0}
            backgroundColor="white"
          />
        </ModalHeader>
        <ModalBody p={0}>
          <Flex direction="column" position="relative" paddingX={4}>
            <Box
              flex="1"
              overflow="auto"
              width="100%"
              backgroundColor="white"
              sx={{
                '::-webkit-scrollbar': {
                  display: 'none'
                }
              }}
            >
              <Accordion
                accordionButtonProps={{
                  _hover: {
                    color: 'primary.500',
                    textDecoration: 'underline'
                  }
                }}
                openIndexes={[]}
                items={accordionItems.filter((item) =>
                  searchOptions.includes(item.id)
                )}
              />
            </Box>
          </Flex>
        </ModalBody>
        <ModalFooter
          position={'sticky'}
          bottom={0}
          bg="white"
          _before={{
            content: '""',
            position: 'absolute',
            top: '-20px',
            left: 0,
            right: 0,
            height: '20px',
            background: 'linear-gradient(to top, rgba(0,0,0,0.1), transparent)',
            pointerEvents: 'none'
          }}
          paddingBottom={0}
        >
          <FilterActions closeAllFilters={closeAllFilters} />
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AllFiltersModal;
