import { Flex } from '@chakra-ui/react';
import FilterActions from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Sidebar/MobileFilters/FilterActions';
import ReviewsFilter from '@components/ReviewsFilter';

interface RatingPanelProps {
  query: any;
  handleInputChange: (name: string, value: any) => void;
  closeAllFilters: () => void;
}

export const RatingPanel: React.FC<RatingPanelProps> = ({
  query,
  handleInputChange,
  closeAllFilters
}) => {
  return (
    <Flex direction="column" gap={5} paddingTop={6}>
      <ReviewsFilter onChange={handleInputChange} value={query.reviews} />
      <FilterActions closeAllFilters={closeAllFilters} />
    </Flex>
  );
};
