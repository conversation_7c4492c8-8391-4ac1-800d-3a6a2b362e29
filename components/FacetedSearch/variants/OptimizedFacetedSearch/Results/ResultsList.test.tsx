import { SearchStates } from '@components/FacetedSearch/constants';
import {
  useFacetedSearchActions,
  useFacetedSearchResult
} from '@components/FacetedSearch/FacetedSearchContext';
import { fireEvent, render, screen } from '@utils/test-utils';

import ResultsList, { ProviderListProps } from './ResultsList';

jest.mock('next/router', () => ({
  useRouter: () => ({ query: {} })
}));

jest.mock('@components/FacetedSearch/FacetedSearchContext', () => ({
  useFacetedSearchActions: jest.fn(),
  useFacetedSearchResult: jest.fn()
}));

const defaultSearch: ProviderListProps['search'] = {
  careType: 'test-care',
  displayBadges: true,
  displayLearnMoreButton: true,
  displayProviderPhoneNumber: true,
  displayRequestInfoButton: true,
  genericBlock1: {
    '@name': 'genericBlock1',
    '@path': 'genericBlock1',
    '@id': 'genericBlock1-id',
    '@nodeType': 'EditableArea',
    items: [],
    '@type': 'EditableAreaType',
    'mgnl:lastModified': '',
    'mgnl:created': '',
    '@nodes': []
  },
  genericBlock2: {
    '@name': 'genericBlock2',
    '@path': 'genericBlock2',
    '@id': 'genericBlock2-id',
    '@nodeType': 'EditableArea',
    items: [],
    '@type': 'EditableAreaType',
    'mgnl:lastModified': '',
    'mgnl:created': '',
    '@nodes': []
  },
  learnMoreButtonColorScheme: 'primary',
  learnMoreButtonText: 'Learn More',
  metadata: { '@id': 'test-id' },
  providerPhoneNumberSource: { field: 'phone' },
  providerTitleColor: 'black',
  providerTitleColorRange: 'dark',
  ratingStarsColor: 'gold',
  ratingStarsColorRange: 'light',
  requestInfoButtonColorScheme: 'secondary',
  requestInfoButtonText: 'Request Info',
  tileBorder: '1px solid',
  tileBorderColor: 'gray',
  tileBorderColorRange: 'lightgray',
  displayCompareOption: false,
  dontOpenInNewTab: false,
  promotionColorScheme: 'blue',
  boxShadow: 'md',
  countOfTilesPerRow: '4',
  isLoading: false
  // Add any other required properties here if needed
};

const defaultOptions = {
  visibleItems: 12,
  filterAndSort: { phoneNumber: {} }
};

const defaultResult: ProviderListProps['result'] = {
  resultCount: 20,
  resultsPerPage: 10,
  results: [],
  searchState: SearchStates.INPUT_WITH_RESULTS,
  amenityCategories: [],
  nearbyCount: 0,
  regionCount: 0
};

describe('ResultsList Show More Button', () => {
  const mockShowMoreResults = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the "Show More" button when hasNextPage is true', () => {
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      showMoreResults: mockShowMoreResults
    });
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      hasNextPage: true,
      isFetchingNextPage: false
    });

    render(
      <ResultsList
        search={defaultSearch}
        options={defaultOptions}
        result={defaultResult}
      />
    );

    const button = screen.getByTestId('show-more-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Show More');
    expect(button).not.toBeDisabled();
  });

  it('disables the "Show More" button when isFetchingNextPage is true', () => {
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      showMoreResults: mockShowMoreResults
    });
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      hasNextPage: true,
      isFetchingNextPage: true
    });

    render(
      <ResultsList
        search={defaultSearch}
        options={defaultOptions}
        result={defaultResult}
      />
    );

    const button = screen.getByTestId('show-more-button');
    expect(button).toBeInTheDocument();
    expect(button).toBeDisabled();
  });

  it('calls showMoreResults when the "Show More" button is clicked', () => {
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      showMoreResults: mockShowMoreResults
    });
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      hasNextPage: true,
      isFetchingNextPage: false
    });

    render(
      <ResultsList
        search={defaultSearch}
        options={defaultOptions}
        result={defaultResult}
      />
    );

    const button = screen.getByTestId('show-more-button');
    fireEvent.click(button);
    expect(mockShowMoreResults).toHaveBeenCalled();
  });

  it('does not render the "Show More" button when hasNextPage is false', () => {
    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      showMoreResults: mockShowMoreResults
    });
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      hasNextPage: false,
      isFetchingNextPage: false
    });

    render(
      <ResultsList
        search={defaultSearch}
        options={defaultOptions}
        result={defaultResult}
      />
    );

    const button = screen.queryByTestId('show-more-button');
    expect(button).toBeNull();
  });
});
