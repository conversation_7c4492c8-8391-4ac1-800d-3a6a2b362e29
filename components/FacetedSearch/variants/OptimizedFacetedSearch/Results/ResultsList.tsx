import { Box, Grid, GridItem, VStack } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { SearchStates } from '@components/FacetedSearch/constants';
import {
  useFacetedSearchActions,
  useFacetedSearchResult
} from '@components/FacetedSearch/FacetedSearchContext';
import { FacetedSearchResponse } from '@components/FacetedSearch/types';
import { RenderResults } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/RenderResults';
import ResultCardSkeleton from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Results/ResultCardSkeleton';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';

import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { PhoneNumberConfig } from '~/types/componentsConfig';
import { EditableAreaType, Metadata } from '~/types/Magnolia';

export interface ProviderListProps {
  options: {
    visibleItems: number;
    filterAndSort?: {
      phoneNumber?: PhoneNumberConfig;
    };
  };
  result: FacetedSearchResponse | undefined;
  search: {
    blurCosts?: boolean;
    boxShadow?: string;
    careType: string;
    countOfTilesPerRow?: string;
    displayBadges?: boolean;
    displayLearnMoreButton?: boolean;
    displayProviderPhoneNumber: boolean;
    displayRequestInfoButton?: boolean;
    fullWidthTile?: boolean;
    genericBlock1: EditableAreaType;
    genericBlock2: EditableAreaType;
    infoButtonInquiryId?: string;
    isLoading: boolean;
    learnMoreButtonColorScheme: string;
    learnMoreButtonText?: string;
    metadata: Metadata;
    providerPhoneNumberSource?: { field: string; providerPhoneNumber?: string };
    providerTitleColor: string;
    providerTitleColorRange: string;
    ratingStarsColor: string;
    ratingStarsColorRange: string;
    readMoreButton?: string;
    requestInfoButtonColorScheme: string;
    requestInfoButtonText?: string;
    tileBorder: string;
    tileBorderColor: string;
    tileBorderColorRange: string;
    displayCompareOption?: boolean;
    dontOpenInNewTab: boolean;
    promotionColorScheme: string;
  };
}

const ResultsList: React.FC<ProviderListProps> = ({
  options,
  result,
  search
}) => {
  const {
    careType,
    displayBadges = true,
    readMoreButton,
    genericBlock1,
    genericBlock2,
    metadata,
    providerTitleColor,
    providerTitleColorRange,
    displayLearnMoreButton,
    learnMoreButtonText,
    displayRequestInfoButton,
    requestInfoButtonColorScheme,
    learnMoreButtonColorScheme,
    ratingStarsColor,
    ratingStarsColorRange,
    infoButtonInquiryId,
    requestInfoButtonText,
    boxShadow = 'lg',
    tileBorder = 'none',
    tileBorderColor = 'none',
    tileBorderColorRange = 'none',
    displayCompareOption = false,
    displayProviderPhoneNumber = false,
    providerPhoneNumberSource,
    countOfTilesPerRow,
    dontOpenInNewTab,
    promotionColorScheme
  } = search;
  const inquiryFormHasSubmitted = useInquiryFormSubmitted();
  const { getProviderDetailsPath, getProviderDescription } =
    useTenantFunctions();
  const showPrice = inquiryFormHasSubmitted;
  const itemsPerRow = Number(countOfTilesPerRow) * 3 || 12;

  const { showMoreResults } = useFacetedSearchActions();
  const { hasNextPage, isFetchingNextPage } = useFacetedSearchResult();

  return (
    <Box>
      <VStack alignItems={'left'} spacing="5" paddingBottom={4}>
        {search.isLoading && !isFetchingNextPage ? (
          <Grid templateColumns="1fr" gap={6}>
            {new Array(6).fill(0).map((value, key) => (
              <GridItem colSpan={3} key={`${key}-${value}`}>
                <ResultCardSkeleton />
              </GridItem>
            ))}
          </Grid>
        ) : (
          <RenderResults
            results={result?.results ?? []}
            searchState={
              result?.searchState ?? SearchStates.INPUT_WITH_NO_RESULTS
            }
            itemsPerRow={itemsPerRow}
            providerTitleColor={providerTitleColor}
            providerTitleColorRange={providerTitleColorRange}
            requestInfoButtonColorScheme={requestInfoButtonColorScheme}
            learnMoreButtonColorScheme={learnMoreButtonColorScheme}
            ratingStarsColor={ratingStarsColor}
            ratingStarsColorRange={ratingStarsColorRange}
            boxShadow={boxShadow}
            tileBorder={tileBorder}
            tileBorderColor={tileBorderColor}
            tileBorderColorRange={tileBorderColorRange}
            displayBadges={displayBadges}
            showPrice={showPrice}
            genericBlock1={genericBlock1}
            genericBlock2={genericBlock2}
            metadata={metadata}
            getProviderDetailsPath={getProviderDetailsPath}
            getProviderDescription={getProviderDescription}
            displayLearnMoreButton={displayLearnMoreButton}
            learnMoreButtonText={learnMoreButtonText}
            displayRequestInfoButton={displayRequestInfoButton}
            requestInfoButtonText={requestInfoButtonText}
            readMoreButton={readMoreButton}
            inquiryId={infoButtonInquiryId}
            displayProviderPhoneNumber={displayProviderPhoneNumber}
            providerPhoneNumberSource={providerPhoneNumberSource}
            careType={careType}
            filterAndSort={options.filterAndSort}
            displayCompareOption={displayCompareOption}
            dontOpenInNewTab={dontOpenInNewTab}
            promotionColorScheme={promotionColorScheme}
          />
        )}
        {isFetchingNextPage && (
          <Grid templateColumns="1fr" gap={6}>
            {new Array(10).fill(0).map((value, key) => (
              <GridItem colSpan={3} key={`${key}-${value}`}>
                <ResultCardSkeleton />
              </GridItem>
            ))}
          </Grid>
        )}
        {hasNextPage && (
          <Button
            colorScheme="primary"
            borderRadius="25px"
            variant="outline"
            backgroundColor="white"
            alignSelf="center"
            fontSize="14px"
            width="auto"
            paddingX={20}
            data-testid="show-more-button"
            elementType={ElementTypes.BUTTON}
            elementAction={ElementActions.SHOW_MORE_RESULTS}
            elementName={ElementNames.SHOW_MORE_BUTTON}
            onClick={showMoreResults}
            isLoading={isFetchingNextPage}
            disabled={isFetchingNextPage}
          >
            Show More
          </Button>
        )}
      </VStack>
    </Box>
  );
};

export default ResultsList;
