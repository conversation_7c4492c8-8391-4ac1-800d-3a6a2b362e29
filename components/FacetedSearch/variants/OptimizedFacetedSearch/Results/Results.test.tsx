import { render, screen } from '@utils/test-utils';

import Results from './Results';
jest.mock('next/router', () => require('next-router-mock'));

jest.mock('@components/FacetedSearch/FacetedSearchContext', () => ({
  useFacetedSearchActions: jest.fn(),
  useFacetedSearchQuery: jest.fn(),
  useFacetedSearchResult: jest.fn()
}));

import {
  useFacetedSearchActions,
  useFacetedSearchQuery,
  useFacetedSearchResult
} from '@components/FacetedSearch/FacetedSearchContext';

describe('Results', () => {
  const mockChangePage = jest.fn();
  const defaultProps = {
    blurCosts: false,
    boxShadow: 'md',
    careType: 'senior-living',
    countOfTilesPerRow: 2,
    displayBadges: true,
    displayLearnMoreButton: true,
    displayProviderPhoneNumber: true,
    displayRequestInfoButton: true,
    visibleLimit: 10,
    filterAndSort: true
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (useFacetedSearchActions as jest.Mock).mockReturnValue({
      changePage: mockChangePage
    });

    (useFacetedSearchQuery as jest.Mock).mockReturnValue({
      page: 1
    });

    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      isFetching: false,
      data: {
        resultCount: 30,
        resultsPerPage: 10,
        results: []
      }
    });
  });

  it('displays result count', () => {
    render(<Results {...defaultProps} />);
    expect(screen.getByText('30 Providers')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      isFetching: true,
      data: {
        resultCount: 0,
        resultsPerPage: 10,
        results: []
      }
    });

    render(<Results {...defaultProps} />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('handles empty results', () => {
    (useFacetedSearchResult as jest.Mock).mockReturnValue({
      isFetching: false,
      data: {
        resultCount: 0,
        resultsPerPage: 10,
        results: []
      }
    });

    render(<Results {...defaultProps} />);
    expect(screen.getByText('0 Providers')).toBeInTheDocument();
  });
});
