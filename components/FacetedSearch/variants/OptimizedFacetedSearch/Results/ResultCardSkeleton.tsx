import { Card } from '@chakra-ui/card';
import { GridItem, LinkBox } from '@chakra-ui/layout';
import { CardBody } from '@chakra-ui/react';
import { Skeleton } from '@chakra-ui/skeleton';

const ResultCardSkeleton = (): JSX.Element => {
  return (
    <LinkBox as="article" width="100%" data-testid="search-result-skeleton">
      <Card variant="elevated" p={0} gap={0} borderRadius={6}>
        <CardBody
          display="grid"
          gridTemplateColumns="repeat(12, 1fr)"
          minHeight="56"
          justifyContent="space-between"
          p={{ base: 2.5, lg: 4 }}
          gap={{ base: 2.5, lg: 4 }}
        >
          <GridItem
            bg="gray.200"
            colSpan={{ base: 12, md: 6 }}
            overflow="hidden"
            rounded="md"
            height={{ base: '150px', md: '205px' }}
          >
            <Skeleton height="100%" width="100%" rounded="md" />
          </GridItem>
          <GridItem
            colSpan={{ base: 12, md: 6 }}
            rounded="md"
            alignItems="start"
            display="flex"
            flexDirection="column"
            gap={2}
          >
            <Skeleton height="47px" rounded="md" width="100%" />
            <Skeleton height="56px" rounded="md" width="100%" />
            <Skeleton height="24px" rounded="md" width="35%" />
            <Skeleton height="24px" rounded="md" width="30%" />
          </GridItem>
        </CardBody>
      </Card>
    </LinkBox>
  );
};

export default ResultCardSkeleton;
