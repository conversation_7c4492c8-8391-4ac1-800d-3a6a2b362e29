'use client';

import { Box } from '@chakra-ui/layout';
import { useFacetedSearchResult } from '@components/FacetedSearch/FacetedSearchContext';
import ResultCount from '@components/FacetedSearch/variants/OptimizedFacetedSearch/ResultCount';
import ResultsList from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Results/ResultsList';

const Results = (props) => {
  const { isFetching, data } = useFacetedSearchResult();
  const resultCount = data?.resultCount || 0;

  return (
    <Box pt={0} px={{ base: 0, md: 8 }}>
      <ResultCount
        resultCount={resultCount}
        isFetching={isFetching}
        borderBottom="none"
        paddingX={4}
        paddingTop={5}
        paddingBottom={3}
        display={{ base: 'block', lg: 'none' }}
      />
      <ResultsList
        search={{
          isLoading: isFetching,
          boxShadow: props.boxShadow,
          careType: props.careType,
          countOfTilesPerRow: props.countOfTilesPerRow,
          displayLearnMoreButton: props.displayLearnMoreButton,
          displayProviderPhoneNumber: props.displayProviderPhoneNumber,
          displayRequestInfoButton: props.displayRequestInfoButton,
          genericBlock1: props.genericBlock1,
          genericBlock2: props.genericBlock2,
          infoButtonInquiryId: props.infoButtonInquiryId,
          learnMoreButtonColorScheme: props.learnMoreButtonColorScheme,
          learnMoreButtonText: props.learnMoreButtonText,
          metadata: props.metadata,
          providerPhoneNumberSource: props.providerPhoneNumberSource,
          providerTitleColor: props.providerTitleColor,
          providerTitleColorRange: props.providerTitleColorRange,
          ratingStarsColor: props.ratingStarsColor,
          ratingStarsColorRange: props.ratingStarsColorRange,
          readMoreButton: props.readMoreButton,
          requestInfoButtonColorScheme: props.requestInfoButtonColorScheme,
          requestInfoButtonText: props.requestInfoButtonText,
          tileBorder: props.tileBorder,
          tileBorderColor: props.tileBorderColor,
          tileBorderColorRange: props.tileBorderColorRange,
          displayCompareOption: props.displayCompareOption,
          dontOpenInNewTab: props.dontOpenInNewTab,
          promotionColorScheme: props.promotionColorScheme
        }}
        result={data}
        options={{
          visibleItems: Number(props.visibleLimit),
          filterAndSort: props.filterAndSort
        }}
      />
    </Box>
  );
};

export default Results;
