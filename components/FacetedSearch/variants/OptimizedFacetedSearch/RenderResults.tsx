'use client';

import { Grid, GridItem, Heading, Link, Text } from '@chakra-ui/layout';
import {
  Community,
  CommunityComparisonBanner,
  ComparisonErrorDialog
} from '@components/CommunityComparison/GeosCommunityComparison';
import { SearchStates } from '@components/FacetedSearch/constants';
import { renderResultProps } from '@components/FacetedSearch/types';
import { getContent } from '@components/FacetedSearch/variants/OptimizedFacetedSearch/getContent';
import MarketingCTA from '@components/FacetedSearch/variants/OptimizedFacetedSearch/Marketing/MarketingCTA';
import { isProviderInList } from '@components/Search/Search.utils';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export const RenderResults = ({
  results,
  searchState,
  providerTitleColor,
  providerTitleColorRange,
  requestInfoButtonColorScheme,
  learnMoreButtonColorScheme,
  ratingStarsColor,
  ratingStarsColorRange,
  boxShadow,
  tileBorder,
  tileBorderColor,
  tileBorderColorRange,
  displayBadges,
  showPrice,
  getProviderDetailsPath,
  getProviderDescription,
  displayLearnMoreButton,
  learnMoreButtonText,
  displayRequestInfoButton,
  requestInfoButtonText,
  readMoreButton,
  inquiryId,
  displayProviderPhoneNumber,
  providerPhoneNumberSource,
  careType,
  filterAndSort,
  queryId,
  listId,
  displayCompareOption = false,
  dontOpenInNewTab,
  promotionColorScheme
}: renderResultProps) => {
  const router = useRouter();
  const selectedCommunities = router.query?.['selectedCommunities'];
  const [isCompareModalOpen, setIsCompareModalOpen] = useState<Boolean>(
    router.query?.['isModalOpen'] === 'true'
  );
  const [providersList, setProvidersList] = useState<Community[]>([]);
  const [displayComparisonErrorDialog, setDisplayComparisonErrorDialog] =
    useState<boolean>(false);
  const genericBlockFrequency = 6;

  useEffect(() => {
    if (selectedCommunities) {
      const selectedItems = (selectedCommunities as string).split(',');
      const filteredProviders = results
        .map((provider, index) => {
          if (selectedItems.includes(String(provider.id))) {
            return {
              ...provider,
              index
            } as Community;
          }
          return null;
        })
        .filter((provider): provider is Community => provider !== null);

      setProvidersList(filteredProviders);
    }
  }, [selectedCommunities, results]);

  useEffect(() => {
    if (router?.query?.hasOwnProperty('isModalOpen')) {
      setIsCompareModalOpen(router.query?.isModalOpen === 'true');
    }
  }, [router.query]);

  const handleCompareProvider = (community: Community) => {
    const isProviderPresent = isProviderInList(community, providersList);

    if (providersList.length > 3 && !isProviderPresent) {
      return setDisplayComparisonErrorDialog(true);
    }

    if (!isProviderPresent) {
      setProvidersList([...providersList, community]);
    } else {
      const filteredProviders = providersList.filter(
        (existingProvider) => existingProvider.id !== community.id
      );
      setProvidersList(filteredProviders);
    }
  };

  switch (searchState) {
    case SearchStates.INPUT_WITH_RESULTS:
      const resultsToRenderFirst = results.slice(0, genericBlockFrequency);
      const resultsToRenderAfter = results.slice(
        genericBlockFrequency,
        results.length
      );
      return (
        <>
          <Grid templateColumns="1fr" gap={4}>
            {resultsToRenderFirst.map((result, index) => {
              return getContent(
                result,
                index,
                showPrice,
                displayBadges,
                getProviderDetailsPath,
                getProviderDescription,
                displayLearnMoreButton,
                learnMoreButtonText,
                displayRequestInfoButton,
                requestInfoButtonText,
                readMoreButton,
                inquiryId,
                requestInfoButtonColorScheme,
                learnMoreButtonColorScheme,
                ratingStarsColor,
                ratingStarsColorRange,
                providerTitleColor,
                providerTitleColorRange,
                boxShadow,
                tileBorder,
                tileBorderColor,
                tileBorderColorRange,
                displayProviderPhoneNumber,
                providerPhoneNumberSource,
                dontOpenInNewTab,
                careType,
                queryId,
                listId,
                isProviderInList(result, providersList),
                displayCompareOption,
                handleCompareProvider,
                promotionColorScheme
              );
            })}
          </Grid>
          <MarketingCTA phoneNumber={filterAndSort?.phoneNumber} />
          <Grid templateColumns="1fr" gap={6}>
            {resultsToRenderAfter.map((result, index) => {
              return getContent(
                result,
                index,
                showPrice,
                displayBadges,
                getProviderDetailsPath,
                getProviderDescription,
                displayLearnMoreButton,
                learnMoreButtonText,
                displayRequestInfoButton,
                requestInfoButtonText,
                readMoreButton,
                inquiryId,
                requestInfoButtonColorScheme,
                learnMoreButtonColorScheme,
                ratingStarsColor,
                ratingStarsColorRange,
                providerTitleColor,
                providerTitleColorRange,
                boxShadow,
                tileBorder,
                tileBorderColor,
                tileBorderColorRange,
                displayProviderPhoneNumber,
                providerPhoneNumberSource,
                dontOpenInNewTab,
                careType,
                queryId,
                listId
              );
            })}
          </Grid>
          {providersList.length > 0 && (
            <CommunityComparisonBanner
              listId={listId}
              router={router}
              queryId={queryId}
              modalId={inquiryId}
              providers={providersList}
              displayBadges={displayBadges}
              onCancel={handleCompareProvider}
              displayViewCommunityButton={true}
              ratingStarsColor={ratingStarsColor}
              providerTitleColor={providerTitleColor}
              viewCommunityButtonColorScheme="secondary"
              ratingStarsColorRange={ratingStarsColorRange}
              openCompareModal={
                Boolean(selectedCommunities) && Boolean(isCompareModalOpen)
              }
              providerTitleColorRange={providerTitleColorRange}
              displayRequestInfoButton={displayRequestInfoButton}
            />
          )}
          {displayComparisonErrorDialog && (
            <ComparisonErrorDialog
              isOpen={displayComparisonErrorDialog}
              onClose={() => setDisplayComparisonErrorDialog(false)}
            />
          )}
        </>
      );
    case SearchStates.NO_SEARCH_INPUT:
      return (
        <GridItem
          display="flex"
          flexDirection={'column'}
          justifyContent="center"
          textAlign="center"
          height={500}
          colSpan={12}
        >
          <Heading size="lg">No Results</Heading>
          <Heading size="md">
            Please change your filters to produce results
          </Heading>
        </GridItem>
      );
    case SearchStates.INPUT_WITH_NO_RESULTS:
      return (
        <Text fontSize="lg" textAlign="center" fontWeight="normal">
          There aren&apos;t any providers that match your preferences. If
          you&apos;ve selected multiple filters, try expanding your search with
          fewer filters.
          <br />
          <br />
          {filterAndSort?.phoneNumber ? (
            <>
              If you&apos;re still unable to find a provider that meets your
              needs, our expert{' '}
              <Text as="span" fontWeight="bold">
                Family Advisor team can help.
              </Text>{' '}
              <Text as="span" fontWeight="bold" color="accent.500">
                Call us at{' '}
                <Link href={`tel:${filterAndSort.phoneNumber.number}`}>
                  {filterAndSort.phoneNumber.label}
                </Link>
                .
              </Text>
            </>
          ) : null}
        </Text>
      );
    default:
      return <>No results</>;
  }
};
