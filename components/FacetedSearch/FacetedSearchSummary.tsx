import { Box, Heading, Text } from '@chakra-ui/layout';
import ClearFiltersButton from '@components/ClearFiltersButton';

import {
  useFacetedSearchActions,
  useFacetedSearchResult
} from './FacetedSearchContext';

interface FacetedSearchSummaryProps {
  heading: string;
}

const DEFAULT_RESULT_COUNT = 0;

const FacetedSearchSummary: React.FC<FacetedSearchSummaryProps> = ({
  heading
}) => {
  const { data } = useFacetedSearchResult();
  const { resetQuery } = useFacetedSearchActions();
  const resultCount = data?.resultCount ?? DEFAULT_RESULT_COUNT;

  const handleClearFilters = () => {
    resetQuery();
  };

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <Heading as="h3" size="lg">
        {heading}
      </Heading>
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        gap={3}
      >
        <Text fontSize="xl">
          <Heading as="span" size="md">
            {resultCount}
          </Heading>{' '}
          Results
        </Text>
        <ClearFiltersButton onClick={handleClearFilters} />
      </Box>
    </Box>
  );
};

export default FacetedSearchSummary;
