import { trackProviderListViewed } from '@components/Analytics/events/ProviderListViewed';
import { fireEvent, render, screen } from '@testing-library/react';

import { Provider } from '~/contexts/Provider';
import { SessionContext } from '~/contexts/SessionContext';

import ProviderList from './ProviderList';

jest.mock('@components/Analytics/events/ProviderListViewed', () => ({
  trackProviderListViewed: jest.fn()
}));

jest.mock('@components/ProviderCard/V2/ProviderCard', () => {
  return function MockProviderCard(props: any) {
    return <div data-testid="provider-card">{props.title}</div>;
  };
});

const mockSessionStorage = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('ProviderList', () => {
  const mockProviders: Provider[] = [
    {
      id: '1',
      name: 'Provider 1',
      address: { formattedAddress: '123 Main St' },
      reviewCount: 10,
      averageRating: 4.5,
      url: '/provider/1',
      photos: [{ url: 'photo1.jpg' }]
    },
    {
      id: '2',
      name: 'Provider 2',
      address: { formattedAddress: '456 Oak St' },
      reviewCount: 5,
      averageRating: 4.0,
      url: '/provider/2',
      photos: [{ url: 'photo2.jpg' }]
    },
    {
      id: '3',
      name: 'Provider 3',
      address: { formattedAddress: '789 Pine St' },
      reviewCount: 15,
      averageRating: 4.8,
      url: '/provider/3',
      photos: [{ url: 'photo3.jpg' }]
    },
    {
      id: '4',
      name: 'Provider 4',
      address: { formattedAddress: '101 Elm St' },
      reviewCount: 8,
      averageRating: 3.9,
      url: '/provider/4',
      photos: [{ url: 'photo4.jpg' }]
    }
  ] as unknown as Provider[];

  const mockSession = {
    sessionId: 'test-session-id'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSessionStorage.clear();
  });

  it('renders nothing when providers array is empty', () => {
    render(<ProviderList providers={[]} />);
    expect(screen.queryByTestId('provider-list')).not.toBeInTheDocument();
  });

  it('renders title and content when provided', () => {
    render(
      <ProviderList
        providers={mockProviders}
        title="Test Title"
        content="Test Content"
      />
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('initially renders only 3 providers when shouldLimitVisibleProviders is true', () => {
    render(<ProviderList providers={mockProviders} />);

    const providerCards = screen.getAllByTestId('provider-card');
    expect(providerCards).toHaveLength(3);
    expect(screen.getByText('Provider 1')).toBeInTheDocument();
    expect(screen.getByText('Provider 2')).toBeInTheDocument();
    expect(screen.getByText('Provider 3')).toBeInTheDocument();
    expect(screen.queryByText('Provider 4')).not.toBeInTheDocument();
  });

  it('shows more providers when "Show More" button is clicked', () => {
    render(<ProviderList providers={mockProviders} />);

    expect(screen.getAllByTestId('provider-card')).toHaveLength(3);

    fireEvent.click(screen.getByText('Show More'));

    expect(screen.getAllByTestId('provider-card')).toHaveLength(4);
    expect(screen.getByText('Provider 4')).toBeInTheDocument();
  });

  it('does not render "Show More" button when all providers are visible', () => {
    render(<ProviderList providers={mockProviders.slice(0, 3)} />);

    expect(screen.queryByText('Show More')).not.toBeInTheDocument();
  });

  it('renders all providers when shouldLimitVisibleProviders is false', () => {
    render(
      <ProviderList
        providers={mockProviders}
        shouldLimitVisibleProviders={false}
      />
    );

    expect(screen.getAllByTestId('provider-card')).toHaveLength(4);
    expect(screen.queryByText('Show More')).not.toBeInTheDocument();
  });

  it('tracks provider list viewed when providers are rendered with session', () => {
    render(
      <SessionContext.Provider
        value={{
          sessionId: 'test-session-id',
          pageSessionId: 'test-page-session-id'
        }}
      >
        <ProviderList
          providers={mockProviders}
          listName="Test List"
          listVersion="2.0"
        />
      </SessionContext.Provider>
    );

    expect(trackProviderListViewed).toHaveBeenCalledWith({
      session: {
        sessionId: 'test-session-id',
        pageSessionId: 'test-page-session-id'
      },
      providers: mockProviders,
      listName: 'Test List',
      listVersion: '2.0'
    });
  });

  it('persists visible count in session storage', () => {
    mockSessionStorage.setItem('provider-list-count-Provider list-1', '2');

    render(<ProviderList providers={mockProviders} />);

    expect(screen.getAllByTestId('provider-card')).toHaveLength(2);

    fireEvent.click(screen.getByText('Show More'));

    expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
      'provider-list-count-Provider list-1',
      '4'
    );
  });
});
