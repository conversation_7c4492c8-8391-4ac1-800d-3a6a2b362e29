.container {
  width: 100%;
  margin: 0 auto;
}

.providerContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.title {
  font-size: 30px;
  font-weight: bold;
  padding-top: 24px;
}

.content {
  font-size: 18px;
  padding-top: 8px;
}

.showMoreButtonContainer {
  display: flex;
  justify-content: right;
}

.showMoreButton {
  font-size: 18px;
  font-weight: bold;
  color: var(--chakra-colors-primary-600);
  background: white;
  border: 2px solid var(--chakra-colors-primary-600);
  cursor: pointer;
  border-radius: 25px;
  padding: 12px 75px;
  margin-top: 2rem;
}

@media (max-width: 900px) {
  .providerContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  .title {
    font-size: 24px;
  }
  .content {
    font-size: 16px;
  }
  .title,
  .content {
    margin-left: 16px;
  }
  .showMoreButtonContainer {
    margin-right: 16px;
  }
}
@media (max-width: 600px) {
  .providerContainer {
    grid-template-columns: 1fr;
    gap: .75rem;
  }
}