'use client';

import { trackProviderListViewed } from '@components/Analytics/events/ProviderListViewed';
import ProviderCard from '@components/ProviderCard/V2/ProviderCard';
import inBrowser from '@utils/inBrowser';
import { useCallback, useEffect, useState } from 'react';

import { Provider } from '~/contexts/Provider';
import { useSessionContext } from '~/contexts/SessionContext';
import {
  calculateProviderMinPrice,
  contractIsSubscription,
  providerIsEnhancedAndNotSuppressed
} from '~/utils/providers';

import styles from './ProviderList.module.css';

const ProviderList = ({
  title,
  content,
  providers,
  listName = 'Provider list',
  listVersion = '1',
  shouldLimitVisibleProviders = true
}: {
  title?: string;
  content?: string;
  providers: Provider[];
  listName?: string;
  listVersion?: string;
  shouldLimitVisibleProviders?: boolean;
}) => {
  const storageKey = `provider-list-count-${listName}-${listVersion}`;

  const [visibleCount, setVisibleCount] = useState(() => {
    if (inBrowser()) {
      const saved = sessionStorage.getItem(storageKey);
      return saved ? parseInt(saved, 10) : 3;
    }
    return 3;
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(storageKey, visibleCount.toString());
    }
  }, [visibleCount, storageKey]);

  const session = useSessionContext();
  const [hasTracked, setHasTracked] = useState(false);
  const trackingEffect = useCallback(() => {
    if (providers.length === 0 || !session?.sessionId || hasTracked) {
      return;
    }

    trackProviderListViewed({
      session,
      providers,
      listName,
      listVersion
    });
    setHasTracked(true);
  }, [providers, session, listName, listVersion, hasTracked]);

  useEffect(() => {
    trackingEffect();
  }, [trackingEffect]);

  if (providers.length === 0) {
    return null;
  }

  const showMore = () => {
    setVisibleCount((prev) => Math.min(prev + 3, providers.length));
  };

  return (
    <div className={styles.container} data-testid="provider-list">
      {title && <h2 className={styles.title}>{title}</h2>}
      {content && <p className={styles.content}>{content}</p>}
      <div className={styles.providerContainer}>
        {providers.map((provider: any, index) => {
          if (shouldLimitVisibleProviders && index >= visibleCount) {
            return null;
          }
          const minPrice = calculateProviderMinPrice({ result: provider });
          const isSubscription = contractIsSubscription(provider);
          const isEnhanced = providerIsEnhancedAndNotSuppressed(provider);
          return (
            <ProviderCard
              key={provider.id}
              {...provider}
              title={provider.name}
              address={provider.address?.formattedAddress || ''}
              reviewCount={provider.reviewCount}
              averageRating={provider.averageRating}
              path={provider.url || ''}
              images={provider.photos?.map((photo) => photo.url) ?? []}
              price={minPrice}
              showVerifiedBadge={isSubscription || isEnhanced}
              promotions={provider.promotions}
              requestInfoButtonText="Get Pricing"
              modalId="saved-inqiury-modal"
              displayBadges
              shouldDisplayCareTypes
              shouldHideDescription
              shouldUseContainmentContext
            />
          );
        })}
      </div>
      {shouldLimitVisibleProviders && visibleCount < providers.length && (
        <div className={styles.showMoreButtonContainer}>
          <button onClick={showMore} className={styles.showMoreButton}>
            Show More
          </button>
        </div>
      )}
    </div>
  );
};

export default ProviderList;
