import { Flex, FormLabel, Switch } from '@chakra-ui/react';

interface ProviderMatchAllFiltersProps {
  onChange(value: boolean): void;
  value?: boolean;
}

const ProviderMatchAllFilters = ({
  value = false,
  onChange
}: ProviderMatchAllFiltersProps) => {
  const name = 'matchAllFilters';

  function handleInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    onChange(e.target.checked);
  }

  return (
    <Flex
      justifyContent="space-between"
      alignItems="center"
      mt={6}
      mb={4}
      ml={4}
    >
      <FormLabel htmlFor={name}>
        Only show providers that fit all selected filters
      </FormLabel>
      <Switch
        id={name}
        size="lg"
        isChecked={value}
        onChange={handleInputChange}
      />
    </Flex>
  );
};

export default ProviderMatchAllFilters;
