import { getPopularCities, PopularCity } from '@hooks/use-popular-cities';
import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
import { getStateFromURL } from '@utils/getStateFromURL';
import { isJSON } from '@utils/isJSON';
import { modifyTrailingSlash } from '@utils/modifyTrailingSlash';
import { GetServerSidePropsContext } from 'next';

import { DEFAULT_CARE_TYPE } from '~/config/senior-homes';
import { findSiteForContext } from '~/contexts/SiteContext';

import { InterlinkingPopularLocalesProps } from './InterlinkingPopularLocales';

type PopularLocale = {
  name: string;
  url: string;
};

const getPopularCitiesForState = (
  cities: Array<PopularLocale>,
  state: string
) => {
  return filterCitiesByState(cities, state).map((locale) => {
    const city = modifyTrailingSlash(false, locale.url).replace(
      /.*\/.*\/(.*)/,
      '$1'
    );
    return city;
  });
};

const filterCitiesByState = (cities: Array<PopularLocale>, state: string) => {
  return cities.filter((city) => {
    const stateFromCity = modifyTrailingSlash(false, city.url).replace(
      /.*\/(.*)\/.*/,
      '$1'
    );
    return stateFromCity === state;
  });
};

export const getServerSideComponentProps = async (
  props: InterlinkingPopularLocalesProps,
  context: GetServerSidePropsContext
): Promise<PopularCity[]> => {
  const site = findSiteForContext(context);
  const params = context.params || {};
  const { popularLocalesArray } = props;
  const state = getStateFromURL(params) || '';
  const careType = getCareTypeFromURL(params) || DEFAULT_CARE_TYPE;
  const hasValidJSON = isJSON(popularLocalesArray);
  const nonValidatedCities = getPopularCitiesForState(
    hasValidJSON ? JSON.parse(popularLocalesArray) : [],
    state
  );

  return await getPopularCities({
    site,
    nonValidatedCities,
    state,
    careType
  });
};
