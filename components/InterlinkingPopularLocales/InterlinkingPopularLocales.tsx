import { Box, Grid, GridItem, Heading, Link } from '@chakra-ui/layout';
import { PopularCity } from '@hooks/use-popular-cities';

import { HeadingSizes } from '~/@types/heading';

export type InterlinkingPopularLocalesProps = {
  title: string;
  titleSize: HeadingSizes;
  popularLocalesArray: string /* JSON */;
  data: PopularCity[] | undefined;
};

const InterlinkingPopularLocales: React.FC<InterlinkingPopularLocalesProps> = ({
  title,
  titleSize,
  data
}) => {
  if (!data?.length) {
    return <></>;
  }

  return (
    <Box
      bgColor="gray.50"
      display="flex"
      flexDirection="column"
      borderRadius="md"
      gap={10}
      py={16}
      px={12}
    >
      <Heading fontSize={titleSize} color="primary.800">
        {title}
      </Heading>
      <Grid
        templateColumns={{
          lg: 'repeat(5, 1fr)',
          md: 'repeat(4, 1fr)',
          sm: 'repeat(2, 1fr)',
          base: 'repeat(1, 1fr)'
        }}
        rowGap={4}
      >
        {data.map(({ name, path }) => (
          <GridItem key={name}>
            <Link textDecoration="underline" href={path}>
              {name}
            </Link>
          </GridItem>
        ))}
      </Grid>
    </Box>
  );
};

export default InterlinkingPopularLocales;
