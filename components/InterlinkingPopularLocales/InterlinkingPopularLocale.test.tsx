import { render, screen } from '@utils/test-utils';

import InterlinkingPopularLocales from './index';

describe('InterlinkingPopularLocales', () => {
  it('should not render when data is undefined', async () => {
    const { container } = render(<InterlinkingPopularLocales />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });
  it('should not render when data is empty', async () => {
    const { container } = render(<InterlinkingPopularLocales data={[]} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when data is available', async () => {
    const data = [
      {
        name: 'City',
        path: 'popular'
      }
    ];
    const title = 'INTERLINK POPULAR LOCALE TITLE';
    const titleZise = 'size';
    const { container } = render(
      <InterlinkingPopularLocales
        data={data}
        title={title}
        titleZise={titleZise}
        switchable={{
          excludeCareTypes: {
            'continuing-care': true,
            'assisted-living': true,
            'memory-care': true,
            'retirement-communities': true,
            'independent-living': true,
            'nursing-homes': true
          }
        }}
      />
    );

    expect(container.firstChild).not.toBeEmptyDOMElement();

    const heading = screen.getByText(title) as HTMLElement;
    expect(heading).not.toBeEmptyDOMElement();

    const gridContentLink = screen.getByText(data[0]?.name);
    expect(gridContentLink.getAttribute('href')).toEqual(data[0]?.path);
  });
});
