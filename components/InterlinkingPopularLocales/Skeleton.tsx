import { Box, Grid, GridItem } from '@chakra-ui/layout';
import { Skeleton as ChakraSkeleton } from '@chakra-ui/skeleton';

const Skeleton = () => {
  return (
    <Box
      bgColor="gray.50"
      display="flex"
      flexDirection="column"
      borderRadius="md"
      gap={10}
      py={16}
      px={12}
    >
      <ChakraSkeleton h="18px" />
      <Grid templateColumns="repeat(5, 1fr)" gap={4}>
        {Array.from({ length: 3 }).map((_, index) => {
          return (
            <GridItem key={index}>
              <ChakraSkeleton h="24px" />
            </GridItem>
          );
        })}
      </Grid>
    </Box>
  );
};

export default Skeleton;
