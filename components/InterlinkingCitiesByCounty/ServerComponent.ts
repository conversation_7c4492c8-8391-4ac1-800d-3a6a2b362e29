import { getCitiesByCountyAndCareType } from '@services/graphql/catalog-nearby-geos';
import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';
import { Catalog } from '~/types/LocaleCatalog';

import { InterlinkingCitiesByCountyProps } from './InterlinkingCitiesByCounty';

const fetchCountyCitiesFromCatalog = async ({ site, careType, county }) => {
  const results = await getCitiesByCountyAndCareType({
    county,
    careType,
    domain: site.path
  });

  return results;
};

export const getServerSideComponentProps = async (
  { careType, county }: InterlinkingCitiesByCountyProps,
  context: GetServerSidePropsContext
): Promise<Array<Catalog>> => {
  const params = context.params || {};
  const careTypeFromURL = getCareTypeFromURL(params);
  const site = findSiteForContext(context);

  const alteredCareType = isObject(careType) ? careType.name : careTypeFromURL;

  const result = await fetchCountyCitiesFromCatalog({
    site,
    careType: alteredCareType,
    county
  });
  return result;
};
