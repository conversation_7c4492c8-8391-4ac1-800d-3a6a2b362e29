import { useBreakpointValue } from '@chakra-ui/react';
import InterlinkingCard from '@components/Interlinking';
import { InterlinkingProps } from '@components/Interlinking/Interlinking';
import { getBackgroundColor } from '@utils/getColor';

export interface InterlinkingCitiesByCountyProps extends InterlinkingProps {
  careType: string | { name: string };
  county: string;
  data: string[];
}

const InterlinkingCitiesByCounty = ({
  title,
  titleSize,
  titleAlignment = 'left',
  headingElement = 'h2',
  columns = '3',
  data,
  bgColor,
  bgColorRange,
  titleColor = 'primary',
  titleColorRange = '700',
  linksColor = 'link',
  linksColorRange = '600',
  linksTextDecoration = 'none',
  hideOnMobile = false
}: InterlinkingCitiesByCountyProps): React.ReactElement => {
  const isVisible = useBreakpointValue({ base: 'none', lg: 'flex' });
  const backgroundColor = getBackgroundColor(bgColor, bgColorRange);
  return data && data?.length > 0 ? (
    <InterlinkingCard
      backgroundColor={backgroundColor}
      py={8}
      px={54}
      display={hideOnMobile ? isVisible : 'flex'}
    >
      <InterlinkingCard.Container>
        {title && (
          <InterlinkingCard.Heading
            title={title}
            titleSize={titleSize}
            titleColor={titleColor}
            titleAlignment={titleAlignment}
            headingElement={headingElement}
            titleColorRange={titleColorRange}
            pb="4"
          />
        )}

        <InterlinkingCard.LinkGrid
          columns={{ base: 1, md: 2, lg: parseInt(columns) }}
        >
          <InterlinkingCard.LinksList
            data={data}
            type="countyCities"
            linksColor={linksColor}
            linksColorRange={linksColorRange}
            linksTextDecoration={linksTextDecoration}
          />
        </InterlinkingCard.LinkGrid>
      </InterlinkingCard.Container>
    </InterlinkingCard>
  ) : (
    <></>
  );
};

export default InterlinkingCitiesByCounty;
