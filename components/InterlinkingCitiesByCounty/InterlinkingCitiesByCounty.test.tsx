import { toCapitalizedWords } from '@utils/strings';
import {
  mockInterlinkingCitiesByCounty,
  render,
  screen
} from '@utils/test-utils';
import startCase from 'lodash/startCase';

import InterlinkingCitiesByCounty from './index';

describe('InterlinkingCitiesByCounty', () => {
  it('should not render component when data is empty', async () => {
    const { container } = render(
      <InterlinkingCitiesByCounty
        data={mockInterlinkingCitiesByCounty.emptyData}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when data is not empty', async () => {
    const { container } = render(
      <InterlinkingCitiesByCounty
        bgColor="white"
        data={mockInterlinkingCitiesByCounty.data}
        careType={mockInterlinkingCitiesByCounty.careType}
      />
    );
    mockInterlinkingCitiesByCounty.data.forEach((e) => {
      expect(
        screen.findByText(
          startCase(
            `Assisted Living in ${startCase(toCapitalizedWords(e.city))}`
          )
        )
      ).toBeVisible;
      expect(
        screen.getByRole('link', {
          name: `Assisted Living in ${startCase(toCapitalizedWords(e.city))}`
        })
      ).toHaveAttribute('href', `${e.urlPath}`);
    });
    expect(container.children).toHaveLength(2);
  });
});
