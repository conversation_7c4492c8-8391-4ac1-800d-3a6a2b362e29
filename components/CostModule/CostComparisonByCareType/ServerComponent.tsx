import { ModularMonolithClient } from '@services/modular-monolith/client';
import { getServiceCategory } from '@utils/getServiceCategory';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { BarChartItemData, CostGraphProps } from '../types';

export const getServerSideComponentProps = async (
  { state, county, careType, geoType, moveInThreshold = 10 }: CostGraphProps,
  context: GetServerSidePropsContext
): Promise<BarChartItemData[] | undefined> => {
  const site = findSiteForContext(context);
  const alteredCounty = county?.replace('County', '').trim();
  const modularMonolithClient = new ModularMonolithClient(site.path);
  const serviceCategory = getServiceCategory(
    isObject(careType) ? careType.name : careType
  );

  if (
    !state ||
    !serviceCategory ||
    ![5, 8, 9, 11].includes(serviceCategory.id)
  ) {
    return undefined;
  }

  const response = await modularMonolithClient.getCostsByCareType({
    state: state,
    county: alteredCounty,
    geoType,
    moveInThreshold
  });

  if (!response || !response.length) {
    return undefined;
  }

  const chartData = response.map((item) => ({
    name: getServiceCategory(item.serviceCategoryId)?.name ?? '',
    cost: Number(
      geoType === 'county' ? item.averageCostCounty : item.averageCostState
    )
  }));
  return chartData;
};
