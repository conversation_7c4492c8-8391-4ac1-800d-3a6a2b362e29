interface CostModuleGradientProps {
  color: string;
  isHorizontal?: boolean;
}

const CostModuleGradient = ({
  color,
  isHorizontal = false
}: CostModuleGradientProps) => {
  const ucColor = color.toUpperCase();
  // A bit of AI magic
  // Convert hex color to RGB

  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.substring(1), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return `${r},${g},${b}`;
  };

  const baseColor = hexToRgb(ucColor);
  const lighterColor = baseColor
    .split(',')
    .map((c) => Math.min(parseInt(c) + 75, 255))
    .join(',');

  return (
    <linearGradient
      id={`colorGradient${ucColor}`}
      x1="0"
      y1={isHorizontal ? '0' : '1'}
      x2={isHorizontal ? '1' : '0'}
      y2="0"
    >
      <stop offset="5%" stopColor={`rgb(${lighterColor})`} stopOpacity={1} />
      <stop offset="95%" stopColor={`rgb(${baseColor})`} stopOpacity={1} />
    </linearGradient>
  );
};

export default CostModuleGradient;
