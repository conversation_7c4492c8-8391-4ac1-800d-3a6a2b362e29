import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Text
} from '@chakra-ui/react';

interface CostModuleAccordionProps {
  title: string;
  body?: string;
  isCollapsed?: boolean;
  children: React.ReactNode;
}

const CostModuleAccordion = ({
  title,
  body,
  isCollapsed = false,
  children
}: CostModuleAccordionProps) => {
  return (
    <Accordion allowToggle defaultIndex={isCollapsed ? [1] : [0]}>
      <AccordionItem border="none">
        {({ isExpanded }) => (
          <>
            <Text as="h2">
              <AccordionButton
                backgroundColor={'#086961'}
                _hover={{ backgroundColor: '#086961' }}
                borderRadius={isExpanded ? '6px 6px 0 0' : 'md'}
                padding={'5'}
              >
                <Box
                  as="span"
                  flex="1"
                  textAlign="left"
                  textColor="white"
                  fontSize="xl"
                  fontWeight="bold"
                >
                  {title}
                </Box>
                {isExpanded ? (
                  <MinusIcon fontSize="12px" color="white" />
                ) : (
                  <AddIcon fontSize="12px" color="white" />
                )}
              </AccordionButton>
            </Text>
            <AccordionPanel
              py={{ base: 5, md: 6 }}
              px={{ base: 4, md: 6 }}
              border="1px solid"
              borderColor="gray.400"
              borderRadius="0 0 6px 6px"
            >
              <Box display="flex" flexDirection="column" position="relative">
                {body && (
                  <Box
                    color="gray.700"
                    fontSize="md"
                    textAlign="left"
                    className="magnolia-text"
                    marginBottom={{ base: 4, md: 6 }}
                    dangerouslySetInnerHTML={{ __html: body }}
                  />
                )}
                {children}
              </Box>
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    </Accordion>
  );
};
export default CostModuleAccordion;
