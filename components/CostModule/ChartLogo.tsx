import { useBreakpointValue } from '@chakra-ui/react';
import DAMImage from '@components/Image/DAMImage';

import { MagnoliaImage } from '~/types/Magnolia';

const ChartLogo = ({ logo }: { logo: MagnoliaImage }): JSX.Element => {
  const maxHeight = useBreakpointValue({ base: '24px', md: '35px' });

  return (
    <DAMImage
      src={logo}
      style={{
        position: 'absolute',
        width: 'min-content',
        maxHeight: maxHeight,
        marginLeft: 'auto',
        right: '0',
        bottom: '0'
      }}
    />
  );
};

export default ChartLogo;
