import Container from '@components/LayoutStructure/Container';
import dynamic from 'next/dynamic';

import CostModuleAccordion from '../CostModuleAccordion/CostModuleAccordion';
import { BarChartProps } from '../types';
const VerticalBarChart = dynamic(
  () => import('@components/CostModule/Graphs/VerticalBarChart')
);
const HorizontalBarChart = dynamic(
  () => import('@components/CostModule/Graphs/HorizontalBarChart')
);
const ChartLogo = dynamic(() => import('@components/CostModule/ChartLogo'));

const CostComparisonByLocale = ({
  title,
  body,
  displayDirection,
  isCollapsed = false,
  logo,
  data
}: BarChartProps): JSX.Element => {
  if (!data) return <></>;
  return (
    <Container>
      <CostModuleAccordion title={title} body={body} isCollapsed={isCollapsed}>
        {displayDirection === 'horizontal' ? (
          <HorizontalBarChart data={data} />
        ) : (
          <VerticalBarChart data={data} />
        )}
        {logo && <ChartLogo logo={logo} />}
      </CostModuleAccordion>
    </Container>
  );
};

export default CostComparisonByLocale;
