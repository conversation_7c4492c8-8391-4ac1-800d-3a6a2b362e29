import { ModularMonolithClient } from '@services/modular-monolith/client';
import { getServiceCategory } from '@utils/getServiceCategory';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { BarChartItemData, CostGraphProps } from '../types';

export const getServerSideComponentProps = async (
  { state, county, careType, geoType, moveInThreshold = 10 }: CostGraphProps,
  context: GetServerSidePropsContext
): Promise<BarChartItemData[] | undefined> => {
  const site = findSiteForContext(context);
  const modularMonolithClient = new ModularMonolithClient(site.path);
  const alteredCounty = county?.replace('County', '').trim() || '';

  const serviceCategory = getServiceCategory(
    isObject(careType) ? careType.name : careType
  );

  if (
    !serviceCategory ||
    !state ||
    ![5, 8, 9, 11].includes(serviceCategory.id)
  ) {
    return undefined;
  }

  const response = await modularMonolithClient.getCostsByLocale({
    state: state,
    county: alteredCounty,
    serviceCategoryId: serviceCategory?.id,
    geoType: geoType,
    moveInThreshold
  });
  if (!response || !response.length) {
    return undefined;
  }

  return response;
};
