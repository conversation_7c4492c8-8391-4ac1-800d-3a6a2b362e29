import Container from '@components/LayoutStructure/Container';
import dynamic from 'next/dynamic';

import CostModuleAccordion from '../CostModuleAccordion/CostModuleAccordion';
import { LineChartProps } from '../types';
const StraightLineChart = dynamic(
  () => import('@components/CostModule/Graphs/StraightLineChart')
);
const ChartLogo = dynamic(() => import('@components/CostModule/ChartLogo'));

const CostByYearByCareType = ({
  title,
  body,
  isCollapsed = false,
  logo,
  data
}: LineChartProps) => {
  if (!data) return <></>;
  return (
    <Container>
      <CostModuleAccordion title={title} body={body} isCollapsed={isCollapsed}>
        <StraightLineChart data={data} />
        {logo && <ChartLogo logo={logo} />}
      </CostModuleAccordion>
    </Container>
  );
};

export default CostByYearByCareType;
