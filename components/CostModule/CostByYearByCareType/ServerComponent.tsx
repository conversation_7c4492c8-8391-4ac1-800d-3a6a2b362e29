import { ModularMonolithClient } from '@services/modular-monolith/client';
import { getServiceCategory } from '@utils/getServiceCategory';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { CostGraphProps, LineChartItemData } from '../types';

export const getServerSideComponentProps = async (
  { state, county, careType, moveInThreshold = 10 }: CostGraphProps,
  context: GetServerSidePropsContext
): Promise<LineChartItemData[] | undefined> => {
  const site = findSiteForContext(context);
  const alteredCounty = county?.replace('County', '').trim();
  const modularMonolithClient = new ModularMonolithClient(site.path);
  const serviceCategory = getServiceCategory(
    isObject(careType) ? careType.name : careType
  );

  if (
    !serviceCategory ||
    !state ||
    ![5, 8, 9, 11].includes(serviceCategory.id)
  ) {
    return undefined;
  }

  const response = await modularMonolithClient.getCostsByYearForCareType({
    state: state,
    county: alteredCounty || '',
    serviceCategoryId: serviceCategory?.id,
    moveInThreshold
  });

  if (!response || !response.length) {
    return undefined;
  }
  let transformedResponse = response;

  if (serviceCategory.id === 11) {
    transformedResponse = response.map((item) => {
      const { date, ...rest } = item;
      const transformedItem = { date };

      Object.keys(rest).forEach((key) => {
        const category = getServiceCategory(parseInt(key));
        const newKey = category ? category.name : key;
        transformedItem[newKey] = rest[key];
      });

      return transformedItem;
    });
  }

  return transformedResponse;
};
