import { useBreakpointValue, useTheme } from '@chakra-ui/react';
import { useColors } from '@utils/useColors';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  LabelList,
  Legend,
  ResponsiveContainer,
  XAxis,
  YAxis
} from 'recharts';
import { Payload } from 'recharts/types/component/DefaultLegendContent';

import CostModuleGradient from '../CostModuleGradient';
import { BarChartItemData } from '../types';
import { formatCurrencyGraph } from '../utils';
const VerticalBarChart = ({ data }: { data: BarChartItemData[] }) => {
  const theme = useTheme();
  const colors = useColors();

  const leftMargin = useBreakpointValue({ base: 0, md: 5 });
  const textSize = useBreakpointValue({ base: '10px', md: '16px' });
  const textSizeBarLabel = useBreakpointValue({ base: 14, md: 24 });
  const barSize = useBreakpointValue({ base: 51, md: 160 });
  const chartHeight = useBreakpointValue({ base: 310, md: 380 });
  const legendPaddingTop = useBreakpointValue({ base: 10, md: 38 });
  const yWidth = useBreakpointValue({ base: 40, md: 55 });
  const colorsGray700 = theme.colors.gray[700];
  const chartPadding = useBreakpointValue({ base: 10, md: 20 });

  const legendData: Payload[] = data.map((item, index) => ({
    value: item.name,
    type: 'rect',
    id: `ID${index + 1}`,
    color: colors[index],
    formatter: (value) => (
      <span
        style={{
          color: 'black',
          paddingLeft: 2,
          paddingRight: 12,
          fontSize: textSize
        }}
      >
        {value}
      </span>
    )
  }));
  const gradients = colors.map((color, index) => (
    <CostModuleGradient color={color} isHorizontal={false} key={index} />
  ));

  return (
    <ResponsiveContainer
      width="100%"
      height="100%"
      minHeight={chartHeight}
      style={{ paddingBottom: chartPadding }}
    >
      <BarChart
        layout="horizontal"
        width={400}
        height={chartHeight}
        data={data}
        margin={{
          top: 30,
          right: 0,
          left: leftMargin,
          bottom: -5
        }}
      >
        <defs>{gradients}</defs>
        <CartesianGrid vertical={true} stroke={theme.colors.gray[200]} />
        <XAxis type="category" dataKey="name" hide />
        <YAxis
          type="number"
          tickFormatter={formatCurrencyGraph}
          tickLine={false}
          width={yWidth}
          tick={{ fontSize: textSize, fontWeight: 700, fill: colorsGray700 }}
        />
        <Bar dataKey="cost" maxBarSize={barSize} radius={[8, 8, 0, 0]}>
          {colors.map((color, index) => (
            <Cell key={`cell-${index}`} fill={`url(#colorGradient${color})`} />
          ))}
          <LabelList
            dataKey="cost"
            position="top"
            formatter={formatCurrencyGraph}
            style={{
              fill: theme.colors.primary[700],
              fontSize: textSizeBarLabel,
              fontWeight: 700
            }}
          />
        </Bar>
        <Legend
          layout="horizontal"
          payload={legendData}
          align="left"
          display="block"
          wrapperStyle={{
            paddingTop: legendPaddingTop,
            marginRight: '20px'
          }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};
export default VerticalBarChart;
