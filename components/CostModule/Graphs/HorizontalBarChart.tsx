import { useBreakpointValue, useTheme } from '@chakra-ui/react';
import maxBy from 'lodash/maxBy';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  LabelList,
  ResponsiveContainer,
  XAxis,
  YAxis
} from 'recharts';

import CostModuleGradient from '../CostModuleGradient';
import { BarChartItemData } from '../types';
import { formatCurrencyGraph } from '../utils';

const HorizontalBarChart = ({ data }: { data: BarChartItemData[] }) => {
  const theme = useTheme();
  const colorsGray700 = theme.colors.gray[700];
  const colorsSecondary500 = theme.colors.secondary[500];
  const longName = (maxBy(data, (el) => el.name.length)?.name.length ?? 0) > 10;
  const leftMargin = useBreakpointValue({
    base: longName ? 23 : 10,
    md: longName ? 70 : 45
  });
  const textSize = useBreakpointValue({ base: '10', md: '16' });
  const barSize = useBreakpointValue({ base: 28, md: 56 });
  const barTextSize = useBreakpointValue({ base: 14, md: 24 });
  const chartHeight = useBreakpointValue({ base: 250, md: 400 });
  const chartPadding = useBreakpointValue({ base: 10, md: 20 });

  return (
    <ResponsiveContainer
      width="100%"
      height="100%"
      minHeight={chartHeight}
      style={{ fontSize: textSize, paddingBottom: chartPadding }}
    >
      <BarChart
        layout="vertical"
        width={500}
        height={chartHeight}
        data={data}
        margin={{
          top: 5,
          right: 10,
          left: leftMargin,
          bottom: 5
        }}
      >
        <defs>
          <CostModuleGradient color={colorsSecondary500} isHorizontal={true} />
        </defs>
        <CartesianGrid horizontal={false} />
        <XAxis
          type="number"
          tickFormatter={formatCurrencyGraph}
          tickLine={false}
          style={{ marginRight: 0 }}
          tick={{ fontSize: textSize, fontWeight: 700, fill: colorsGray700 }}
        />
        <YAxis
          type="category"
          dataKey="name"
          tickLine={false}
          width={40}
          tick={{ fontSize: textSize, fontWeight: 700, fill: colorsGray700 }}
        />
        <Bar
          dataKey="cost"
          fill="url(#colorGradient#E27455)"
          maxBarSize={barSize}
          radius={[0, 8, 8, 0]}
        >
          <LabelList
            dataKey="cost"
            position="insideRight"
            formatter={formatCurrencyGraph}
            style={{ fill: 'white', fontSize: barTextSize, fontWeight: 700 }}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default HorizontalBarChart;
