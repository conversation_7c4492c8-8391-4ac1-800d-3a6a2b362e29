import { FlexProps, useBreakpointValue, useTheme } from '@chakra-ui/react';
import { useColors } from '@utils/useColors';
import { CgLoadbar } from 'react-icons/cg';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';

import { LineChartItemData } from '../types';
import { formatCurrencyGraph } from '../utils';

const StraightLineChart = ({ data }: { data: LineChartItemData[] }) => {
  const theme = useTheme();
  const colors = useColors();
  const colorsGray700 = theme.colors.gray[700];
  const textSize = useBreakpointValue({ base: '10px', md: '16px' });
  const yWidth = useBreakpointValue({ base: 40, md: 55 });
  const legendPaddingTop = useBreakpointValue({ base: 10, md: 38 });
  const dataPoints = Object.keys(data[0]).slice(1, 5);
  const chartHeight = useBreakpointValue({ base: 250, md: 350 });
  const chartPadding = useBreakpointValue({ base: 0, md: 20 });
  const legendFlexWrap: FlexProps['flexWrap'] = useBreakpointValue({
    base: 'wrap',
    md: 'nowrap'
  });
  const legendFlexWidth = useBreakpointValue({ base: '50%', md: 'auto' });
  const legendFlexPadding = useBreakpointValue({ base: 7, md: 14 });

  return (
    <ResponsiveContainer
      width="100%"
      height="100%"
      minHeight={chartHeight}
      style={{
        fontSize: textSize,
        position: 'relative',
        paddingBottom: chartPadding
      }}
    >
      <LineChart
        width={600}
        height={chartHeight}
        data={data}
        margin={{
          top: 0,
          right: 20,
          left: 0,
          bottom: 5
        }}
      >
        <CartesianGrid />
        <XAxis
          dataKey="date"
          interval={0}
          tickMargin={5}
          textAnchor="middle"
          fontWeight={600}
        />
        <YAxis
          tickFormatter={formatCurrencyGraph}
          width={yWidth}
          tick={{ fontSize: textSize, fontWeight: 600, fill: colorsGray700 }}
        />
        <Tooltip formatter={formatCurrencyGraph} />
        {dataPoints.map((item, index) => (
          <Line
            key={index}
            dataKey={item}
            stroke={colors[index]}
            strokeWidth={2}
            dot={{ fill: colors[index] }}
          />
        ))}
        <Legend
          width={200}
          iconType="rect"
          wrapperStyle={{
            paddingTop: legendPaddingTop,
            paddingRight: '40px',
            width: '100%'
          }}
          content={(props) => {
            return (
              <div style={{ display: 'flex', flexWrap: legendFlexWrap }}>
                {props?.payload?.map((entry, index) => (
                  <div
                    key={`item-${index}`}
                    style={{
                      color: entry.color,
                      display: 'flex',
                      width: legendFlexWidth,
                      alignItems: 'center',
                      paddingRight: legendFlexPadding
                    }}
                  >
                    <CgLoadbar size={24} />
                    <span style={{ color: 'black', paddingLeft: 2 }}>
                      {entry.value}
                    </span>
                  </div>
                ))}
              </div>
            );
          }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};
export default StraightLineChart;
