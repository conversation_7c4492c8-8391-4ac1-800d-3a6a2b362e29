import { CareType } from '@components/FacetedSearch/types';

import { MagnoliaImage } from '~/types/Magnolia';

export interface BarChartItemData {
  name: string;
  cost: number;
}
export interface LineChartItemData {
  [key: string]: number;
}
export interface CostGraphProps {
  title: string;
  state: string;
  careType: CareType;
  geoType: string;
  moveInThreshold: number;
  body?: string;
  isCollapsed?: boolean;
  county?: string;
  logo?: MagnoliaImage;
  displayDirection?: string;
}

export interface LineChartProps extends CostGraphProps {
  data: LineChartItemData[] | null;
}
export interface BarChartProps extends CostGraphProps {
  data: BarChartItemData[] | null;
}
