import { Center, Divider, Link, Text } from '@chakra-ui/layout';
import { LinkProps } from '@chakra-ui/react';
import { useContext } from 'react';

import SiteContext from '~/contexts/SiteContext';
import { Domains } from '~/types/Domains';
interface Props extends LinkProps {
  isSubscription: boolean;
  isEnhanced: boolean;
  legacyId?: string;
}

const PageTitleBannerClaimListingButton: React.FC<Props> = ({
  isEnhanced,
  legacyId = '',
  isSubscription,
  ...rest
}) => {
  const { site } = useContext(SiteContext);
  const isCaringWebSite = site?.path === Domains.CaringDomains.LIVE;

  if (!isCaringWebSite || isEnhanced || isSubscription || !legacyId)
    return null;

  return (
    <>
      <Center height="36px" mx="22px">
        <Divider orientation="vertical" />
      </Center>
      <Link
        isExternal
        color="primary.700"
        aria-labelledby="claim-this-listing"
        to="/partners/get-listed/"
        rel="nofollow"
        alignSelf="center"
        {...rest}
      >
        <Text id="claim-this-listing" fontSize="md">
          Claim this listing
        </Text>
      </Link>
    </>
  );
};

export default PageTitleBannerClaimListingButton;
