import { Link, Text } from '@chakra-ui/layout';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import dynamic from 'next/dynamic';
import { useContext } from 'react';
import { MdPhone } from 'react-icons/md';

import SiteContext from '~/contexts/SiteContext';
import { formatPhone } from '~/utils/strings';

const PageTitleBannerItem = dynamic(() => import('./PageTitleBannerItem'));

interface Props {
  phoneNumber?: string;
  providerPhoneNumber?: string;
  isSubscription: boolean;
  isEnhanced?: boolean;
  phoneNumberPreText?: string;
  phoneNumberPostText?: string;
}

const PhoneNumber = ({
  phoneNumber,
  providerPhoneNumber,
  isSubscription,
  isEnhanced,
  phoneNumberPostText,
  phoneNumberPreText,
  domain = ''
}) => {
  const elementClicked = useElementClicked();

  const handleAnalyticsCallClick = (e) => {
    switch (domain) {
      case 'seniorhomes.com':
        return elementClicked({
          element: {
            type: ElementTypes.TELEPHONE,
            action: ElementActions.PHONE_CALL,
            name: ElementNames.PHONE_CALL_BUTTON,
            text: e.target.innerText,
            color: '',
            textColor: 'secondary.400'
          },
          destinationUrl: phoneNumber
        });

      case 'caring.com':
        return elementClicked({
          element: {
            type: ElementTypes.TELEPHONE,
            action: ElementActions.PHONE_CALL,
            name: ElementNames.PHONE_CALL_BUTTON,
            text: e.target.innerText,
            color: '',
            textColor: isEnhanced ? 'primary.900' : 'secondary.400'
          },
          destinationUrl: phoneNumber
        });
      default:
        return elementClicked({
          element: {
            type: ElementTypes.TELEPHONE,
            action: ElementActions.PHONE_CALL,
            name: ElementNames.PHONE_CALL_BUTTON,
            text: e.target.innerText,
            color: '',
            textColor: 'white'
          },
          destinationUrl: phoneNumber
        });
    }
  };
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <>
          {phoneNumber && (
            <Link
              onClick={handleAnalyticsCallClick}
              href={`tel:+1${phoneNumber}`}
              textDecoration={'none'}
              color="secondary.400"
            >
              <PageTitleBannerItem
                text={
                  <>
                    {phoneNumberPreText ? `${phoneNumberPreText} ` : ''}

                    <Text
                      as="span"
                      fontSize={{ base: 'md', lg: 'xl' }}
                      fontWeight="bold"
                    >
                      {formatPhone(String(phoneNumber))}
                    </Text>

                    {phoneNumberPostText ? ` ${phoneNumberPostText}` : ''}
                  </>
                }
                icon={MdPhone}
                color="secondary.400"
              />
            </Link>
          )}
        </>
      );
    case 'caring.com':
      return (
        <>
          {phoneNumber && !isSubscription && (
            <Link
              onClick={handleAnalyticsCallClick}
              href={`tel:+1${phoneNumber}`}
              textDecoration={'none'}
              color={isEnhanced ? 'primary.900' : 'secondary.400'}
            >
              <PageTitleBannerItem
                fontSize="2xl"
                text={`${
                  phoneNumberPreText && !isEnhanced ? phoneNumberPreText : ' '
                }${formatPhone(String(phoneNumber))} ${
                  phoneNumberPostText && !isEnhanced
                    ? `${phoneNumberPostText}`
                    : ''
                }`}
                icon={MdPhone}
                color={isEnhanced ? 'primary.900' : 'secondary.400'}
                iconProps={{ width: '29px', height: '29px' }}
              />
            </Link>
          )}

          {isSubscription && (
            <Link
              onClick={handleAnalyticsCallClick}
              href={`tel:+1${
                providerPhoneNumber.replace('+1', '') || phoneNumber
              }`}
              marginInlineStart={0}
              color="primary.900"
            >
              <PageTitleBannerItem
                fontSize="2xl"
                text={`${formatPhone(
                  String(providerPhoneNumber || phoneNumber)
                )}`}
                icon={MdPhone}
                color="primary.900"
                iconProps={{ width: '29px', height: '29px' }}
              />
            </Link>
          )}
        </>
      );
    default:
      return (
        <>
          {phoneNumber && (
            <Link
              onClick={handleAnalyticsCallClick}
              href={`tel:+1${phoneNumber}`}
              textDecoration={'none'}
            >
              <PageTitleBannerItem
                text={`${
                  phoneNumberPreText ? phoneNumberPreText : ' '
                }${formatPhone(String(phoneNumber))} ${
                  phoneNumberPostText ? `${phoneNumberPostText}` : ''
                }`}
                icon={MdPhone}
              />
            </Link>
          )}
        </>
      );
  }
};

const PageTitleBannerPhoneNumber: React.FC<Props> = ({
  phoneNumber,
  providerPhoneNumber,
  isSubscription,
  isEnhanced,
  phoneNumberPostText,
  phoneNumberPreText
}) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';

  return (
    <PhoneNumber
      phoneNumber={phoneNumber}
      providerPhoneNumber={providerPhoneNumber}
      isSubscription={isSubscription}
      isEnhanced={isEnhanced}
      phoneNumberPostText={phoneNumberPostText}
      phoneNumberPreText={phoneNumberPreText}
      domain={domain}
    />
  );
};

export default PageTitleBannerPhoneNumber;
