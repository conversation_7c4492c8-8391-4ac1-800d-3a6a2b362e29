import { PageContext } from '@components/LayoutStructure/Contexts';
import FeaturedAmenities from '@components/ProviderCard/FeaturedAmenities';
import { getFeaturedAmenities } from '@utils/amenities';
import { useContext } from 'react';

import { Amenities, LegacyAmenities } from '~/contexts/Provider';

const PageTitleBannerAmenities: React.FC<{
  legacyAmenities: LegacyAmenities | undefined;
  amenities: Amenities | undefined;
}> = ({ legacyAmenities, amenities }) => {
  const page = useContext(PageContext)?.page;
  const amenityNode =
    page?.['main'][
      page?.main['@nodes']?.find(
        (value) =>
          page.main[value]['mgnl:template'] ===
          'spa-lm:components/offeringListing'
      ) || -1
    ];

  const useLegacyAmenities = amenityNode?.useLegacyAmenities;
  const availableAmenites = getFeaturedAmenities(
    useLegacyAmenities ? legacyAmenities || {} : amenities || []
  );
  return <FeaturedAmenities amenities={availableAmenites} hasJumpLink={true} />;
};

export default PageTitleBannerAmenities;
