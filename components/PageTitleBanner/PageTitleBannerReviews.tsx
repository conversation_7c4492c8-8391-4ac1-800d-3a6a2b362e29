import { Flex, Text } from '@chakra-ui/layout';
import { PageContext } from '@components/LayoutStructure/Contexts';
import { PageContextType } from '@components/LayoutStructure/Contexts.types';
import { validateComponentRender } from '@components/LayoutStructure/helpers';
import ReviewStars from '@components/ReviewStars';
import useProviderFallback from '@hooks/use-provider-fallback';
import { ProviderStatus } from '@hooks/use-provider-fallback.types';
import useTranslation from '@hooks/use-translation';
import { pluralize } from '@utils/strings';
import { createID } from '@utils/strings';
import { useContext } from 'react';

import { Provider } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { Domain } from '~/types/Domains';

interface Props {
  averageRating: string;
  reviewCount: number;
  ratingColor?: string;
  ratingColorRange?: string;
}

export const getReviewsHeading = (
  pageContext: PageContextType,
  provider: Provider | null,
  status: ProviderStatus,
  domain: Domain,
  t: (key: string, fallback: string) => string
) => {
  return (
    pageContext?.page.main &&
    pageContext?.page?.main['@nodes']
      ?.map((value) => {
        if (
          pageContext.page.main[value].headingElement &&
          pageContext.page.main[value].headingElement === 'h2' &&
          pageContext.page.main[value]['mgnl:template'] ===
            'spa-lm:components/reviews' &&
          validateComponentRender(
            pageContext.page.main[value]['mgnl:template'],
            pageContext,
            value,
            provider,
            status,
            domain,
            t
          )
        ) {
          return pageContext.page.main[value].title;
        }
      })
      .filter((value) => value !== undefined)[0]
  );
};

const PageTitleBannerReviews: React.FC<Props> = ({
  averageRating,
  reviewCount,
  ratingColor = 'tertiary',
  ratingColorRange = '400'
}) => {
  const { provider, status } = useProviderFallback();
  const pageContext = useContext(PageContext);
  const siteContext = useContext(SiteContext);
  const domain = siteContext.site?.path as Domain;
  const { t } = useTranslation();
  const reviewHeading = getReviewsHeading(
    pageContext,
    provider,
    status,
    domain,
    t
  );
  const pluralizedReviewCount = pluralize(reviewCount, 'review');
  return (
    <Flex
      as="a"
      display="flex"
      alignItems="center"
      href={`#${createID(reviewHeading)}`}
    >
      <Text
        fontSize={{ base: '20px', xl: '30px' }}
        fontWeight={700}
        color={`${ratingColor}.${ratingColorRange}`}
        aria-label={`${averageRating} star rating`}
      >
        {averageRating}
      </Text>
      <ReviewStars
        rating={averageRating}
        size={{ base: 7, xl: 9 }}
        color={`${ratingColor}.${ratingColorRange}`}
      />
      <Text
        color="gray.700"
        fontSize={{ base: 'xs', xl: 'lg' }}
        mx={{ base: '3px', xl: '10px' }}
      >
        ({reviewCount} {pluralizedReviewCount})
      </Text>
    </Flex>
  );
};

export default PageTitleBannerReviews;
