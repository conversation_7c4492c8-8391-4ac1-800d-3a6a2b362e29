import { Icon as ChakraIcon } from '@chakra-ui/icon';
import { Flex, StackProps, Text as ChakraText } from '@chakra-ui/layout';
import { useContext } from 'react';
import { IconBaseProps, IconType } from 'react-icons';

import SiteContext from '~/contexts/SiteContext';

interface Props extends StackProps {
  text: React.ReactNode;
  icon: IconType;
  iconProps?: IconBaseProps;
}

const Icon = ({ icon, domain = '', iconProps }) => {
  switch (domain) {
    case 'caring.com':
      return (
        <ChakraIcon
          as={icon}
          w="26px"
          h="26px"
          role="presentation"
          {...iconProps}
        />
      );
    default:
      return (
        <ChakraIcon
          as={icon}
          boxSize="24px"
          role="presentation"
          color="primary.700"
          {...iconProps}
        />
      );
  }
};

const Text = ({ text, domain = '' }) => {
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <ChakraText
          whiteSpace={{ base: 'normal', xl: 'nowrap' }}
          fontSize="sm"
          ml={2}
          color="primary.700"
        >
          {text}
        </ChakraText>
      );
    case 'caring.com':
      return (
        <ChakraText
          whiteSpace={{ base: 'normal', md: 'nowrap' }}
          fontWeight="bold"
          ml={2}
        >
          {text}
        </ChakraText>
      );
    default:
      return <ChakraText>{text}</ChakraText>;
  }
};

const PageTitleBannerItem: React.FC<Props> = ({
  text,
  icon,
  color,
  iconProps,
  ...rest
}) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';

  return (
    <Flex {...rest} alignItems="center">
      <Icon icon={icon} domain={domain} iconProps={iconProps} />
      <Text text={text} domain={domain} />
    </Flex>
  );
};

export default PageTitleBannerItem;
