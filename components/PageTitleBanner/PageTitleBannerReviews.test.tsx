import { render } from '@utils/test-utils';
import React from 'react';

// import customRender from '@utils/test-utils';
import PageTitleBannerReviews from './PageTitleBannerReviews';

jest.mock('@components/ReviewStars', () => {
  const reviewStarsMock = () => <div data-testid="review-stars" />;
  return reviewStarsMock;
});
describe('<ProviderReviews />', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  test('PageTitleBannerReviews renders with correct averageRating and reviewCount', () => {
    const props = {
      averageRating: '4.5',
      reviewCount: 10
    };

    const { getByText, getByTestId } = render(
      <PageTitleBannerReviews {...props} />
    );
    expect(getByText('4.5')).toBeInTheDocument();
    expect(getByText('(10 reviews)')).toBeInTheDocument();
    expect(getByTestId('review-stars')).toBeInTheDocument();
  });

  test('PageTitleBannerReviews renders with whole number averageRating and singular review count', () => {
    const props = {
      averageRating: '4',
      reviewCount: 1
    };

    const { getByText, getByTestId } = render(
      <PageTitleBannerReviews {...props} />
    );

    expect(getByText('4')).toBeInTheDocument();
    expect(getByText('(1 review)')).toBeInTheDocument();
    expect(getByTestId('review-stars')).toBeInTheDocument();
  });

  test('PageTitleBannerReviews renders with averageRating as zero and no reviews', () => {
    const props = {
      averageRating: '0',
      reviewCount: 0
    };

    const { getByText, getByTestId } = render(
      <PageTitleBannerReviews {...props} />
    );

    expect(getByText('0')).toBeInTheDocument();
    expect(getByText('(0 reviews)')).toBeInTheDocument();
    expect(getByTestId('review-stars')).toBeInTheDocument();
  });

  test('PageTitleBannerReviews renders with high averageRating and plural review count', () => {
    const props = {
      averageRating: '4.9',
      reviewCount: 50
    };

    const { getByText, getByTestId } = render(
      <PageTitleBannerReviews {...props} />
    );

    expect(getByText('4.9')).toBeInTheDocument();
    expect(getByText('(50 reviews)')).toBeInTheDocument();
    expect(getByTestId('review-stars')).toBeInTheDocument();
  });
});
