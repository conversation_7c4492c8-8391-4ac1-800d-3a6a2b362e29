import { Text } from '@chakra-ui/layout';
import { Tag as ChakraTag } from '@chakra-ui/tag';

type Props = {
  text: string;
};

const PageTitleBannerTag: React.FC<Props> = ({ text }) => {
  return (
    <ChakraTag size="md" bgColor="primary.700" flexShrink={0} data-testid="tag">
      <Text fontSize="sm" color="white">
        {text}
      </Text>
    </ChakraTag>
  );
};

export default PageTitleBannerTag;
