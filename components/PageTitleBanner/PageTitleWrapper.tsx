import { chakra, Heading, HeadingProps } from '@chakra-ui/react';
import { sizes } from '@styles/themes/caring';
import React, { ReactNode } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';

interface Props extends HeadingProps {
  children: string | ReactNode | undefined;
  element: HeadingElements;
  id: string | undefined;
  size: HeadingSizes;
}

const PageTitleWrapper: React.FC<Props> = ({
  element,
  children,
  size = '2xl',
  ...props
}) => {
  const Wrapper = chakra(Heading, {
    baseStyle: {
      h1: {
        fontFamily: 'heading',
        ...(size && sizes[size])
      }
    }
  });

  const DynamicTag = element || 'div';

  return (
    <Wrapper as="div" {...props}>
      <DynamicTag>{children}</DynamicTag>
    </Wrapper>
  );
};

export default PageTitleWrapper;
