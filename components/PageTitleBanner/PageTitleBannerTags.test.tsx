import { render, screen } from '@utils/test-utils';

import SiteContext, { findSiteForDomain } from '~/contexts/SiteContext';
import { CaringDomains, SeniorHomesDomains } from '~/types/Domains';

import PageTitleBannerTags from './PageTitleBannerTags';

describe('<PageTitleBannerTags />', () => {
  const customRender = (component: React.ReactNode, domain: string) => {
    const siteProps = {
      site: findSiteForDomain(domain)
    };

    return render(
      <SiteContext.Provider value={siteProps}>{component}</SiteContext.Provider>
    );
  };

  it('returns nothing when tags is empty', () => {
    const { container } = customRender(
      <PageTitleBannerTags tags={[]} />,
      SeniorHomesDomains.LIVE
    );
    expect(container.textContent).toEqual('');
  });

  it('returns one tag element for each tag string when domain is Senior Homes', () => {
    customRender(
      <PageTitleBannerTags tags={['Assisted Living', 'Memory Care']} />,
      SeniorHomesDomains.LIVE
    );

    const tags = screen.queryAllByTestId('tag');
    expect(tags).toHaveLength(2);
    expect(tags.map((tag) => tag.textContent)).toMatchObject([
      'Assisted Living',
      'Memory Care'
    ]);
  });

  it('returns a textual description of all tags when domain is Caring', () => {
    const { container } = customRender(
      <PageTitleBannerTags
        tags={['Assisted Living', 'Memory Care']}
        tagsDescription={
          "<a href='/null/undefined/undefined/undefined'>Assisted Living</a> and <a href='/null/undefined/undefined/undefined'>Memory Care</a>"
        }
      />,
      CaringDomains.LIVE
    );

    const tags = screen.queryAllByTestId('tag');
    expect(tags).toHaveLength(0);
    expect(container.textContent).toEqual(
      'Offers Assisted Living and Memory Care'
    );
  });
});
