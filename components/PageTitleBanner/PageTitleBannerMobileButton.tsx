import { Button } from '@chakra-ui/button';
import { Show } from '@chakra-ui/media-query';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { useContext } from 'react';
import { MdPhone } from 'react-icons/md';

import SiteContext from '~/contexts/SiteContext';
import { formatPhone } from '~/utils/strings';
interface Props {
  phoneNumber: string;
}

const ContactButton = ({
  phoneNumber,
  handleAnalyticsCallClick,
  domain = ''
}) => {
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <Button
          marginTop="2"
          as="a"
          onClick={handleAnalyticsCallClick}
          href={`tel:+1${phoneNumber}`}
          colorScheme="primary"
          size="lg"
          variant="outline"
        >
          Call us at {formatPhone(String(phoneNumber))}
        </Button>
      );
    default:
      return (
        <Button
          marginTop={6}
          as="a"
          onClick={handleAnalyticsCallClick}
          href={`tel:+1${phoneNumber}`}
          colorScheme="secondary"
          size="lg"
          width="100%"
          fontSize="md"
          variant="solid"
          leftIcon={<MdPhone size={18} />}
        >
          Call a Family Advisor
        </Button>
      );
  }
};

const PageTitleBannerMobileButton: React.FC<Props> = ({ phoneNumber }) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const elementClicked = useElementClicked();
  const handleAnalyticsCallClick = () => {
    switch (domain) {
      case 'seniorhomes.com':
        return elementClicked({
          element: {
            type: ElementTypes.TELEPHONE,
            action: ElementActions.PHONE_CALL,
            name: ElementNames.PHONE_CALL_BUTTON,
            text: `Call us at ${formatPhone(String(phoneNumber))}`,
            color: 'primary',
            textColor: 'white'
          },
          destinationUrl: phoneNumber
        });
      default:
        return elementClicked({
          element: {
            type: ElementTypes.TELEPHONE,
            action: ElementActions.PHONE_CALL,
            name: ElementNames.PHONE_CALL_BUTTON,
            text: 'Call a Family Advisor',
            color: 'secondary',
            textColor: 'white'
          },
          destinationUrl: phoneNumber
        });
    }
  };

  if (domain === 'caring.com') {
    return null;
  }

  return (
    <Show below="md">
      <ContactButton
        phoneNumber={phoneNumber}
        handleAnalyticsCallClick={handleAnalyticsCallClick}
        domain={domain}
      />
    </Show>
  );
};

export default PageTitleBannerMobileButton;
