import PageTitleBannerClaimListingButton from '@components/PageTitleBanner/PageTitleBannerClaimListingButton';
import { render, screen } from '@utils/test-utils';

import SiteContext, { SiteDefinition } from '~/contexts/SiteContext';

describe('PageTitleBannerClaimListingButton', () => {
  it('should render', () => {
    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } as SiteDefinition }}
      >
        <PageTitleBannerClaimListingButton
          isSubscription={false}
          isEnhanced={false}
          legacyId="45"
        />
      </SiteContext.Provider>
    );
    expect(screen.getByText('Claim this listing')).toBeInTheDocument();
  });

  it("shouldn't render if provider is enhanced", () => {
    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } as SiteDefinition }}
      >
        <PageTitleBannerClaimListingButton
          isSubscription={false}
          isEnhanced={true}
          legacyId="45"
        />
      </SiteContext.Provider>
    );

    expect(screen.queryByText('Claim this listing')).not.toBeInTheDocument();
  });

  it("shouldn't render if provider has a subscription", () => {
    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } as SiteDefinition }}
      >
        <PageTitleBannerClaimListingButton
          isSubscription={true}
          isEnhanced={false}
          legacyId="45"
        />
      </SiteContext.Provider>
    );

    expect(screen.queryByText('Claim this listing')).not.toBeInTheDocument();
  });
});
