import { render } from '@testing-library/react';
import React from 'react';

import PageTitleWrapper from './PageTitleWrapper';

describe('PageTitleWrapper', () => {
  it('renders children correctly', () => {
    const { getByText } = render(
      <PageTitleWrapper element="h1" size="2xl" id="title-id">
        Title
      </PageTitleWrapper>
    );

    expect(getByText('Title')).toBeInTheDocument();
  });
});
