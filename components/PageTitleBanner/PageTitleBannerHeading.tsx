import { HStack } from '@chakra-ui/layout';
import { createID } from '@utils/strings';

import { HeadingElements, HeadingSizes } from '~/@types/heading';

import PageTitleWrapper from './PageTitleWrapper';

interface Props {
  title: string | undefined;
  headingElement: HeadingElements;
  headingSize: HeadingSizes;
}

const PageTitleBannerHeading: React.FC<Props> = ({
  title = '',
  headingElement,
  headingSize
}) => {
  return (
    <HStack spacing="1" fontWeight="bold" alignItems="center">
      <PageTitleWrapper
        id={createID(title)}
        element={headingElement}
        size={headingSize}
        marginRight={4}
        style={{ scrollMarginTop: 48 }}
      >
        {title}
      </PageTitleWrapper>
    </HStack>
  );
};

export default PageTitleBannerHeading;
