import { render } from '@testing-library/react';
import React from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import { screen } from '~/utils/test-utils';

import PageTitleBannerHeading from './PageTitleBannerHeading';

// Mock useContext
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn()
}));

const fakeData = {
  title: 'Sample Title',
  headingElement: 'h2',
  headingSize: 'lg',
  isSubscription: true,
  isEnhanced: false,
  verifiedText: ''
};

describe('PageTitleBannerHeading', () => {
  it('should render PageTitleBannerHeading component with title', () => {
    const props = {
      title: 'Example Title',
      headingElement: 'h1' as HeadingElements,
      headingSize: 'xl' as HeadingSizes
    };

    render(<PageTitleBannerHeading {...props} />);

    expect(screen.getByText('Example Title')).toBeInTheDocument();
  });
});
