import { Box } from '@chakra-ui/layout';
import HtmlToReact from '@components/HtmlToReact';
import { useContext } from 'react';

import SiteContext from '~/contexts/SiteContext';

import PageTitleBannerTag from './PageTitleBannerTag';

type Props = {
  tags: string[];
  tagsDescription?: string;
};

const TagList = ({
  tags,
  tagsDescription,
  domain
}: Props & { domain: string }) => {
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <Box display="flex" flexWrap="wrap" gap={2}>
          {tags.map((text, index) => (
            <PageTitleBannerTag key={index} text={text} />
          ))}
        </Box>
      );
    case 'caring.com':
      return (
        <Box
          fontSize="sm"
          color={'gray.700'}
          lineHeight={1.7}
          style={{ marginTop: 4 }}
        >
          {HtmlToReact({ html: `Offers ${tagsDescription}` })}
        </Box>
      );
    default:
      return <></>;
  }
};

const PageTitleBannerTags: React.FC<Props> = ({ tags, tagsDescription }) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  if (tags?.length === 0) {
    return <></>;
  }
  return (
    <TagList tags={tags} tagsDescription={tagsDescription} domain={domain} />
  );
};

export default PageTitleBannerTags;
