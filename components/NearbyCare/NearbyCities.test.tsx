import { mockNearbyCities, render } from '@utils/test-utils';
import fetchMock from 'jest-fetch-mock';

import NearbyCities from './NearbyCities';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    asPath: '/mocked-path'
  }))
}));

beforeEach(() => {
  fetchMock.resetMocks();
});

describe('NearbyCities', () => {
  it('should render NearbyCities component', async () => {
    const { getByText, container } = render(
      <NearbyCities
        careType={''}
        links={mockNearbyCities.links}
        catalogLinks={mockNearbyCities?.catalogLinks || ''}
        content={mockNearbyCities.content}
        heading={mockNearbyCities.heading}
        headingElement={mockNearbyCities.headingElement}
        initialData={[]}
        useCatalogForNearby={true}
        city={mockNearbyCities.city}
        state={mockNearbyCities.state}
      />
    );

    const heading = getByText(mockNearbyCities.heading);

    expect(heading).toBeInTheDocument();

    const content = getByText(mockNearbyCities.content);

    expect(content).toBeInTheDocument();
  });

  it('should render component with catalog links', async () => {
    const { container } = render(
      <NearbyCities
        careType={mockNearbyCities.careTypeAssisted}
        links={mockNearbyCities.links}
        catalogLinks={mockNearbyCities?.catalogLinksAssisted}
        content={mockNearbyCities.content}
        heading={mockNearbyCities.heading}
        enableTrailingSlash={true}
        useCatalogForNearby={true}
        initialData={[]}
        city={mockNearbyCities.city}
        state={mockNearbyCities.state}
      />
    );
    expect(container.firstChild).toBeInTheDocument();
  });

  it('should render component with links', async () => {
    const { getByText } = render(
      <NearbyCities
        careType={''}
        links={mockNearbyCities.links}
        content={mockNearbyCities.content}
        heading={mockNearbyCities.heading}
        headingElement={mockNearbyCities.headingElement}
      />
    );
    mockNearbyCities.links.forEach((e) => {
      expect(getByText(e.linkText)).toBeInTheDocument();
      expect(getByText(e.linkText)).toBeVisible;
    });
  });
});
