import useLocaleCatalog from '@hooks/use-locale-catalog';
import {
  mockNearbyOtherCareTypeSameCity,
  render,
  screen
} from '@utils/test-utils';
import fetchMock from 'jest-fetch-mock';

import NearbyOtherCareTypeSameCity from './NearbyOtherCareTypeSameCity';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    asPath: '/mocked-path'
  }))
}));

jest.mock('@hooks/use-locale-catalog', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('NearbyOtherCareTypeSameCity', () => {
  beforeEach(() => {
    fetchMock.resetMocks();
    useLocaleCatalog.mockReturnValue({
      data: mockNearbyOtherCareTypeSameCity.localeCatalog
    });
  });

  it('should render NearbyOtherCareTypeSameCity component', async () => {
    const { container } = render(
      <NearbyOtherCareTypeSameCity
        heading={mockNearbyOtherCareTypeSameCity.heading}
        content={mockNearbyOtherCareTypeSameCity.content}
        links={mockNearbyOtherCareTypeSameCity.links}
        careType={mockNearbyOtherCareTypeSameCity.careType}
      />
    );

    const heading = screen.getByText(mockNearbyOtherCareTypeSameCity.heading);

    expect(heading).toBeInTheDocument();
    expect(heading).toBeVisible;

    const headingEl = container.querySelector('h3');

    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;

    mockNearbyOtherCareTypeSameCity.links.forEach((e) => {
      expect(screen.getByText(e.linkText)).toBeInTheDocument();
      expect(screen.getByText(e.linkText)).toBeVisible;
    });

    const content = screen.getByText(mockNearbyOtherCareTypeSameCity.content);
    expect(content).toBeVisible();
  });
  it('should render NearbyOtherCareTypeSameCity with only text link', async () => {
    const headingElement = mockNearbyOtherCareTypeSameCity.headingElement;
    const { getByText, container } = render(
      <NearbyOtherCareTypeSameCity
        headingElement={headingElement}
        heading={mockNearbyOtherCareTypeSameCity.heading}
        content={mockNearbyOtherCareTypeSameCity.content}
        links={mockNearbyOtherCareTypeSameCity.onlyText}
        careType={mockNearbyOtherCareTypeSameCity.careType}
        city={mockNearbyOtherCareTypeSameCity.city}
        state={mockNearbyOtherCareTypeSameCity.state}
        useCatalogForNearby={false}
      />
    );
    const headingEl = container.querySelector(headingElement);
    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;
    expect(container.firstChild).toBeInTheDocument();
  });
  it('should render NearbyOtherCareTypeSameCity with useCatalogForNearby', async () => {
    const { container } = render(
      <NearbyOtherCareTypeSameCity
        heading={mockNearbyOtherCareTypeSameCity.heading}
        content={mockNearbyOtherCareTypeSameCity.content}
        links={mockNearbyOtherCareTypeSameCity.links}
        careType={mockNearbyOtherCareTypeSameCity.careType}
        useCatalogForNearby={true}
        city={mockNearbyOtherCareTypeSameCity.city}
        state={mockNearbyOtherCareTypeSameCity.state}
      />
    );
    expect(container.firstChild).toBeInTheDocument();
  });
  it('should render NearbyOtherCareTypeSameCity without links', async () => {
    const { container } = render(
      <NearbyOtherCareTypeSameCity
        heading={mockNearbyOtherCareTypeSameCity.heading}
        content={mockNearbyOtherCareTypeSameCity.content}
        links={[]}
        careType={mockNearbyOtherCareTypeSameCity.careType}
      />
    );
    expect(container.firstChild).toBeInTheDocument();
  });
});
