import useLocaleCatalog from '@hooks/use-locale-catalog';
import {
  nearbyOtherCareTypesNearbyCity,
  render,
  screen
} from '@utils/test-utils';
import fetchMock from 'jest-fetch-mock';

import NearbyOtherCareTypesNearbyCity from './NearbyOtherCareTypesNearbyCity';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    asPath: '/mocked-path'
  }))
}));

jest.mock('@hooks/use-locale-catalog', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('NearbyOtherCareTypeSameCity', () => {
  beforeEach(() => {
    fetchMock.resetMocks();
    useLocaleCatalog.mockReturnValue({
      data: nearbyOtherCareTypesNearbyCity.localeCatalog
    });
  });

  it('should render NearbyOtherCareTypesNearbyCity component', async () => {
    const { container } = render(
      <NearbyOtherCareTypesNearbyCity
        heading={nearbyOtherCareTypesNearbyCity.heading}
        content={nearbyOtherCareTypesNearbyCity.content}
        links={nearbyOtherCareTypesNearbyCity.links}
        careType={nearbyOtherCareTypesNearbyCity.careType}
      />
    );

    const heading = screen.getByText(nearbyOtherCareTypesNearbyCity.heading);

    expect(heading).toBeInTheDocument();
    expect(heading).toBeVisible;

    const headingEl = container.querySelector('h3');

    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;

    nearbyOtherCareTypesNearbyCity.links.forEach((e) => {
      expect(screen.getByText(e.linkText)).toBeInTheDocument();
      expect(screen.getByText(e.linkText)).toBeVisible;
    });

    const content = screen.getByText(nearbyOtherCareTypesNearbyCity.content);
    expect(content).toBeVisible();
  });

  it('should render NearbyOtherCareTypesNearbyCity component with custom heading element', async () => {
    const { container } = render(
      <NearbyOtherCareTypesNearbyCity
        heading={nearbyOtherCareTypesNearbyCity.heading}
        headingElement={nearbyOtherCareTypesNearbyCity.headingElement}
        content={nearbyOtherCareTypesNearbyCity.content}
        links={nearbyOtherCareTypesNearbyCity.links}
        careType={nearbyOtherCareTypesNearbyCity.careType}
      />
    );

    const heading = screen.getByText(nearbyOtherCareTypesNearbyCity.heading);

    expect(heading).toBeInTheDocument();
    expect(heading).toBeVisible;

    const headingEl = container.querySelector(
      nearbyOtherCareTypesNearbyCity.headingElement
    );

    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;
  });
});
