import { mockNearbyCounties, render, screen } from '@utils/test-utils';
import fetchMock from 'jest-fetch-mock';

import NearbyCounties from './NearbyCounties';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    asPath: '/mocked-path'
  }))
}));

beforeEach(() => {
  fetchMock.resetMocks();
});

describe('NearbyCounties', () => {
  it('should render NearbyCounties component', async () => {
    const { container } = render(
      <NearbyCounties
        heading={mockNearbyCounties.heading}
        content={mockNearbyCounties.content}
        links={mockNearbyCounties.links}
        careType={mockNearbyCounties.careType}
      />
    );

    const heading = screen.getByText(mockNearbyCounties.heading);

    expect(heading).toBeInTheDocument();
    expect(heading).toBeVisible;

    const headingEl = container.querySelector('h3');

    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;

    mockNearbyCounties.links.forEach((e) => {
      expect(screen.getByText(e.linkText)).toBeInTheDocument();
      expect(screen.getByText(e.linkText)).toBeVisible;
    });

    const content = screen.getByText(mockNearbyCounties.content);
    expect(content).toBeVisible();
  });
  it('should render NearbyCounties with heading element', async () => {
    const { getByText, container } = render(
      <NearbyCounties
        headingElement={mockNearbyCounties.headingElement}
        heading={mockNearbyCounties.heading}
        content={mockNearbyCounties.content}
        links={mockNearbyCounties.onlyText}
        careType={mockNearbyCounties.careType}
      />
    );
    const headingEl = container.querySelector('h4');
    expect(headingEl).toBeInTheDocument();
    expect(headingEl).toBeVisible;
  });
});
