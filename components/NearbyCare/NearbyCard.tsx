import {
  GridItem,
  Heading,
  <PERSON>,
  LinkBox,
  LinkOverlay,
  Text,
  VStack
} from '@chakra-ui/layout';

type NearbyCardProps = {
  children: React.ReactNode;
};

type NearbyCardHeaderProps = {
  children: React.ReactNode;
  href: string;
};

type NearbyCardSectionProps = {
  label: string;
  value: string;
};

export const NearbyCardHeader: React.FC<NearbyCardHeaderProps> = ({
  children,
  href
}) => {
  return (
    <Heading as="p" size="md">
      <Link href={href}>
        <LinkOverlay>{children}</LinkOverlay>
      </Link>
    </Heading>
  );
};

export const NearbyCardSection: React.FC<NearbyCardSectionProps> = ({
  label,
  value
}) => {
  return (
    <>
      <Text fontSize="sm" color="gray.700">
        {label}
      </Text>
      <Heading as="p" size="sm">
        {value}
      </Heading>
    </>
  );
};

const NearbyCard: React.FC<NearbyCardProps> = ({ children }) => {
  return (
    <GridItem colSpan={3}>
      <LinkBox as="article" height="full">
        <VStack
          align="stretch"
          bg="white"
          border="1px"
          borderColor="gray.50"
          boxShadow="md"
          p="2.5"
          rounded="md"
          spacing="2.5"
          height="full"
        >
          {children}
        </VStack>
      </LinkBox>
    </GridItem>
  );
};

export default NearbyCard;
