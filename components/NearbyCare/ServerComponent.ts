import { fetchNearbyProviders } from '@hooks/provider';
import { getLocaleCatalogEntries } from '@hooks/use-locale-catalog';
import { isJSON } from '@utils/isJSON';
import { SiteDefinition } from 'contexts/SiteContext';
import isObject from 'lodash/isObject';
import kebabCase from 'lodash/kebabCase';
import lowerCase from 'lodash/lowerCase';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import { FetchResponse } from './NearbyCare';
import { NearbyCareProps, NearbyProps } from './types';
const fetchNearbyCitiesFromCatalog = async ({
  site,
  nearbyCitiesSameType,
  careType,
  state,
  city
}: {
  site: SiteDefinition;
  nearbyCitiesSameType: NearbyProps;
  careType: string;
  state: string;
  city: string;
}) => {
  const catalogLinks = nearbyCitiesSameType?.catalogLinks || '';
  const parsedLinks =
    catalogLinks && isJSON(catalogLinks) && JSON.parse(catalogLinks);
  const hasCatalogLinks = careType === 'Assisted Living' && parsedLinks.data;
  const shouldFetch =
    nearbyCitiesSameType?.enable &&
    nearbyCitiesSameType?.useCatalogForNearby &&
    !hasCatalogLinks;

  if (shouldFetch) {
    return getLocaleCatalogEntries({
      orderBy: 'urlPath asc',
      'state[eq]': lowerCase(state),
      'city[eq]': lowerCase(city),
      'careType[eq]': kebabCase(careType),
      '@ancestor': `/${site.path}`
    });
  }

  return [];
};

const fetchNearbyOtherCareTypeSameCityFromCatalog = async ({
  site,
  otherCareTypesSameCity,
  careType,
  state,
  city
}: {
  site: SiteDefinition;
  otherCareTypesSameCity: NearbyProps;
  careType: string;
  state: string;
  city: string;
}) => {
  const shouldFetch =
    otherCareTypesSameCity?.enable &&
    otherCareTypesSameCity?.useCatalogForNearby;

  if (shouldFetch) {
    return getLocaleCatalogEntries({
      orderBy: 'urlPath asc',
      'state[eq]': lowerCase(state),
      'city[eq]': lowerCase(city),
      'careType[ne]': kebabCase(careType),
      '@ancestor': `/${site.path}`
    });
  }

  return [];
};

export const getServerSideComponentProps = async (
  {
    numberOfItemsToDisplay = '8',
    state,
    city,
    county,
    careType,
    latitude,
    longitude,
    nearbyCitiesSameType,
    otherCareTypesSameCity
  }: NearbyCareProps,
  context: GetServerSidePropsContext
): Promise<FetchResponse> => {
  const site = findSiteForContext(context);
  const lat = parseFloat(latitude ?? '') || 0;
  const lng = parseFloat(longitude ?? '') || 0;
  const alteredCareType: string = isObject(careType) ? careType.name : careType;
  const result = await Promise.allSettled([
    fetchNearbyProviders({
      careType: alteredCareType,
      hitsPerPage: Number(numberOfItemsToDisplay),
      page: 0,
      latitude: lat,
      longitude: lng,
      domain: site.path,
      enhancedListing: true
    }),
    fetchNearbyCitiesFromCatalog({
      site,
      nearbyCitiesSameType,
      careType: alteredCareType,
      state,
      city
    }),
    fetchNearbyOtherCareTypeSameCityFromCatalog({
      site,
      otherCareTypesSameCity,
      careType: alteredCareType,
      state,
      city
    })
  ]);

  const nearbyProviders =
    result[0].status === 'fulfilled' ? result[0].value : {};
  const nearbyCities = result[1].status === 'fulfilled' ? result[1].value : [];
  const nearbyOtherCareTypeSameCity =
    result[2].status === 'fulfilled' ? result[2].value : [];
  return {
    nearbyProviders,
    nearbyCities,
    nearbyOtherCareTypeSameCity
  };
};
