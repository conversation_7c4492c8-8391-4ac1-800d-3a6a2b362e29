import { mockAvatarTiles, render, screen } from '@utils/test-utils';

import AvatarTiles from './AvatarTiles';

jest.mock('~/contexts/ModalContext');

describe('AvatarTiles', () => {
  test('renders the component with given props', () => {
    render(
      <AvatarTiles
        avatars={mockAvatarTiles.avatars}
        cardColor={mockAvatarTiles.cardColor}
        textColor={mockAvatarTiles.textColor}
        titleAlignment="center"
        cardTextAlignment="left"
        showAvatar={mockAvatarTiles.showAvatar}
        description={mockAvatarTiles.description}
        headingElement={mockAvatarTiles.headingElement}
        title={mockAvatarTiles.title}
      />
    );

    expect(screen.getByText(mockAvatarTiles.title)).toBeInTheDocument();
    expect(screen.getByText(mockAvatarTiles.description)).toBeInTheDocument();
    expect(
      screen.getByAltText(
        mockAvatarTiles.avatars.avatars0.switchable.image?.metadata
          .caption as string
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(mockAvatarTiles.avatars.avatars0.name)
    ).toBeInTheDocument();
    expect(
      screen.getAllByText(mockAvatarTiles.avatars.avatars0.description)
    ).toHaveLength(mockAvatarTiles.avatars['@nodes'].length);
    expect(
      screen.getAllByText(mockAvatarTiles.avatars.avatars0.role)
    ).toHaveLength(mockAvatarTiles.avatars['@nodes'].length);
  });
  test('renders the component with given props without avatar images', () => {
    render(
      <AvatarTiles
        avatars={mockAvatarTiles.avatars}
        cardColor={mockAvatarTiles.cardColor}
        textColor={mockAvatarTiles.textColor}
        titleAlignment="center"
        cardTextAlignment="left"
        showAvatar={false}
        description={mockAvatarTiles.description}
        headingElement={mockAvatarTiles.headingElement}
        title={mockAvatarTiles.title}
      />
    );

    expect(screen.getByText(mockAvatarTiles.title)).toBeInTheDocument();
    expect(screen.getByText(mockAvatarTiles.description)).toBeInTheDocument();
    expect(
      !screen.findByAltText(
        mockAvatarTiles.avatars.avatars0.switchable.image?.metadata
          .caption as string
      )
    ).toBeFalsy();
  });
});
