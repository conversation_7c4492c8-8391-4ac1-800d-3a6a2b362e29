import CheckboxInput from '@components/CheckboxInput';

interface LifestyleFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const lifestyleFilterItems = [
  {
    value: 'pet-friendly',
    label: 'Pet Friendly'
  },
  {
    value: 'active',
    label: 'Active Lifestyle'
  }
];

function LifestyleFilter({ onChange, value }: LifestyleFilterProps) {
  return (
    <CheckboxInput
      name="lifestyle"
      onChange={onChange}
      items={lifestyleFilterItems}
      value={value}
    />
  );
}

export default LifestyleFilter;
