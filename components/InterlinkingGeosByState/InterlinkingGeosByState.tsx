'use client';

import InterlinkingCard from '@components/Interlinking';
import { InterlinkingProps } from '@components/Interlinking/Interlinking';
import Container from '@components/LayoutStructure/Container';
import { Geo } from '@services/modular-monolith/types/interlinking.type';
import { getBackgroundColor } from '@utils/getColor';
import { useMemo, useState } from 'react';
// TO BE USED WITH MODMOND ENDPOINT LOGIC
// import { getGeosByCareType } from '@services/interlinking/api';
// import SiteContext from '~/contexts/SiteContext';
// import { getDomainFilter, parseApiCareType } from '@utils/interlinking';
// import { useRouter } from 'next/router';
// import { strip } from '@utils/parser';
// import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
// import { getStateFromURL } from '@utils/getStateFromURL';

export type ListType = 'cities' | 'counties';

export interface InterlinkingData {
  items: Array<Geo>;
  total: number;
  page: number;
}

export type InterlinkingGeosByStateProps = InterlinkingProps & {
  careType: string | { name: string };
  limit?: number;
  state: string;
  listType?: ListType;
  buttonState?: 'solid' | 'outline' | 'ghost' | 'link';
  buttonLabel?: string;
  buttonTextColor?: string;
  buttonBgColor?: string;
  enableShowMoreButton: boolean;
  populationFrom: number;
  populationTo: number;
  data: InterlinkingData;
};

const InterlinkingGeosByState = ({
  title,
  titleSize,
  titleAlignment = 'left',
  headingElement = 'h2',
  columns = '1',
  data = {
    items: [],
    total: 0,
    page: 1
  },
  listType = 'cities',
  bgColor,
  bgColorRange,
  titleColor = 'primary',
  titleColorRange = '700',
  linksColor = 'link',
  linksColorRange = '600',
  linksTextDecoration = 'none',
  buttonLabel = '',
  buttonState,
  buttonBgColor = 'primary',
  buttonTextColor,
  limit = 100,
  careType,
  state,
  enableShowMoreButton = false,
  populationFrom,
  populationTo
}: InterlinkingGeosByStateProps): React.ReactElement => {
  // SHOW MORE BUTTON LOGIC MODMON
  // const router = useRouter();
  // const [size, setSize] = useState<number>(Number(limit));
  // const [items, setItems] = useState<Array<Geo>>(data?.items);
  // const [totalPages, setTotalPages] = useState<number>(
  //   Math.ceil((data?.total || 0) / (Number(limit) || 1))
  // );
  // const [currentPage, setCurrentPage] = useState<number>(
  //   Number(data?.page || 1)
  // );
  // const siteProps = useContext(SiteContext);
  // const domain = siteProps.site?.path ?? '';

  const backgroundColor = getBackgroundColor(bgColor, bgColorRange);

  // LOGIC FOR SHOW MORE BUTTON LEGACY - TO BE DEPRECATED
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = Number(limit);
  const allItems = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);
  const totalPages = Math.ceil(allItems.length / itemsPerPage);
  const [itemsToShow, setItemsToShow] = useState<Geo[]>(() => {
    return allItems.map((item, index) => {
      if (index < limit) {
        item.display = 'block';
      } else {
        item.display = 'none';
      }
      return item;
    });
  });

  const handleShowMore = async () => {
    // LOGIC FOR SHOW MORE BUTTON FOR LEGACY - TO BE DEPRECATED
    if (currentPage < totalPages) {
      // Calculate the start and end index for the elements on the next page
      const startIndex = currentPage * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;

      // Filter the elements to be shown on the next page
      const itemsOnNextPage = allItems.map((item, index) => {
        if (index < endIndex) {
          item.display = 'block';
        } else {
          item.display = 'none';
        }
        return item;
      });

      // Update the current page and add the elements to the state
      setCurrentPage(currentPage + 1);
      setItemsToShow([...itemsOnNextPage]);
    }

    // SHOW MORE BUTTON - LOGIC FOR MODMON ENDPOINT
    // const alteredCareType = strip(careType) ?? getCareTypeFromURL(router.query);
    // const stateValue = state ?? getStateFromURL(router.query);

    // if (currentPage !== totalPages && limit) {
    //   const response = await getGeosByCareType({
    //     alteredCareType,
    //     careType: parseApiCareType(alteredCareType),
    //     state: stateValue || undefined,
    //     listType: listType,
    //     domain: getDomainFilter(domain),
    //     populationFrom: populationFrom,
    //     populationTo: populationTo,
    //     page: currentPage + 1,
    //     pageSize: size,
    //   });
    //   setCurrentPage((prevPage) => prevPage + 1);
    //   setSize(Number(response?.pageSize || limit));
    //   setTotalPages(
    //     Math.ceil(
    //       (response?.total || 0) / (Number(response?.pageSize || limit) || 1)
    //     )
    //   );
    //   setItems((prevItems) => [
    //     ...new Set([...prevItems, ...(response?.items || [])]),
    //   ]);
    // }
  };

  return allItems && allItems?.length > 0 ? (
    <Container>
      <InterlinkingCard>
        <InterlinkingCard.Container bgColor={backgroundColor}>
          {title && (
            <InterlinkingCard.Heading
              title={title}
              titleSize={titleSize}
              titleColor={titleColor}
              titleAlignment={titleAlignment}
              headingElement={headingElement}
              titleColorRange={titleColorRange}
            />
          )}
          <InterlinkingCard.ListContainer
            align="stretch"
            borderRadius={12}
            backgroundColor={backgroundColor}
          >
            <InterlinkingCard.LinkGrid columns={parseInt(columns)}>
              <InterlinkingCard.ApiLinkList
                data={itemsToShow}
                linksColor={linksColor}
                linksColorRange={linksColorRange}
                linksTextDecoration={linksTextDecoration}
              />
            </InterlinkingCard.LinkGrid>
            {enableShowMoreButton &&
              limit > 0 &&
              currentPage !== totalPages && (
                <InterlinkingCard.Button
                  label={buttonLabel}
                  bgColor={buttonBgColor}
                  onClick={handleShowMore}
                  buttonState={buttonState}
                  textColor={buttonTextColor}
                />
              )}
          </InterlinkingCard.ListContainer>
        </InterlinkingCard.Container>
      </InterlinkingCard>
    </Container>
  ) : (
    <></>
  );
};

export default InterlinkingGeosByState;
