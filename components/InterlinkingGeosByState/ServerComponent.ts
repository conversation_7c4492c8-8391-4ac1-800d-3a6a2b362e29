import { getGeosByStateAndCareType } from '@services/interlinking/api';
import { getCareTypeFromURL } from '@utils/getCareTypeFromURL';
import { getStateFromURL } from '@utils/getStateFromURL';
import { getDomainFilter, parseApiCareType } from '@utils/interlinking';
import isObject from 'lodash/isObject';
import { GetServerSidePropsContext } from 'next';

import { findSiteForContext } from '~/contexts/SiteContext';

import {
  InterlinkingData,
  InterlinkingGeosByStateProps,
  ListType
} from './InterlinkingGeosByState';
type GetGeoPagesType = {
  domain: string;
  listType?: ListType;
  careType: string;
  alteredCareType: string;
  state: string;
  populationFrom?: number;
  populationTo?: number;
  pageSize?: number;
};

const getInterlinkingGeoLocation = async (
  payload: GetGeoPagesType,
  context: GetServerSidePropsContext
) => {
  const { alteredCareType, state, listType = 'cities' } = payload;

  const response = await getGeosByStateAndCareType(
    context,
    state,
    alteredCareType,
    listType
  );

  const data = response || {
    items: [],
    total: 0,
    page: 1
  };
  return data;
};

export const getServerSideComponentProps = async (
  props: InterlinkingGeosByStateProps,
  context: GetServerSidePropsContext
): Promise<InterlinkingData> => {
  const { careType, populationFrom, populationTo, limit, state, listType } =
    props;

  const params = context.params || {};
  const site = findSiteForContext(context);
  const careTypeFromURL = getCareTypeFromURL(params) ?? '';
  const alteredCareType = isObject(careType) ? careType.name : careTypeFromURL;
  const stateValue = state ?? getStateFromURL(params);

  const result = await getInterlinkingGeoLocation(
    {
      listType,
      alteredCareType,
      domain: getDomainFilter(site.path),
      careType: parseApiCareType(alteredCareType),
      state: stateValue,
      populationFrom: populationFrom,
      populationTo: populationTo,
      pageSize: limit
    },
    context
  );

  return result;
};
