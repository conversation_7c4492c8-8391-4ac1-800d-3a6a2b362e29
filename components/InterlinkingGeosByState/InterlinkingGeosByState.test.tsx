import { toCapitalizedWords } from '@utils/strings';
import {
  fireEvent,
  mockInterlinkingGeosByState,
  render,
  screen
} from '@utils/test-utils';
import startCase from 'lodash/startCase';

import InterlinkingGeosByState from './index';

jest.mock('next/router', () => ({
  useRouter() {
    return {
      asPath: '/some-path/to-city'
    };
  }
}));

describe('InterlinkingGeosByState', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  it('should not render component when data is empty', async () => {
    const { container } = render(
      <InterlinkingGeosByState data={mockInterlinkingGeosByState.emptyData} />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render when data is not empty', async () => {
    const { container } = render(
      <InterlinkingGeosByState
        bgColor="white"
        data={mockInterlinkingGeosByState.data}
        careType={mockInterlinkingGeosByState.careType.name}
      />
    );
    mockInterlinkingGeosByState.data.items.forEach((e) => {
      expect(
        screen.getByText(startCase(toCapitalizedWords(e.name)))
      ).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: toCapitalizedWords(e.name) })
      ).toHaveAttribute('href', `${e.link}`);
    });
    expect(container.children).toHaveLength(2);
  });

  // TO BE UPDATED ONCE MODMON ENDPOINT IS USED FOR GEOS LISTING
  it('should show more links when "Show More" button is clicked - legacy', () => {
    const { getByText } = render(
      <InterlinkingGeosByState
        title="My Interlinking"
        titleSize="h2"
        titleAlignment="left"
        columns="2"
        data={mockInterlinkingGeosByState.data}
        bgColor="white"
        bgColorRange="300"
        titleColor="primary"
        titleColorRange="700"
        linksColor="link"
        linksColorRange="600"
        linksTextDecoration="none"
        buttonLabel="Show More"
        buttonColor="primary"
        buttonColorRange="500"
        enableShowMoreButton={true}
        populationFrom={100000}
        populationTo={500000}
        limit={1}
      />
    );

    const geos = mockInterlinkingGeosByState.dataPaginated;

    const getElement = (name: string) => {
      return getByText(startCase(toCapitalizedWords(name)));
    };

    let firstItem = getElement(geos.items[0].name);
    let secondItem = getElement(geos.items[1].name);

    expect(firstItem).toBeInTheDocument();
    expect(secondItem).toBeInTheDocument();
    expect(firstItem).toBeVisible();
    expect(secondItem).not.toBeVisible();

    fireEvent.click(getByText('Show More'));

    firstItem = getElement(geos.items[0].name);
    secondItem = getElement(geos.items[1].name);
    expect(firstItem).toBeVisible();
    expect(secondItem).toBeVisible();
  });
});
