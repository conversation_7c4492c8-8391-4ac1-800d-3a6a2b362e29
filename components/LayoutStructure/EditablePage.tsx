import { EditablePage as MagnoliaEditablePage } from '@magnolia/react-editor';
import { ComponentType } from 'react';
interface DynamicComponent {
  [key: string]: ComponentType<any>;
}

interface StandardComponent {
  [key: string]: (props: any) => JSX.Element | null;
}

export interface EditablePageProps {
  content: any;
  config?: {
    componentMappings: DynamicComponent | StandardComponent;
  };
  templateAnnotations: any;
}

export function EditablePage({
  content,
  config,
  templateAnnotations
}: EditablePageProps) {
  return (
    <MagnoliaEditablePage
      content={content}
      config={config}
      templateAnnotations={templateAnnotations}
    />
  );
}
