import { validateAndProcessFAQs } from '@components/FAQ/FAQ.utils';
import {
  PageContextType,
  StoryContextType
} from '@components/LayoutStructure/Contexts.types';
import { getProviderAmenitiesFromServices } from '@components/OfferingListing/OfferingListing';
import { validateProviderCareTypes } from '@components/ProviderCareTypes/ProviderCareTypes';
import { extractPages } from '@components/RelatedArticle/helpers';
import { ProviderStatus } from '@hooks/use-provider-fallback.types';
import { isAmenitiesEmpty } from '@utils/amenities';
import { providerIsEnhancedAndNotSuppressed } from '@utils/providers';
import * as htmlparser2 from 'htmlparser2';
import orderBy from 'lodash/orderBy';

import { Provider } from '~/contexts/Provider';
import { Domain } from '~/types/Domains';

const nearbySections = [
  'communities',
  'nearbyCitiesSameType',
  'nearbyCountiesSameType',
  'otherCareTypesSameCity',
  'otherCareTypesNearbyCity'
];

const getTitlesFromColumn = (
  column: Node,
  pageContext: PageContextType,
  storyContext: StoryContextType,
  provider: Provider | null,
  status: ProviderStatus,
  domain: Domain | undefined,
  t: (key: string, fallback: string) => string
): Array<{ title: string; linkText: string }> => {
  const columnTitles: Array<{ title: string; linkText: string }> = [];
  for (const componentKey of Object.keys(column)) {
    const component = column[componentKey];
    if (component['mgnl:template'] === 'spa-lm:components/story') {
      applyStoryTitles(columnTitles, storyContext);
    }
    if (
      component.headingElement &&
      component.headingElement === 'h2' &&
      component.title &&
      component.linkText &&
      validateComponentRender(
        component['mgnl:template'],
        pageContext,
        component,
        provider,
        status,
        domain,
        t
      )
    ) {
      columnTitles.push({
        title: component.title,
        linkText: component.linkText
      });
    }

    const nestedSections = component['@nodes'];
    if (nestedSections && nestedSections.length > 0) {
      for (const sectionKey of nestedSections) {
        const section = component[sectionKey];
        if (
          section?.headingElement &&
          section?.headingElement === 'h2' &&
          section.title &&
          section.linkText
        ) {
          columnTitles.push({
            title: section.title,
            linkText: section.linkText
          });
        }

        if (section) {
          const collapsibleComponents = section['@nodes'];
          if (collapsibleComponents && collapsibleComponents.length > 0) {
            for (const itemKey of collapsibleComponents) {
              const element = section[itemKey];
              if (
                element?.headingElement &&
                element?.headingElement === 'h2' &&
                element?.title &&
                element?.linkText
              ) {
                columnTitles.push({
                  title: element.title,
                  linkText: element.linkText
                });
              }
            }
          }
        }
      }
    }
  }
  return columnTitles;
};

export const getPageHeadings = (
  pageContext: PageContextType,
  storyContext: StoryContextType,
  provider: Provider | null,
  status: ProviderStatus,
  domain: Domain | undefined,
  t: (key: string, fallback: string) => string
): Array<{ title: string; linkText: string }> => {
  const titles: Array<{ title: string; linkText: string }> = [];

  pageContext?.page?.main &&
    pageContext?.page?.main['@nodes']?.map((value) => {
      if (
        pageContext.page.main[value]['mgnl:template'] ===
        'spa-lm:components/story'
      ) {
        const storyTitles = getStoryTitles(storyContext);
        if (storyTitles.length > 0) {
          storyTitles.forEach(function (heading) {
            titles.push({
              title: heading.title,
              linkText: heading.linkText
            });
          });
        }
      }

      const isRelatedArticleComponent =
        pageContext?.page?.main[value]['mgnl:template'] ===
        'spa-lm:components/relatedArticles';

      const isTableOfContentsComponent =
        pageContext?.page?.main[value]['mgnl:template'] ===
        'spa-lm:components/tableOfContents';
      const hasRelatedArticles =
        isRelatedArticleComponent &&
        extractPages(
          pageContext?.page?.main[value].pages,
          pageContext?.page?.careType
        ).length > 0;

      if (
        pageContext?.page?.main[value].headingElement &&
        pageContext?.page?.main[value].headingElement === 'h2'
      ) {
        if (pageContext?.page?.main[value].title) {
          if (isRelatedArticleComponent) {
            hasRelatedArticles
              ? titles.push(pageContext?.page?.main[value].title)
              : null;
          } else if (isTableOfContentsComponent) {
            // do nothing
          } else {
            titles.push(pageContext?.page?.main[value].title);
          }
        }
      }

      for (let key of nearbySections) {
        const section = pageContext?.page?.main[value][key];
        if (
          section &&
          section.headingElement &&
          section.headingElement === 'h2'
        ) {
          titles.push(section.heading);
        }
      }

      const columns = pageContext?.page?.main[value];
      for (const columnName in columns) {
        if (columnName.startsWith('column')) {
          const columnTitles = getTitlesFromColumn(
            columns[columnName],
            pageContext,
            storyContext,
            provider,
            status,
            domain,
            t
          );
          if (columnTitles.length > 0) {
            columnTitles.forEach(function (heading) {
              titles.push({
                title: heading.title,
                linkText: ''
              });
            });
          }
        }
      }
    });
  return titles || [];
};

export const validateComponentRender = (
  template: string,
  pageContext: PageContextType,
  value: any,
  provider: Provider | null,
  status: ProviderStatus,
  domain: Domain | undefined,
  t: (key: string, fallback: string) => string,
  extraData?: Record<any, any> | null
) => {
  switch (template) {
    case 'spa-lm:components/relatedArticles':
      const hasRelatedArticles =
        extractPages(
          pageContext?.page?.main[value].pages,
          pageContext?.page?.careType
        ).length > 0;
      return hasRelatedArticles ? true : false;
    case 'spa-lm:components/sectionFAQ':
      const hasFaqs = validateAndProcessFAQs(value.switchable, provider);
      return hasFaqs.length > 0 ? true : false;
    case 'spa-lm:components/providerCareTypes':
      const shouldRenderContent = validateProviderCareTypes(provider);
      return shouldRenderContent;
    case 'spa-lm:components/offeringListing':
      if (!provider || status !== ProviderStatus.COMPLETE || !t) return false;

      const amenities = getProviderAmenitiesFromServices(provider);
      const amenitiesLegacy = provider?.amenitiesLegacy ?? {};
      const hasAmenities = !isAmenitiesEmpty(
        amenities ?? amenitiesLegacy,
        domain,
        t
      );
      return hasAmenities;
    case 'communities':
      const isBasicListing = provider
        ? !providerIsEnhancedAndNotSuppressed(provider)
        : true;
      const nearbyCommunities =
        orderBy(
          extraData?.data?.nearbyProviders?.results,
          ['bayesian_average'],
          ['desc']
        ) || [];
      const showNearbyCommunities =
        extraData?.communities?.enable && nearbyCommunities.length > 0;
      return showNearbyCommunities && isBasicListing;
    default:
      return true;
  }
};

export const getCustomHeadings = (
  pageContext: PageContextType,
  storyContext: StoryContextType,
  provider: Provider | null,
  status: ProviderStatus,
  domain: Domain,
  t: (key: string, fallback: string) => string
): Array<{ title: string; linkText: string }> => {
  const titles: Array<{ title: string; linkText: string }> = [];
  pageContext?.page?.main &&
    pageContext?.page?.main['@nodes']?.map((value) => {
      if (
        pageContext.page.main[value]['mgnl:template'] ===
        'spa-lm:components/story'
      ) {
        applyStoryTitles(titles, storyContext);
      }

      if (
        pageContext?.page?.main[value].headingElement &&
        pageContext?.page?.main[value].headingElement === 'h2' &&
        pageContext?.page?.main[value].linkText &&
        validateComponentRender(
          pageContext?.page?.main[value]['mgnl:template'],
          pageContext,
          value,
          provider,
          status,
          domain,
          t
        )
      ) {
        const el = pageContext?.page?.main[value];

        titles.push({
          title: el?.title,
          linkText: el?.linkText
        });
      }

      for (let key of nearbySections) {
        const section = pageContext?.page?.main[value][key];
        if (
          section &&
          section.headingElement &&
          section.headingElement === 'h2' &&
          section.linkText &&
          validateComponentRender(
            key,
            pageContext,
            pageContext?.page?.main[value][key],
            provider,
            status,
            domain,
            t,
            {
              data: pageContext?.page?.main[value]?.data || {},
              communities: pageContext?.page?.main[value]?.communities || {}
            }
          )
        ) {
          if (section?.heading && section?.linkText) {
            titles.push({
              title: section?.heading,
              linkText: section?.linkText
            });
          }
        }
      }

      const columns = pageContext?.page?.main[value];
      for (const columnName in columns) {
        if (columnName.startsWith('column')) {
          const columnTitles = getTitlesFromColumn(
            columns[columnName],
            pageContext,
            storyContext,
            provider,
            status,
            domain,
            t
          );
          if (columnTitles.length > 0) {
            titles.push(...columnTitles);
          }
        }
      }
    });

  return titles || [];
};

export const applyStoryTitles = (
  titles: Array<{ title: string; linkText: string }>,
  storyContext: StoryContextType
): Array<{ title: string; linkText: string }> => {
  storyContext?.main &&
    storyContext?.main['@nodes']?.map((value) => {
      if (storyContext?.main) {
        if (
          storyContext.main[value].headingElement &&
          storyContext.main[value].headingElement === 'h2' &&
          storyContext.main[value].linkText
        ) {
          const el = storyContext?.main[value];
          titles.push({
            title: el?.title,
            linkText: el?.linkText
          });
        } else if (
          storyContext.main[value]['mgnl:template'] === 'spa-lm:components/text'
        ) {
          const dom = htmlparser2.parseDocument(
            storyContext.main[value]['text']
          );
          dom.children.map((element) => {
            if (
              element.type === 'tag' &&
              element.name === 'h2' &&
              element.children[0]['data']
            ) {
              titles.push({
                title: element.children[0]['data'],
                linkText: ''
              });
            }
          });
        }
      }
    });
  return titles;
};

export const getStoryTitles = (
  storyContext: StoryContextType
): Array<{ title: string; linkText: string }> => {
  const storyTitles: Array<{ title: string; linkText: string }> = [];

  applyStoryTitles(storyTitles, storyContext);

  return storyTitles;
};
