import NextSeo from '@components/NextSeo';
import { render } from '@utils/test-utils';
import { useRouter } from 'next/router';

import ProviderContext from '~/contexts/Provider';

import PageContainer from './PageContainer';
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

jest.mock('@components/JsonLd/JsonLdNextSeo', () => ({
  JsonLdNextSeo: jest.fn(() => <div data-testid="JsonLdNextSeo" />)
}));

jest.mock('@components/JsonLd/JsonLdLocalBusinessProviderAndReviews', () => ({
  JsonLdLocalBusinessProviderAndReviews: jest.fn(() => (
    <div data-testid="JsonLdLocalBusinessProviderAndReviews" />
  ))
}));

jest.mock('@components/NextSeo', () =>
  jest.fn(() => <div data-testid="NextSeo" />)
);

describe('PageContainer', () => {
  const mockPage = {
    metaTitle: 'Test Title',
    metaDescription: 'Test Description',
    metaKeywords: 'Test Keywords',
    openGraph: {},
    canonical: 'https://example.com',
    noindex: false,
    nofollow: false,
    jsonSchemas: {}
  };

  const mockContext = {};
  const mockTemplateAnnotations = {};

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ query: {} });
  });

  it('renders JsonLdNextSeo and NextSeo', () => {
    const { getByTestId } = render(
      <PageContainer
        page={mockPage}
        templateAnnotations={mockTemplateAnnotations}
        context={mockContext}
      />
    );

    expect(getByTestId('JsonLdNextSeo')).toBeInTheDocument();
    expect(getByTestId('NextSeo')).toBeInTheDocument();
  });

  it('sets canonical to undefined when query includes LISTING_PAGE', () => {
    const mockPage = {
      metaTitle: 'Test Title',
      metaDescription: 'Test Description',
      metaKeywords: 'Test Keywords',
      openGraph: {},
      canonical: 'https://example.com',
      noindex: false,
      nofollow: false,
      jsonSchemas: {}
    };

    const mockContext = {};
    const mockTemplateAnnotations = {};

    (useRouter as jest.Mock).mockReturnValue({
      query: {
        'listing-page': '2',
        careTypeOrState: 'assisted-living',
        stateOrCountyOrCity: 'north-carolina',
        countyOrCityOrProvider: 'charlotte'
      }
    });

    render(
      <PageContainer
        page={mockPage}
        templateAnnotations={mockTemplateAnnotations}
        context={mockContext}
      />
    );

    const nextSeoProps = jest.mocked(NextSeo).mock.calls[0][0];
    expect(nextSeoProps.canonical).toBeUndefined();
  });

  it('renders PageContainer with JsonLdNextSeo and NextSeo', () => {
    const { getByTestId } = render(
      <PageContainer
        page={mockPage}
        templateAnnotations={mockTemplateAnnotations}
        context={mockContext}
      />
    );

    expect(getByTestId('JsonLdNextSeo')).toBeInTheDocument();
    expect(getByTestId('NextSeo')).toBeInTheDocument();
  });

  it('renders PageContainer with correct NextSeo values, noindex and nofollow are false', () => {
    const { getByTestId } = render(
      <PageContainer
        page={mockPage}
        templateAnnotations={mockTemplateAnnotations}
        context={mockContext}
      />
    );

    const NextSeoValues = jest.mocked(NextSeo).mock.calls[0][0];
    expect(NextSeoValues).toEqual({
      title: mockPage.metaTitle,
      description: mockPage.metaDescription,
      keywords: mockPage.metaKeywords,
      openGraph: mockPage.openGraph,
      canonical: mockPage.canonical,
      noindex: false,
      nofollow: false
    });
  });

  it('renders PageContainer with noIndex true in NextSeo values, noindex and nofollow are true', () => {
    const mockPage = {
      metaTitle: 'Test Title',
      metaDescription: 'Test Description',
      metaKeywords: 'Test Keywords',
      openGraph: {},
      canonical: 'https://example.com',
      noindex: true,
      nofollow: true,
      jsonSchemas: {}
    };

    const { getByTestId } = render(
      <PageContainer
        page={mockPage}
        templateAnnotations={mockTemplateAnnotations}
        context={mockContext}
      />
    );

    const NextSeoValues = jest.mocked(NextSeo).mock.calls[0][0];

    expect(NextSeoValues).toEqual({
      title: mockPage.metaTitle,
      description: mockPage.metaDescription,
      keywords: mockPage.metaKeywords,
      openGraph: mockPage.openGraph,
      canonical: mockPage.canonical,
      noindex: true,
      nofollow: true
    });
  });

  it('renders JsonLdLocalBusinessProviderAndReviews when provider exists', () => {
    const mockProvider = { name: 'Test Provider' };

    const { getByTestId } = render(
      <ProviderContext.Provider value={{ provider: mockProvider }}>
        <PageContainer
          page={mockPage}
          templateAnnotations={mockTemplateAnnotations}
          context={mockContext}
        />
      </ProviderContext.Provider>
    );

    expect(
      getByTestId('JsonLdLocalBusinessProviderAndReviews')
    ).toBeInTheDocument();
  });

  it('does not render JsonLdLocalBusinessProviderAndReviews when provider does not exist', () => {
    const { queryByTestId } = render(
      <ProviderContext.Provider value={{ provider: null }}>
        <PageContainer
          page={mockPage}
          templateAnnotations={mockTemplateAnnotations}
          context={mockContext}
        />
      </ProviderContext.Provider>
    );

    expect(
      queryByTestId('JsonLdLocalBusinessProviderAndReviews')
    ).not.toBeInTheDocument();
  });
});
