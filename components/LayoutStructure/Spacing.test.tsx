import { render } from '~/utils/test-utils';

import Spacing from './Spacing';

describe('Spacing', () => {
  it('should render', () => {
    const { container } = render(<Spacing />);
    expect(container).not.toBeEmptyDOMElement();
  });

  it('should render with a divider', () => {
    const { getByTestId } = render(<Spacing showDivider={true} />);
    expect(getByTestId('divider')).toBeInTheDocument();
  });
});
