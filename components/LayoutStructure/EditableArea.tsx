'use client';

import { Box, BoxProps } from '@chakra-ui/layout';
import { EditableArea as MagnoliaEditableArea } from '@magnolia/react-editor';
import { ComponentProps } from 'react';

const EditableArea: React.FC<
  Omit<BoxProps, 'as' | 'content'> & ComponentProps<typeof MagnoliaEditableArea>
> = ({ children, ...rest }) => {
  return (
    <Box as={MagnoliaEditableArea} {...rest}>
      {children}
    </Box>
  );
};

export default EditableArea;
