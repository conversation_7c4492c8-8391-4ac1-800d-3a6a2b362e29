import { getProvidersByIds } from '@services/providers/getProvidersByIds';
import { getProviderIdsFromCookies } from '@utils/getProviderIdsFromCookies';
import { act, render, screen, waitFor } from '@utils/test-utils';

import SavedProvidersContent from './SavedProvidersContent';

jest.mock('@services/providers/getProvidersByIds');
jest.mock('@utils/getProviderIdsFromCookies');
jest.mock('@hooks/use-cookie-storage-value', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation((cookieName) => {
    if (cookieName === 'saved_providers') {
      return '["1"]';
    }
    return null;
  })
}));

describe('SavedProvidersContent', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (getProviderIdsFromCookies as jest.Mock).mockImplementation(
      (cookieName) => {
        if (cookieName === 'saved_providers') {
          return ['1'];
        }
        return [];
      }
    );

    (getProvidersByIds as jest.Mock).mockImplementation((ids) => {
      return Promise.resolve(
        ids.map((id) => ({
          id,
          name: `Provider ${id}`,
          address: { formattedAddress: '123 Test St' },
          photos: [],
          reviewCount: 5,
          averageRating: 4.5,
          url: `/provider/${id}`
        }))
      );
    });
  });

  it('renders loading state when providers are being fetched', async () => {
    (getProviderIdsFromCookies as jest.Mock).mockImplementation(
      (cookieName) => {
        if (cookieName === 'saved_providers') {
          return ['1', '2', '3'];
        }
        return [];
      }
    );

    (getProvidersByIds as jest.Mock).mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 100);
      });
    });

    render(<SavedProvidersContent />);

    await waitFor(() => {
      expect(screen.getAllByTestId('saved-provider-skeleton')).toHaveLength(3);
    });
  });

  it('renders empty state when no saved providers', async () => {
    (getProviderIdsFromCookies as jest.Mock).mockReturnValue([]);

    render(<SavedProvidersContent />);

    await waitFor(() => {
      expect(screen.getByText('Saved Providers (0)')).toBeInTheDocument();
      expect(
        screen.getByText('Add communities to your favorites')
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "You haven't added any communities to your favorites yet. Use the search to find communities to save below."
        )
      ).toBeInTheDocument();
    });
  });

  it('renders saved providers when available', async () => {
    (getProviderIdsFromCookies as jest.Mock).mockImplementation(
      (cookieName) => {
        if (cookieName === 'saved_providers') {
          return ['1'];
        }
        return [];
      }
    );

    await act(async () => {
      render(<SavedProvidersContent />);
    });

    await waitFor(() => {
      expect(screen.getByText(/Saved Providers \(1\)/)).toBeInTheDocument();
    });

    await waitFor(() => {
      const providerList = screen.getByTestId('provider-list');
      expect(providerList).toBeInTheDocument();

      const providerCards = screen.getAllByText(/Provider 1/);
      expect(providerCards.length).toBeGreaterThan(0);
    });
  });
  it('renders recently viewed providers when available', async () => {
    (getProviderIdsFromCookies as jest.Mock).mockImplementation(
      (cookieName) => {
        if (cookieName === 'recently_viewed_providers') {
          return ['1'];
        }
        return [];
      }
    );

    await act(async () => {
      render(<SavedProvidersContent />);
    });

    await waitFor(() => {
      expect(screen.getByText('Saved Providers (0)')).toBeInTheDocument();
      expect(screen.getByText('Recently Viewed Providers')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Be sure to save any of the providers you recently viewed that you want to view again later.'
        )
      ).toBeInTheDocument();

      const providerList = screen.getByTestId('provider-list');
      expect(providerList).toBeInTheDocument();

      const providerCards = screen.getAllByText(/Provider 1/);
      expect(providerCards.length).toBeGreaterThan(0);
    });
  });

  it('renders both saved and recently viewed providers when available', async () => {
    (getProviderIdsFromCookies as jest.Mock).mockImplementation(
      (cookieName) => {
        if (cookieName === 'saved_providers') {
          return ['1'];
        }
        if (cookieName === 'recently_viewed_providers') {
          return ['2'];
        }
        return [];
      }
    );

    await act(async () => {
      render(<SavedProvidersContent />);
    });

    await waitFor(() => {
      expect(screen.getByText(/Saved Providers \(1\)/)).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Recently Viewed Providers')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Be sure to save any of the providers you recently viewed that you want to view again later.'
        )
      ).toBeInTheDocument();

      const providerLists = screen.getAllByTestId('provider-list');
      expect(providerLists.length).toBe(2);

      const providerCards1 = screen.getAllByText(/Provider 1/);
      expect(providerCards1.length).toBeGreaterThan(0);

      const providerCards2 = screen.getAllByText(/Provider 2/);
      expect(providerCards2.length).toBeGreaterThan(0);
    });
  });

  it('handles error when fetching providers', async () => {
    (getProviderIdsFromCookies as jest.Mock)
      .mockReturnValueOnce(['1', '2'])
      .mockReturnValueOnce(['3']);
    (getProvidersByIds as jest.Mock).mockRejectedValue(
      new Error('Failed to fetch')
    );

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    render(<SavedProvidersContent />);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error fetching providers:',
        expect.any(Error)
      );
    });

    consoleSpy.mockRestore();
  });
});
