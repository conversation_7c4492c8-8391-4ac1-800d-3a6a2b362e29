import { ChakraProvider } from '@chakra-ui/react';
import MiniSelfSearch from '@components/MiniSelfSearch/MiniSelfSearch';
import { render, screen } from '@testing-library/react';

import SavedProvidersEmptyState from './SavedProvidersEmptyState';

jest.mock('uuid', () => ({
  v5: jest.fn(() => 'test-uuid-123'),
  __esModule: true,
  default: {
    v5: jest.fn(() => 'test-uuid-123')
  }
}));
const uuidv5 = require('uuid').v5;
uuidv5.URL = 'url-namespace-uuid';

jest.mock('@components/MiniSelfSearch/MiniSelfSearch', () => {
  return jest.fn(() => (
    <div data-testid="mini-self-search-mock">Search Component</div>
  ));
});

describe('SavedProvidersEmptyState', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with correct heading and text', () => {
    render(
      <ChakraProvider>
        <SavedProvidersEmptyState />
      </ChakraProvider>
    );

    expect(
      screen.getByRole('heading', {
        name: /add communities to your favorites/i
      })
    ).toBeInTheDocument();

    expect(
      screen.getByText(
        /you haven't added any communities to your favorites yet/i
      )
    ).toBeInTheDocument();
  });

  it('renders the heart icon', () => {
    const { container } = render(
      <ChakraProvider>
        <SavedProvidersEmptyState />
      </ChakraProvider>
    );

    const iconContainer = container.querySelector('svg');
    expect(iconContainer).toBeInTheDocument();
  });

  it('renders the MiniSelfSearch component', () => {
    render(
      <ChakraProvider>
        <SavedProvidersEmptyState />
      </ChakraProvider>
    );

    expect(screen.getByTestId('mini-self-search-mock')).toBeInTheDocument();
  });

  it('passes the correct props to MiniSelfSearch', () => {
    render(
      <ChakraProvider>
        <SavedProvidersEmptyState />
      </ChakraProvider>
    );

    expect(MiniSelfSearch).toHaveBeenCalledWith(
      expect.objectContaining({
        text: 'Search',
        textColor: 'white',
        bgColor: 'secondary',
        miniSelfSearchId: 'test-uuid-123',
        maxWidth: '100%',
        buttonOutsideInput: false
      }),
      expect.anything()
    );
  });
});
