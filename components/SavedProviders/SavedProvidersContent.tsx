'use client';

import { Divider, Grid, GridItem } from '@chakra-ui/react';
import { Skeleton } from '@chakra-ui/skeleton';
import Heading from '@components/Heading';
import InquiryForm from '@components/InquiryForm';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import Container from '@components/LayoutStructure/Container';
import ProviderList from '@components/ProviderList/ProviderList';
import SavedProvidersEmptyState from '@components/SavedProviders/SavedProvidersEmptyState';
import useCookieStorageValue from '@hooks/use-cookie-storage-value';
import { getProvidersByIds } from '@services/providers/getProvidersByIds';
import { getProviderIdsFromCookies } from '@utils/getProviderIdsFromCookies';
import inBrowser from '@utils/inBrowser';
import { useEffect, useMemo, useState } from 'react';

import {
  LEGAL_DISCLOSURE,
  RECENT_PROVIDERS_COOKIE,
  SAVED_PROVIDERS_COOKIE_NAME
} from '~/constants';
import { Provider } from '~/contexts/Provider';

export default function SavedProvidersContent() {
  const savedCookieValue = useCookieStorageValue(SAVED_PROVIDERS_COOKIE_NAME);
  const recentlyViewedCookieValue = useCookieStorageValue(
    RECENT_PROVIDERS_COOKIE
  );
  const [savedProviders, setSavedProviders] = useState<Provider[]>([]);
  const [recentlyViewedProviders, setRecentlyViewedProviders] = useState<
    Provider[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      if (!inBrowser()) {
        return;
      }

      const savedIds = getProviderIdsFromCookies(SAVED_PROVIDERS_COOKIE_NAME);
      const recentIds = getProviderIdsFromCookies(RECENT_PROVIDERS_COOKIE);

      const allIds = [...new Set([...(savedIds || []), ...(recentIds || [])])];
      if (allIds.length === 0) {
        setSavedProviders([]);
        setRecentlyViewedProviders([]);
        setIsLoading(false);
        return;
      }

      try {
        const allProviders = await getProvidersByIds(allIds);

        if (savedIds?.length > 0) {
          const savedProvidersData = allProviders.filter((provider) =>
            savedIds.includes(provider?.id)
          );
          setSavedProviders(savedProvidersData);
        } else {
          setSavedProviders([]);
        }

        if (recentIds?.length > 0) {
          const recentProvidersData = allProviders.filter((provider) =>
            recentIds.includes(provider.id)
          );
          setRecentlyViewedProviders(recentProvidersData);
        } else {
          setRecentlyViewedProviders([]);
        }
      } catch (error) {
        console.error('Error fetching providers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [savedCookieValue, recentlyViewedCookieValue]);

  const SavedProviders = useMemo(() => {
    if (isLoading) {
      return (
        <Grid
          templateColumns={{ base: '1fr', md: 'repeat(3, 1fr)' }}
          gap={{ base: 4, md: 6 }}
        >
          {Array.from({ length: 3 }).map((_, index) => (
            <GridItem key={index}>
              <Skeleton
                height="330px"
                width="100%"
                borderRadius="md"
                data-testid="saved-provider-skeleton"
              />
            </GridItem>
          ))}
        </Grid>
      );
    }

    if (savedProviders.length === 0) {
      return <SavedProvidersEmptyState />;
    }

    return (
      <ProviderList providers={savedProviders} listName="Favorites List" />
    );
  }, [isLoading, savedProviders]);

  return (
    <>
      <Container
        backgroundColor="var(--chakra-colors-background-50)"
        borderRadius="12px"
        paddingBottom={9}
        marginBottom={8}
        paddingX={{ base: 0, md: 8 }}
      >
        <Heading
          title={`Saved Providers (${
            !isLoading ? savedProviders.length : '0'
          })`}
          headingSize="xl"
          mobileHeadingSize="lg"
          marginTop={8}
          withContainer={false}
          marginLeft={{ base: 4, md: 0 }}
        />
        <Divider
          marginY={{ base: 4, md: 8 }}
          marginX={{ base: 4, md: 0 }}
          width="auto"
        />
        {SavedProviders}
        {recentlyViewedProviders.length > 0 && (
          <Container
            backgroundColor="white"
            marginTop={12}
            borderRadius="12px"
            paddingBottom={6}
            paddingX={{ base: 0, md: 8 }}
            boxShadow="lg"
          >
            <ProviderList
              title="Recently Viewed Providers"
              content="Be sure to save any of the providers you recently viewed that you want to view again later."
              providers={recentlyViewedProviders}
              listName="Recently Viewed List"
              shouldLimitVisibleProviders={false}
            />
          </Container>
        )}
      </Container>
      <InquiryForm
        formId="saved-inqiury-modal"
        display={Display.FIT_CONTENT_MODAL}
        metadata={{
          ['@id']: '00000000-0000-0000-0000-000000000000'
        }}
        ctaAction={CTAAction.DISPLAY_COST_DATA}
        title="Get Expert Advice & Pricing Information"
        ctaText="Request more info"
        ctaColorScheme="secondary"
        legalDisclosure={LEGAL_DISCLOSURE}
      />
    </>
  );
}
