import { Box, Flex, Heading, Text } from '@chakra-ui/react';
import MiniSelfSearch from '@components/MiniSelfSearch/MiniSelfSearch';
import { MdFavoriteBorder } from 'react-icons/md';
import { v5 as uuidv5 } from 'uuid';

// Generate a UUID using the name and a consistent namespace
// This approach creates a deterministic UUID that will be the constant all the time.
const SEARCH_ID = uuidv5('saved-providers-empty-state', uuidv5.URL);

const SavedProvidersEmptyState = () => {
  return (
    <Flex
      direction="column"
      align="center"
      textAlign="center"
      mx="auto"
      gap={4}
      m={4}
      mb={24}
    >
      <Box
        color="gray.700"
        mb={2}
        width={{ base: '60px', md: '115px' }}
        height={{ base: '56px', md: '115px' }}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <MdFavoriteBorder size="100%" />
      </Box>
      <Heading as="h1" size={{ base: 'md', md: 'lg' }}>
        Add communities to your favorites
      </Heading>
      <Box width={{ base: '100%', md: '520px' }} textAlign="left">
        <Text color="gray.700" mb={6} fontSize={{ base: 'sm', md: 'lg' }}>
          You haven&apos;t added any communities to your favorites yet. Use the
          search to find communities to save below.
        </Text>

        {/*Location based search leads to local search page with the location the user entered here applied*/}
        <MiniSelfSearch
          text="Search"
          textColor="white"
          bgColor="secondary"
          miniSelfSearchId={SEARCH_ID}
          maxWidth="100%"
          buttonOutsideInput={false}
          enablePredictiveSearch={true}
          careType="senior-living"
        />
      </Box>
    </Flex>
  );
};

export default SavedProvidersEmptyState;
