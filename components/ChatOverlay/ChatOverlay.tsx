import BottomPopup from '@components/BottomPopup/BottomPopup';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import dynamic from 'next/dynamic';

import { Navigation } from '~/services/graphql/navigation';
import { Metadata, Page } from '~/types/Magnolia';

import { StringToIconKeys } from '../RenderIcon';

export type ChatOverlayProps = Exclude<
  Exclude<Navigation['footer'], null>['chatOverlay'],
  null
> & {
  metadata: Pick<Metadata, '@id'>;
  page?: Page;
};
const InquiryForm = dynamic(() => import('@components/InquiryForm'));

const ChatOverlay: React.FC<ChatOverlayProps> = ({
  page,
  formId,
  formAction = CTAAction.REQUEST_INFO,
  rollUpType,
  title,
  thankYouMessage,
  thankYouMessageColor,
  legalDisclosure,
  metadata,
  allowedPageTypes,
  popup
}) => {
  if (!popup) return <></>;
  if (!popup.title && !popup.icon) return <></>;
  if (
    allowedPageTypes &&
    allowedPageTypes.length !== 0 &&
    !allowedPageTypes.includes(page?.pageType ?? '')
  ) {
    return <></>;
  }

  return (
    <BottomPopup>
      <BottomPopup.Closed
        field={popup.field}
        title={popup.title || undefined}
        icon={(popup.icon as StringToIconKeys) || undefined}
        color={popup.color || undefined}
        size={popup.size || undefined}
        variant={popup.variant || undefined}
        visibility={popup.visibility}
      />
      <BottomPopup.Opened title={popup.title || undefined}>
        <InquiryForm
          title={title || undefined}
          formId={formId || 'chat-overlay-form'}
          display={Display.VERTICAL}
          rollUpType={rollUpType || undefined}
          ctaAction={formAction}
          isSideBar
          legalDisclosure={legalDisclosure || undefined}
          hasWrapContainer={false}
          thankYouMessage={thankYouMessage || undefined}
          thankYouMessageColor={thankYouMessageColor || undefined}
          metadata={metadata}
        />
      </BottomPopup.Opened>
    </BottomPopup>
  );
};

export default ChatOverlay;
