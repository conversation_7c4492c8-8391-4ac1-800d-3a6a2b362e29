import { render } from '@utils/test-utils';

import ChatOverlay from './ChatOverlay';
describe('<ChatOverlay />', () => {
  it('renders the CTA button when current page type is allowed and there are configs for the CTA button', () => {
    const { container } = render(
      <ChatOverlay
        popup={{ title: 'Click Here' }}
        allowedPageTypes={['allowed-page-type']}
        page={{ pageType: 'allowed-page-type' }}
      />
    );
    expect(container.firstChild).not.toBeEmptyDOMElement();
  });

  it('renders the CTA button when there list of allowed page types is empty', () => {
    const { container } = render(
      <ChatOverlay
        popup={{ title: 'Click Here' }}
        page={{ pageType: 'allowed-page-type' }}
      />
    );
    expect(container.firstChild).not.toBeEmptyDOMElement();
  });

  it('does not render anything when current page type is not allowed', () => {
    const { container } = render(
      <ChatOverlay
        popup={{ title: 'Click Here' }}
        allowedPageTypes={['one-page-type']}
        page={{ pageType: 'another-page-type' }}
      />
    );
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('does not render anything when there is no popup config', () => {
    const { container } = render(<ChatOverlay />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('does not render anything when the popup config has nothing to show on the CTA', () => {
    const { container } = render(<ChatOverlay popup={{}} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });
});
