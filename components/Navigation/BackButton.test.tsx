import BackButton from '@components/Navigation/BackButton';
import { hasHistory } from '@components/Navigation/Breadcrumb';
import { fireEvent, render, screen } from '@utils/test-utils';

import SiteContext, { SiteContextType } from '~/contexts/SiteContext';
const mockBack = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => ({
    back: mockBack
  })
}));
describe('BackButton', () => {
  it("doesn't render if referrer is not the same page", () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { hostname: 'www.caring.com' }
    });
    Object.defineProperty(window.document, 'referrer', {
      writable: true,
      value: 'https://www.other.com'
    });
    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } } as SiteContextType}
      >
        <BackButton showButton={hasHistory()} />
      </SiteContext.Provider>
    );
    expect(document.querySelector('button')).not.toBeInTheDocument();
  });

  it('renders if referrer is the same page and calls back on click', () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { hostname: 'www.caring.com' }
    });
    Object.defineProperty(window.document, 'referrer', {
      writable: true,
      value: 'https://www.caring.com'
    });
    Object.defineProperty(window, 'history', {
      writable: true,
      value: ['something', 'something']
    });
    window.tracking = {
      track: jest.fn()
    };
    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } } as SiteContextType}
      >
        <BackButton showButton={hasHistory()} />
      </SiteContext.Provider>
    );
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    fireEvent.click(button);

    expect(mockBack).toHaveBeenCalled();
  });

  it("doesn't render if is not caring", () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { hostname: 'www.notcaring.com' }
    });
    Object.defineProperty(window.document, 'referrer', {
      writable: true,
      value: 'https://www.notcaring.com'
    });
    window.tracking = {
      track: jest.fn()
    };
    render(
      <SiteContext.Provider
        value={{ site: { path: 'notcaring.com' } } as SiteContextType}
      >
        <BackButton showButton={hasHistory()} />
      </SiteContext.Provider>
    );
    const button = screen.queryByRole('button');
    expect(button).not.toBeInTheDocument();
  });

  it("doesn't render if history is 1", () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { hostname: 'www.caring.com' }
    });
    Object.defineProperty(window.document, 'referrer', {
      writable: true,
      value: 'https://www.caring.com'
    });
    window.tracking = {
      track: jest.fn()
    };
    Object.defineProperty(window, 'history', {
      writable: true,
      value: ['something']
    });

    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } } as SiteContextType}
      >
        <BackButton showButton={hasHistory()} />
      </SiteContext.Provider>
    );
    const button = screen.queryByRole('button');
    expect(button).not.toBeInTheDocument();
  });

  it('renders if history is more than 1', () => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { hostname: 'www.caring.com' }
    });
    Object.defineProperty(window.document, 'referrer', {
      writable: true,
      value: 'https://www.caring.com'
    });
    window.tracking = {
      track: jest.fn()
    };
    Object.defineProperty(window, 'history', {
      writable: true,
      value: ['something', 'something']
    });

    render(
      <SiteContext.Provider
        value={{ site: { path: 'caring.com' } } as SiteContextType}
      >
        <BackButton showButton={hasHistory()} />
      </SiteContext.Provider>
    );
    const button = screen.queryByRole('button');
    expect(button).toBeInTheDocument();
  });
});
