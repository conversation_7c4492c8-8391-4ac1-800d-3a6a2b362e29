import {
  mockBreadcrumb,
  render,
  setDesktopScreen,
  setMobileScreen
} from '@utils/test-utils';

import ProviderContext from '~/contexts/Provider';
import SiteContext, { sitesConfig } from '~/contexts/SiteContext';
import { homeLink } from '~/contexts/TenantFunctions/CaringFunctions';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { CaringDomains } from '~/types/Domains';

import { Breadcrumb } from './';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {}
  }))
}));

jest.mock('~/contexts/TenantFunctionsContext');

let mockGetBreadcrumbLinks;

beforeEach(() => {
  mockGetBreadcrumbLinks = jest.fn();
  (useTenantFunctions as jest.Mock).mockImplementation(() => ({
    getBreadcrumbLinks: mockGetBreadcrumbLinks
  }));
});

const setBreadcrumbLinks = (links) => {
  mockGetBreadcrumbLinks.mockImplementation(() => links);
};

describe('Breadcrumb', () => {
  beforeEach(() => {
    setBreadcrumbLinks(mockBreadcrumb.items);
  });

  it('should render breadcrumb component', async () => {
    const { container } = render(<Breadcrumb />);

    expect(container.firstChild).toBeInTheDocument();
  });

  it('should render breadcrumb with items', async () => {
    const { getByText, container } = render(<Breadcrumb />);

    const firstItem = getByText(mockBreadcrumb.items[0].label);

    expect(firstItem).toBeInTheDocument();

    const lastItem = getByText(mockBreadcrumb.items[1].label);

    expect(lastItem).toBeInTheDocument();
  });

  it('should render the Share button on provider pages', async () => {
    const { getByTestId } = render(
      <ProviderContext.Provider
        value={{
          provider: {
            id: '1',
            averageRating: 0,
            description: '',
            name: '',
            phoneNumber: '',
            reviewCount: 0,
            slug: ''
          },
          setProvider: () => {}
        }}
      >
        <Breadcrumb />
      </ProviderContext.Provider>
    );
    const shareButton = getByTestId('share-button');
    expect(shareButton).toBeInTheDocument();
  });

  it('should not render the Back button', async () => {
    const { queryByRole } = render(<Breadcrumb />);
    const backButton = queryByRole('button', { name: /back/i });
    expect(backButton).not.toBeInTheDocument();
  });

  it('should not render the Share button on non-provider pages', async () => {
    const { queryByTestId } = render(<Breadcrumb />);
    const shareButton = queryByTestId('share-button');
    expect(shareButton).not.toBeInTheDocument();
  });

  describe('Caring.com', () => {
    beforeEach(() => {
      setBreadcrumbLinks([homeLink, ...mockBreadcrumb.items]);
    });

    const caringSiteConfig =
      sitesConfig.find((site) => site.name === 'Caring.com') ?? null;

    it('should not render the home link on non-provider pages on desktop', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const homeLink = queryByText('Home');
      expect(homeLink).not.toBeInTheDocument();
    });

    it('should not render the home link on non-provider pages on mobile', async () => {
      setMobileScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const homeLink = queryByText('Home');
      expect(homeLink).not.toBeInTheDocument();
    });

    it('should not render the county on the breadcrumb on the city page on desktop', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const countyLink = queryByText('County');
      expect(countyLink).not.toBeInTheDocument();
    });

    it('should not render the county on the breadcrumb on the city page on mobile', async () => {
      setMobileScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const countyLink = queryByText('County');
      expect(countyLink).not.toBeInTheDocument();
    });

    it('should render the breadcrumb when a provider is indexed', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <ProviderContext.Provider
            value={{
              provider: {
                id: '1',
                averageRating: 0,
                description: '',
                name: '',
                phoneNumber: '',
                reviewCount: 0,
                slug: '',
                isIndexed: true
              },
              setProvider: () => {}
            }}
          >
            <Breadcrumb />
          </ProviderContext.Provider>
        </SiteContext.Provider>
      );
      const breadcrumb = queryByText('Senior Living');
      expect(breadcrumb).toBeInTheDocument();
    });

    it('should not render the breadcrumb when a provider is not indexed', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <ProviderContext.Provider
            value={{
              provider: {
                id: '1',
                averageRating: 0,
                description: '',
                name: '',
                phoneNumber: '',
                reviewCount: 0,
                slug: '',
                isIndexed: false
              },
              setProvider: () => {}
            }}
          >
            <Breadcrumb />
          </ProviderContext.Provider>
        </SiteContext.Provider>
      );
      const breadcrumb = queryByText('Senior Living');
      expect(breadcrumb).not.toBeInTheDocument();
    });
  });

  describe('SeniorHomes.com', () => {
    beforeEach(() => {
      setBreadcrumbLinks([homeLink, ...mockBreadcrumb.items]);
    });

    const seniorHomesSiteConfig =
      sitesConfig.find((site) => site.name === 'Senior Homes') ?? null;

    it('should not render the home link on non-provider pages on desktop', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const homeLink = queryByText('Home');
      expect(homeLink).not.toBeInTheDocument();
    });

    it('should not render the home link on non-provider pages on mobile', async () => {
      setDesktopScreen();
      const { queryByText } = render(
        <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const homeLink = queryByText('Home');
      expect(homeLink).not.toBeInTheDocument();
    });
  });

  describe('SaveProviderButton', () => {
    const caringSiteConfig =
      sitesConfig.find((site) => site.path === CaringDomains.LIVE) ?? null;

    const providerContext = {
      provider: {
        id: '1',
        averageRating: 0,
        description: '',
        name: '',
        phoneNumber: '',
        reviewCount: 0,
        slug: ''
      },
      setProvider: () => {}
    };

    it('should show SaveProviderButton on Caring.com provider pages', async () => {
      const { getByTestId } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <ProviderContext.Provider value={providerContext}>
            <Breadcrumb />
          </ProviderContext.Provider>
        </SiteContext.Provider>
      );

      const saveButton = getByTestId('save-provider-button');
      expect(saveButton).toBeInTheDocument();
    });

    it('should not show SaveProviderButton on SeniorHomes.com provider pages', async () => {
      const seniorHomesSiteConfig =
        sitesConfig.find((site) => site.name === 'Senior Homes') ?? null;

      const { queryByTestId } = render(
        <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
          <ProviderContext.Provider value={providerContext}>
            <Breadcrumb />
          </ProviderContext.Provider>
        </SiteContext.Provider>
      );

      const saveButton = queryByTestId('save-provider-button');
      expect(saveButton).not.toBeInTheDocument();
    });

    it('should not show SaveProviderButton when no provider exists', async () => {
      const { queryByTestId } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <Breadcrumb />
        </SiteContext.Provider>
      );
      const saveButton = queryByTestId('save-provider-button');
      expect(saveButton).not.toBeInTheDocument();
    });

    it('should show SaveProviderButton alongside ShareButton on provider pages', async () => {
      const { getByTestId } = render(
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <ProviderContext.Provider value={providerContext}>
            <Breadcrumb />
          </ProviderContext.Provider>
        </SiteContext.Provider>
      );

      const shareButton = getByTestId('share-button');
      const saveButton = getByTestId('save-provider-button');

      expect(shareButton).toBeInTheDocument();
      expect(saveButton).toBeInTheDocument();
    });
  });
});
