'use client';

import {
  Breadcrumb as ChakraBreadcrumb,
  BreadcrumbItem,
  BreadcrumbLink
} from '@chakra-ui/breadcrumb';
import { Box, Flex, Stack } from '@chakra-ui/layout';
import { useBreakpointValue } from '@chakra-ui/media-query';
import AdvertisingDisclosure from '@components/AdvertisingDisclosure';
import Container from '@components/LayoutStructure/Container';
import BackButton from '@components/Navigation/BackButton';
import SaveProviderButton from '@components/ProviderCard/V2/SaveProviderButton';
import ShareButton from '@components/ShareButton/ShareButton';
import { useParams, usePathname } from 'next/navigation';
import { BreadcrumbJsonLd } from 'next-seo';
import { useContext, useState } from 'react';

import CatalogContext from '~/contexts/CatalogContext';
import ProviderContext from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import StoryContext from '~/contexts/StoryContext';
import { useTenantFunctions } from '~/contexts/TenantFunctionsContext';
import { CaringDomains } from '~/types/Domains';

export type BreadcrumbItem = {
  label: string;
  url: string;
};

export const hasHistory = (): boolean => {
  const referrer = document.referrer;
  const sameHost = referrer
    ? new URL(referrer).hostname === window.location.hostname
    : false;

  const gotHistory = window.history.length > 1;

  return sameHost && gotHistory;
};

const Breadcrumbs = ({
  items,
  showBackButton = false
}: {
  items: BreadcrumbItem[];
  showBackButton?: boolean;
}) => {
  return (
    <ChakraBreadcrumb
      fontWeight={400}
      sx={{ ol: { flexWrap: 'wrap' }, span: { color: 'gray.700' } }}
      fontSize="sm"
      spacing="0.25rem"
    >
      {showBackButton ? (
        <BreadcrumbItem>
          <BackButton showButton={true} />
        </BreadcrumbItem>
      ) : null}

      {items.map((item, index) => {
        const isLastItem = index === items.length - 1;
        return (
          <BreadcrumbItem
            key={index}
            color="gray.700"
            isCurrentPage={isLastItem}
          >
            {isLastItem ? (
              <BreadcrumbLink
                as="span"
                cursor="initial"
                color="gray.700"
                _hover={{ textDecoration: 'none' }}
              >
                {item?.label}
              </BreadcrumbLink>
            ) : (
              <BreadcrumbLink href={item.url}>{item.label}</BreadcrumbLink>
            )}
          </BreadcrumbItem>
        );
      })}
    </ChakraBreadcrumb>
  );
};

export type Props = {
  noHorizontalPadding?: boolean;
  noVerticalMargin?: boolean;
  advertisingDisclosure?: string;
};

const Breadcrumb = ({
  noHorizontalPadding,
  noVerticalMargin = false,
  advertisingDisclosure
}: Props) => {
  const [showBackButton, setShowBackButton] = useState(false);
  const isDesktop = useBreakpointValue({ base: false, md: true });
  const siteContext = useContext(SiteContext);
  const DOMAIN = `https://www.${siteContext.site?.path}`;
  const provider = useContext(ProviderContext)?.provider;
  const catalog = useContext(CatalogContext);
  const storyContext = useContext(StoryContext);
  const pathname = usePathname();
  const params = useParams();
  const shouldShowSaveProviderButton =
    (siteContext?.site?.path === CaringDomains.LIVE && provider) || false; //Show save button only on Caring.com provider pages
  const providerId = provider?.id || '';

  const displayHasAdvertisingDisclosure =
    advertisingDisclosure && storyContext?.hasAdvertisingDisclosure;
  const { getBreadcrumbLinks } = useTenantFunctions();

  let items = [] as BreadcrumbItem[];
  if (!provider || provider.isIndexed) {
    items = getBreadcrumbLinks({
      provider,
      navigationData: { pathname: pathname || '', params: params || {} },
      catalog
    });
    items = items.filter((item) => item.label !== 'Home');

    if (catalog?.type === 'geo-city') {
      items = items.filter((item) => item.label !== catalog.county);
    }
  }

  const marginBottom = noVerticalMargin ? { mb: '0' } : undefined;
  return (
    <Container
      noHorizontalPadding={noHorizontalPadding}
      pt={showBackButton && !isDesktop ? '2' : '8'}
      pb="6"
      {...marginBottom}
    >
      {isDesktop ? (
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Breadcrumbs items={items} showBackButton={showBackButton} />
          {provider ? <ShareButton showText={showBackButton} /> : null}
          {shouldShowSaveProviderButton && (
            <SaveProviderButton providerId={providerId} />
          )}
          {displayHasAdvertisingDisclosure && (
            <AdvertisingDisclosure
              advertisingDisclosure={advertisingDisclosure}
              displayOnDesktop
            />
          )}
        </Stack>
      ) : (
        <Stack
          alignItems={showBackButton ? 'flex-start' : 'center'}
          direction={showBackButton ? 'column' : 'row'}
        >
          {showBackButton ? (
            <>
              {/* On provider pages, the back button needs to be displayed along with the share button. */}
              {provider ? (
                <>
                  <Flex alignSelf="stretch" alignItems="center" height="40px">
                    <Box flex="1">
                      <BackButton showButton={showBackButton} />
                    </Box>

                    <Box flex="1">
                      <Flex>
                        <ShareButton showText={showBackButton} />
                        {shouldShowSaveProviderButton && (
                          <SaveProviderButton providerId={providerId} />
                        )}
                      </Flex>
                    </Box>
                  </Flex>

                  <Breadcrumbs items={items} />
                </>
              ) : (
                <Breadcrumbs items={items} showBackButton={showBackButton} />
              )}
            </>
          ) : (
            <>
              <Breadcrumbs items={items} />

              {provider ? <ShareButton showText={showBackButton} /> : null}
              {shouldShowSaveProviderButton && (
                <SaveProviderButton providerId={providerId} />
              )}
            </>
          )}
        </Stack>
      )}

      <BreadcrumbJsonLd
        itemListElements={items.map((item, index) => ({
          position: index + 1,
          name: item?.label,
          item: `${DOMAIN}${item?.url}`
        }))}
      />
    </Container>
  );
};

export default Breadcrumb;
