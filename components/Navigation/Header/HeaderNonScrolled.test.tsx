import { Chakra<PERSON>rovider } from '@chakra-ui/react';
import { HeaderNavigation } from '@components/Navigation';
import { fireEvent, render, screen } from '@testing-library/react';

import SiteContext, { sitesConfig } from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';

import HeaderNonScrolled from './HeaderNonScrolled';

jest.mock('./NavigationDrawer/NavigationDrawer', () => {
  return {
    __esModule: true,
    default: ({ placement }: { placement?: string }) => (
      <div data-testid={`navigation-drawer${placement ? `-${placement}` : ''}`}>
        Navigation Drawer
      </div>
    )
  };
});

jest.mock('./HeaderLogo', () => {
  return {
    __esModule: true,
    default: (props: any) => {
      if (!props.logo || !props.logoAlt) {
        return null;
      }
      return <div data-testid="header-logo">Header Logo: {props.logoAlt}</div>;
    }
  };
});

jest.mock('./HeaderMainItems', () => {
  return {
    __esModule: true,
    default: (props: any) => (
      <div data-testid="header-main-items">
        Main Items: {props.main.length} items
      </div>
    )
  };
});

jest.mock('./SecondaryMenuItem', () => {
  return {
    __esModule: true,
    default: ({ item }: { item: any }) => (
      <div data-testid="secondary-menu-item">Secondary Item: {item.text}</div>
    )
  };
});

jest.mock('./SavedProvidersNavButton', () => {
  return {
    __esModule: true,
    default: ({ visibility }: { visibility: string }) => (
      <div data-testid="saved-provider-nav-button" data-visibility={visibility}>
        Saved Providers Button
      </div>
    )
  };
});

describe('HeaderNonScrolled', () => {
  const caringSiteConfig =
    sitesConfig.find((site) => site.path === CaringDomains.LIVE) ?? null;

  const mockProps: HeaderNavigation = {
    logo: {
      logo: { link: '/logo.png', caption: 'Logo Caption' },
      logoMaxWidth: '200',
      mobileLogo: { link: '/mobile-logo.png', caption: 'Mobile Logo Caption' },
      mobileLogoMaxWidth: '100',
      logoAlt: 'Test Logo',
      logoUrl: 'https://example.com'
    },
    main: [
      {
        id: 'main-item-1',
        type: 'link' as const,
        position: 'main' as const,
        variant: 'solid' as const,
        text: 'Main Item 1',
        children: [],
        visibility: 'always',
        mobile: true,
        desktop: true,
        textColor: 'black',
        textColorRange: '500',
        icon: 'MdHome',
        secondText: '',
        secondTextColor: '',
        secondTextColorRange: '',
        link: {
          url: '/main1',
          enabled: true
        },
        inquiryForm: { enabled: false }
      },
      {
        id: 'main-item-2',
        type: 'link' as const,
        position: 'main' as const,
        variant: 'solid' as const,
        text: 'Main Item 2',
        children: [],
        visibility: 'always',
        mobile: true,
        desktop: true,
        textColor: 'black',
        textColorRange: '500',
        icon: 'MdHome',
        secondText: '',
        secondTextColor: '',
        secondTextColorRange: '',
        link: {
          url: '/main2',
          enabled: true
        },
        inquiryForm: { enabled: false }
      }
    ],
    secondary: [
      {
        id: 'secondary-item-1',
        type: 'link' as const,
        position: 'secondary' as const,
        variant: 'solid' as const,
        text: 'Secondary Item 1',
        children: [],
        visibility: 'always',
        mobile: true,
        desktop: true,
        textColor: 'black',
        textColorRange: '500',
        icon: 'MdHome',
        secondText: '',
        secondTextColor: '',
        secondTextColorRange: '',
        link: {
          url: '/secondary1',
          enabled: true
        },
        inquiryForm: { enabled: false }
      },
      {
        id: 'secondary-item-2',
        type: 'link' as const,
        position: 'secondary' as const,
        variant: 'solid' as const,
        text: 'Secondary Item 2',
        children: [],
        visibility: 'always',
        mobile: true,
        desktop: true,
        textColor: 'black',
        textColorRange: '500',
        icon: 'MdHome',
        secondText: '',
        secondTextColor: '',
        secondTextColorRange: '',
        link: {
          url: '/secondary2',
          enabled: true
        },
        inquiryForm: { enabled: false }
      }
    ],
    metadata: { '@id': 'test-header' },
    useAlternateDrawerMenu: false,
    mainMenuPosition: 'center',
    intro: '',
    showShadow: false
  };

  const headerProps = {
    ...mockProps,
    providerPageHeadings: [
      { title: 'Provider Title', linkText: 'Provider Link' }
    ],
    geoPageHeadings: ['Geo Heading 1', 'Geo Heading 2']
  };

  it('renders with all components when all props are provided', () => {
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    expect(screen.getByTestId('header-logo')).toBeInTheDocument();
    expect(screen.getByTestId('header-logo')).toHaveTextContent('Test Logo');

    expect(screen.getByTestId('header-main-items')).toBeInTheDocument();
    expect(screen.getByTestId('header-main-items')).toHaveTextContent(
      '2 items'
    );

    expect(screen.getAllByTestId('secondary-menu-item').length).toBe(2);
    expect(screen.getAllByTestId('secondary-menu-item')[0]).toHaveTextContent(
      'Secondary Item 1'
    );
    expect(screen.getAllByTestId('secondary-menu-item')[1]).toHaveTextContent(
      'Secondary Item 2'
    );
  });

  it('renders the menu toggle button when not using alternate drawer menu', () => {
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toBeInTheDocument();
  });

  it('renders the NavigationDrawer when using alternate drawer menu', () => {
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} useAlternateDrawerMenu={true} />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    expect(screen.getByTestId('navigation-drawer-left')).toBeInTheDocument();
    expect(screen.getByTestId('navigation-drawer')).toBeInTheDocument();
  });

  it('toggles the mobile menu when the toggle button is clicked', () => {
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    const menuButton = screen.getByLabelText('Toggle menu');

    const mobileMenu = screen.getByTestId('header-main-items').parentElement;
    expect(mobileMenu).toHaveStyle('opacity: 0');
    fireEvent.click(menuButton);
    expect(mobileMenu).toHaveStyle('opacity: 100');
    fireEvent.click(menuButton);
    expect(mobileMenu).toHaveStyle('opacity: 0');
  });

  it('does not render SavedProvidersNavButton when site is not Caring.com', () => {
    const seniorHomesSiteConfig =
      sitesConfig.find((site) => site.name === 'Senior Homes') ?? null;
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
          <HeaderNonScrolled {...headerProps} />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    expect(
      screen.queryByTestId('saved-provider-nav-button')
    ).not.toBeInTheDocument();
  });

  it('does not render components when corresponding props are empty', () => {
    render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled
            {...headerProps}
            logo={{
              logo: null,
              logoMaxWidth: '',
              mobileLogo: null,
              mobileLogoMaxWidth: '',
              logoAlt: '',
              logoUrl: ''
            }}
            main={[]}
            secondary={[]}
          />
        </SiteContext.Provider>
      </ChakraProvider>
    );
    expect(screen.queryByTestId('header-logo')).not.toBeInTheDocument();
    expect(screen.queryByTestId('header-main-items')).not.toBeInTheDocument();
    expect(screen.queryByTestId('secondary-menu-item')).not.toBeInTheDocument();
  });

  it('applies correct mainMenuPosition styles', () => {
    const { rerender } = render(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} mainMenuPosition="left" />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    let mainMenu = screen.getByTestId('header-main-items').parentElement;
    expect(mainMenu).toHaveStyle('margin-right: auto');

    rerender(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} mainMenuPosition="right" />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    mainMenu = screen.getByTestId('header-main-items').parentElement;
    expect(mainMenu).toHaveStyle('margin-left: auto');

    rerender(
      <ChakraProvider>
        <SiteContext.Provider value={{ site: caringSiteConfig }}>
          <HeaderNonScrolled {...headerProps} mainMenuPosition="center" />
        </SiteContext.Provider>
      </ChakraProvider>
    );

    mainMenu = screen.getByTestId('header-main-items').parentElement;
    expect(mainMenu).not.toHaveStyle('margin-left: auto');
    expect(mainMenu).not.toHaveStyle('margin-right: auto');
  });
});
