import HeaderJumpLinks from '@components/Navigation/Header/HeaderJumpLinks';
import { useProviderFallback } from '@hooks/use-provider-fallback';
import { render, screen } from '@utils/test-utils';
import { mockProviderV2 } from '@utils/test-utils/mocks/provider';

jest.mock('@hooks/use-provider-fallback');

const mockUseProviderFallback = useProviderFallback as jest.MockedFunction<
  typeof useProviderFallback
>;

jest.mock('@components/LayoutStructure/EditablePage', () => ({
  getCustomHeadings: () => [
    {
      title: 'title',
      linkText: 'linkText'
    }
  ],
  getPageHeadings: () => [
    {
      title: 'title',
      linkText: 'linkText'
    }
  ]
}));

jest.mock('@components/Navigation/Header/Header', () => ({
  itemDisplay: jest.fn(() => ({ base: 'block' }))
}));
describe('HeaderJumpLinks', () => {
  it('should render correctly when on a provider page', () => {
    mockUseProviderFallback.mockReturnValue({
      provider: mockProviderV2,
      status: 1
    });
    render(
      <HeaderJumpLinks
        title="Jump To"
        geoPageHeadings={[{ title: 'test', linkText: 'linkText' }]}
        providerPageHeadings={[{ title: 'test', linkText: 'linkText' }]}
      />
    );
    const menuItem = screen.getByText('Jump To');
    expect(menuItem).toBeInTheDocument();

    const link = screen.getByText('linkText');
    expect(link).toBeInTheDocument();
  });

  it('should render correctly when on a geo page', () => {
    mockUseProviderFallback.mockReturnValue({
      provider: null,
      status: 3
    });
    render(
      <HeaderJumpLinks
        title="Jump To"
        geoPageHeadings={[{ title: 'title', linkText: 'linkText' }]}
        providerPageHeadings={[{ title: 'test', linkText: 'linkText' }]}
      />
    );
    const menuItem = screen.getByText('Jump To');
    expect(menuItem).toBeInTheDocument();

    const link = screen.getByText('linkText');
    expect(link).toBeInTheDocument();
  });
});
