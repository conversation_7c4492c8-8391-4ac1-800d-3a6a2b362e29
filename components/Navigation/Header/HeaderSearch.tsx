import { Icon } from '@chakra-ui/icon';
import { Input, InputGroup, InputRightElement } from '@chakra-ui/input';
import { Box, BoxProps } from '@chakra-ui/react';
import useSearchStepSubmission from '@components/Analytics/events/SearchStepSubmission';
import useSearchSubmission from '@components/Analytics/events/SearchSubmission';
import {
  NavigationItem,
  NavigationItemType
} from '@components/Navigation/Navigation.types';
import { STRING_TO_ICON_CLASS } from '@components/RenderIcon';
import useSearch from '@hooks/useSearch';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

import { itemDisplay } from './Header';

const AutocompleteInput = dynamic(
  () => import('@components/AutocompleteInput/AutocompleteInput')
);

interface HeaderSearchProps extends BoxProps {
  item: Omit<NavigationItem, 'url'>;
  type: Omit<
    NavigationItemType,
    'link' | 'button' | 'jump_links' | 'whoAreYouSearchingFor'
  >;
  inputGroupProps?: BoxProps;
  inputRightElementProps?: BoxProps;
}

const HeaderSearch: React.FC<HeaderSearchProps> = ({
  item,
  type,
  inputGroupProps,
  inputRightElementProps,
  ...rest
}) => {
  const { buildSearchUrl } = useSearch();
  const searchStepSubmission = useSearchStepSubmission();
  const searchSubmission = useSearchSubmission();
  const [keyword, setKeyword] = useState('');

  const handleSearchEvents = () => {
    const stepInstanceId = uuidv4();
    const searchTemplateId = item.id || uuidv4();
    const stepId = uuidv5(item.text, searchTemplateId);
    const searchInstanceId = uuidv4();

    searchStepSubmission({
      search_template_id: searchTemplateId,
      search_instance_id: searchInstanceId,
      step_id: stepId,
      step_instance_id: stepInstanceId,
      step_index: 1,
      step_content: [
        {
          prompt_id: uuidv5(item.text, searchTemplateId),
          prompt_type: 'text',
          prompt_instance_id: uuidv4(),
          prompt_index: 1,
          prompt_value: item.text,
          response_array: [
            {
              response_value: keyword,
              response_id: uuidv4()
            }
          ]
        }
      ]
    });

    searchSubmission({
      search_template_id: searchTemplateId,
      search_instance_id: searchInstanceId,
      step_submissions: [
        {
          step_id: stepId,
          step_instance_id: stepInstanceId,
          step_index: 1
        }
      ]
    });
  };

  const handleSearchSubmit = (maybeKeyword?: string) => {
    const searchPageUrl = buildSearchUrl({
      keyword: maybeKeyword ?? keyword,
      careType: 'senior-living'
    });

    handleSearchEvents();
    location.assign(searchPageUrl);
  };

  return (
    <Box
      display={itemDisplay(item.visibility)}
      {...rest}
      data-testid="search-bar"
    >
      <form
        style={{ width: '100%' }}
        onSubmit={(e) => {
          e.preventDefault();
          handleSearchSubmit();
        }}
      >
        <InputGroup maxWidth={320} width="100%" {...inputGroupProps}>
          {type === 'search' ? (
            <Input
              name="location"
              value={keyword || ''}
              onChange={(e) => setKeyword(e.target.value)}
              placeholder={item.text}
              aria-label="search-keyword"
            />
          ) : (
            <AutocompleteInput
              aria-label="search-keyword"
              bg="white"
              size="lg"
              defaultValue={keyword || ''}
              placeholder={item.text}
              onSelection={handleSearchSubmit}
              setKeyword={setKeyword}
              name="location"
              listContainerProps={{
                zIndex: 10000
              }}
              fontSize="md"
              padding="0 40px 0 16px"
              height={10}
              borderColor="inherit"
            />
          )}
          {item.icon && (
            <InputRightElement
              color="gray.300"
              cursor="pointer"
              {...inputRightElementProps}
              onClick={() => handleSearchSubmit()}
            >
              <Icon as={STRING_TO_ICON_CLASS[item.icon]} width={6} height={6} />
            </InputRightElement>
          )}
        </InputGroup>
      </form>
    </Box>
  );
};

export default HeaderSearch;
