import { Button } from '@chakra-ui/button';
import { Icon } from '@chakra-ui/icon';
import { Box } from '@chakra-ui/layout';
import { Visibility } from '@components/Navigation/Navigation.types';
import useCookieStorageValue from '@hooks/use-cookie-storage-value';
import { useMemo } from 'react';
import { MdFavorite, MdFavoriteBorder } from 'react-icons/md';

import { SAVED_PROVIDERS_COOKIE_NAME } from '~/constants';

import { itemDisplay } from './Header';

interface SavedProvidersNavButtonProps {
  visibility?: Visibility;
  display?: string;
  buttonText?: string;
  wrapperClassName?: string;
}

const SavedProvidersNavButton: React.FC<SavedProvidersNavButtonProps> = ({
  visibility = 'desktop',
  display = 'flex',
  buttonText = 'Saved',
  wrapperClassName
}) => {
  const savedProviders = useCookieStorageValue(SAVED_PROVIDERS_COOKIE_NAME);

  const hasSavedProviders = useMemo(() => {
    if (!savedProviders) {
      return false;
    }

    try {
      const savedProvidersList = JSON.parse(savedProviders);
      return Array.isArray(savedProvidersList) && savedProvidersList.length > 0;
    } catch (e) {
      console.error('Error parsing saved providers', e);
      return false;
    }
  }, [savedProviders]);

  const handleViewSavedPage = () => {
    window.location.href = '/saved';
  };

  const buttonComponent = (
    <Button
      leftIcon={
        <Box
          position="relative"
          width="27px"
          height="27px"
          borderRadius="full"
          border="1px solid"
          borderColor="gray.300"
          bg="white"
          display="flex"
          alignItems="center"
          justifyContent="center"
          aria-hidden="true"
        >
          <Icon
            as={hasSavedProviders ? MdFavorite : MdFavoriteBorder}
            boxSize="18px"
            color={hasSavedProviders ? 'red.500' : 'gray.500'}
            aria-hidden="true"
          />
        </Box>
      }
      iconSpacing="6px"
      variant="ghost"
      onClick={handleViewSavedPage}
      fontSize="14px"
      size="sm"
      aria-label="Navigate to Saved providers Page"
      display={itemDisplay(visibility, display)}
      px="0"
      _hover={{
        bg: 'transparent',
        color: 'inherit',
        textDecoration: 'underline'
      }}
      tabIndex={0}
      data-testid="saved-provider-nav-button"
      zIndex={-1}
    >
      <Box fontWeight={'normal'} color={'gray.700'}>
        {buttonText}
      </Box>
    </Button>
  );

  return <div className={wrapperClassName}>{buttonComponent}</div>;
};

export default SavedProvidersNavButton;
