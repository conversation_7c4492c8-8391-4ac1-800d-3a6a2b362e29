import HeaderProviderScrolled from '@components/Navigation/Header/HeaderProviderScrolled';
import { render, screen, setDesktopScreen } from '@utils/test-utils';

import { HeaderNavigation } from '../Navigation.types';
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    push: jest.fn()
  }))
}));

describe('HeaderProviderScrolled', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
    window.tracking.ready = jest.fn(() => Promise.resolve());
  });

  const mockLogo = {
    logo: { link: '/path/to/logo', caption: 'caring.com' },
    logoUrl: 'https://www.caring.com',
    mobileLogo: { link: '/path/to/mobileLogo', caption: 'caring.com' },
    mobileLogoMaxWidth: '100',
    logoMaxWidth: '200',
    logoAlt: 'Logo Alt Text'
  };

  const mockMainItems = [
    {
      text: 'Main Items',
      type: 'whoAreYouSearchingFor',
      mobile: true,
      desktop: true,
      link: { enabled: false, url: '' },
      inquiryForm: { enabled: false },
      children: []
    }
  ] as unknown as HeaderNavigation['main'];

  const mockSecondaryItems = [
    {
      id: 'a84089ab-af6a-4f2e-ab30-6b63cac12c43',
      type: 'whoAreYouSearchingFor',
      position: 'secondary',
      variant: 'outline',
      visibility: 'always',
      mobile: true,
      desktop: true,
      text: 'Who are you searching for?',
      textColor: 'secondary',
      textColorRange: '500',
      secondText: '',
      secondTextColor: 'secondary',
      secondTextColorRange: '200',
      icon: '',
      link: {
        enabled: false,
        url: ''
      },
      inquiryForm: {
        enabled: true,
        ctaAction: 'request-info',
        ctaText: 'Get costs',
        rollUpType: 'senior-living',
        title: 'How can we help?',
        thankYouMessage: '',
        thankYouMessageColor: '',
        legalDisclosure: '<p>This is a disclosure</p>\n'
      },
      children: []
    }
  ] as unknown as HeaderNavigation['secondary'];

  const mockMetadata = {
    '@id': '123456',
    '@name': '',
    '@path': '',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:template': '',
    'mgnl:created': '',
    '@nodes': []
  };

  it('renders the component correctly', () => {
    setDesktopScreen();
    render(
      <HeaderProviderScrolled
        logo={mockLogo}
        main={mockMainItems}
        secondary={mockSecondaryItems}
        metadata={mockMetadata}
      />
    );

    const logoElement = screen.getByTestId('desktopLogo');
    const mainItemsElement = screen.getByText('Main Items');
    const secondaryItems = screen.getByText('Who are you searching for?');

    expect(logoElement).toBeInTheDocument();
    expect(mainItemsElement).toBeInTheDocument();
    expect(secondaryItems).toBeInTheDocument();
  });

  it('renders the component correctly on mobile', () => {
    render(
      <HeaderProviderScrolled
        logo={mockLogo}
        main={mockMainItems}
        secondary={mockSecondaryItems}
        metadata={mockMetadata}
      />
    );

    const mobileLogoElement = screen.queryByTestId('mobileLogo');
    const desktopLogoElement = screen.queryByTestId('desktopLogo');
    const mainItems = screen.getByText('Main Items');
    const secondaryItems = screen.getByText('Who are you searching for?');

    expect(mobileLogoElement).toBeInTheDocument();
    expect(desktopLogoElement).not.toBeVisible();
    expect(mainItems).toBeInTheDocument();
    expect(secondaryItems).toBeInTheDocument();
  });
});
