import { memo } from 'react';
import { MdChevronRight } from 'react-icons/md';

import { NavigationItem } from './NavigationDrawerContent';
import styles from './SubNavigation.module.css';

const SubNavigation: React.FC<{
  navigationContent: NavigationItem;
  currentPage: string;
  onNavigate: (page: string) => void;
}> = memo(({ navigationContent, currentPage, onNavigate }) => {
  const mainLinkIsLink =
    navigationContent.mainLink.href && navigationContent.mainLink.href !== '';
  const isHidden =
    currentPage === navigationContent.mainLink.text ? '' : styles.hidden;
  return (
    <ul
      key={`${navigationContent.mainLink.text}-sublinks`}
      className={[styles.subNav, isHidden].join(' ').trimEnd()}
    >
      <li
        key={`${navigationContent.mainLink.text}`}
        className={styles.listItem}
      >
        {mainLinkIsLink ? (
          <a
            className={styles.mainLink}
            href={navigationContent.mainLink.href}
            onClick={() => onNavigate(navigationContent.mainLink.text)}
          >
            {navigationContent.mainLink.subText} <MdChevronRight size="24px" />
          </a>
        ) : (
          <span className={styles.text}>{navigationContent.mainLink.text}</span>
        )}
      </li>
      {navigationContent.subLinks.map((item, index) => {
        const isFeatured = item.type === 'featured';
        const isViewAll = item.type === 'viewAllButton';
        let subLinkStyle;
        if (isFeatured) {
          subLinkStyle = styles.featured;
        } else if (isViewAll) {
          subLinkStyle = styles.viewAll;
        } else if (item.noBorder) {
          subLinkStyle = styles.listItem;
        } else {
          subLinkStyle = styles.listItemBorder;
        }

        return (
          <li key={index} className={subLinkStyle}>
            {item.title && <span className={styles.title}>{item.title}</span>}
            <ul className={styles.fontSizeSm}>
              {item.links.map((link, linkIndex) => {
                const isButton = link?.type === 'button';
                return isButton ? (
                  <li
                    key={`${linkIndex}-${link}-link`}
                    className={styles.subLink}
                  >
                    <a
                      className={styles.buttonPrimary}
                      href={link.href}
                      rel="noopener noreferrer"
                    >
                      {link.text}
                    </a>
                  </li>
                ) : (
                  <li
                    key={`${linkIndex}-${link}-button`}
                    className={styles.listItemHover}
                  >
                    <a href={link.href} className={styles.link}>
                      {link.text}
                    </a>
                  </li>
                );
              })}
            </ul>
          </li>
        );
      })}
    </ul>
  );
});

SubNavigation.displayName = 'SubNavigation';

export default SubNavigation;
