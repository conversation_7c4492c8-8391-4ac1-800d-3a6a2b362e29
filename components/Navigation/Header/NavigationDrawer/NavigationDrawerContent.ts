export interface NavigationLink {
  text: string;
  href: string;
  type?: string;
}

export interface SubLink {
  title?: string;
  type?: string;
  noBorder?: boolean;
  links: NavigationLink[];
}

export interface NavigationItem {
  mainLink: {
    text: string;
    subText?: string;
    href?: string;
  };
  subLinks: SubLink[];
}

export const navigationContent: NavigationItem[] = [
  {
    mainLink: {
      text: 'Assisted Living',
      subText: 'View all Assisted Living',
      href: '/senior-living/assisted-living'
    },
    subLinks: [
      {
        type: 'featured',
        links: [
          {
            text: 'Assisted Living Resources',
            href: '/resources/assisted-living'
          },
          {
            text: 'How to Choose an Assisted Living Facility',
            href: '/resources/choosing-an-assisted-living-facility'
          },
          {
            text: 'How to Pay for Assisted Living',
            href: '/resources/how-to-pay-for-assisted-living'
          }
        ]
      },
      {
        title: 'Explore Assisted Living Near You',
        links: [
          {
            text: 'Houston, TX',
            href: '/senior-living/assisted-living/texas/houston'
          },
          {
            text: 'New York, NY',
            href: '/senior-living/assisted-living/new-york/new-york'
          },
          {
            text: 'Phoenix, AZ',
            href: '/senior-living/assisted-living/arizona/phoenix'
          },
          {
            text: 'Orlando, FL',
            href: '/senior-living/assisted-living/florida/orlando'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-living/assisted-living/illinois/chicago'
          },
          {
            text: 'Los Angeles, CA',
            href: '/senior-living/assisted-living/california/los-angeles'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All Assisted Living Facilities',
            href: '/senior-living/assisted-living',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Memory Care',
      subText: 'View all Memory Care',
      href: '/senior-living/memory-care-facilities'
    },
    subLinks: [
      {
        type: 'featured',
        links: [
          {
            text: 'Memory Care Resources',
            href: '/resources/memory-care'
          },
          {
            text: 'How to Pay for Memory Care Facilities',
            href: '/resources/how-to-pay-for-memory-care-facilities'
          },
          {
            text: '7 Stages of Dementia: A Guide for Caregivers',
            href: '/resources/7-stages-of-dementia'
          }
        ]
      },
      {
        title: 'Explore Memory Care Near You',
        links: [
          {
            text: 'Houston, TX',
            href: '/senior-living/memory-care-facilities/texas/houston'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-living/memory-care-facilities/illinois/chicago'
          },
          {
            text: 'Los Angeles, CA',
            href: '/senior-living/memory-care-facilities/california/los-angeles'
          },
          {
            text: 'New York, NY',
            href: '/senior-living/memory-care-facilities/new-york/new-york'
          },
          {
            text: 'Atlanta, GA',
            href: '/senior-living/memory-care-facilities/georgia/atlanta'
          },
          {
            text: 'Miami, FL',
            href: '/senior-living/assisted-living/florida/miami'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All Memory Care Facilities',
            href: '/senior-living/memory-care-facilities',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Independent Living',
      subText: 'View all Independent Living',
      href: '/senior-living/independent-living'
    },
    subLinks: [
      {
        type: 'featured',
        links: [
          {
            text: 'Independent Living Resources',
            href: '/resources/independent-living'
          },
          {
            text: 'How to Pay for Independent Living',
            href: '/resources/how-to-pay-for-independent-living'
          },
          {
            text: 'How to Choose an Independent Living Facility',
            href: '/resources/choosing-an-independent-living-community'
          }
        ]
      },
      {
        title: 'Explore Independent Living Near You',
        links: [
          {
            text: 'New York, NY',
            href: '/senior-living/independent-living/new-york/new-york'
          },
          {
            text: 'San Antonio, TX',
            href: '/senior-living/independent-living/texas/san-antonio'
          },
          {
            text: 'Portland, OR',
            href: '/senior-living/independent-living/oregon/portland'
          },
          {
            text: 'Pennsylvania, PA',
            href: '/senior-living/independent-living/pennsylvania/philadelphia'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-living/independent-living/illinois/chicago'
          },
          {
            text: 'San Jose, CA',
            href: '/senior-living/independent-living/california/san-jose'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All Independent Living Facilities',
            href: '/senior-living/independent-living',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Nursing Homes',
      subText: 'View all Nursing Homes',
      href: '/senior-living/nursing-homes'
    },
    subLinks: [
      {
        title: 'Resources',
        type: 'featured',
        links: [
          {
            text: 'How to Pay for Nursing Home Care',
            href: '/resources/how-to-pay-for-nursing-homes'
          },
          {
            text: 'A Guide to Nursing Home Requirements',
            href: '/resources/guide-to-nursing-home-requirements-for-seniors'
          }
        ]
      },
      {
        title: 'Explore Nursing Homes Near You',
        links: [
          {
            text: 'New York, NY',
            href: '/senior-living/nursing-homes/new-york/new-york'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-living/nursing-homes/illinois/chicago'
          },
          {
            text: 'Houston, TX',
            href: '/senior-living/nursing-homes/texas/houston'
          },
          {
            text: 'Los Angeles, CA',
            href: '/senior-living/nursing-homes/california/los-angeles'
          },
          {
            text: 'Philadelphia, PA',
            href: '/senior-living/nursing-homes/pennsylvania/philadelphia'
          },
          {
            text: 'Phoenix, AZ',
            href: '/senior-living/nursing-homes/arizona/phoenix'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All Nursing Homes',
            href: '/senior-living/nursing-homes',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'In-Home Care',
      subText: 'View all In-Home Care',
      href: '/senior-care/in-home-care'
    },
    subLinks: [
      {
        type: 'featured',
        links: [
          {
            text: 'Home Care Resources',
            href: '/resources/home-care'
          },
          {
            text: 'How to Pay for In-Home Care',
            href: '/senior-care/in-home-care/how-to-pay'
          },
          {
            text: 'A Guide to Around the Clock Care',
            href: '/resources/guide-to-24-hour-in-home-care'
          }
        ]
      },
      {
        title: 'Explore In-Home Care Near You',
        links: [
          {
            text: 'Houston, TX',
            href: '/senior-care/in-home-care/texas/houston'
          },
          {
            text: 'New York, NY',
            href: '/senior-care/in-home-care/new-york/new-york'
          },
          {
            text: 'Philadelphia, PA',
            href: '/senior-care/in-home-care/pennsylvania/philadelphia'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-care/in-home-care/illinois/chicago'
          },
          {
            text: 'Los Angeles, CA',
            href: '/senior-care/in-home-care/california/los-angeles'
          },
          {
            text: 'Miami, FL',
            href: '/senior-care/in-home-care/florida/miami'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All In-Home Care Options',
            href: '/senior-care/in-home-care',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Home Health Care Agencies',
      subText: 'View all Home Health Agencies',
      href: '/senior-care/home-health-agencies'
    },
    subLinks: [
      {
        title: 'Explore Home Health Care Agencies Near You',
        links: [
          {
            text: 'New York, NY',
            href: '/senior-care/home-health-agencies/new-york/new-york'
          },
          {
            text: 'Chicago, IL',
            href: '/senior-care/home-health-agencies/illinois/chicago'
          },
          {
            text: 'Atlanta, GA',
            href: '/senior-care/home-health-agencies/georgia/atlanta'
          },
          {
            text: 'Houston, TX',
            href: '/senior-care/home-health-agencies/texas/houston'
          },
          {
            text: 'Philadelphia, PA',
            href: '/senior-care/home-health-agencies/pennsylvania/philadelphia'
          },
          {
            text: 'Los Angeles, CA',
            href: '/senior-care/home-health-agencies/california/los-angeles'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View All Home Health Agencies',
            href: '/senior-care/home-health-agencies',
            type: 'button'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Other Senior Living',
      subText: 'Other Senior Living'
    },
    subLinks: [
      {
        noBorder: true,
        links: [
          {
            text: 'Senior Living Resources',
            href: '/resources/senior-living'
          },
          {
            text: '55+ Communities',
            href: '/senior-living/55-plus-communities'
          },
          {
            text: 'Adult Day Care',
            href: '/senior-living/adult-day-care'
          },
          {
            text: 'Continuing Care Retirement Communities',
            href: '/senior-living/continuing-care-retirement-communities'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Other Senior Care',
      subText: 'Other Senior Care'
    },
    subLinks: [
      {
        noBorder: true,
        links: [
          {
            text: 'Senior Care Resources',
            href: '/resources/senior-care'
          },
          {
            text: 'Geriatric Care Managers',
            href: '/senior-care/geriatric-care-managers'
          },
          {
            text: 'Hospice',
            href: '/senior-care/hospices'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'About Caring.com',
      subText: 'About Caring.com',
      href: '/about'
    },
    subLinks: [
      {
        noBorder: true,
        links: [
          {
            text: 'Leadership',
            href: 'https://careers.caring.com/leadership'
          },
          {
            text: 'Meet our Experts',
            href: 'https://careers.caring.com/our-experts'
          },
          {
            text: 'Newsroom',
            href: 'https://careers.caring.com/in-the-news'
          },
          {
            text: 'Careers',
            href: 'https://careers.caring.com/careers'
          },
          {
            text: 'Contact Us',
            href: 'https://careers.caring.com/contact'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Trusted Reviews on Caring.com',
      href: '/about/reviews_faq'
    },
    subLinks: []
  },
  {
    mainLink: {
      text: 'Caring Stars',
      subText: 'Caring Stars',
      href: '/bestseniorliving'
    },
    subLinks: [
      {
        noBorder: true,
        links: [
          {
            text: 'Senior Living Winners',
            href: '/bestseniorliving/caring-stars-senior-living'
          },
          {
            text: 'Senior Care Winners',
            href: '/bestseniorliving/caring-stars-senior-care'
          }
        ]
      }
    ]
  },
  {
    mainLink: {
      text: 'Resources',
      subText: 'View all Resources',
      href: '/resources'
    },
    subLinks: [
      {
        noBorder: true,
        links: [
          {
            text: 'Dementia and Memory Care',
            href: '/resources/memory-care'
          },
          {
            text: 'Assisted Living',
            href: '/resources/assisted-living'
          },
          {
            text: 'Senior Living',
            href: '/resources/senior-living'
          },
          {
            text: 'Senior Care',
            href: '/resources/senior-care'
          },
          {
            text: 'Home Care',
            href: '/resources/home-care'
          },
          {
            text: 'Independent Living',
            href: '/resources/independent-living'
          },
          {
            text: 'Financial and Legal',
            href: '/resources/financial-and-legal'
          },
          {
            text: 'Senior Products and Services',
            href: '/senior-products'
          },
          {
            text: 'Caregiver Support',
            href: '/resources/caregivers'
          },
          {
            text: 'Surveys and Research',
            href: '/resources/surveys-and-research'
          },
          {
            text: 'Health and Wellness',
            href: '/resources/health-and-wellness'
          },
          {
            text: 'Travel and Lifestyle',
            href: '/resources/travel-and-lifestyle'
          }
        ]
      },
      {
        title: 'Featured Resources',
        type: 'featured',
        links: [
          {
            text: 'A Guide to Medicare Benefits',
            href: '/medicare'
          },
          {
            text: "Guide to Alzheimer's Caregiving",
            href: '/resources/alzheimers-caregiving'
          },
          {
            text: 'Selling Your House To Pay For Senior Care',
            href: '/resources/selling-a-house-to-pay-for-senior-care'
          },
          {
            text: 'What is Dementia?',
            href: '/resources/dementia'
          },
          {
            text: 'A Guide to Social Security for Seniors',
            href: '/resources/social-security'
          },
          {
            text: 'What Are Activities of Daily Living?',
            href: '/resources/activities-of-daily-living'
          },
          {
            text: 'Family Caregiver Guide',
            href: '/resources/preparing-for-caregiving'
          },
          {
            text: 'Estate Planning',
            href: '/resources/estate-planning'
          },
          {
            text: 'Finding the Right Caregiver Support and Resources',
            href: '/resources/caregiver-support'
          },
          {
            text: 'Ultimate Guide to Government Aid for Seniors',
            href: '/resources/ultimate-guide-to-government-aid-for-seniors'
          }
        ]
      },
      {
        type: 'viewAllButton',
        links: [
          {
            text: 'View More Resources',
            href: '/resources',
            type: 'button'
          }
        ]
      }
    ]
  }
];
