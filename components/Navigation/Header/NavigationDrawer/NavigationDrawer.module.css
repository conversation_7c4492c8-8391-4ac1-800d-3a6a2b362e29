.drawerMenu {
  cursor: pointer;
}

.drawer {
  position: fixed;
  top: 0;
  width: 320px;
  height: 100%;
  background-color: white;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
.drawer.left {
  left: 0;
}

.drawer.right {
  right: 0;
}
.drawer .content {
  padding: 0 20px;
  text-align: left;
  overflow-y: auto;
}

.drawer ul {
  list-style-type: none;
}

.drawer ul li {
  font-size: 16px;
  font-weight: normal;
}

.drawer .header {
  min-height: 64px;
  display: flex;
  justify-content: space-between;
  padding-right: 16px;
  align-items: center;
}

.drawer .headerMain {
  justify-content: flex-end;
}

.drawer .headerSecondary {
  margin-left: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.drawer .closeButton {
  background: none;
  border: none;
  font-size: 36px;
  cursor: pointer;
}

.drawer .backButton {
  line-height: normal;
  font-size: 16px;
}
.drawer .backButton svg {
  display: inline-block;
  vertical-align: middle;
}
.backButton:hover {
  text-decoration: underline;
}

.backButton span {
  vertical-align: middle;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.hidden {
  display: none;
}

.noScroll {
  overflow: hidden;
  margin-right: 15px;
}

.noScroll header {
  margin-left: -7.5px;
}

.saved-provider-btn-wrapper{
  padding: 12px 0px;
  border-top: 1px solid var(--chakra-colors-gray-200);
  border-bottom: 1px solid var(--chakra-colors-gray-200);
  margin-bottom: 16px;
}
