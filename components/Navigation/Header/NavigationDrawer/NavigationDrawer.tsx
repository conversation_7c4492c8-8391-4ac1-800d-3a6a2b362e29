import HeaderSearch from '@components/Navigation/Header/HeaderSearch';
import MainNavigation from '@components/Navigation/Header/NavigationDrawer/MainNavigation';
import SavedProvidersNavButton from '@components/Navigation/Header/SavedProvidersNavButton';
import { NavigationItem } from '@components/Navigation/Navigation.types';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { MdChevronLeft, MdMenu } from 'react-icons/md';

import SiteContext from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';

import styles from './NavigationDrawer.module.css';

const HeaderSearchProps: NavigationItem = {
  id: '00000000-0000-0000-0000-000000000000',
  type: 'predictive_search',
  position: 'main',
  visibility: 'always',
  mobile: false,
  desktop: false,
  text: 'Enter City, State, or Zip',
  textColor: 'secondary',
  textColorRange: '200',
  secondText: '',
  secondTextColor: 'secondary',
  secondTextColorRange: '200',
  icon: 'MdSearch',
  url: '',
  children: []
};

const NavigationDrawer: React.FC<{ placement?: string }> = ({
  placement = 'right'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('main');
  const handleNavigation = useCallback((page: string) => {
    setCurrentPage(page);
  }, []);
  const onOpen = () => setIsOpen(true);
  const onClose = () => setIsOpen(false);
  const siteContext = useContext(SiteContext);
  const shouldShowSavedProvidersNavButton =
    siteContext?.site?.path === CaringDomains.LIVE || false;

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add(styles.noScroll);
    } else {
      document.body.classList.remove(styles.noScroll);
    }
    return () => {
      document.body.classList.remove(styles.noScroll);
    };
  }, [isOpen, placement]);

  const additionalHeaderStyles =
    currentPage === 'main' ? styles.headerMain : styles.headerSecondary;
  const placementStyles = placement === 'left' ? styles.left : styles.right;
  const drawerOpenStyles = isOpen ? '' : styles.hidden;

  // Wrapping the content in a useMemo to prevent re-rendering in order to improve performance and reduce
  // unnecessary re-renders which increases the INP (Interaction to Next Paint) time.
  const renderContent = useMemo(
    () => (
      <>
        {currentPage === 'main' && (
          <>
            <HeaderSearch
              item={HeaderSearchProps}
              type="predictive_search"
              paddingBottom={6}
              inputRightElementProps={{
                backgroundColor: 'primary.600',
                borderEndRadius: 'md',
                color: 'white'
              }}
            />
            {shouldShowSavedProvidersNavButton && (
              <SavedProvidersNavButton
                visibility="always"
                buttonText="Saved Providers"
                wrapperClassName={styles['saved-provider-btn-wrapper']}
              />
            )}
          </>
        )}
        <MainNavigation
          currentPage={currentPage}
          onNavigate={handleNavigation}
          display={currentPage === 'main' ? 'block' : 'none'}
        />
      </>
    ),
    [currentPage, handleNavigation, shouldShowSavedProvidersNavButton]
  );

  return (
    <>
      <MdMenu
        size="32px"
        onClick={onOpen}
        aria-label="Toggle menu"
        className={styles.drawerMenu}
      />
      <div
        className={[styles.drawer, placementStyles, drawerOpenStyles]
          .join(' ')
          .trimEnd()}
        role="dialog"
      >
        <div className={`${styles.header} ${additionalHeaderStyles}`}>
          {currentPage !== 'main' && (
            <button
              onClick={() => handleNavigation('main')}
              className={styles.backButton}
              aria-label="Back"
            >
              <MdChevronLeft size="24px" />

              <span>Back</span>
            </button>
          )}
          <button
            onClick={onClose}
            className={styles.closeButton}
            aria-label="Close"
          >
            &times;
          </button>
        </div>

        <div className={styles.content}>{renderContent}</div>
      </div>
      {isOpen && (
        <div
          onClick={onClose}
          className={[styles.overlay, drawerOpenStyles].join(' ').trimEnd()}
        />
      )}
    </>
  );
};

export default NavigationDrawer;
