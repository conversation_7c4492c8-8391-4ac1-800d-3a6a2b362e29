.subNav .listItem {
  padding-top: 16px;

}
.subNav .listItemBorder {
  padding-top: 16px;
  border-top: 1px solid #c2c2c2;

}

.listItemHover:hover {
  font-weight: 700;
}

.subNav ul {
  margin-top: 8px;
}

.subNav ul li {
  margin-bottom: 8px;
}

.mainLink {
  display: flex;
  font-size: 16px;
  text-decoration: underline;
  margin-bottom: 8px;
}

.mainLink:hover {
  font-weight: bold;
}

.subLink {
  margin-bottom: 8px;
}

.viewAll {
  margin: 16px 0
}

.link {
  display: flex;
  font-size: 14px;
  text-decoration: underline;
}

.text {
  font-weight: bold;
  padding-top: 16px;
  padding-bottom: 4px;
}

.subNav .featured {
  background-color: #fbf9f5;
  border-radius: 12px;
  padding: 8px 12px 4px;
  margin-bottom: 24px;
}

.title {
  font-weight: bold;
}

.button {
  color: #2a948b;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 8px 16px;
  font-size: 14px;
}

.buttonPrimary {
  color: white;
  background-color: #048079;
  border-radius: 4px;
  padding: 8px 18px;
  font-size: 12px;
  font-weight: bold;
}

.buttonPrimary:hover {
  background-color: #086961;
}

.fontSizeSm {
  font-size: 14px;
  margin-bottom: 8px;
}

.hidden {
  display: none;
}