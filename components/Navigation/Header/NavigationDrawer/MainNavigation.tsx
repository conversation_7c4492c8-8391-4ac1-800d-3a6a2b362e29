import SubNavigation from '@components/Navigation/Header/NavigationDrawer/SubNavigation';
import { memo } from 'react';
import { MdChevronRight } from 'react-icons/md';

import styles from './MainNavigation.module.css';
import { navigationContent } from './NavigationDrawerContent';

interface MainNavigationProps {
  currentPage: string;
  onNavigate: (page: string) => void;
  display: string;
}

const MainNavigation: React.FC<MainNavigationProps> = memo(
  ({ currentPage, onNavigate }) => (
    <ul>
      {navigationContent.map((item, index) => {
        const hasSubLinks = item.subLinks.length > 0;
        const hasMainLink = item.mainLink.text && item.mainLink.text !== '';
        const mainClassName =
          currentPage === 'main' ? { className: styles.mainLinkListItem } : {};
        return (
          <li
            key={`${index}-${item.mainLink.text}-mainLink`}
            {...mainClassName}
          >
            {hasMainLink && hasSubLinks ? (
              <>
                <p
                  onClick={() => onNavigate(item.mainLink.text)}
                  className={
                    currentPage === 'main' ? styles.visible : styles.hidden
                  }
                >
                  {item.mainLink.text}{' '}
                  {hasSubLinks && <MdChevronRight size="24px" />}
                </p>
                <SubNavigation
                  navigationContent={item}
                  currentPage={currentPage}
                  onNavigate={onNavigate}
                />
              </>
            ) : (
              <a
                href={item.mainLink.href}
                className={
                  currentPage === 'main' ? styles.visible : styles.hidden
                }
              >
                {item.mainLink.text}
              </a>
            )}
          </li>
        );
      })}
    </ul>
  )
);

MainNavigation.displayName = 'MainNavigation';

export default MainNavigation;
