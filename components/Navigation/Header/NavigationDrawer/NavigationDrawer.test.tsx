import { fireEvent, render, screen, waitFor } from '@utils/test-utils';
import React from 'react';

import SiteContext, { sitesConfig } from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';

import NavigationDrawer from './NavigationDrawer';

jest.setTimeout(10000);

describe('NavigationDrawer', () => {
  const caringSiteConfig =
    sitesConfig.find((site) => site.path === CaringDomains.LIVE) ?? null;
  it('renders the toggle button', () => {
    render(<NavigationDrawer />);
    const toggleButton = screen.getByLabelText('Toggle menu');
    expect(toggleButton).toBeInTheDocument();
  });

  it('opens the drawer when the toggle button is clicked', async () => {
    render(<NavigationDrawer />);
    const toggleButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(toggleButton);
    await waitFor(() => {
      const drawer = screen.getByRole('dialog');
      expect(drawer).not.toHaveClass('hidden');
    });
  });

  it('closes the drawer when the close button is clicked', async () => {
    render(<NavigationDrawer />);
    const toggleButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(toggleButton);
    await waitFor(() => {
      const drawer = screen.getByRole('dialog');
      expect(drawer).not.toHaveClass('hidden');
    });
    const closeButton = screen.getByLabelText('Close');
    fireEvent.click(closeButton);
    await waitFor(() => {
      const drawer = screen.getByRole('dialog');
      expect(drawer).toHaveClass('hidden');
    });
  });

  it('does not have an animation', async () => {
    render(<NavigationDrawer />);
    const toggleButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(toggleButton);
    await waitFor(() => {
      const drawerContent = screen.getByRole('dialog');
      expect(drawerContent).not.toHaveAttribute('transform');
      expect(drawerContent).not.toHaveAttribute('transition');
    });
  });

  it('renders SavedProvidersNavButton only when site is Caring.com', async () => {
    render(
      <SiteContext.Provider value={{ site: caringSiteConfig }}>
        <NavigationDrawer />
      </SiteContext.Provider>
    );
    const toggleButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(toggleButton);
    await waitFor(() => {
      expect(
        screen.getByTestId('saved-provider-nav-button')
      ).toBeInTheDocument();
    });
  });

  it('does not render SavedProvidersNavButton when site is not Caring.com', async () => {
    const seniorHomesSiteConfig =
      sitesConfig.find((site) => site.name === 'Senior Homes') ?? null;
    render(
      <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
        <NavigationDrawer />
      </SiteContext.Provider>
    );
    const toggleButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(toggleButton);
    await waitFor(() => {
      expect(
        screen.queryByTestId('saved-provider-nav-button')
      ).not.toBeInTheDocument();
    });
  });
});
