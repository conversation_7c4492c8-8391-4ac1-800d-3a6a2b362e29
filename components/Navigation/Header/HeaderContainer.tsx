'use client';
import { PageContextType } from '@components/LayoutStructure/Contexts.types';
import {
  getCustomHeadings,
  getPageHeadings
} from '@components/LayoutStructure/helpers';
import Header from '@components/Navigation/Header/Header';
import HeaderGeoScrolled from '@components/Navigation/Header/HeaderGeoScrolled';
import HeaderNonScrolled from '@components/Navigation/Header/HeaderNonScrolled';
import HeaderProviderScrolled from '@components/Navigation/Header/HeaderProviderScrolled';
import { HeaderNavigation } from '@components/Navigation/Navigation.types';
import useTranslation from '@hooks/use-translation';

import { SiteDefinition } from '~/contexts/SiteContext';
import { PageProps, Story } from '~/types/Magnolia';
import { getPageType } from '~/utils/pageType';

export interface HeaderContainerProps {
  mainHeader: HeaderNavigation | null;
  headerProviderScrolledPage?: HeaderNavigation | null;
  headerGeoScrolledPage?: HeaderNavigation | null;
  headerSem?: HeaderNavigation | null;
  isAccountEnabled?: boolean;
  page?: PageProps['page'];
  story?: Story | null;
  provider?: PageProps['provider'] | null;
  site: SiteDefinition;
}

const HeaderContainer = ({
  mainHeader,
  headerProviderScrolledPage = null,
  headerGeoScrolledPage = null,
  headerSem = null,
  isAccountEnabled = false,
  page,
  story = null,
  provider = null,
  site
}: HeaderContainerProps) => {
  const { t } = useTranslation();
  const pageType = page?.pageType;
  const { isProviderPage, isSemPage, isGeoPage } = getPageType(pageType);

  const header: HeaderNavigation | null = isSemPage ? headerSem : mainHeader;
  let scrolledHeader: HeaderNavigation | null = null;

  if (isProviderPage && headerProviderScrolledPage) {
    scrolledHeader = headerProviderScrolledPage;
  } else if (isGeoPage && headerGeoScrolledPage) {
    scrolledHeader = headerGeoScrolledPage;
  }

  if (!header) {
    return null;
  }

  const geoPageHeadings = getPageHeadings(
    { page } as PageContextType,
    story,
    provider,
    2,
    site.path,
    t
  );

  const providerPageHeadings = getCustomHeadings(
    { page } as PageContextType,
    story,
    provider,
    2,
    site.path,
    t
  );

  return (
    <Header
      isAccountEnabled={isAccountEnabled}
      sameBackgroundAsBody={page?.backgroundColor?.headerWithSameColor}
      logo={header.logo}
      regularHeaderComponent={
        <HeaderNonScrolled
          logo={header.logo}
          main={header.main}
          secondary={header.secondary}
          metadata={header.metadata}
          mainMenuPosition={header.mainMenuPosition}
          isAccountEnabled={isSemPage ? false : isAccountEnabled}
          providerPageHeadings={providerPageHeadings}
          geoPageHeadings={geoPageHeadings}
          useAlternateDrawerMenu={header.useAlternateDrawerMenu || false}
          isSemPage={isSemPage}
        />
      }
      scrolledHeaderComponent={
        (isGeoPage && scrolledHeader && (
          <HeaderGeoScrolled
            logo={scrolledHeader.logo}
            main={scrolledHeader.main}
            secondary={scrolledHeader.secondary}
            metadata={scrolledHeader.metadata}
            geoPageHeadings={geoPageHeadings}
          />
        )) ||
        (isProviderPage && scrolledHeader && (
          <HeaderProviderScrolled
            logo={scrolledHeader.logo}
            main={scrolledHeader.main}
            secondary={scrolledHeader.secondary}
            metadata={scrolledHeader.metadata}
            providerPageHeadings={providerPageHeadings}
          />
        ))
      }
      showShadow={header.showShadow}
      scrolledShowShadow={scrolledHeader?.showShadow}
      isProviderPage={isProviderPage}
      isGeoPage={isGeoPage}
      isSemPage={isSemPage}
      page={page}
      providerPageHeadings={providerPageHeadings}
      geoPageHeadings={geoPageHeadings}
    />
  );
};

export default HeaderContainer;
