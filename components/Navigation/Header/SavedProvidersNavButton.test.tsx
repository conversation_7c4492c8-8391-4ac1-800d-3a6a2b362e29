import { ChakraProvider } from '@chakra-ui/react';
import useCookieStorageValue from '@hooks/use-cookie-storage-value';
import { fireEvent, render, screen } from '@testing-library/react';

import SavedProvidersNavButton from './SavedProvidersNavButton';

jest.mock('@hooks/use-cookie-storage-value', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('SavedProvidersNavButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default props', () => {
    render(
      <ChakraProvider>
        <SavedProvidersNavButton />
      </ChakraProvider>
    );

    const button = screen.getByTestId('saved-provider-nav-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Saved');
  });

  it('renders with custom button text', () => {
    render(
      <ChakraProvider>
        <SavedProvidersNavButton buttonText="My Favorites" />
      </ChakraProvider>
    );

    const button = screen.getByTestId('saved-provider-nav-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('My Favorites');
  });

  it('opens /saved in same tab when default onClick is used', () => {
    const hrefSetter = jest.fn();
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: {
        set href(value) {
          hrefSetter(value);
        }
      }
    });

    render(
      <ChakraProvider>
        <SavedProvidersNavButton />
      </ChakraProvider>
    );

    const button = screen.getByTestId('saved-provider-nav-button');
    fireEvent.click(button);

    expect(hrefSetter).toHaveBeenCalledWith('/saved');
  });

  it('handles JSON parse error gracefully', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue('invalid-json');
    console.error = jest.fn();

    render(
      <ChakraProvider>
        <SavedProvidersNavButton />
      </ChakraProvider>
    );
    expect(console.error).toHaveBeenCalledWith(
      'Error parsing saved providers',
      expect.any(Error)
    );
  });

  it('renders with no saved providers', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(null);

    render(
      <ChakraProvider>
        <SavedProvidersNavButton />
      </ChakraProvider>
    );

    const button = screen.getByTestId('saved-provider-nav-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Saved');
  });
});
