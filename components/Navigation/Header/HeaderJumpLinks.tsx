import { useDisclosure } from '@chakra-ui/hooks';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { Box, Link, Text } from '@chakra-ui/layout';
import { Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/menu';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import {
  Visibility,
  VISIBILITY_TYPES
} from '@components/Navigation/Navigation.types';
import useProviderFallback from '@hooks/use-provider-fallback';
import { getColor } from '@utils/getColor';
import { createID } from '@utils/strings';

import { itemDisplay } from './Header';

interface Props {
  title: string;
  visibility?: Visibility;
  titleColor?: {
    color: string;
    range: string;
  };
  providerPageHeadings?: { title: string; linkText: string }[];
  geoPageHeadings?: { title: string; linkText: string }[];
}

const HeaderJumpLinks = ({
  title,
  visibility = VISIBILITY_TYPES['ALWAYS'],
  titleColor = { color: 'primary', range: '900' },
  providerPageHeadings,
  geoPageHeadings
}: Props) => {
  const { provider, status } = useProviderFallback();
  const elementClicked = useElementClicked();
  const color = getColor(titleColor.color, titleColor.range);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleLinkClick = (linkText: string, title: string) => {
    elementClicked({
      element: {
        type: ElementTypes.LINK,
        action: ElementActions.JUMP_LINK,
        name: ElementNames.MENU_TABLE_OF_CONTENTS_BUTTON,
        text: linkText,
        color: 'white',
        textColor: color
      },
      destinationUrl: `#${createID(title)}`
    });
  };

  return (
    <Box
      minWidth={{ base: 'auto', md: '186px' }}
      width={{ base: '100%', lg: '242px' }}
      display={itemDisplay(visibility)}
    >
      <Menu isOpen={isOpen} onClose={onClose}>
        <MenuButton
          onClick={onOpen}
          onMouseEnter={onOpen}
          onMouseLeave={onClose}
          shadow="md"
          borderRadius={isOpen ? '12px 12px 0 0' : 'xl'}
          fontWeight="bold"
          width="100%"
          mr={{ base: 0, xl: 'auto' }}
          py={2}
          px={4}
        >
          <Box
            as="span"
            fontSize="sm"
            color={color}
            display="flex"
            whiteSpace="nowrap"
            justifyContent="space-between"
            alignItems="center"
          >
            <span>{title}</span>
            {isOpen ? (
              <ChevronUpIcon width="24px" height="24px" aria-hidden={true} />
            ) : (
              <ChevronDownIcon width="24px" height="24px" aria-hidden={true} />
            )}
          </Box>
        </MenuButton>
        <MenuList
          onMouseEnter={onOpen}
          onMouseLeave={onClose}
          marginTop="-8px"
          border="none"
          borderRadius="0 0 12px 12px"
          shadow="md"
          rootProps={{ marginTop: '-8px' }}
          width={{ base: '100%', lg: '242px' }}
        >
          {!!provider &&
            providerPageHeadings?.map(({ title, linkText }, index) => (
              <MenuItem key={`${title}-${index}`} py={0}>
                <Link
                  onClick={() => handleLinkClick(linkText, title)}
                  href={`#${createID(title)}`}
                  py={2}
                  fontWeight="normal"
                  width="100%"
                >
                  <Text as="span" fontSize="sm" color={color}>
                    {linkText}
                  </Text>
                </Link>
              </MenuItem>
            ))}
          {!provider &&
            geoPageHeadings?.map(({ title, linkText }, index) => (
              <MenuItem key={`${title}-${index}`} py={0}>
                <Link
                  onClick={() => handleLinkClick(title, linkText)}
                  href={`#${createID(linkText ? linkText : title)}`}
                  py={2}
                  fontWeight="normal"
                  width="100%"
                >
                  <Text as="span" fontSize="sm" color={color}>
                    {linkText ? linkText : title}
                  </Text>
                </Link>
              </MenuItem>
            ))}
        </MenuList>
      </Menu>
    </Box>
  );
};

export default HeaderJumpLinks;
