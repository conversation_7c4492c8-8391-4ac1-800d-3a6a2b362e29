'use client';

import { Box } from '@chakra-ui/layout';
import Container from '@components/LayoutStructure/Container';
import {
  HeaderNavigation,
  NavigationItem,
  Visibility
} from '@components/Navigation';
import HeaderSearch from '@components/Navigation/Header/HeaderSearch';
import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';
import { useContext, useMemo, useState } from 'react';

import SiteContext from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';
import { PageProps } from '~/types/Magnolia';

import HeaderLogo from './HeaderLogo';
import SavedProvidersNavButton from './SavedProvidersNavButton';

export interface HeaderProps {
  logo: HeaderNavigation['logo'];
  main: HeaderNavigation['main'];
  secondary: HeaderNavigation['secondary'];
  metadata: HeaderNavigation['metadata'];
  showShadow?: boolean;
  mainMenuPosition?: string;
  providerPageHeadings?: { title: string; linkText: string }[];
  geoPageHeadings?: { title: string; linkText: string }[];
}

interface HeaderComponentProps {
  isAccountEnabled?: boolean;
  sameBackgroundAsBody?: boolean;
  isScrolledHeader?: boolean;
  isProviderPage?: boolean;
  isGeoPage?: boolean;
  isSemPage?: boolean;
  page?: PageProps['page'];
  providerPageHeadings: { title: string; linkText: string }[];
  geoPageHeadings: { title: string; linkText: string }[];
  regularHeaderComponent?: React.ReactNode;
  scrolledHeaderComponent?: React.ReactNode;
  showShadow?: boolean;
  scrolledShowShadow?: boolean;
  logo?: HeaderNavigation['logo'];
}

export const itemDisplay = (
  visibility: Visibility | null,
  display?: string
): { [key: string]: string } => {
  const displayValue = display ?? 'block';
  switch (visibility) {
    case 'mobile':
      return {
        base: displayValue,
        md: 'none'
      };
    case 'desktop':
      return {
        base: 'none',
        lg: displayValue
      };
    case 'tablet':
      return {
        base: 'none',
        md: displayValue,
        lg: 'none'
      };
    case 'tabletPlus':
      return {
        base: 'none',
        md: displayValue
      };
    case 'tabletMinus':
      return {
        base: displayValue,
        lg: 'none'
      };
    default:
      return {
        base: displayValue,
        lg: displayValue
      };
  }
};

const HeaderSearchProps: NavigationItem = {
  id: '00000000-0000-0000-0000-000000000000',
  type: 'predictive_search',
  position: 'main',
  visibility: 'always',
  mobile: false,
  desktop: false,
  text: 'Search for a location',
  textColor: 'secondary',
  textColorRange: '200',
  secondText: '',
  secondTextColor: 'secondary',
  secondTextColorRange: '200',
  icon: 'MdSearch',
  url: '',
  children: []
};

const Header: React.FC<HeaderComponentProps> = ({
  regularHeaderComponent,
  scrolledHeaderComponent,
  sameBackgroundAsBody = false,
  isProviderPage = false,
  isGeoPage = false,
  isSemPage = false,
  logo
}) => {
  const siteContext = useContext(SiteContext);
  const [isScrolled, setIsScrolled] = useState(false);

  const shouldShowSavedProvidersNavButton = useMemo(
    () => siteContext?.site?.path === CaringDomains.LIVE || false,
    [siteContext?.site?.path]
  );

  const isCaringWebSite = useMemo(
    () => siteContext?.site?.path === CaringDomains.LIVE || false,
    [siteContext?.site?.path]
  );
  const fixed = !isSemPage;

  useIsomorphicLayoutEffect(() => {
    setIsScrolled(window.scrollY > 0);
    const handleScroll = () => setIsScrolled(window.scrollY > 0);

    if (isProviderPage || isGeoPage) {
      document.addEventListener('scroll', handleScroll);
    }

    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [isProviderPage, isGeoPage]);

  return (
    <Container
      ignoreMaxWidth
      as="header"
      display="flex"
      flexDirection="column"
      width="100%"
      backgroundColor={sameBackgroundAsBody ? 'transparent' : 'white'}
      height="auto"
      position={fixed ? 'fixed' : undefined}
      left="0"
      right="0"
      top="0"
      paddingTop={isSemPage ? '16px' : '0'}
    >
      {isCaringWebSite && !isSemPage && (
        <Box
          display={{ base: 'none', xl: 'flex' }}
          alignItems="center"
          maxW="1280px"
          m="0 auto"
          width="100%"
          justifyContent="space-between"
          paddingX={{ base: 0, xl: '32px' }}
          paddingBottom={{ base: 0, xl: '8px' }}
          paddingTop={{ base: 0, xl: '16px' }}
        >
          {logo && <HeaderLogo {...logo} />}
          <HeaderSearch
            item={HeaderSearchProps}
            type="predictive_search"
            width="100%"
            inputGroupProps={{
              maxWidth: '571px',
              margin: '0 auto'
            }}
            inputRightElementProps={{
              backgroundColor: 'primary.600',
              borderEndRadius: 'md',
              color: 'white'
            }}
            zIndex="1"
          />
          {shouldShowSavedProvidersNavButton && (
            <SavedProvidersNavButton visibility="always" />
          )}
        </Box>
      )}

      <Box
        paddingY={isSemPage ? 0 : { base: 2, xl: 4 }}
        backgroundColor={isSemPage ? 'transparent' : 'white'}
        boxShadow={isSemPage ? 'none' : '0px 2px 4px rgba(0, 0, 0, 0.1)'}
      >
        {!isSemPage && isScrolled && scrolledHeaderComponent
          ? scrolledHeaderComponent
          : regularHeaderComponent}
      </Box>
    </Container>
  );
};

export default Header;
