import { Box, Flex, HStack } from '@chakra-ui/layout';
import LAYOUT from '@components/Layouts/layoutConstants';

import { HeaderProps } from './Header';
import HeaderLogo from './HeaderLogo';
import HeaderMainItems from './HeaderMainItems';
import SecondaryMenuItem from './SecondaryMenuItem';

const HeaderProviderScrolled: React.FC<HeaderProps> = ({
  logo,
  main,
  secondary,
  metadata,
  providerPageHeadings
}) => {
  return (
    <Box
      maxW={LAYOUT.CONTAINER_MAX_WIDTH}
      px={LAYOUT.CONTAINER_HORIZONTAL_PADDING}
      width="100%"
      m="auto"
      display="flex"
      alignItems="center"
      gap={6}
      zIndex="sticky"
    >
      <Flex flexWrap={{ base: 'wrap', md: 'nowrap' }} width="100%" rowGap={3}>
        <Flex
          justifyContent="flex-start"
          width="100%"
          gridTemplateColumns="repeat(2, 1fr)"
          gap={3}
          alignItems="center"
          fontWeight="bold"
          fontSize="14px"
        >
          <Box display={{ base: 'block', xl: 'none' }} minWidth="124px">
            <HeaderLogo {...logo} />
          </Box>
          <HeaderMainItems
            main={main}
            metadata={metadata}
            providerPageHeadings={providerPageHeadings}
          />
        </Flex>
        <Flex
          justifyContent={{ base: 'center', sm: 'flex-end' }}
          width="100%"
          gridTemplateColumns="repeat(2, 1fr)"
          gap={3}
          alignItems="center"
          fontWeight="bold"
          fontSize="14px"
        >
          <HStack
            display="flex"
            width={{ base: '100%', md: 'auto' }}
            gridTemplateColumns="repeat(2, 1fr)"
            justifyContent="end"
            alignItems="center"
            fontWeight="bold"
            fontSize="14px"
          >
            {secondary.map((item, i) => (
              <SecondaryMenuItem
                key={i}
                item={item}
                metadata={metadata}
                providerPageHeadings={providerPageHeadings}
              />
            ))}
          </HStack>
        </Flex>
      </Flex>
    </Box>
  );
};

export default HeaderProviderScrolled;
