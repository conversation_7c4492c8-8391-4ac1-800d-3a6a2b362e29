import { HeaderNavigation } from '@components/Navigation';
import { render, screen, waitFor } from '@utils/test-utils';
import React from 'react';

import { CARING_SITE } from '~/app/_constants/sites';
import { Story } from '~/types/Magnolia';

import HeaderContainer from './HeaderContainer';

const mainHeader = {
  logo: {},
  main: [
    {
      text: 'Main menu item',
      link: {
        url: '/provider'
      },
      children: []
    }
  ],
  secondary: []
} as unknown as HeaderNavigation;

describe('HeaderContainer', () => {
  it('render the header', async () => {
    const headerProviderScrolledPage = {
      logo: {},
      main: [
        {
          text: 'Scrolled Main menu item',
          link: {
            url: '/provider'
          },
          children: []
        }
      ],
      secondary: []
    } as unknown as HeaderNavigation;

    render(
      <HeaderContainer
        mainHeader={mainHeader}
        headerSem={null}
        headerProviderScrolledPage={headerProviderScrolledPage}
        headerGeoScrolledPage={null}
        isAccountEnabled={false}
        page={{ pageType: 'provider' } as any}
        story={{} as Story}
        provider={null}
        site={CARING_SITE}
      />
    );

    await waitFor(() => {
      expect(screen.queryByText('Main menu item')).toBeInTheDocument();
    });
  });

  it('should render SEM header if in template', async () => {
    const semHeader = {
      logo: {},
      main: [
        {
          text: 'Sem header',
          link: {
            url: '/provider'
          },
          children: []
        }
      ],
      secondary: []
    } as unknown as HeaderNavigation;

    render(
      <HeaderContainer
        mainHeader={semHeader}
        headerProviderScrolledPage={null}
        headerGeoScrolledPage={null}
        headerSem={semHeader}
        isAccountEnabled={false}
        page={{ pageType: 'multi-step-form' } as any}
        story={{} as Story}
        provider={null}
        site={CARING_SITE}
      />
    );

    await waitFor(() => {
      expect(screen.queryByText('Sem header')).toBeInTheDocument();
    });
  });

  it('render the header scrolled', async () => {
    const headerProviderScrolledPage = {
      logo: {},
      main: [
        {
          text: 'Scrolled Main menu item',
          link: {
            url: '/provider'
          },
          children: []
        }
      ],
      secondary: []
    } as unknown as HeaderNavigation;

    Object.defineProperty(window, 'scrollY', { value: 1 });
    render(
      <HeaderContainer
        mainHeader={mainHeader}
        headerProviderScrolledPage={headerProviderScrolledPage}
        headerGeoScrolledPage={null}
        isAccountEnabled={false}
        headerSem={null}
        page={{ pageType: 'provider' } as any}
        story={{} as Story}
        provider={null}
        site={CARING_SITE}
      />
    );

    await waitFor(() => {
      expect(screen.queryByText('Scrolled Main menu item')).toBeInTheDocument();
    });
  });
});
