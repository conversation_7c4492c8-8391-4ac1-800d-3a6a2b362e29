import HeaderLogo from '@components/Navigation/Header/HeaderLogo';
import { render, screen, setDesktopScreen } from '@utils/test-utils';

describe('HeaderLogo', () => {
  const logo = {
    logo: { link: '/path/to/logo', caption: 'caring.com' },
    logoUrl: 'https://www.caring.com',
    mobileLogo: { link: '/path/to/mobileLogo', caption: 'caring.com' },
    mobileLogoMaxWidth: '100',
    logoMaxWidth: '200',
    logoAlt: 'Logo Alt Text'
  };

  it('renders the desktop logo with the correct attributes', () => {
    setDesktopScreen();
    render(<HeaderLogo {...logo} />);
    const logoLink = screen.getByRole('link');
    const desktopLogoImage = screen.getByTestId('desktopLogo');

    expect(desktopLogoImage).toBeInTheDocument();
    expect(logoLink).toHaveAttribute('href', 'https://www.caring.com');
    expect(desktopLogoImage).toHaveAttribute('alt', 'Logo Alt Text');
    expect(desktopLogoImage).toHaveAttribute('height', '32');
    expect(desktopLogoImage).toHaveAttribute('width', '200');
  });

  it('renders the mobile logo with the correct attributes', () => {
    render(<HeaderLogo {...logo} />);
    const logoLink = screen.getByRole('link');
    const mobileLogoImage = screen.getByTestId('mobileLogo');

    expect(mobileLogoImage).toBeInTheDocument();
    expect(logoLink).toHaveAttribute('href', 'https://www.caring.com');
    expect(mobileLogoImage).toHaveAttribute('alt', 'Logo Alt Text');
    expect(mobileLogoImage).toHaveAttribute('height', '40');
    expect(mobileLogoImage).toHaveAttribute('width', '100');
  });

  it('does not render any link when url is empty', () => {
    render(<HeaderLogo {...logo} logoUrl="" />);
    const logoLink = screen.queryByRole('link');
    expect(logoLink).not.toBeInTheDocument();
  });
});
