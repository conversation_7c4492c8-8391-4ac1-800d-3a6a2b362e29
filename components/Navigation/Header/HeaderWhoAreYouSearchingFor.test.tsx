import { CTAAction } from '@components/InquiryForm/InquiryForm.types';
import HeaderWhoAreYouSearchingFor, {
  renderButton
} from '@components/Navigation/Header/HeaderWhoAreYouSearchingFor';
import { fireEvent, render, screen, setDesktopScreen } from '@utils/test-utils';

import { RollUpType } from '~/types/RollUpType';

import { MenuItemInquiryForm } from '../Navigation.types';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    push: jest.fn()
  }))
}));
const mockShowModal = jest.fn();
const mockHideModal = jest.fn();
jest.mock('~/contexts/ModalContext', () => ({
  useModal: jest.fn(() => ({
    visible: true,
    show: mockShowModal,
    hide: mockHideModal
  })),
  useModalControls: jest.fn(() => ({
    show: mockShowModal,
    hide: mockHideModal
  }))
}));

describe('WhoAreYouSearchingForPrompt', () => {
  beforeEach(() => {
    const mockAnalytics = {
      ready: jest.fn(),
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  const mockInquiryForm: MenuItemInquiryForm = {
    enabled: true,
    ctaAction: CTAAction.REQUEST_INFO,
    ctaText: 'Get Costs',
    rollUpType: RollUpType.SENIOR_LIVING,
    title: undefined,
    thankYouMessage: undefined,
    thankYouMessageColor: undefined,
    legalDisclosure: undefined
  };

  const mockMetadata = {
    '@id': '123456',
    '@name': '',
    '@path': '',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:template': '',
    'mgnl:created': '',
    '@nodes': []
  };

  it('renders the section title', () => {
    render(
      <HeaderWhoAreYouSearchingFor
        sectionTitle="Who are you searching for?"
        inquiryForm={mockInquiryForm}
        metadata={mockMetadata}
      />
    );

    const sectionTitle = screen.getByText('Who are you searching for?');
    expect(sectionTitle).toBeInTheDocument();
  });

  it('renders the buttons on desktop', () => {
    setDesktopScreen();

    render(
      <HeaderWhoAreYouSearchingFor
        sectionTitle="Who are you searching for?"
        inquiryForm={mockInquiryForm}
        metadata={mockMetadata}
      />
    );
    const selfButton = screen.getByTestId('myself-button');
    const spouseButton = screen.getByTestId('spouse-button');
    const parentsButton = screen.getByTestId('parents-button');
    const otherButton = screen.getByTestId('other-button');

    expect(selfButton).toBeInTheDocument();
    expect(spouseButton).toBeInTheDocument();
    expect(parentsButton).toBeInTheDocument();
    expect(otherButton).toBeInTheDocument();
  });
  it('renders the options on mobile', () => {
    render(
      <HeaderWhoAreYouSearchingFor
        sectionTitle="Who are you searching for?"
        inquiryForm={mockInquiryForm}
        metadata={mockMetadata}
      />
    );
    const selfButton = screen.getByTestId('myself-button');
    const spouseButton = screen.getByTestId('spouse-button');
    const parentsButton = screen.getByTestId('parents-button');
    const otherButton = screen.getByTestId('other-button');

    expect(selfButton).toBeInTheDocument();
    expect(spouseButton).toBeInTheDocument();
    expect(parentsButton).toBeInTheDocument();
    expect(otherButton).toBeInTheDocument();
  });
  it('opens the inquiry form when a button is clicked', async () => {
    render(
      <HeaderWhoAreYouSearchingFor
        sectionTitle="Who are you searching for?"
        inquiryForm={mockInquiryForm}
        metadata={mockMetadata}
      />
    );
    const selfButton = screen.getByTestId('myself-button');
    fireEvent.click(selfButton);
    expect(mockShowModal).toHaveBeenCalled();
    const myselfOption = screen.getByRole('option', {
      name: 'Myself'
    }) as HTMLOptionElement;
    const otherOption = screen.getByRole('option', {
      name: 'Other'
    }) as HTMLOptionElement;
    const parentOption = screen.getByRole('option', {
      name: 'Parent(s)'
    }) as HTMLOptionElement;
    const spouseOption = screen.getByRole('option', {
      name: 'Spouse'
    }) as HTMLOptionElement;

    expect(myselfOption.selected).toBeTruthy();
    expect(otherOption.selected).toBeFalsy();
    expect(parentOption.selected).toBeFalsy();
    expect(spouseOption.selected).toBeFalsy();
  });

  describe('renderButton', () => {
    it('renders the button with the correct label', () => {
      const option = {
        label: 'Test Button',
        value: 'test',
        testId: 'test-button'
      };
      const handleButtonClick = jest.fn();
      const buttonColor = 'primary';
      const buttonVariant = 'solid';

      render(
        renderButton(
          option,
          handleButtonClick,
          buttonColor,
          buttonVariant,
          'modal-uuid'
        )
      );

      const button = screen.getByTestId('test-button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('Test Button');
    });

    it('calls the handleButtonClick function with the correct option value when clicked', () => {
      const option = {
        label: 'Test Button',
        value: 'test',
        testId: 'test-button'
      };
      const handleButtonClick = jest.fn();
      const buttonColor = 'primary';
      const buttonVariant = 'solid';

      render(
        renderButton(
          option,
          handleButtonClick,
          buttonColor,
          buttonVariant,
          'modal-uuid'
        )
      );
      const button = screen.getByTestId('test-button');
      fireEvent.click(button);

      expect(handleButtonClick).toHaveBeenCalledWith('test');
    });
  });
});
