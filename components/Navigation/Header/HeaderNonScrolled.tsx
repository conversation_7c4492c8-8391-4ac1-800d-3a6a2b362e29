import { IconButton } from '@chakra-ui/button';
import { useDisclosure } from '@chakra-ui/hooks';
import { Icon } from '@chakra-ui/icon';
import { Flex, HStack } from '@chakra-ui/layout';
import LAYOUT from '@components/Layouts/layoutConstants';
import { HeaderNavigation } from '@components/Navigation';
import HeaderMainItems from '@components/Navigation/Header/HeaderMainItems';
import SecondaryMenuItem from '@components/Navigation/Header/SecondaryMenuItem';
import { useContext } from 'react';
import { MdMenu } from 'react-icons/md';

import SiteContext from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';

import HeaderLogo from './HeaderLogo';
import NavigationDrawer from './NavigationDrawer/NavigationDrawer';

interface HeaderNonScrolledProps {
  logo: HeaderNavigation['logo'];
  main: HeaderNavigation['main'];
  secondary: HeaderNavigation['secondary'];
  mainMenuPosition?: string;
  isAccountEnabled?: boolean;
  useAlternateDrawerMenu: boolean;
  metadata: HeaderNavigation['metadata'];
  providerPageHeadings: { title: string; linkText: string }[];
  geoPageHeadings: { title: string; linkText: string }[];
  isSemPage?: boolean;
}

const HeaderNonScrolled: React.FC<HeaderNonScrolledProps> = ({
  logo,
  main,
  secondary,
  mainMenuPosition,
  isAccountEnabled = false,
  useAlternateDrawerMenu,
  metadata,
  providerPageHeadings,
  geoPageHeadings,
  isSemPage = false
}) => {
  const { isOpen, onToggle } = useDisclosure();
  const showMainItems = Boolean(main.length);
  const showLogo = Boolean(logo);
  const showSecondaryItems = Boolean(secondary.length || isAccountEnabled);
  const siteContext = useContext(SiteContext);
  const isNotCaringWebSite =
    siteContext?.site?.path !== CaringDomains.LIVE || false;
  return (
    <Flex
      maxW={LAYOUT.CONTAINER_MAX_WIDTH}
      px={LAYOUT.CONTAINER_HORIZONTAL_PADDING}
      width="100%"
      m="auto"
      alignItems="center"
      gap={6}
      justifyContent={isSemPage ? 'space-around' : 'space-between'}
      position="relative"
    >
      {showMainItems && !useAlternateDrawerMenu && (
        <Flex
          display={{ base: 'block', xl: 'none' }}
          ml={{ base: '-8px', xl: 'unset' }}
        >
          <IconButton
            aria-label="Toggle menu"
            onClick={onToggle}
            icon={<Icon as={MdMenu} boxSize="32px" />}
            variant="ghost"
          />
        </Flex>
      )}
      {useAlternateDrawerMenu && (
        <Flex display={{ base: 'block', xl: 'none' }}>
          <NavigationDrawer placement="left" />
        </Flex>
      )}
      {showLogo && isNotCaringWebSite && <HeaderLogo {...logo} />}
      {showLogo && !isNotCaringWebSite && (
        <Flex display={isSemPage ? 'flex' : { base: 'flex', xl: 'none' }}>
          <HeaderLogo {...logo} />
        </Flex>
      )}

      {showMainItems && (
        <Flex
          as="nav"
          gap={{ base: 2, xl: 4 }}
          width="100%"
          backgroundColor={{ base: 'white', xl: 'transparent' }}
          py={{ base: 4, xl: '0' }}
          px={{ base: 8, xl: '0' }}
          transition={{ base: 'opacity .2s ease', xl: 'none' }}
          alignItems={{ base: 'start', xl: 'center' }}
          direction={{ base: 'column', xl: 'row' }}
          position={{ base: 'absolute', xl: 'static' }}
          top={{ base: '63px', xl: 'auto' }}
          left={{ base: '0', xl: 'auto' }}
          right={{ base: '0', xl: 'auto' }}
          opacity={{ base: isOpen ? 100 : 0, xl: 100 }}
          pointerEvents={{ base: isOpen ? 'auto' : 'none', xl: 'auto' }}
          boxShadow={{ base: 'md', xl: 'none' }}
          {...(mainMenuPosition === 'left'
            ? { marginRight: 'auto' }
            : mainMenuPosition === 'right'
            ? { marginLeft: 'auto' }
            : {})}
        >
          <HeaderMainItems
            main={main}
            metadata={metadata}
            providerPageHeadings={providerPageHeadings}
            geoPageHeadings={geoPageHeadings}
          />
        </Flex>
      )}

      {showSecondaryItems && (
        <Flex
          display="flex"
          textAlign="right"
          justifyContent="flex-end"
          width={{ base: 'auto', xl: '100%' }}
          gridTemplateColumns="repeat(2, 1fr)"
          gap={3}
          alignItems="center"
          fontWeight="bold"
          fontSize="14px"
        >
          <HStack
            display="flex"
            justifyContent="end"
            alignItems="center"
            fontWeight="bold"
            gap="2"
          >
            {secondary.map((item, i) => (
              <SecondaryMenuItem
                key={i}
                item={item}
                metadata={metadata}
                geoPageHeadings={geoPageHeadings}
                providerPageHeadings={providerPageHeadings}
              />
            ))}
          </HStack>
          {useAlternateDrawerMenu && (
            <Flex display={{ base: 'none', xl: 'block' }}>
              <NavigationDrawer />
            </Flex>
          )}
        </Flex>
      )}
    </Flex>
  );
};

export default HeaderNonScrolled;
