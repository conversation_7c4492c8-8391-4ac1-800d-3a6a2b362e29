import HeaderGeoScrolled from '@components/Navigation/Header/HeaderGeoScrolled';
import { render, screen, setDesktopScreen } from '@utils/test-utils';

import { HeaderNavigation } from '../Navigation.types';
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    push: jest.fn()
  }))
}));
describe('HeaderGeoScrolled', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
    window.tracking.ready = jest.fn(() => Promise.resolve());
  });
  const mockLogo = {
    logo: { link: '/path/to/logo', caption: 'caring.com' },
    logoUrl: 'https://www.caring.com',
    mobileLogo: { link: '/path/to/mobileLogo', caption: 'caring.com' },
    mobileLogoMaxWidth: '100',
    logoMaxWidth: '200',
    logoAlt: 'Logo Alt Text'
  };

  const mockMainItems = [
    {
      text: 'Main Items',
      mobile: false,
      desktop: false,
      link: { enabled: false, url: '' },
      inquiryForm: { enabled: false },
      children: []
    }
  ] as unknown as HeaderNavigation['main'];

  const mockSecondaryItems = [
    {
      id: 'a84089ab-af6a-4f2e-ab30-6b63cac12c43',
      type: 'whoAreYouSearchingFor',
      position: 'secondary',
      variant: 'outline',
      visibility: 'always',
      mobile: true,
      desktop: true,
      text: 'Who are you searching for?',
      textColor: 'secondary',
      textColorRange: '500',
      secondText: '',
      secondTextColor: 'secondary',
      secondTextColorRange: '200',
      icon: '',
      link: {
        enabled: false,
        url: ''
      },
      inquiryForm: {
        enabled: false
      },
      children: []
    }
  ] as unknown as HeaderNavigation['secondary'];

  const mockMetadata = {
    '@id': '123456',
    '@name': '',
    '@path': '',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:template': '',
    'mgnl:created': '',
    '@nodes': []
  };

  it('renders the component correctly on desktop', () => {
    setDesktopScreen();
    render(
      <HeaderGeoScrolled
        logo={mockLogo}
        main={mockMainItems}
        secondary={mockSecondaryItems}
        metadata={mockMetadata}
      />
    );

    const logoElement = screen.getByTestId('desktopLogo');
    const mainItems = screen.getByText('Main Items');
    const secondaryItems = screen.getByText('Who are you searching for?');

    expect(logoElement).toBeInTheDocument();
    expect(mainItems).toBeInTheDocument();
    expect(secondaryItems).toBeInTheDocument();
  });
  it('renders the component correctly on mobile', () => {
    render(
      <HeaderGeoScrolled
        logo={mockLogo}
        main={mockMainItems}
        secondary={mockSecondaryItems}
        metadata={mockMetadata}
      />
    );

    const mobileLogoElement = screen.queryByTestId('mobileLogo');
    const desktopLogoElement = screen.queryByTestId('desktopLogo');
    const mainItems = screen.getByText('Main Items');
    const secondaryItems = screen.getByText('Who are you searching for?');

    expect(mobileLogoElement).not.toBeInTheDocument();
    expect(desktopLogoElement).not.toBeInTheDocument();
    expect(mainItems).toBeInTheDocument();
    expect(secondaryItems).toBeInTheDocument();
  });
});
