import { useBreakpoint } from '@chakra-ui/media-query';
import HeaderNonScrolled from '@components/Navigation/Header/HeaderNonScrolled';
// import userEvent from '@testing-library/user-event';
import {
  fireEvent,
  mockHeader,
  render,
  screen,
  setMobileScreen,
  waitFor
} from '@utils/test-utils';

import SiteContext, { sitesConfig } from '~/contexts/SiteContext';
import { CaringDomains, SeniorHomesDomains } from '~/types/Domains';

import { Header } from '..';

jest.mock('@chakra-ui/media-query', () => ({
  useBreakpoint: jest.fn()
}));

describe('Header', () => {
  const analyticsMock = {
    track: jest.fn()
  };

  beforeEach(() => {
    window.tracking = analyticsMock;
    analyticsMock.track.mockReset();
  });

  it('renders passed component when not scrolled', async () => {
    (useBreakpoint as jest.Mock).mockReturnValue('xl');
    render(
      <Header
        regularHeaderComponent={<span>component</span>}
        scrolledHeaderComponent={<span>scrolled</span>}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
      />
    );

    expect(screen.queryByText('component')).toBeInTheDocument();
  });

  it('renders passed scrolled component when scrolled', async () => {
    Object.defineProperty(window, 'scrollY', { value: 1 });
    render(
      <Header
        regularHeaderComponent={<span>component</span>}
        scrolledHeaderComponent={<span>scrolled</span>}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
        isScrolledHeader={true}
      />
    );

    expect(screen.queryByText('scrolled')).toBeInTheDocument();
  });

  it('should render component with link event', async () => {
    const { findByText } = render(
      <Header
        regularHeaderComponent={
          <HeaderNonScrolled
            logo={mockHeader.logo}
            main={mockHeader.main}
            secondary={mockHeader.secondary}
            metadata={mockHeader.metadata}
            mainMenuPosition={mockHeader.mainMenuPosition}
            isAccountEnabled={false}
            providerPageHeadings={[]}
            geoPageHeadings={[]}
            useAlternateDrawerMenu={mockHeader.useAlternateDrawerMenu}
          />
        }
        scrolledHeaderComponent={null}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
      />
    );

    expect(findByText(mockHeader.secondary[0].text)).toBeTruthy();
    expect(findByText(mockHeader.secondary[1].text)).toBeTruthy();
  });

  it('should render component with phone number', async () => {
    const { findByText } = render(
      <Header
        regularHeaderComponent={
          <HeaderNonScrolled
            logo={mockHeader.logo}
            main={mockHeader.main}
            secondary={mockHeader.secondary}
            metadata={mockHeader.metadata}
            mainMenuPosition={mockHeader.mainMenuPosition}
            isAccountEnabled={false}
            providerPageHeadings={[]}
            geoPageHeadings={[]}
            useAlternateDrawerMenu={mockHeader.useAlternateDrawerMenu}
          />
        }
        scrolledHeaderComponent={null}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
      />
    );
    expect(findByText(mockHeader.secondaryPhone[0].text)).toBeTruthy();

    expect(findByText(mockHeader.secondaryPhone[1].text)).toBeTruthy();
  });

  // it('should render component with search input', async () => {
  //   const { getAllByLabelText } = render(
  //     <Header
  //       regularHeaderComponent={
  //         <HeaderNonScrolled
  //           logo={mockHeader.logo}
  //           main={mockHeader.main}
  //           secondary={mockHeader.secondary}
  //           metadata={mockHeader.metadata}
  //           mainMenuPosition={mockHeader.mainMenuPosition}
  //           isAccountEnabled={false}
  //           providerPageHeadings={[]}
  //           geoPageHeadings={[]}
  //         />
  //       }
  //       scrolledHeaderComponent={null}
  //       providerPageHeadings={[]}
  //       geoPageHeadings={[]}
  //     />
  //   );

  //   const input = getAllByLabelText('search-keyword', {
  //     selector: 'input',
  //   })[0] as HTMLInputElement;

  //   input.focus();

  //   await userEvent.keyboard(mockHeader.search, {
  //     skipClick: true,
  //   });

  //   await waitFor(() => {
  //     expect(input.value).toBe(mockHeader.search);
  //   });

  //   await userEvent.keyboard('{enter}', {
  //     skipClick: true,
  //   });

  //   await waitFor(() => {
  //     expect(analyticsMock.track).toHaveBeenCalled();
  //     expect(analyticsMock.track).toHaveBeenCalledWith(
  //       'Search Step Submission',
  //       {
  //         page_session_id: '',
  //         search_step_submission_json:
  //           '{"search_step_submission":[{"search_template_id":"00000000-0000-0000-0000-000000000000","search_instance_id":"00000000-0000-0000-0000-000000000000","step_id":"00000000-0000-0000-0000-000000000000","step_instance_id":"00000000-0000-0000-0000-000000000000","step_index":1,"step_content":[{"prompt_id":"00000000-0000-0000-0000-000000000000","prompt_type":"text","prompt_instance_id":"00000000-0000-0000-0000-000000000000","prompt_index":1,"prompt_value":"Sit nostrud eu adipisicing officia laborum sint irure ex irure.","response_array":[{"response_value":"Test","response_id":"00000000-0000-0000-0000-000000000000"}]}]}]}',
  //         session_id: '',
  //         form_type: '',
  //       }
  //     );
  //   });
  // });

  // The test passes when you run it individually, but fails when you run all tests.
  // it('should render component with mouseOver event', async () => {
  //   (useBreakpoint as jest.Mock).mockReturnValue('xl');
  //   Object.defineProperty(window, 'scrollY', { value: 0 });
  //   const { container } = render(
  //     <Header
  //       regularHeaderComponent={
  //         <HeaderNonScrolled
  //           logo={mockHeader.logo}
  //           main={mockHeader.main}
  //           secondary={mockHeader.secondary}
  //           metadata={mockHeader.metadata}
  //           mainMenuPosition={mockHeader.mainMenuPosition}
  //           isAccountEnabled={false}
  //           providerPageHeadings={[]}
  //           geoPageHeadings={[]}
  //         />
  //       }
  //       scrolledHeaderComponent={null}
  //       providerPageHeadings={[]}
  //       geoPageHeadings={[]}
  //     />
  //   );

  //   const before = container.innerHTML;

  //   const box = screen.getAllByTestId('box-header');

  //   fireEvent.mouseOver(box[0]);

  //   const after = container.innerHTML;

  //   await waitFor(() => {
  //     expect(before).not.toEqual(after);
  //   });
  // });

  it('should render component with toggle event', async () => {
    const { getByLabelText, container } = render(
      <Header
        regularHeaderComponent={
          <HeaderNonScrolled
            logo={mockHeader.logo}
            main={mockHeader.main}
            secondary={mockHeader.secondary}
            metadata={mockHeader.metadata}
            mainMenuPosition={mockHeader.mainMenuPosition}
            isAccountEnabled={false}
            providerPageHeadings={[]}
            geoPageHeadings={[]}
            useAlternateDrawerMenu={mockHeader.useAlternateDrawerMenu}
          />
        }
        scrolledHeaderComponent={null}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
      />
    );

    const before = container.innerHTML;

    const btn = screen.getByLabelText('Toggle menu', { selector: 'button' });

    fireEvent.click(btn);

    const after = container.innerHTML;

    await waitFor(() => {
      expect(before).not.toEqual(after);
    });
  });

  it('should render component with alternate drawer menu when enabled', async () => {
    setMobileScreen();
    render(
      <Header
        regularHeaderComponent={
          <HeaderNonScrolled
            logo={mockHeader.logo}
            main={mockHeader.main}
            secondary={mockHeader.secondary}
            metadata={mockHeader.metadata}
            mainMenuPosition={mockHeader.mainMenuPosition}
            isAccountEnabled={false}
            providerPageHeadings={[]}
            geoPageHeadings={[]}
            useAlternateDrawerMenu={true}
          />
        }
        scrolledHeaderComponent={null}
        providerPageHeadings={[]}
        geoPageHeadings={[]}
      />
    );
    expect(await screen.queryByText('Vulputate')).not.toBeInTheDocument();
    expect(
      await screen.findAllByText('Explore Assisted Living Near You')
    ).toHaveLength(2);
  });
  it('renders SavedProvidersNavButton only when site is Caring.com', () => {
    const caringSiteConfig =
      sitesConfig.find((site) => site.path === CaringDomains.LIVE) ?? null;
    render(
      <SiteContext.Provider value={{ site: caringSiteConfig }}>
        <Header
          regularHeaderComponent={
            <HeaderNonScrolled
              logo={mockHeader.logo}
              main={mockHeader.main}
              secondary={mockHeader.secondary}
              metadata={mockHeader.metadata}
              mainMenuPosition={mockHeader.mainMenuPosition}
              isAccountEnabled={false}
              providerPageHeadings={[]}
              geoPageHeadings={[]}
              useAlternateDrawerMenu={false}
            />
          }
          scrolledHeaderComponent={null}
          providerPageHeadings={[]}
          geoPageHeadings={[]}
        />
      </SiteContext.Provider>
    );

    expect(screen.getByTestId('saved-provider-nav-button')).toBeInTheDocument();
  });
  it('does not render SavedProvidersNavButton when site is not Caring.com', () => {
    const seniorHomesSiteConfig =
      sitesConfig.find((site) => site.path === SeniorHomesDomains.LIVE) ?? null;
    render(
      <SiteContext.Provider value={{ site: seniorHomesSiteConfig }}>
        <Header
          regularHeaderComponent={
            <HeaderNonScrolled
              logo={mockHeader.logo}
              main={mockHeader.main}
              secondary={mockHeader.secondary}
              metadata={mockHeader.metadata}
              mainMenuPosition={mockHeader.mainMenuPosition}
              isAccountEnabled={false}
              providerPageHeadings={[]}
              geoPageHeadings={[]}
              useAlternateDrawerMenu={false}
            />
          }
          scrolledHeaderComponent={null}
          providerPageHeadings={[]}
          geoPageHeadings={[]}
        />
      </SiteContext.Provider>
    );

    expect(
      screen.queryByTestId('saved-provider-nav-button')
    ).not.toBeInTheDocument();
  });
  it('renders SearchBar when site is Caring.com and search is not in the main menu', () => {
    const caringSiteConfig =
      sitesConfig.find((site) => site.path === CaringDomains.LIVE) ?? null;
    const mockHeaderMainWithoutSearch = {
      ...mockHeader.main,
      main: mockHeader.main.filter((item) => item.type !== 'search')
    };
    render(
      <SiteContext.Provider value={{ site: caringSiteConfig }}>
        <Header
          regularHeaderComponent={
            <HeaderNonScrolled
              logo={mockHeader.logo}
              main={mockHeaderMainWithoutSearch}
              secondary={mockHeader.secondary}
              metadata={mockHeader.metadata}
              mainMenuPosition={mockHeader.mainMenuPosition}
              isAccountEnabled={false}
              providerPageHeadings={[]}
              geoPageHeadings={[]}
              useAlternateDrawerMenu={false}
            />
          }
          scrolledHeaderComponent={null}
          providerPageHeadings={[]}
          geoPageHeadings={[]}
        />
      </SiteContext.Provider>
    );

    expect(screen.getByTestId('search-bar')).toBeInTheDocument();
  });
  it('does not render SearchBar when site is Seniorhomes.com and search is not in the main menu', () => {
    const seniorhomesSiteConfig =
      sitesConfig.find((site) => site.path === SeniorHomesDomains.LIVE) ?? null;
    const mockHeaderMainWithoutSearch = {
      ...mockHeader.main,
      main: mockHeader.main.filter((item) => item.type !== 'search')
    };
    render(
      <SiteContext.Provider value={{ site: seniorhomesSiteConfig }}>
        <Header
          regularHeaderComponent={
            <HeaderNonScrolled
              logo={mockHeader.logo}
              main={mockHeaderMainWithoutSearch}
              secondary={mockHeader.secondary}
              metadata={mockHeader.metadata}
              mainMenuPosition={mockHeader.mainMenuPosition}
              isAccountEnabled={false}
              providerPageHeadings={[]}
              geoPageHeadings={[]}
              useAlternateDrawerMenu={false}
            />
          }
          scrolledHeaderComponent={null}
          providerPageHeadings={[]}
          geoPageHeadings={[]}
        />
      </SiteContext.Provider>
    );

    expect(screen.queryByTestId('search-bar')).not.toBeInTheDocument();
  });
});
