import { Box, Flex } from '@chakra-ui/layout';
import { useBreakpointValue } from '@chakra-ui/media-query';
import LAYOUT from '@components/Layouts/layoutConstants';

import { HeaderProps } from './Header';
import HeaderLogo from './HeaderLogo';
import HeaderMainItems from './HeaderMainItems';
import SecondaryMenuItem from './SecondaryMenuItem';

const HeaderGeoScrolled: React.FC<HeaderProps> = ({
  logo,
  main,
  secondary,
  metadata,
  geoPageHeadings
}) => {
  const isTabletPlus = useBreakpointValue({ base: false, md: true });

  return (
    <Box
      maxW={LAYOUT.CONTAINER_MAX_WIDTH}
      px={LAYOUT.CONTAINER_HORIZONTAL_PADDING}
      width="100%"
      m="auto"
      display="flex"
      alignItems="center"
      gap={{ base: 2, md: 6 }}
      zIndex="sticky"
    >
      {isTabletPlus && <HeaderLogo {...logo} />}
      <Flex justifyContent="flex-start" width="auto">
        <HeaderMainItems
          main={main}
          metadata={metadata}
          geoPageHeadings={geoPageHeadings}
        />
      </Flex>
      <Flex justifyContent={{ base: 'center', md: 'flex-end' }} width="100%">
        {secondary.map((item, i) => (
          <SecondaryMenuItem
            key={i}
            item={item}
            metadata={metadata}
            geoPageHeadings={geoPageHeadings}
          />
        ))}
      </Flex>
    </Box>
  );
};

export default HeaderGeoScrolled;
