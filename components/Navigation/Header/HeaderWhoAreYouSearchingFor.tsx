import { useDisclosure } from '@chakra-ui/hooks';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { useBreakpointValue } from '@chakra-ui/media-query';
import { Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/menu';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button, { ButtonProps } from '@components/Button';
import InquiryForm from '@components/InquiryForm';
import { InquiryFormProps } from '@components/InquiryForm/InquiryForm';
import { CTAAction, Display } from '@components/InquiryForm/InquiryForm.types';
import { inquiryForOptions } from '@components/InquiryForm/InquiryForm.utils';
import {
  Visibility,
  VISIBILITY_TYPES
} from '@components/Navigation/Navigation.types';
import { useState } from 'react';

import { useModalControls } from '~/contexts/ModalContext';

import { MenuItemInquiryForm } from '../Navigation.types';
import { itemDisplay } from './Header';

export interface HeaderWhoAreYouSearchingForProps {
  sectionTitle?: string;
  buttonColor?: string;
  buttonVariant?: 'solid' | 'outline' | 'ghost';
  ctaAction?: InquiryFormProps['ctaAction'];
  inquiryForm: MenuItemInquiryForm;
  visibility?: Visibility;
  metadata: InquiryFormProps['metadata'];
}

export const renderButton = (
  option: { label: string; value: string; testId: string },
  handleButtonClick: (option: string) => void,
  buttonColor: string,
  buttonVariant: string,
  modalId: string,
  rest?: ButtonProps
) => {
  return (
    <Button
      key={`${option.value}-${modalId}`}
      width={{ base: '82px', xl: '110px' }}
      size={{ base: 'sm', xl: 'md' }}
      colorScheme={buttonColor}
      variant={buttonVariant}
      onClick={() => handleButtonClick(option.value)}
      data-testid={option.testId}
      elementAction={ElementActions.OPEN_MODAL}
      elementName={ElementNames.WHO_ARE_YOU_SEARCHING_FOR}
      elementType={ElementTypes.BUTTON}
      {...rest}
    >
      {option.label}
    </Button>
  );
};

const buttonOptions = [
  { label: 'Self', value: inquiryForOptions[3], testId: 'myself-button' },
  { label: 'Partner', value: inquiryForOptions[4], testId: 'spouse-button' },
  { label: 'Parent(s)', value: inquiryForOptions[0], testId: 'parents-button' },
  { label: 'Other', value: inquiryForOptions[7], testId: 'other-button' }
];

const HeaderWhoAreYouSearchingFor = ({
  sectionTitle = 'Who are you searching for?',
  buttonColor = 'primary',
  buttonVariant = 'solid',
  visibility = VISIBILITY_TYPES['ALWAYS'],
  ctaAction,
  inquiryForm,
  metadata
}: HeaderWhoAreYouSearchingForProps): React.ReactElement => {
  const [selectedOption, setSelectedOption] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const modalId = (metadata && metadata['@id']) ?? '';
  const isMobile = useBreakpointValue({ base: true, md: false });
  const { show } = useModalControls(modalId);

  const handleButtonClick = (option: string) => {
    setSelectedOption(option);
    requestAnimationFrame(() => {
      show();
    });
  };

  return (
    <Flex
      justifyContent="center"
      flexWrap="nowrap"
      display={itemDisplay(visibility)}
      width={isMobile ? '100%' : 'auto'}
    >
      {isMobile ? (
        <Box width="100%">
          <Menu isOpen={isOpen} onClose={onClose} matchWidth={true}>
            <MenuButton
              onClick={onOpen}
              onMouseEnter={onOpen}
              onMouseLeave={onClose}
              shadow="md"
              borderRadius={isOpen ? '12px 12px 0 0' : 'xl'}
              fontWeight="bold"
              width="100%"
              mr={{ base: 0, xl: 'auto' }}
              py={2}
              px={4}
            >
              <Box
                as="span"
                fontSize="sm"
                color="primary.900"
                display="flex"
                whiteSpace="nowrap"
                justifyContent="space-between"
                alignItems="center"
              >
                <span>{sectionTitle}</span>
                {isOpen ? (
                  <ChevronUpIcon
                    width="24px"
                    height="24px"
                    aria-hidden={true}
                  />
                ) : (
                  <ChevronDownIcon
                    width="24px"
                    height="24px"
                    aria-hidden={true}
                  />
                )}
              </Box>
            </MenuButton>
            <MenuList
              onMouseEnter={onOpen}
              onMouseLeave={onClose}
              marginTop="-8px"
              border="none"
              borderRadius="0 0 12px 12px"
              shadow="md"
            >
              {buttonOptions?.map(({ label, value, testId }, index) => (
                <MenuItem
                  as="span"
                  fontSize="sm"
                  color="primary.900"
                  key={`${value}-${index}`}
                  py={2}
                  onClick={() => handleButtonClick(value)}
                  data-testid={testId}
                >
                  {label}
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
        </Box>
      ) : (
        <Stack direction="row" alignItems="center">
          <Text
            fontSize={{ base: 'sm', xl: 'md' }}
            color="primary.900"
            fontWeight="bold"
            textAlign="right"
          >
            {sectionTitle}
          </Text>
          {buttonOptions.map((option) =>
            renderButton(
              option,
              handleButtonClick,
              buttonColor,
              buttonVariant,
              modalId
            )
          )}
        </Stack>
      )}

      {inquiryForm?.enabled && (
        <Box position="absolute">
          <InquiryForm
            {...inquiryForm}
            formId={modalId}
            display={Display.FIT_CONTENT_MODAL}
            ctaAction={ctaAction ?? CTAAction.REQUEST_INFO}
            ctaText={inquiryForm.ctaText ?? 'Get Costs'}
            rollUpType={inquiryForm.rollUpType ?? undefined}
            values={{ whoAreYouLookingFor: selectedOption }}
            metadata={metadata}
          />
        </Box>
      )}
    </Flex>
  );
};

export default HeaderWhoAreYouSearchingFor;
