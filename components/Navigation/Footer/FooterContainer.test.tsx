import FooterContainer from '@components/Navigation/Footer/FooterContainer';
import { FooterNavigation } from '@components/Navigation/Navigation.types';
import { render, screen } from '@utils/test-utils';

describe('FooterContainer', () => {
  const defaultFooter = {
    intro: 'Intro for default footer',
    logo: {},
    main: [],
    secondary: []
  } as unknown as FooterNavigation;

  const footerSem = {
    intro: 'Intro for SEM footer',
    logo: {},
    main: [{ text: 'Footer item' }],
    secondary: []
  } as unknown as FooterNavigation;

  it('should render default footer if not sem page', () => {
    render(
      <FooterContainer
        mainFooter={defaultFooter}
        footerSem={footerSem}
        page={{} as any}
        template="spa-lm:pages/provider"
      />
    );

    expect(screen.queryAllByText(defaultFooter.intro)[0]).toBeInTheDocument();
  });

  it('should render sem footer if sem page', () => {
    render(
      <FooterContainer
        mainFooter={defaultFooter}
        footerSem={footerSem}
        page={{} as any}
        template="spa-lm:pages/sem"
      />
    );

    expect(screen.queryAllByText('Footer item')[0]).toBeInTheDocument();
  });
});
