import { mockFooter, render, screen } from '@utils/test-utils';

import { Footer } from '..';

describe('Footer', () => {
  it('should render component without secondary items', async () => {
    const { container } = render(
      <Footer
        intro={mockFooter.intro}
        logo={mockFooter.logo}
        main={mockFooter.main}
        secondary={[]}
      />
    );
    const btn_group = container.getElementsByClassName('chakra-button__group');
    const div_stack = btn_group[0].getElementsByClassName('chakra-stack');

    expect(div_stack.length).toBe(1);
    expect(div_stack[0].querySelector('a')).toBe(null);
  });

  it('should render component with a given text', async () => {
    const introText = mockFooter.intro;
    render(
      <Footer
        intro={introText}
        logo={mockFooter.logo}
        main={mockFooter.main}
        secondary={mockFooter.secondary}
      />
    );

    const text = screen.getAllByText(introText);
    expect(text[0]).toBeInTheDocument();
  });
});
