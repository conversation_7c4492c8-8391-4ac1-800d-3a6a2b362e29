import { render } from '@utils/test-utils';

import { Metadata } from '~/types/Magnolia';

import RelatedArticles, { RelatedArticlesProps } from './RelatedArticle';

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    query: {},
    asPath: 'test'
  }))
}));
describe('RelatedArticles component', () => {
  const mockPages = {
    '@name': 'pages',
    '@path': '/caring.com/home/<USER>/09/pages',
    '@id': '98162e55-a9c1-473a-85ac-6f284cf0757f',
    '@nodeType': 'mgnl:contentNode',
    'mgnl:lastModified': '2023-04-28T15:26:24.647-03:00',
    'mgnl:created': '2023-04-28T13:00:59.740-03:00',
    'mgnl:template': '',
    pages0: {
      '@name': 'pages0',
      '@path': '/caring.com/home/<USER>/09/pages/pages0',
      '@id': 'bd1d1c2c-b9ee-4700-8e7a-2c3eea07b3f0',
      '@nodeType': 'mgnl:contentNode',
      'mgnl:lastModified': '2023-04-28T15:26:24.642-03:00',
      image: {
        '@name': 'Caring Star 2023 promotion',
        '@path':
          '/Homepage/Heros/Promotion/Screenshot-2022-12-19-at-3.37-1.png',
        '@id': 'jcr:4d1e8064-2b3a-4e2f-91e8-628f93d39cd6',
        '@link':
          '/caring-paas-webapp-1.3/dam/jcr:4d1e8064-2b3a-4e2f-91e8-628f93d39cd6/Screenshot%202022-12-19%20at%203.37%201.png',
        metadata: [Object]
      },
      'mgnl:created': '2023-04-28T15:26:24.642-03:00',
      switchable: {
        '@name': 'switchable',
        '@path': '/caring.com/home/<USER>/09/pages/pages0/switchable',
        '@id': '4160ca86-283e-482c-8e1d-a45bfaa6bf80',
        '@nodeType': 'mgnl:contentNode',
        title: 'Caring site',
        url: 'www.caring.com',
        'mgnl:lastModified': '2023-04-28T15:26:24.644-03:00',
        field: 'external',
        'mgnl:created': '2023-04-28T15:26:24.644-03:00',
        '@nodes': []
      },
      '@nodes': ['switchable']
    },
    '@nodes': ['pages0']
  };

  const mockMetadata: Metadata = {
    '@name': '09',
    '@path': '/caring.com/home/<USER>/09',
    '@id': 'b9dbb859-888d-4bbb-bf80-ca3477ac9279',
    '@nodeType': 'mgnl:component',
    'mgnl:template': 'spa-lm:components/relatedArticles',
    'mgnl:created': '2023-04-28T13:00:59.596-03:00',
    'mgnl:lastModified': '2023-04-28T15:26:24.647-03:00',
    '@nodes': ['pages']
  };

  const mockProps: RelatedArticlesProps = {
    headingElement: 'h2',
    title: 'Related Articles',
    withImages: false,
    pages: mockPages,
    metadata: mockMetadata
  };

  it('renders the component with the correct props', () => {
    const { getByText, getAllByTestId } = render(
      <RelatedArticles {...mockProps} />
    );
    expect(getByText('Related Articles')).toBeInTheDocument();
    expect(getAllByTestId('related-article')).toHaveLength(1);
  });

  it('renders an empty component', () => {
    const emptyMockProps: RelatedArticlesProps = {
      headingElement: 'h2',
      title: '',
      text: '<p></p>\n',
      withImages: false,
      metadata: mockMetadata
    };
    const { container } = render(<RelatedArticles {...emptyMockProps} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });
});
