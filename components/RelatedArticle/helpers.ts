import { createID } from '@utils/strings';

import { CareType } from '~/types/LocaleCatalog';
import { MagnoliaImage, Metadata } from '~/types/Magnolia';

export interface PageWithImage {
  type: 'internal' | 'external';
  careType?: CareType;
  title: string;
  url: string;
  page?: Metadata;
  image: MagnoliaImage | string;
}

export const extractPages = (
  pages: Pick<Metadata, '@nodes'>,
  careTypeSlug?: string
): Array<PageWithImage> => {
  const extracted: Array<PageWithImage> = [];
  if (pages?.['@nodes']) {
    pages['@nodes'].forEach((key) => {
      if (pages[key]?.switchable?.title) {
        const subpage = {
          type: pages[key].switchable.field,
          page: pages[key].switchable.page,
          title: pages[key].switchable.title,
          url: pages[key].switchable.url,
          image: pages[key].image,
          careType: pages[key].careType
        };
        extracted.push(subpage);
      }
    });
  }

  return extracted.filter(
    (node) => !node.careType || createID(node.careType.name) === careTypeSlug
  );
};
