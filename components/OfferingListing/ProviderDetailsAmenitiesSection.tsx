import {
  ListIcon,
  ListItem,
  Stack,
  Text,
  UnorderedList
} from '@chakra-ui/layout';
import Heading from '@components/Heading';
import { STRING_TO_ICON_COMPONENT } from '@components/RenderIcon';
import { getAmenityCategoryIcon } from '@utils/amenities';
import { useContext } from 'react';
import { MdCheck } from 'react-icons/md';

import { Amenities } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';

const SectionHeading = ({ heading }) => {
  return (
    <Heading
      headingElement="h3"
      headingSize="md"
      title={heading}
      withContainer={false}
    />
  );
};

const SectionLabel = ({ domain, amenityCategoryId, sectionLabel }) => {
  switch (domain) {
    case 'caring.com':
      return (
        <Stack direction="row" alignItems="center">
          {STRING_TO_ICON_COMPONENT[getAmenityCategoryIcon(amenityCategoryId)]}
          <SectionHeading heading={sectionLabel} />
        </Stack>
      );
    default:
      return <SectionHeading heading={sectionLabel} />;
  }
};

const AmenityIcon = ({ domain }) => {
  switch (domain) {
    case 'caring.com':
      return <ListIcon as={MdCheck} mr={0} boxSize="6" height="27px" />;
    default:
      return (
        <ListIcon as={MdCheck} color="primary.500" boxSize="6" height="27px" />
      );
  }
};

interface Props {
  sectionLabel: string;
  amenityCategoryId?: number | undefined;
  offeringsList?: Amenities;
  isLocked?: boolean;
  itemsToShow?: number;
}

const Offering: React.FC<Props> = ({
  sectionLabel,
  amenityCategoryId = null,
  offeringsList,
  isLocked,
  itemsToShow = -1
}) => {
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const offeringsToShow =
    itemsToShow > -1
      ? (offeringsList ?? []).slice(0, itemsToShow)
      : offeringsList;

  return (
    <Stack py={4}>
      <SectionLabel
        domain={domain}
        amenityCategoryId={amenityCategoryId}
        sectionLabel={sectionLabel}
      />
      <UnorderedList>
        {offeringsToShow?.map((offering, index) => (
          <ListItem key={index} fontSize="lg" display="flex">
            <AmenityIcon domain={domain} />
            <Text as="span" filter={isLocked ? 'blur(10px)' : ''} ml={2}>
              {offering.amenityName}
            </Text>
          </ListItem>
        ))}
      </UnorderedList>
    </Stack>
  );
};

export default Offering;
