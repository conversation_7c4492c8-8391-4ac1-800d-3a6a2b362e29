'use client';

import useTranslation from '@hooks/use-translation';
import {
  DeviceVisibility,
  useResponsiveDisplay
} from '@hooks/useResponsiveDisplay';
import { Parser } from '@utils/parser';
import dynamic from 'next/dynamic';
import { useContext } from 'react';

import { HeadingElements } from '~/@types/heading';
import ProviderContext, { Amenities, Provider } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';
import { getExtraAmenities, isAmenitiesEmpty } from '~/utils/amenities';
import { providerIsEnhancedAndNotSuppressed } from '~/utils/providers';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
const ProviderDetailsAmenitiesLegacy = dynamic(
  () => import('./legacy/ProviderDetailsAmenitiesLegacy')
);
const ProviderDetailsAmenities = dynamic(
  () => import('./ProviderDetailsAmenities')
);
interface Props {
  templateId?: string;
  title?: string;
  itemsToShow?: number;
  blurAmenities?: boolean;
  inquiryId?: string;
  headingElement?: HeadingElements;
  deviceVisibility?: DeviceVisibility;
  useLegacyAmenities?: boolean;
}

export const getProviderAmenitiesFromServices = (
  provider: Provider
): Amenities => {
  let amenities: Amenities = [];
  let amenityIds: Array<number> = [0];

  if (provider?.services) {
    provider.services.forEach((service) => {
      service.amenities.forEach((amenity) => {
        const amenityId = amenity?.amenityId ? amenity?.amenityId : 0;
        if (!amenityIds.includes(amenityId)) {
          amenityIds.push(amenityId);
          amenities.push(amenity);
        }
      });
    });
  }

  return amenities;
};

const OfferingListingComponent: React.FC<Props> = ({
  itemsToShow,
  title,
  templateId,
  headingElement,
  blurAmenities = false,
  inquiryId = '',
  deviceVisibility,
  useLegacyAmenities = false
}: Props) => {
  const { t } = useTranslation();
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const provider = useContext(ProviderContext)?.provider;
  const isHidden = useResponsiveDisplay(deviceVisibility);

  if (isHidden) {
    return <></>;
  }
  if (!provider) {
    return null;
  }

  const amenities = getProviderAmenitiesFromServices(provider);
  const amenitiesLegacy = provider?.amenitiesLegacy ?? {};
  const providerName = provider?.name ?? '';
  const heading = title || `What ${providerName} Offers`;
  const isEnhanced = providerIsEnhancedAndNotSuppressed(provider);
  const extraAmenities = getExtraAmenities(amenitiesLegacy);
  const allAmenities = amenities.concat(extraAmenities);

  if (isAmenitiesEmpty(amenities ? amenities : amenitiesLegacy, domain, t)) {
    return null;
  }

  const parsedHeading = Parser({
    source: heading,
    values: { provider: provider || {} },
    strip: true
  });

  return (
    <Container id="offering-listing">
      {amenities ? (
        <ProviderDetailsAmenities
          heading={parsedHeading}
          headingElement={headingElement}
          amenities={allAmenities}
          isEnhanced={isEnhanced}
          itemsToShow={itemsToShow ?? -1}
          blurAmenities={blurAmenities}
          inquiryId={inquiryId}
        />
      ) : (
        <ProviderDetailsAmenitiesLegacy
          heading={heading}
          headingElement={headingElement}
          amenities={amenitiesLegacy}
          itemsToShow={itemsToShow ?? -1}
        />
      )}
    </Container>
  );
};

export default OfferingListingComponent;
