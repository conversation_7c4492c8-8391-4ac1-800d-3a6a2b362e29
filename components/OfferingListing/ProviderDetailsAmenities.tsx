import { useDisclosure } from '@chakra-ui/hooks';
import { Box, Stack } from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import Heading from '@components/Heading';
import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { useScrollToAnchor } from '@hooks/useScrollToAnchor';
import { useContext, useRef } from 'react';

import { HeadingElements } from '~/@types/heading';
import { useModalControls } from '~/contexts/ModalContext';
import { Amenities } from '~/contexts/Provider';
import SiteContext from '~/contexts/SiteContext';

import ProviderDetailsAmenitiesSection from './ProviderDetailsAmenitiesSection';

const ViewAllServicesButton = ({
  domain,
  viewAll,
  handleViewAllServicesClick
}) => {
  const text = viewAll ? 'View fewer services' : 'View all services';

  switch (domain) {
    case 'caring.com':
      return (
        <Button
          onClick={handleViewAllServicesClick}
          colorScheme="primary"
          size="lg"
          variant="outline"
          mx={4}
          width="fit-content"
          fontSize="16px"
          elementAction={
            viewAll ? ElementActions.COLLAPSE : ElementActions.EXPAND
          }
          elementName={
            viewAll
              ? ElementNames.VIEW_FEWER_SERVICES
              : ElementNames.VIEW_ALL_SERVICES
          }
          elementType={ElementTypes.BUTTON}
        >
          {text}
        </Button>
      );
    default:
      return (
        <Button
          onClick={handleViewAllServicesClick}
          colorScheme="primary"
          size="lg"
          variant="outline"
          width="full"
          elementAction={
            viewAll ? ElementActions.COLLAPSE : ElementActions.EXPAND
          }
          elementName={
            viewAll
              ? ElementNames.VIEW_FEWER_SERVICES
              : ElementNames.VIEW_ALL_SERVICES
          }
          elementType={ElementTypes.BUTTON}
        >
          {text}
        </Button>
      );
  }
};

type Group = {
  label: string;
  amenityCategoryId: number | undefined;
  amenities: Amenities;
};

interface Props {
  amenities: Amenities;
  isEnhanced?: boolean;
  heading: string;
  itemsToShow: number;
  blurAmenities: boolean;
  inquiryId: string;
  headingElement?: HeadingElements;
}

const ProviderDetailsAmenities: React.FC<Props> = ({
  amenities,
  isEnhanced,
  heading,
  itemsToShow,
  blurAmenities,
  inquiryId,
  headingElement = 'h2'
}) => {
  const siteProps = useContext(SiteContext);
  const { show: showInquiryForm } = useModalControls(inquiryId);
  const targetRef = useRef<HTMLHeadingElement>(null);
  const domain = siteProps.site?.path ?? '';
  const inquiryFormHasSubmitted = useInquiryFormSubmitted();
  const showUnlock = blurAmenities && !isEnhanced && !inquiryFormHasSubmitted;
  const buildGroups = (amenities: Amenities) => {
    const results = amenities.reduce(function (r, a) {
      if (a.amenityCategoryName) {
        r[a.amenityCategoryName] = r[a.amenityCategoryName] || [];
        r[a.amenityCategoryName].push(a);
      }
      return r;
    }, Object.create(null));

    const groups: Array<Group> = [];
    for (const result in results) {
      groups.push({
        label: result,
        amenityCategoryId: results[result][0].amenityCategoryId,
        amenities: results[result]
      });
    }
    return groups;
  };
  const groups = buildGroups(amenities);
  const { isOpen: viewAll, onToggle: toggleViewAll } = useDisclosure({
    defaultIsOpen: itemsToShow === -1
  });

  const { baseAmenities, extraAmenities } = groups.reduce(
    (acc, group) => {
      if (group.label === 'Additional Amenity Information') {
        acc.extraAmenities.push(group);
      } else {
        acc.baseAmenities.push(group);
      }
      return acc;
    },
    { baseAmenities: [], extraAmenities: [] } as {
      baseAmenities: Group[];
      extraAmenities: Group[];
    }
  );
  const columns: Array<Array<Group>> = [[], [], []];
  let i = 0;
  baseAmenities
    .filter((group) => group.amenities.length > 0)
    .forEach((group) => {
      columns[i].push(group);
      if (i < 2) i++;
      else i = 0;
    });
  const hasMultipleRows = columns.some((column) => column.length > 0);

  // workaround to fix issue with jumplink in chrome on redirects
  useScrollToAnchor(targetRef, heading, true);

  return (
    <Stack width="full">
      <Heading
        ref={targetRef}
        headingElement={headingElement}
        headingSize="lg"
        title={heading}
        withContainer={false}
      />
      <Box
        display="grid"
        gridTemplateColumns={{ lg: 'repeat(3, 1fr)', base: '1fr' }}
        gridGap={4}
      >
        {columns.map((column, index) => (
          <Stack key={index} spacing="0">
            {column.map(
              (group, index) =>
                (index === 0 || viewAll) && (
                  <ProviderDetailsAmenitiesSection
                    key={index}
                    sectionLabel={group.label}
                    amenityCategoryId={group.amenityCategoryId}
                    offeringsList={group.amenities}
                    isLocked={showUnlock}
                    itemsToShow={viewAll ? -1 : itemsToShow}
                  />
                )
            )}
          </Stack>
        ))}
      </Box>
      {extraAmenities
        .filter((group) => group.amenities.length > 0)
        .map((group, index) => (
          <Box display="grid" gridTemplateColumns="1fr" pt={2} key={index}>
            {viewAll && (
              <ProviderDetailsAmenitiesSection
                sectionLabel={group.label}
                amenityCategoryId={group.amenityCategoryId}
                offeringsList={group.amenities}
                isLocked={showUnlock}
                itemsToShow={viewAll ? -1 : itemsToShow}
              />
            )}
          </Box>
        ))}

      {showUnlock && (
        <Button
          colorScheme="secondary"
          size="lg"
          alignSelf={'center'}
          variant="solid"
          mx={4}
          width="xl"
          fontSize={'16px'}
          onClick={showInquiryForm}
          elementAction={ElementActions.OPEN_MODAL}
          elementName={ElementNames.UNLOCK_AMENITIES}
          elementType={ElementTypes.BUTTON}
        >
          {'Unlock Amenity Information'}
        </Button>
      )}
      {!showUnlock && hasMultipleRows && (
        <ViewAllServicesButton
          domain={domain}
          viewAll={viewAll}
          handleViewAllServicesClick={toggleViewAll}
        />
      )}
    </Stack>
  );
};

export default ProviderDetailsAmenities;
