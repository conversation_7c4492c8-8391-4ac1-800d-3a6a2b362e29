import { formatMoney } from './utils';

describe('utils', () => {
  describe('formatMoney', () => {
    const mockValues = [
      { value: '100', expected: '$100' },
      { value: '$100', expected: '$100' },
      { value: 100, expected: '$100' },
      { value: 'test', expected: 'test' },
      { value: '', expected: '' },
      { value: null, expected: '' },
      { value: undefined, expected: '' },
      { value: false, expected: '' }
    ];
    test.each(mockValues)(
      'result should be "$expected" when value is "$value"',
      ({ value, expected }) => {
        expect(formatMoney(value)).toBe(expected);
      }
    );
  });
});
