import useInquiryFormSubmitted from '@hooks/use-inquiry-form-submitted';
import { fireEvent, mockAmenities, render, screen } from '@utils/test-utils';

import { useModalControls } from '~/contexts/ModalContext';

import ProviderDetailsAmenities from './ProviderDetailsAmenities';

jest.mock('~/contexts/ModalContext');
jest.mock('@hooks/use-inquiry-form-submitted');

describe('ProviderDetailsAmenities', () => {
  const mockShowInquiryForm = jest.fn();
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
    (useModalControls as jest.Mock).mockReturnValue({
      show: mockShowInquiryForm
    });
  });
  it('renders the heading', () => {
    render(
      <ProviderDetailsAmenities
        amenities={mockAmenities}
        heading="Test Heading"
        itemsToShow={3}
        blurAmenities={false}
        inquiryId="test-inquiry-id"
      />
    );

    expect(
      screen.getByRole('heading', { name: 'Test Heading' })
    ).toBeInTheDocument();
  });

  it('renders "View all services" when amenities exceed itemsToShow', () => {
    render(
      <ProviderDetailsAmenities
        amenities={mockAmenities}
        heading="Test Heading"
        itemsToShow={2}
        blurAmenities={false}
        inquiryId="test-inquiry-id"
      />
    );

    expect(screen.getByText('Amenity 1')).toBeInTheDocument();
    expect(screen.getByText('Amenity 2')).toBeInTheDocument();
    expect(screen.getByText('Amenity 3')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'View all services' }));
  });

  it('renders "Unlock Amenity Information" and does not render "View all services" button when "blurAmenities" is true', () => {
    render(
      <ProviderDetailsAmenities
        amenities={mockAmenities}
        heading="Test Heading"
        itemsToShow={3}
        blurAmenities={true}
        inquiryId="test-inquiry-id"
      />
    );

    expect(screen.getByText('Amenity 1')).toBeInTheDocument();
    expect(screen.getByText('Amenity 2')).toBeInTheDocument();
    expect(
      screen.queryByRole('button', { name: 'View all services' })
    ).toBeFalsy();
    expect(
      screen.getByRole('button', { name: 'Unlock Amenity Information' })
    ).toBeInTheDocument();
  });

  it('calls showInquiryForm when "unlock amenity information" button is clicked', () => {
    (useInquiryFormSubmitted as jest.Mock).mockReturnValue(false);
    render(
      <ProviderDetailsAmenities
        amenities={mockAmenities}
        heading="Test Heading"
        itemsToShow={2}
        blurAmenities={true}
        inquiryId="test-inquiry-id"
        isEnhanced={false}
      />
    );
    fireEvent.click(
      screen.getByRole('button', { name: /unlock amenity information/i })
    );
    expect(mockShowInquiryForm).toHaveBeenCalled();
  });
});
