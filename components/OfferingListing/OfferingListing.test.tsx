import mockProvider from '@mocks/data/providers/cambridge-village-of-apex-27502.json';

import { getProviderAmenitiesFromServices } from './OfferingListing';

describe('OfferingListing', () => {
  describe('getProviderAmenitiesFromServices', () => {
    let defaultProvider = mockProvider;

    defaultProvider.services.push({
      id: 'fake',
      category: {
        imageURL:
          'https://canario-staging.s3.amazonaws.com/costs-of-community-images/independent-care.png',
        description: 'Independent Care ',
        name: 'Independent Care'
      },
      amenities: [
        {
          id: '19cd6311-d792-4f0f-947f-2ad86ccb22ea',
          amenityId: 349,
          amenityName: 'Pet restrictions',
          amenityCategoryId: 22,
          amenityCategoryName: 'Pets'
        },
        {
          id: 'aba8c72b-a342-4ab7-9d10-d5a5b8c65b31',
          amenityId: 448,
          amenityName: 'All Inclusive Rent',
          amenityCategoryId: 9,
          amenityCategoryName: 'Financing & Payment Options'
        },
        {
          id: '282e4d19-2716-4892-9e82-b71d5f829ee6',
          amenityId: 450,
          amenityName: 'Rent And Care Fees',
          amenityCategoryId: 9,
          amenityCategoryName: 'Financing & Payment Options'
        }
      ],
      costs: undefined,
      accommodations: []
    });

    const defaultAmenities = getProviderAmenitiesFromServices(defaultProvider);

    it('should return 10 Amenities', () => {
      expect(defaultAmenities.length).toEqual(10);
    });

    it('should return zero Amenities', () => {
      const providerWithNoServices = {
        ...defaultProvider,
        services: []
      };

      const amenitiesWithNoServices = getProviderAmenitiesFromServices(
        providerWithNoServices
      );

      expect(amenitiesWithNoServices.length).toEqual(0);
    });
  });
});
