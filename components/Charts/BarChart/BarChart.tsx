import { Box, Grid, Heading, Text, VStack } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

import { HeadingElements } from '~/@types/heading';

import ProgressChart from '../ProgressChart';

interface BarChartPropsFromAdmin {
  title: string;
  headingElement?: HeadingElements;
  description?: string;
  label: string;
  value: string;
  maxValue: string;
  color: string;
  displayValue: string;
  switchable: {
    field: string;
    itemsLimit?: string;
  };
  chartData: string;
}

interface ChartDataRecord {
  label: string;
  value: number;
  displayValue: string;
  color: string;
}

const BarChart: React.FC<BarChartPropsFromAdmin> = (props) => {
  const {
    title,
    headingElement,
    description,
    label,
    displayValue,
    value,
    maxValue,
    color,
    switchable,
    chartData
  } = props;
  const dataType = switchable.field;
  let json: { data: Array<ChartDataRecord> } | null = null;
  let error: String | null = null;

  try {
    json = JSON.parse(chartData);
    if (!Array.isArray(json)) {
      error = 'Invalid chartData: provided JSON data is not an array';
    }
  } catch {
    error =
      'Invalid chartData: please check your data is a properly formatted JSON Array';
  }
  const jsonData = json?.data || [];
  const itemsLimit = Number(switchable.itemsLimit) || 0;
  const itemsToDisplay = !switchable.itemsLimit
    ? json?.data.length
    : itemsLimit <= jsonData.length
    ? itemsLimit
    : json?.data.length;

  const data = jsonData.slice(0, itemsToDisplay);

  const getValue = (value, maxValue) =>
    (100 * Number(value)) / Number(maxValue);

  return (
    <Box bg="blackAlpha.50" roundedBottom="lg">
      <Container p="8">
        <VStack gap="2" pb="8" align="flex-start">
          <Heading as={headingElement} size="lg">
            {title}
          </Heading>
          {description && <Text>{description}</Text>}
        </VStack>
        {dataType === 'object' ? (
          <Box bg="white" p="5" rounded="md" boxShadow="lg">
            <ProgressChart
              id={label}
              label={label}
              value={getValue(value, maxValue)}
              displayValue={displayValue}
              labelType="heading"
              color={color}
            />
          </Box>
        ) : (
          <Grid
            templateColumns={{
              lg: 'repeat(3, 1fr)',
              sm: 'repeat(2, 1fr)',
              base: '1fr'
            }}
            gap="8"
          >
            {data?.map((item, index) => (
              <Box bg="white" p="5" rounded="md" boxShadow="lg" key={index}>
                <ProgressChart
                  id={item[label]}
                  label={item[label]}
                  value={getValue(item[value], item[maxValue])}
                  displayValue={item[displayValue]}
                  labelType="heading"
                  color={item.color}
                />
              </Box>
            ))}
          </Grid>
        )}
      </Container>
    </Box>
  );
};

export default BarChart;
