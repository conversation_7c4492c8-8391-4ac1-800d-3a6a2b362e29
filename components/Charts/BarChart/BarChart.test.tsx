import { render, screen } from '@utils/test-utils';

import { HeadingElements } from '~/@types/heading';
import axe from '~/axe-helper';

import BarChart from './BarChart';

const mockBarChartProps = {
  title: 'Title',
  headingElement: 'h2' as HeadingElements,
  description: 'Description',
  label: 'Spokane',
  value: '50',
  maxValue: '100',
  color: 'red',
  displayValue: '$50.00',
  switchable: {
    field: 'object'
  },
  chartData: JSON.stringify({
    data: [
      {
        label: 'Texas',
        value: 40,
        maxValue: 100,
        displayValue: '$3,988',
        color: 'red'
      },
      {
        label: 'Texas',
        value: 72,
        maxValue: 100,
        displayValue: '$3,988',
        color: 'blue'
      },
      {
        label: 'Texas',
        value: 35,
        maxValue: 100,
        displayValue: '$3,988',
        color: 'orange'
      },
      {
        label: 'Texas',
        value: 87,
        maxValue: 100,
        displayValue: '$3,988',
        color: 'pink'
      },
      {
        label: 'Texas',
        value: 10,
        maxValue: 100,
        displayValue: '$3,988',
        color: 'yellow'
      }
    ]
  })
};

jest.mock('next/router', () => ({
  useRouter() {
    return {
      asPath: '/some-path/to-provider'
    };
  }
}));

describe('<BarChart />', () => {
  test('renders bar charts without violations', async () => {
    const { container } = render(<BarChart {...mockBarChartProps} />);
    expect(await axe(container)).toHaveNoViolations();
  });

  test('renders correctly object data', async () => {
    render(<BarChart {...mockBarChartProps} />);
    const heading = screen.getByRole('heading', {
      name: 'Title'
    });
    expect(heading).toBeInTheDocument();
    expect(screen.getByText(mockBarChartProps.description)).toBeInTheDocument();
    expect(screen.getByText(mockBarChartProps.label)).toBeVisible();
    expect(screen.getByText(mockBarChartProps.displayValue)).toBeVisible();
  });

  test('renders correctly array data with limit', async () => {
    const mockArrayBarChartProps = {
      ...mockBarChartProps,
      label: 'label',
      value: 'value',
      maxValue: 'maxValue',
      color: 'color',
      displayValue: 'displayValue',
      switchable: {
        field: 'array',
        itemsLimit: '3'
      }
    };
    render(<BarChart {...mockArrayBarChartProps} />);
    expect(screen.getByText(mockBarChartProps.description)).toBeInTheDocument();

    const barChartLabel = screen.getAllByText('Texas');
    expect(barChartLabel.length).toBeGreaterThanOrEqual(3);
    const barCharDisplayValue = screen.getAllByText('$3,988');
    expect(barCharDisplayValue.length).toBeGreaterThanOrEqual(3);
  });
  test('renders correctly array data without limit', async () => {
    const mockArrayBarChartProps = {
      ...mockBarChartProps,
      label: 'label',
      value: 'value',
      maxValue: 'maxValue',
      color: 'color',
      displayValue: 'displayValue',
      switchable: {
        field: 'array',
        itemsLimit: '8'
      }
    };
    render(<BarChart {...mockArrayBarChartProps} />);
    expect(screen.getByText(mockBarChartProps.description)).toBeInTheDocument();

    const barChartLabel = screen.getAllByText('Texas');
    expect(barChartLabel.length).toBeGreaterThanOrEqual(5);
    const barCharDisplayValue = screen.getAllByText('$3,988');
    expect(barCharDisplayValue.length).toBeGreaterThanOrEqual(5);
  });
});
