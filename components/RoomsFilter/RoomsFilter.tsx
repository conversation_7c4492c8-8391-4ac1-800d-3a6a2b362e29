import CheckboxInput from '@components/CheckboxInput';

interface RoomsFilterProps {
  onChange(name: string, value: string[]): void;
  value: string[];
}

export const roomFilterItems = [
  {
    value: 'studio',
    label: 'Studio'
  },
  {
    value: '1-bedroom',
    label: '1 Bedroom'
  },
  {
    value: '2-bedroom',
    label: '2 Bedroom'
  },
  {
    value: '3-bedroom',
    label: '3 Bedroom'
  },
  {
    value: 'respite',
    label: 'Respite / Short-Term Stay'
  }
];

function RoomsFilter({ onChange, value }: RoomsFilterProps) {
  return (
    <CheckboxInput
      name="roomType"
      onChange={onChange}
      items={roomFilterItems}
      value={value}
    />
  );
}

export default RoomsFilter;
