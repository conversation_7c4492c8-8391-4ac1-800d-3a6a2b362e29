import { render, screen } from '@utils/test-utils';

import Modal from './Modal';
import { IModalProps } from './Modal.types';

const mockedProps: IModalProps = {
  title: 'Modal title',
  size: 'md',
  onClose: () => jest.fn()
};

describe('Modal', () => {
  it('should render when visible', () => {
    render(
      <Modal
        visible
        onClose={mockedProps.onClose}
        size={mockedProps.size}
        title={mockedProps.title}
      />
    );

    expect(screen.getByText(mockedProps.title as string)).toBeInTheDocument();
  });

  it('should not render when invisible', () => {
    render(
      <Modal
        onClose={mockedProps.onClose}
        size={mockedProps.size}
        title={mockedProps.title}
      />
    );

    expect(
      screen.queryByText(mockedProps.title as string)
    ).not.toBeInTheDocument();
  });
});
