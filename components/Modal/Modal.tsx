import { Box, Text } from '@chakra-ui/layout';
import {
  Modal as <PERSON>kraModal,
  ModalCloseButton,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalOverlay
} from '@chakra-ui/modal';

import { IModalProps } from './Modal.types';

const Modal: React.FC<IModalProps> = ({
  visible = false,
  allowInnerScroll = false,
  onClose,
  children,
  preTitle,
  title = '',
  postTitle,
  size
}) => {
  return (
    <ChakraModal
      onClose={onClose}
      size={size}
      isOpen={visible}
      preserveScrollBarGap
      returnFocusOnClose
    >
      <ModalOverlay />
      <ModalContent>
        <Box overflow="hidden" borderRadius="md">
          <ModalCloseButton data-testid="modal-close-btn" />
          <Box
            data-testid="modal-content"
            p={8}
            overflowY={allowInnerScroll ? 'scroll' : undefined}
            maxH={allowInnerScroll ? '80vh' : undefined}
            className={allowInnerScroll ? 'custom-scrollbar' : undefined}
          >
            <ModalHeader padding={0} textAlign="center">
              {preTitle && (
                <Text fontWeight="400" fontSize="md" paddingBottom={4}>
                  {preTitle}
                </Text>
              )}
              {title}
              {postTitle && (
                <Text
                  fontWeight="400"
                  fontSize="md"
                  paddingTop={4}
                  paddingBottom={6}
                >
                  {postTitle}
                </Text>
              )}
            </ModalHeader>
            {children}
          </Box>
        </Box>
      </ModalContent>
    </ChakraModal>
  );
};

export default Modal;
