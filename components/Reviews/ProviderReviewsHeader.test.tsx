import { render, screen } from '@utils/test-utils';
import { mockReviews } from '@utils/test-utils/mocks/reviews';

import { Award, ParametricReviewData } from '~/contexts/Provider';

import ProviderReviewsHeader from './ProviderReviewsHeader';

describe('<ProviderReviewsHeader />', () => {
  it('should render the header with the correct awards and links', () => {
    const caringStars: Award[] = [
      {
        name: 'Caring Stars',
        year: 2025
      }
    ];
    const homeCarePulse: Award[] = [
      {
        year: 2025,
        name: 'Provider of Choice'
      },
      {
        year: 2025,
        name: 'Employer of Choice'
      },
      {
        year: 2025,
        name: 'Leader in Experience'
      },
      {
        year: 2025,
        name: 'Home Care Pulse Certified'
      }
    ];

    const parametricReviewData: ParametricReviewData = {
      activitiesName: '',
      activitiesCount: '0',
      activitiesRatingValue: '0',
      activitiesMinimumRating: '0',
      activitiesMaximumRating: '0',
      facilitiesName: '',
      facilitiesCount: '0',
      facilitiesRatingValue: '0',
      facilitiesMinimumRating: '0',
      facilitiesMaximumRating: '0',
      foodName: '',
      foodCount: '0',
      foodRatingValue: '0',
      foodMinimumRating: '0',
      foodMaximumRating: '0',
      staffName: '',
      staffCount: '0',
      staffRatingValue: '0',
      staffMinimumRating: '0',
      staffMaximumRating: '0',
      valueName: '',
      valueCount: '0',
      valueRatingValue: '0',
      valueMinimumRating: '0',
      valueMaximumRating: '0'
      // Add other required properties here
    };

    render(
      <ProviderReviewsHeader
        responsiveStyles={(styles) => styles}
        reviewCount={mockReviews.length}
        averageRating={'4.5'}
        caringStars={caringStars}
        homeCarePulse={homeCarePulse}
        parametricReviewData={parametricReviewData}
        providerName="Provider Name"
        description=""
        includeAggregateReview={true}
        includeCountOfReview={true}
        includeFallbackIfNoReviewAvailable={true}
        displayBadges={true}
        displayHomeCarePulseBadges={true}
        includeParametricReview={true}
        ratingColor=""
        ratingColorRange=""
        parametricReviewColor=""
        reviewGuidelinesURL=""
        contactUsURL=""
        defaultOpenModal={false}
        metadata={{ '@id': 'test-id' }}
      />
    );

    expect(
      screen.getByTestId('Provider of Choice-2025-badge')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('Employer of Choice-2025-badge')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('Leader in Experience-2025-badge')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('Home Care Pulse Certified-2025-badge')
    ).toBeInTheDocument();

    expect(screen.getByTestId('Provider of Choice-2025-badge')).toHaveAttribute(
      'href',
      '/bestseniorliving/home-care-pulse-awards/'
    );
    expect(screen.getByTestId('Employer of Choice-2025-badge')).toHaveAttribute(
      'href',
      '/bestseniorliving/home-care-pulse-awards/'
    );
    expect(
      screen.getByTestId('Leader in Experience-2025-badge')
    ).toHaveAttribute('href', '/bestseniorliving/home-care-pulse-awards/');
    expect(
      screen.getByTestId('Home Care Pulse Certified-2025-badge')
    ).toHaveAttribute('href', '/bestseniorliving/home-care-pulse-awards/');
  });
});
