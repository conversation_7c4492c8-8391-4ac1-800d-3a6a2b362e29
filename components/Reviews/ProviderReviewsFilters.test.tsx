import { fireEvent, render, screen } from '@testing-library/react';
import { mockReviews } from 'utils/test-utils/mocks/reviews';

import ProviderReviewsFilters from './ProviderReviewsFilters';

describe('<ProviderReviewsFilters />', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  test('renders star rating checkboxes', () => {
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={() => {}}
        resetAll={() => {}}
      />
    );

    expect(screen.getByTestId('star-5')).toBeInTheDocument();
    expect(screen.getByTestId('star-4')).toBeInTheDocument();
    expect(screen.getByTestId('star-3')).toBeInTheDocument();
    expect(screen.getByTestId('star-2')).toBeInTheDocument();
    expect(screen.getByTestId('star-1')).toBeInTheDocument();
  });

  test('selects star rating checkbox', () => {
    const setFiltersMock = jest.fn();
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={setFiltersMock}
        resetAll={() => {}}
      />
    );

    const starCheckbox = screen.getByTestId('star-5');
    fireEvent.click(starCheckbox);

    expect(setFiltersMock).toHaveBeenCalledWith({
      selectedStars: [5],
      selectedCareTypes: [],
      selectedRelationships: []
    });
  });

  test('renders care type checkboxes', () => {
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={() => {}}
        resetAll={() => {}}
      />
    );

    expect(
      screen.getByRole('checkbox', {
        name: 'Assisted Living (4)'
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('checkbox', {
        name: 'Independent Living (1)'
      })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('checkbox', {
        name: 'Memory Care (1)'
      })
    ).toBeInTheDocument();
  });

  test('selects care type checkbox', () => {
    const setFiltersMock = jest.fn();
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={setFiltersMock}
        resetAll={() => {}}
      />
    );

    const careTypeCheckbox = screen.getByRole('checkbox', {
      name: 'Assisted Living (4)'
    });
    fireEvent.click(careTypeCheckbox);

    expect(setFiltersMock).toHaveBeenCalledWith({
      selectedStars: [],
      selectedCareTypes: ['Assisted Living'],
      selectedRelationships: []
    });
  });

  test('renders relationship checkboxes', () => {
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={() => {}}
        resetAll={() => {}}
      />
    );

    expect(
      screen.getByLabelText('Family/friends of residents (2)')
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Senior residents (2)')).toBeInTheDocument();
    expect(
      screen.getByLabelText('Visitors/tour takers (1)')
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Past clients (1)')).toBeInTheDocument();
  });

  test('selects relationship checkbox', () => {
    const setFiltersMock = jest.fn();
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={setFiltersMock}
        resetAll={() => {}}
      />
    );

    const relationshipCheckbox = screen.getByLabelText(
      'Family/friends of residents (2)'
    );
    fireEvent.click(relationshipCheckbox);

    expect(setFiltersMock).toHaveBeenCalledWith({
      selectedStars: [],
      selectedCareTypes: [],
      selectedRelationships: [
        'I am a friend or relative of a current/past resident',
        'I am a friend or relative of a resident'
      ]
    });
  });

  test('clears all filters', async () => {
    const setFiltersMock = jest.fn();
    render(
      <ProviderReviewsFilters
        isOpen={true}
        reviews={mockReviews}
        setFilters={setFiltersMock}
        resetAll={() => {}}
      />
    );

    const resetButton = screen.getAllByText('Reset All');

    fireEvent.click(resetButton[0]);

    expect(setFiltersMock).toHaveBeenCalledWith({
      selectedStars: [],
      selectedCareTypes: [],
      selectedRelationships: []
    });
  });
});
