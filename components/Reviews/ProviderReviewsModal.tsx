import { Button } from '@chakra-ui/button';
import { Text } from '@chakra-ui/layout';
import {
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay
} from '@chakra-ui/modal';
import HtmlToReact from '@components/HtmlToReact';

import ProviderReviewResponse from './ProviderReviewResponse';
import ProviderReviewsCardHeader from './ProviderReviewsCardHeader';

interface ProviderReviewsModalProps {
  author: string;
  reviewText: string;
  createdAt?: string;
  rating: number;
  title: string;
  providerResponse?: string | null;
  serviceCategoryName: string | undefined;
  isOpen: boolean;
  onClose: () => void;
  handleReadLess: () => void;
}

const ProviderReviewsModal: React.FC<ProviderReviewsModalProps> = ({
  author,
  reviewText,
  createdAt,
  rating,
  title,
  providerResponse,
  serviceCategoryName,
  isOpen,
  onClose,
  handleReadLess
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <ProviderReviewsCardHeader
            author={author}
            createdAt={createdAt}
            rating={rating}
            title={title}
            serviceCategoryName={serviceCategoryName}
          />
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text fontSize="md" whiteSpace="pre-line">
            {HtmlToReact({ html: reviewText })}
          </Text>
          {providerResponse && (
            <ProviderReviewResponse response={providerResponse} />
          )}
        </ModalBody>

        <ModalFooter>
          <Button
            variant="outline"
            colorScheme="primary"
            onClick={handleReadLess}
          >
            Read Less
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ProviderReviewsModal;
