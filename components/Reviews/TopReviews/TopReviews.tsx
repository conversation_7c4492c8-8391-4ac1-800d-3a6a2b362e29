import { Grid } from '@chakra-ui/layout';
import dynamic from 'next/dynamic';
const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);
import { Section } from '@components/Sections';

import { HeadingElements } from '~/@types/heading';
import { ProviderReview } from '~/types/reviews';

import ProviderReviewsCard from '../ProviderReviewsCard';

export interface TopReviewProps {
  title: string;
  text?: string;
  itemsToShow: string;
  headingElement?: HeadingElements;
  data?: ProviderReview[];
}

const TopReviews: React.FC<TopReviewProps> = ({
  title,
  text,
  itemsToShow,
  headingElement,
  data
}: TopReviewProps) => {
  const hasReviews = data?.length;

  if (!hasReviews) return <></>;

  return (
    <Container bg="gray.50" id="top_reviews_section" mt={16}>
      <Section title={title} headingElement={headingElement} richText={text} />
      {hasReviews && (
        <Grid templateColumns={{ lg: 'repeat(3, 1fr)' }} gap="4" mt={8}>
          {data.map((review, index) => (
            <a href={review.path} key={review.id}>
              <ProviderReviewsCard
                author={review.authorName}
                reviewText={review.content}
                createdAt={review.createdAt}
                rating={review.rating}
                title={review.title}
                isVisible={index < parseInt(itemsToShow)}
                serviceCategoryName={review.serviceCategoryName}
                subtitle={`Review of ${review.resourceName}`}
                displayButton={false}
              />
            </a>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default TopReviews;
