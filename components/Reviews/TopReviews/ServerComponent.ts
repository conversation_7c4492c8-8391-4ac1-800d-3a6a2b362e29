import { getReviews } from '@services/review/api';
import { getCityFromURL } from '@utils/getCityFromURL';
import { getStateAndCountyFromPageContext } from '@utils/getStateAndCountyFromPage';
import { getStateFromURL } from '@utils/getStateFromURL';
import { GetServerSidePropsContext } from 'next';
import { ParsedUrlQuery } from 'querystring';

import { findSiteForContext, SiteDefinition } from '~/contexts/SiteContext';
import { ProviderReview } from '~/types/reviews';

import { TopReviewProps } from './TopReviews';

export const getServerSideComponentProps = async (
  props: TopReviewProps,
  context: GetServerSidePropsContext
): Promise<ProviderReview[]> => {
  const site: SiteDefinition = findSiteForContext(context);
  const domain = site.path;
  const params = context.params as ParsedUrlQuery;
  const state = getStateFromURL(params) || '';
  const city = getCityFromURL(params) || '';
  const { county } = getStateAndCountyFromPageContext(params) || '';
  const { itemsToShow } = props;
  const topReviews = await getReviews({
    city,
    state,
    county,
    domain,
    limit: itemsToShow
  });

  return topReviews || [];
};
