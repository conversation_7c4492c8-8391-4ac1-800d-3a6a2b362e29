import userEvent from '@testing-library/user-event';

import axe from '~/axe-helper';
import SiteContext, { SiteDefinition } from '~/contexts/SiteContext';
import { render, screen, waitFor } from '~/utils/test-utils';

import ProviderReviewsCard from './ProviderReviewsCard';

const mockReview = {
  id: '1',
  createdAt: new Date(2020, 0, 1).toISOString(),
  rating: 5,
  title: 'Some review title',
  content: 'Some review content',
  authorUrl: 'https://www.google.com',
  authorName: 'Some author name',
  providerResponse: 'Some provider response'
};
describe('<ProviderReviews />', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  test('renders the card without violations', async () => {
    const { container } = render(
      <ProviderReviewsCard
        author={mockReview.authorName}
        createdAt={mockReview.createdAt}
        rating={mockReview.rating}
        reviewText={mockReview.content}
        title={mockReview.title}
        providerResponse={mockReview.providerResponse}
        isVisible={true}
      />
    );

    expect(screen.getByText(mockReview.authorName)).toBeVisible();
    expect(screen.getByText('January 1, 2020')).toBeVisible();
    expect(screen.getByText(mockReview.title)).toBeVisible();
    expect(screen.getByText(mockReview.content)).toBeInTheDocument();
    expect(await axe(container)).toHaveNoViolations();
  });

  test('read more opens a modal with more information', async () => {
    render(
      <SiteContext.Provider
        value={
          { site: { path: 'seniorhomes.com' } } as { site: SiteDefinition }
        }
      >
        <ProviderReviewsCard
          author={mockReview.authorName}
          createdAt={mockReview.createdAt}
          rating={mockReview.rating}
          reviewText={mockReview.content}
          title={mockReview.title}
          providerResponse={mockReview.providerResponse}
          isVisible={true}
        />
      </SiteContext.Provider>
    );

    // Modal closed
    expect(
      screen.queryByRole('button', { name: /read less/i })
    ).not.toBeInTheDocument();
    expect(screen.getByText(mockReview.providerResponse)).toBeInTheDocument();

    userEvent.click(screen.getByRole('button', { name: /read more/i }));

    // Modal opened
    await waitFor(() => expect(screen.getByRole('dialog')).toBeVisible(), {
      timeout: 20000
    });

    userEvent.click(screen.getByRole('button', { name: /read less/i }));

    // Modal closed again
    await waitFor(() => expect(screen.getByRole('dialog')).not.toBeVisible(), {
      timeout: 20000
    });
  }, 10000);

  test("modal does not display provider response when there isn't one", async () => {
    render(
      <ProviderReviewsCard
        author={mockReview.authorName}
        createdAt={mockReview.createdAt}
        rating={mockReview.rating}
        reviewText={mockReview.content}
        title={mockReview.title}
        providerResponse={null}
        isVisible={true}
      />
    );

    userEvent.click(screen.getByRole('button', { name: /read more/i }));

    await screen.findByText(mockReview.title);
    expect(screen.queryByText(/Provider response/i)).not.toBeInTheDocument();
  });

  // This test is skipped because the html entities are not being decoded in the component, they are being decoded in the server.
  test.skip('handles html entities in review text', async () => {
    render(
      <ProviderReviewsCard
        author={mockReview.authorName}
        createdAt={mockReview.createdAt}
        rating={mockReview.rating}
        reviewText={'Hello &amp; World'}
        title={mockReview.title}
        providerResponse={mockReview.providerResponse}
        isVisible={true}
      />
    );

    expect(screen.getByText('Hello & World')).toBeInTheDocument();
    expect(screen.queryByText(/&amp;/)).not.toBeInTheDocument();
  });
});
