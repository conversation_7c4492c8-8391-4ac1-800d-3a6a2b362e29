import { sortReviews } from './ProviderReviewsList';

describe('sortReviews', () => {
  const reviews = [
    {
      rating: 4,
      createdAt: '2022-01-01'
    },
    {
      rating: 5,
      createdAt: '2022-01-02'
    },
    {
      rating: 3,
      createdAt: '2022-01-03'
    },
    {
      rating: 4,
      createdAt: '2021-01-01'
    }
  ];

  test('should sort reviews by rating in descending order and by date in descending order', () => {
    const sortedReviews = sortReviews(reviews, 'rating_review_desc');
    expect(sortedReviews).toEqual([
      {
        rating: 5,
        createdAt: '2022-01-02'
      },
      {
        rating: 4,
        createdAt: '2022-01-01'
      },
      {
        rating: 4,
        createdAt: '2021-01-01'
      },
      {
        rating: 3,
        createdAt: '2022-01-03'
      }
    ]);
  });

  test('should sort reviews by rating in ascending order and by date in descending order', () => {
    const sortedReviews = sortReviews(reviews, 'rating_review_asc');
    expect(sortedReviews).toEqual([
      {
        rating: 3,
        createdAt: '2022-01-03'
      },
      {
        rating: 4,
        createdAt: '2022-01-01'
      },
      {
        rating: 4,
        createdAt: '2021-01-01'
      },
      {
        rating: 5,
        createdAt: '2022-01-02'
      }
    ]);
  });

  test('should sort reviews by date in ascending order', () => {
    const sortedReviews = sortReviews(reviews, 'date_review_asc');
    expect(sortedReviews).toEqual([
      {
        rating: 4,
        createdAt: '2021-01-01'
      },
      {
        rating: 4,
        createdAt: '2022-01-01'
      },
      {
        rating: 5,
        createdAt: '2022-01-02'
      },
      {
        rating: 3,
        createdAt: '2022-01-03'
      }
    ]);
  });

  test('should sort reviews by date in descending order (default)', () => {
    const sortedReviews = sortReviews(reviews, 'unknown_order');
    expect(sortedReviews).toEqual([
      {
        rating: 3,
        createdAt: '2022-01-03'
      },
      {
        rating: 5,
        createdAt: '2022-01-02'
      },
      {
        rating: 4,
        createdAt: '2022-01-01'
      },
      {
        rating: 4,
        createdAt: '2021-01-01'
      }
    ]);
  });
});
