import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mockReviews } from 'utils/test-utils/mocks/reviews';

import ProviderReviewsSearch from './ProviderReviewsSearch';

describe('ProviderReviewsSearch', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  it('should render the search input', () => {
    render(
      <ProviderReviewsSearch
        originalReviews={mockReviews}
        reviewTotal={mockReviews.length}
        searchValue={''}
        filters={{
          selectedStars: [],
          selectedCareTypes: [],
          selectedRelationships: []
        }}
        sortOrder={''}
        setSearchValue={() => {}}
        setFilters={() => {}}
        setSortOrder={() => {}}
        metadataId={'00000000-0000-0000-0000-000000000000'}
        resetAll={() => {}}
      />
    );
    const searchInput = screen.getByPlaceholderText('Search Reviews');
    expect(searchInput).toBeInTheDocument();
  });

  it('should update the search value when input changes', async () => {
    const setSearchValue = jest.fn();

    render(
      <ProviderReviewsSearch
        originalReviews={mockReviews}
        reviewTotal={mockReviews.length}
        searchValue={''}
        filters={{
          selectedStars: [],
          selectedCareTypes: [],
          selectedRelationships: []
        }}
        sortOrder={''}
        setSearchValue={setSearchValue}
        setFilters={() => {}}
        setSortOrder={() => {}}
        metadataId={'00000000-0000-0000-0000-000000000000'}
        resetAll={() => {}}
      />
    );
    const searchInput = screen.getByPlaceholderText(
      'Search Reviews'
    ) as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: 'excellent' } });
    await waitFor(() => {
      expect(setSearchValue).toHaveBeenCalledWith('excellent');
    });
  });

  it('should clear the search value when clear button is clicked', async () => {
    const setFilters = jest.fn();

    render(
      <ProviderReviewsSearch
        originalReviews={mockReviews}
        reviewTotal={mockReviews.length}
        searchValue={'great'}
        filters={{
          selectedStars: [],
          selectedCareTypes: [],
          selectedRelationships: []
        }}
        sortOrder={''}
        setSearchValue={() => {}}
        setFilters={setFilters}
        setSortOrder={() => {}}
        metadataId={'00000000-0000-0000-0000-000000000000'}
        resetAll={() => {}}
      />
    );

    const clearButton = screen.getByTestId('clear-button');
    fireEvent.click(clearButton);
    await waitFor(() => {
      expect(setFilters).toHaveBeenCalledWith({
        selectedStars: [],
        selectedCareTypes: [],
        selectedRelationships: []
      });
    });
  });
  it('should render the sort button', () => {
    render(
      <ProviderReviewsSearch
        originalReviews={mockReviews}
        reviewTotal={mockReviews.length}
        searchValue={''}
        filters={{
          selectedStars: [],
          selectedCareTypes: [],
          selectedRelationships: []
        }}
        sortOrder={''}
        setSearchValue={() => {}}
        setFilters={() => {}}
        setSortOrder={() => {}}
        metadataId={'00000000-0000-0000-0000-000000000000'}
        resetAll={() => {}}
      />
    );
    const sortButton = screen.getByText('Sort By');
    expect(sortButton).toBeInTheDocument();
  });
  it('should render the sort options when sort button is clicked', async () => {
    const setSortOrder = jest.fn();

    render(
      <ProviderReviewsSearch
        originalReviews={mockReviews}
        reviewTotal={mockReviews.length}
        searchValue={''}
        filters={{
          selectedStars: [],
          selectedCareTypes: [],
          selectedRelationships: []
        }}
        sortOrder={''}
        setSearchValue={() => {}}
        setFilters={() => {}}
        setSortOrder={setSortOrder}
        metadataId={'00000000-0000-0000-0000-000000000000'}
        resetAll={() => {}}
      />
    );
    const sortMenuInput = screen.getByText('Newest') as HTMLInputElement;
    fireEvent.click(sortMenuInput);
    await waitFor(() => {
      expect(setSortOrder).toHaveBeenCalledWith('date_review_desc');
    });
  });
});
