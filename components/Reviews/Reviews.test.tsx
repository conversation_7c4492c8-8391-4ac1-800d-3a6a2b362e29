import { mockProvider } from '@mocks/use-provider-fallback-mock';
import userEvent from '@testing-library/user-event';
import {
  fireEvent,
  render,
  screen,
  setDesktopScreen,
  waitFor
} from '@utils/test-utils';

import axe from '~/axe-helper';
import { ProviderContextWrapper } from '~/contexts/Provider';
import SiteContext, { SiteDefinition } from '~/contexts/SiteContext';

import Reviews from './Reviews';

const mockReviewProps = {
  title: 'Reviews',
  headingElement: 'h3',
  description: '',
  displayBadges: true,
  includeAggregateReview: true,
  includeCountOfReview: true,
  includeFallbackIfNoReviewAvailable: true,
  includeParametricReview: true,
  orderBy: 'most_recent_review_desc',
  activitiesName: 'Activities',
  activitiesCount: '2',
  activitiesRatingValue: '4.5',
  activitiesMinimumRating: '',
  activitiesMaximumRating: '',
  facilitiesName: 'Facilities',
  facilitiesCount: '2',
  facilitiesRatingValue: '3.5',
  facilitiesMinimumRating: '',
  facilitiesMaximumRating: '',
  foodName: 'Food',
  foodCount: '2',
  foodRatingValue: '3.5',
  foodMinimumRating: '',
  foodMaximumRating: '',
  staffName: 'Staff',
  staffCount: '2',
  staffRatingValue: '4.5',
  staffMinimumRating: '',
  staffMaximumRating: '',
  valueName: 'Value',
  valueCount: '2',
  valueRatingValue: '4.5',
  valueMinimumRating: '',
  valueMaximumRating: '',
  metadata: {
    '@id': 'id'
  }
};

jest.setTimeout(30000);
function renderReviewsWithProvider(provider, reviewProps, domain = '') {
  return render(
    <SiteContext.Provider
      value={{ site: { path: domain } } as { site: SiteDefinition }}
    >
      <ProviderContextWrapper provider={provider}>
        <Reviews {...reviewProps} />
      </ProviderContextWrapper>
    </SiteContext.Provider>
  );
}
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => {
    return {
      asPath: '/some-path/to-provider'
    };
  })
}));

describe('<Reviews />', () => {
  test('renders reviews and badges without violations', async () => {
    const { container } = renderReviewsWithProvider(
      mockProvider,
      mockReviewProps
    );
    expect(await axe(container)).toHaveNoViolations();
  });

  test('should render empty view without violations', async () => {
    const { container } = renderReviewsWithProvider(null, mockReviewProps);
    expect(screen.queryByText('Read all reviews')).not.toBeInTheDocument();
    expect(screen.queryByText(/0 reviews/i)).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/star rating for/i)).not.toBeInTheDocument();
    expect(await axe(container)).toHaveNoViolations();
  });

  describe('Parametrized ratings', () => {
    test('renders all the parametrized ratings', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps);
      expect(
        screen.getByLabelText(/4.5 star rating for activities/i)
      ).toBeVisible();
      expect(
        screen.getByLabelText(/3.5 star rating for facilities/i)
      ).toBeVisible();
      expect(screen.getByLabelText(/3.5 star rating for food/i)).toBeVisible();
      expect(screen.getByLabelText(/4.5 star rating for staff/i)).toBeVisible();
      expect(screen.getByLabelText(/4.5 star rating for value/i)).toBeVisible();
    });

    test('renders element with correct ID', () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps);
      expect(screen.getByTestId('reviews')).toBeInTheDocument();
      expect(screen.queryByText('Reviews')).toBeInTheDocument();
    });

    test('renders only parametrized ratings with counts higher than 0', async () => {
      const newMockReviewProps = {
        ...mockReviewProps,
        foodCount: '',
        foodRatingValue: '',
        staffCount: '',
        staffRatingValue: '',
        valueCount: '',
        valueRatingValue: ''
      };
      renderReviewsWithProvider(mockProvider, newMockReviewProps);

      expect(
        screen.getByLabelText(/4.5 star rating for activities/i)
      ).toBeVisible();
      expect(
        screen.getByLabelText(/3.5 star rating for facilities/i)
      ).toBeVisible();
      expect(
        screen.queryByLabelText(/star rating for food/i)
      ).not.toBeInTheDocument();
      expect(
        screen.queryByLabelText(/star rating for staff/i)
      ).not.toBeInTheDocument();
      expect(
        screen.queryByLabelText(/star rating for value/i)
      ).not.toBeInTheDocument();
    });
  });

  describe('Reviews', () => {
    test('should render average rating and the review count', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps);
      expect(screen.getAllByLabelText(/5 star rating/i)).toHaveLength(9);
      expect(screen.getAllByLabelText(/4 star rating/i)).toHaveLength(2);
      expect(screen.getAllByLabelText(/3 star rating/i)).toHaveLength(2);
    });
    test('should render reviews', () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps);
      expect(
        screen.getByText(mockProvider.reviews[0].content)
      ).toBeInTheDocument();
      expect(
        screen.getByText(mockProvider.reviews[0].authorName)
      ).toBeInTheDocument();
      expect(screen.getByLabelText('4.9 star rating')).toBeInTheDocument();
    });

    test('should render service category name if caring.com', () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps, 'caring.com');
      const serviceCategoryNames = screen.getAllByText(
        mockProvider.reviews[0].serviceCategoryName
      );
      expect(serviceCategoryNames.length).toBe(mockProvider.reviews.length);
    });
  });

  describe('View all expand/collapse', () => {
    const mockMoreReviews = {
      ...mockProvider,
      reviews: [
        { ...mockProvider.reviews[0], content: 'review 1', id: '1' },
        { ...mockProvider.reviews[0], content: 'review 2', id: '2' },
        { ...mockProvider.reviews[0], content: 'review 3', id: '3' },
        { ...mockProvider.reviews[0], content: 'review 4', id: '4' },
        { ...mockProvider.reviews[0], content: 'review 5', id: '5' },
        { ...mockProvider.reviews[0], content: 'review 6', id: '6' },
        { ...mockProvider.reviews[0], content: 'review 7', id: '7' },
        { ...mockProvider.reviews[0], content: 'review 8', id: '8' },
        { ...mockProvider.reviews[0], content: 'review 9', id: '9' },
        { ...mockProvider.reviews[0], content: 'review 10', id: '10' },
        { ...mockProvider.reviews[0], content: 'review 11', id: '11' },
        { ...mockProvider.reviews[0], content: 'review 12', id: '12' },
        { ...mockProvider.reviews[0], content: 'review 13', id: '13' },
        { ...mockProvider.reviews[0], content: 'review 14', id: '14' },
        { ...mockProvider.reviews[0], content: 'review 15', id: '15' },
        { ...mockProvider.reviews[0], content: 'review 16', id: '16' }
      ]
    };

    test('should not render read more reviews button when there are only 6 reviews', () => {
      const mockMoreReviews = {
        ...mockProvider,
        reviews: [
          { ...mockProvider.reviews[0], content: 'review 1', id: '1' },
          { ...mockProvider.reviews[0], content: 'review 2', id: '2' },
          { ...mockProvider.reviews[0], content: 'review 3', id: '3' },
          { ...mockProvider.reviews[0], content: 'review 4', id: '4' },
          { ...mockProvider.reviews[0], content: 'review 5', id: '5' },
          { ...mockProvider.reviews[0], content: 'review 6', id: '6' }
        ]
      };
      renderReviewsWithProvider(mockMoreReviews, mockReviewProps);
      expect(
        screen.queryByRole('button', { name: /read more reviews/i })
      ).not.toBeInTheDocument();
    });
    test('should render the first 6 reviews', () => {
      renderReviewsWithProvider(mockMoreReviews, mockReviewProps);
      expect(screen.getByText('review 1')).toBeInTheDocument();
      expect(screen.getByText('review 6')).toBeInTheDocument();
      expect(screen.getByText('review 8')).not.toBeVisible();
      expect(screen.queryByText('review 10')).not.toBeVisible();
    });

    test("should render 8+ reviews after clicking 'Read more reviews'", async () => {
      renderReviewsWithProvider(mockMoreReviews, mockReviewProps);

      expect(screen.queryByText('review 10')).not.toBeVisible();

      userEvent.click(
        screen.getByRole('button', { name: 'Read more reviews' })
      );

      await waitFor(() => expect(screen.getByText('review 10')).toBeVisible(), {
        timeout: 20000
      });

      await waitFor(
        () => expect(screen.getByText('review 16')).not.toBeVisible(),
        {
          timeout: 20000
        }
      );

      userEvent.click(
        screen.getByRole('button', { name: 'Read more reviews' })
      );
      await waitFor(
        () => expect(screen.getByText('review 16')).not.toBeVisible(),
        {
          timeout: 20000
        }
      );
    }, 20000);
  });

  describe('Caring star badges', () => {
    test('should not include any caring star badges if they are empty', async () => {
      const mockReviewsWithoutStars = {
        ...mockProvider,
        caringStars: []
      };
      renderReviewsWithProvider(mockReviewsWithoutStars, mockReviewProps);

      expect(
        screen.queryByAltText(/Top rated on Caring.com on/i)
      ).not.toBeInTheDocument();
    });

    test('Desktop - displays caring star badges when they are present', async () => {
      setDesktopScreen();
      renderReviewsWithProvider(mockProvider, mockReviewProps);

      expect(screen.getByAltText(/Top rated on Caring.com/i)).toBeVisible();
    });

    test('Mobile - displays caring star badges when they are present', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewProps);

      expect(screen.getByAltText(/Top rated on Caring.com/i)).toBeVisible();
    });
  });

  describe('Review modal', () => {
    test('should open review modal if defaultOpenModal is true', async () => {
      renderReviewsWithProvider(
        {
          ...mockProvider,
          reviewCount: mockProvider.reviewCount
        },
        { ...mockReviewProps, defaultOpenModal: true },
        'caring.com'
      );
      const modal = screen.getByRole('dialog');
      await waitFor(() => {
        expect(modal).toBeVisible();
      });
    });

    test('should not open review modal if defaultOpenModal is false', async () => {
      renderReviewsWithProvider(
        {
          ...mockProvider,
          reviewCount: mockProvider.reviewCount
        },
        { ...mockReviewProps, defaultOpenModal: false },
        'caring.com'
      );

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });
  });

  describe('Review search', () => {
    const mockReviewPropsWithSearch = {
      ...mockReviewProps,
      enableReviewSearch: true
    };
    test('should filter reviews by search value', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithSearch);

      const searchInput = screen.getByPlaceholderText('Search Reviews');
      fireEvent.change(searchInput, { target: { value: 'excellent' } });

      await waitFor(() => {
        expect(screen.getByText('1 review mentions "excellent"')).toBeVisible();
        expect(
          screen.queryByText('2 reviews mention "FOOD"')
        ).not.toBeInTheDocument();
      });
    });

    test('should not display search information when search value is less than 3 characters', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithSearch);
      const searchInput = screen.getByPlaceholderText('Search Reviews');
      fireEvent.change(searchInput, { target: { value: 'fo' } });

      await waitFor(() => {
        expect(
          screen.queryByText('2 reviews mention "fo"')
        ).not.toBeInTheDocument();
      });
    });

    test('should not display search information when no reviews are found', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithSearch);

      const searchInput = screen.getByPlaceholderText('Search Reviews');
      fireEvent.change(searchInput, { target: { value: 'bad' } });

      await waitFor(() => {
        expect(screen.getByText('No reviews found')).toBeVisible();
        expect(
          screen.getByText('Try adjusting your search to see more reviews.')
        ).toBeVisible();
        expect(screen.queryByText('bad')).not.toBeInTheDocument();
      });
    });

    test('should clear search value when clear button is clicked', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithSearch);

      const searchInput = screen.getByPlaceholderText('Search Reviews');
      fireEvent.change(searchInput, { target: { value: 'excellent' } });
      await waitFor(() => {
        expect(
          screen.getByText('1 review mentions "excellent"')
        ).toBeInTheDocument();
      });

      const clearButton = screen.getByTestId('clear-button');
      userEvent.click(clearButton);
      await waitFor(() => {
        expect(
          screen.queryByText('1 review mentions "excellent"')
        ).not.toBeInTheDocument();
      });
    });
  });
  describe('Review filters', () => {
    beforeEach(() => {
      const mockAnalytics = {
        track: jest.fn()
      };
      window.tracking = mockAnalytics;
    });
    const mockReviewPropsWithFilters = {
      ...mockReviewProps,
      enableReviewSearch: true
    };

    test('should filter reviews by selected stars', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithFilters);
      const showFilters = screen.getByRole('button', { name: 'Show Filters' });
      fireEvent.click(showFilters);

      await waitFor(() => {
        const starFilterCheckbox = screen.getByTestId('star-5');
        userEvent.click(starFilterCheckbox);
        expect(screen.getByText('2 reviews found')).toBeVisible();
      });
    });

    test('should filter reviews by selected care types', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithFilters);
      const showFilters = screen.getByRole('button', { name: 'Show Filters' });
      fireEvent.click(showFilters);
      const filterCareType = screen.getByRole('checkbox', {
        name: 'Independent Living (8)'
      });
      fireEvent.click(filterCareType);

      await waitFor(() => {
        expect(screen.getByText('8 reviews found')).toBeVisible();
      });
    });

    test('should filter reviews by selected relationships', async () => {
      renderReviewsWithProvider(mockProvider, mockReviewPropsWithFilters);
      const showFilters = screen.getByRole('button', { name: 'Show Filters' });
      fireEvent.click(showFilters);
      const filterRelationship = screen.getByRole('checkbox', {
        name: 'Family/friends of residents (4)'
      });
      fireEvent.click(filterRelationship);

      await waitFor(() => {
        expect(screen.getByText('4 reviews found')).toBeVisible();
      });
    });
  });
});
