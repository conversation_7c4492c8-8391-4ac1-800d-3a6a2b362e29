'use client';

import { Box } from '@chakra-ui/layout';
import { useBreakpointValue } from '@chakra-ui/media-query';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import Heading from '@components/Heading';
import { truncateText } from '@utils/truncateText';
import dynamic from 'next/dynamic';
import { useState } from 'react';

const Container = dynamic(
  () => import('@components/LayoutStructure/Container')
);

const MAX_TEXT_LENGTH = 250;

export interface GeoIntroProps {
  title?: string;
  text?: string;
}

const GeoIntro = ({ title, text = '' }: GeoIntroProps): JSX.Element => {
  const [showingMoreIntro, setShowingMoreIntro] = useState(false);
  const isMobile = useBreakpointValue({ base: true, md: false });
  const shouldReduceIntro = text.length > MAX_TEXT_LENGTH && isMobile;
  const { textStart, textEnd } = truncateText(text, MAX_TEXT_LENGTH);
  const content = shouldReduceIntro
    ? textStart +
      (showingMoreIntro ? textEnd : `... <div class="hidden">${textEnd}</div>`)
    : text;

  if (!title) return <></>;

  return (
    <Container>
      <Box maxW="100%">
        {title && (
          <Heading
            headingElement="h1"
            headingSize="2xl"
            title={title}
            withContainer={false}
          />
        )}
        {text && (
          <>
            <Box
              className="magnolia-text"
              fontSize="lg"
              mt="2"
              dangerouslySetInnerHTML={{
                __html: content
              }}
            />
            {shouldReduceIntro && (
              <Button
                colorScheme="primary"
                size="xs"
                variant="link"
                zIndex={10}
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.preventDefault();
                  setShowingMoreIntro(!showingMoreIntro);
                }}
                elementAction={
                  showingMoreIntro
                    ? ElementActions.COLLAPSE
                    : ElementActions.EXPAND
                }
                elementName={ElementNames.GENERIC_BUTTON}
                elementType={ElementTypes.BUTTON}
              >
                READ {showingMoreIntro ? 'LESS' : 'MORE'}
              </Button>
            )}
          </>
        )}
      </Box>
    </Container>
  );
};

export default GeoIntro;
