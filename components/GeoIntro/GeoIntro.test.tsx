import { mockSection, render } from '@utils/test-utils';

jest.mock('@chakra-ui/media-query', () => ({
  useBreakpointValue: jest.fn()
}));

import GeoIntro from './';

describe('GeoIntro', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  it('should not render component when title is undefined', async () => {
    const { container } = render(<GeoIntro title={undefined} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('should render component when text is empty', async () => {
    const { container } = render(<GeoIntro title={mockSection.title} />);
    expect(container.firstChild).toHaveTextContent(mockSection.title);
  });

  it('should render component', async () => {
    const { container } = render(
      <GeoIntro title={mockSection.title} text={mockSection.richText} />
    );
    expect(container.firstChild).toHaveTextContent(mockSection.title);
    expect(container.firstChild).toHaveTextContent(mockSection.richText);
  });
});
