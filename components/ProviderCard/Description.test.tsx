import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';
import { fireEvent, render, screen } from '@utils/test-utils';
import { mockProviderDescriptions } from '@utils/test-utils';

import Description from './Description';

describe('Description', () => {
  const mockAnalytics = { track: jest.fn() };

  beforeEach(() => {
    window.tracking = mockAnalytics;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should not render component when there isnt a description or review', async () => {
    const { container } = render(<Description />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });
  it('should render component when description exists and lastReviewSnippet is empty', async () => {
    const { container } = render(
      <Description
        description={mockProviderDescriptions.providerDescription}
        lastReviewSnippet=""
        readMoreButton=""
      />
    );
    expect(container.firstChild).toHaveTextContent(
      mockProviderDescriptions.providerDescription
    );
  });
  it('should render component when lastReviewSnippet exists and description is empty and domain is caring.com', async () => {
    const { container } = render(
      <Description
        description=""
        lastReviewSnippet={mockProviderDescriptions.lastReviewSnippet}
        readMoreButton=""
        domain="caring.com"
      />
    );
    expect(container.firstChild).toHaveTextContent(
      mockProviderDescriptions.lastReviewSnippet
    );
  });
  it('should not render the lastReviewSnippet when the domain is not caring.com', async () => {
    const { container } = render(
      <Description
        description={mockProviderDescriptions.providerDescription}
        lastReviewSnippet={mockProviderDescriptions.lastReviewSnippet}
        readMoreButton=""
        domain="seniorhomes.com"
      />
    );
    expect(container.firstChild).toHaveTextContent(
      mockProviderDescriptions.providerDescription
    );
  });

  it('should render READ MORE button with correct tracking props when readMoreButton is "redirect_to_provider_page"', () => {
    const props = {
      description: mockProviderDescriptions.providerDescription,
      readMoreButton: 'redirect_to_provider_page',
      path: '/provider/test',
      domain: 'caring.com',
      legacyId: '123',
      queryId: '456',
      listId: '789'
    };

    render(<Description {...props} />);

    const button = screen.getByText('READ MORE');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('href', '/provider/test');

    fireEvent.click(button);

    expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
      destination_url: '/provider/test',
      element: {
        action: ElementActions.VIEW_PROVIDER,
        name: ElementNames.PROVIDER_CARD,
        type: ElementTypes.LINK,
        color: 'tertiary',
        text: 'READ MORE',
        text_color: 'white',
        id: undefined
      },
      query: {
        location_id: '123',
        query_id: '456',
        list_id: '789'
      },
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
    });
  });

  it('should render expand/collapse button with correct tracking props for long description', () => {
    const longDescription = 'A'.repeat(150);

    render(
      <Description
        description={longDescription}
        domain="caring.com"
        legacyId="123"
        queryId="456"
        listId="789"
      />
    );

    const button = screen.getByText('READ MORE');
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
      destination_url: '',
      element: {
        action: ElementActions.EXPAND,
        name: ElementNames.GENERIC_BUTTON,
        type: ElementTypes.BUTTON,
        color: 'tertiary',
        text: 'READ ,MORE',
        text_color: 'white',
        id: undefined
      },
      query: {
        location_id: '',
        query_id: '',
        list_id: ''
      },
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
    });

    const collapseButton = screen.getByText('READ LESS');
    fireEvent.click(collapseButton);
    expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
      destination_url: '',
      element: {
        action: ElementActions.COLLAPSE,
        name: ElementNames.GENERIC_BUTTON,
        type: ElementTypes.BUTTON,
        color: 'tertiary',
        text: 'READ ,LESS',
        text_color: 'white',
        id: undefined
      },
      query: {
        location_id: '',
        query_id: '',
        list_id: ''
      },
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
    });
  });

  it('should handle missing tracking props gracefully', () => {
    render(
      <Description
        description={mockProviderDescriptions.providerDescription}
        readMoreButton="redirect_to_provider_page"
        path="/provider/test"
        domain="caring.com"
      />
    );

    const button = screen.getByText('READ MORE');
    fireEvent.click(button);

    expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
      destination_url: '/provider/test',
      element: {
        action: ElementActions.VIEW_PROVIDER,
        name: ElementNames.PROVIDER_CARD,
        type: ElementTypes.LINK,
        color: 'tertiary',
        text: 'READ MORE',
        text_color: 'white',
        id: undefined
      },
      query: {
        location_id: '',
        query_id: '',
        list_id: ''
      },
      page_session_id: '',
      session_id: '',
      dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
    });
  });
});
