import { Box, Link, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button, { ButtonProps } from '@components/Button';
import { useState } from 'react';

interface DescriptionProps {
  lastReviewSnippet?: string;
  description?: string;
  readMoreButton?: string;
  domain?: string;
  path?: string;
  openInNewTab?: boolean;
  legacyId?: string;
  queryId?: string;
  listId?: string;
}

const MAX_LENGTH_OF_DESCRIPTION = 140;

const ProviderDescription = ({
  description,
  lastReviewSnippet,
  readMoreButton,
  path,
  colorScheme = 'primary',
  buttonSize = 'xs',
  openInNewTab = false,
  legacyId,
  queryId,
  listId
}: {
  description?: string;
  lastReviewSnippet?: string;
  readMoreButton?: string;
  path?: string;
  colorScheme?: ButtonProps['colorScheme'];
  buttonSize?: ButtonProps['size'];
  openInNewTab?: boolean;
  legacyId?: string;
  queryId?: string;
  listId?: string;
}) => {
  const [showingMoreDescription, setShowingMoreDescription] = useState(false);
  const cleanedDescription = description?.replace(/(<([^>]+)>)/gi, '') ?? '';

  const shouldReduceDescription =
    cleanedDescription.length > MAX_LENGTH_OF_DESCRIPTION;

  const providerDescription =
    shouldReduceDescription && !showingMoreDescription
      ? cleanedDescription.slice(0, MAX_LENGTH_OF_DESCRIPTION) + '...'
      : cleanedDescription;
  const hasDescription = !!providerDescription || !!lastReviewSnippet;
  if (!hasDescription) {
    return null;
  }
  return (
    <Box w="full">
      <Text
        wordBreak="break-word"
        fontSize="sm"
        className="magnolia-text"
        dangerouslySetInnerHTML={{
          __html: hasDescription
            ? `"${
                !!lastReviewSnippet ? lastReviewSnippet : providerDescription
              }"`
            : ''
        }}
      />

      {readMoreButton === 'redirect_to_provider_page' ? (
        <Button
          as={Link}
          href={path}
          colorScheme={colorScheme}
          size={buttonSize}
          variant="link"
          isExternal={openInNewTab}
          elementAction={ElementActions.VIEW_PROVIDER}
          elementName={ElementNames.PROVIDER_CARD}
          elementType={ElementTypes.LINK}
          destinationUrl={path}
          query={{
            locationId: legacyId || '',
            queryId: queryId || '',
            listId: listId || ''
          }}
        >
          READ MORE
        </Button>
      ) : (
        <>
          {shouldReduceDescription && (
            <Button
              colorScheme={colorScheme}
              size={buttonSize}
              variant="link"
              zIndex={10}
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault();
                setShowingMoreDescription(!showingMoreDescription);
              }}
              elementAction={
                showingMoreDescription
                  ? ElementActions.COLLAPSE
                  : ElementActions.EXPAND
              }
            >
              READ {showingMoreDescription ? 'LESS' : 'MORE'}
            </Button>
          )}
        </>
      )}
    </Box>
  );
};

const Description: React.FC<DescriptionProps> = ({
  description,
  lastReviewSnippet,
  readMoreButton,
  path,
  domain,
  openInNewTab,
  legacyId,
  queryId,
  listId
}) => {
  switch (domain) {
    case 'seniorhomes.com':
      return (
        <ProviderDescription
          description={description}
          readMoreButton={readMoreButton}
          path={path}
          colorScheme="primary"
          buttonSize="xs"
          openInNewTab={openInNewTab}
          legacyId={legacyId}
          queryId={queryId}
          listId={listId}
        />
      );
    case 'caring.com':
      return (
        <ProviderDescription
          description={description}
          lastReviewSnippet={lastReviewSnippet}
          readMoreButton={readMoreButton}
          path={path}
          colorScheme="tertiary"
          buttonSize="sm"
          openInNewTab={openInNewTab}
          legacyId={legacyId}
          queryId={queryId}
          listId={listId}
        />
      );
    default:
      return (
        <ProviderDescription
          description={description}
          readMoreButton={readMoreButton}
          path={path}
          openInNewTab={openInNewTab}
          legacyId={legacyId}
          queryId={queryId}
          listId={listId}
        />
      );
  }
};

export default Description;
