import { Button, ButtonProps } from '@chakra-ui/button';
import { Text } from '@chakra-ui/layout';
import { Promotion } from '@components/FacetedSearch/types';
import { MdTouchApp } from 'react-icons/md';

interface PromotionButtonProps extends ButtonProps {
  label: string;
  subLabel: string;
  colorScheme: string;
  activePromotion?: Promotion | undefined;
}

const PromotionButton: React.FC<PromotionButtonProps> = ({
  label,
  subLabel,
  colorScheme = 'primary',
  activePromotion,
  ...rest
}) => {
  return (
    <Button
      borderRadius="50px"
      boxShadow="md"
      colorScheme={`${colorScheme}`}
      height="34px"
      size="xs"
      pointerEvents={!activePromotion ? 'none' : 'auto'}
      {...rest}
    >
      {activePromotion && <MdTouchApp size={23} />}
      <Text as="span">
        {label}{' '}
        <Text as="span" fontWeight={400}>
          {subLabel}
        </Text>
      </Text>
    </Button>
  );
};

export default PromotionButton;
