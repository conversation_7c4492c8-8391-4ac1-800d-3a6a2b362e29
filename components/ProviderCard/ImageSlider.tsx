import { IconProps } from '@chakra-ui/icons';
import { Box, Icon, Link } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import WhichImage from '@components/Image/WhichImage';
import SearchResultImage from '@components/Search/SearchResultImage';
import { useSwipe } from '@hooks/useSwipe';
import { useState } from 'react';
import {
  MdOutlineArrowBackIos,
  MdOutlineArrowForwardIos
} from 'react-icons/md';

interface ImageSliderProps {
  domain: string;
  images: string[];
  title: string;
  path: string;
  openInNewTab?: boolean;
  query: {
    locationId: string;
    queryId: string;
    listId: string;
  };
  zIndex?: number;
  size?: 'small' | 'large';
}

type SliderProps = Omit<ImageSliderProps, 'domain'>;

const ButtonIconProps: IconProps = {
  color: 'gray.900',
  backgroundColor: 'white',
  opacity: '.75',
  _hover: { color: 'gray.700' },
  cursor: 'pointer',
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  borderRadius: '100%',
  padding: 3,
  width: 10,
  height: 10,
  fontSize: '16px',
  zIndex: 1
};
const Slider: React.FC<SliderProps> = ({
  images,
  title,
  path,
  openInNewTab,
  query,
  zIndex,
  size
}) => {
  const [index, setIndex] = useState(0);
  const elementClicked = useElementClicked();

  const sliderHeight =
    size === 'small' ? '152px' : { base: '175px', md: '325px' };
  const imageHeight = size === 'small' ? 152 : 400;
  const imageWidth = size === 'small' ? 172 : 600;

  const nextSlide = () => {
    const newIndex = index === images.length - 1 ? 0 : index + 1;
    setIndex(newIndex);
  };

  const prevSlide = () => {
    const newIndex = index === 0 ? images.length - 1 : index - 1;
    setIndex(newIndex);
  };
  const { handleTouchStart, handleTouchEnd } = useSwipe({
    onSwipeLeft: prevSlide,
    onSwipeRight: nextSlide
  });

  if (images.length <= 1)
    return <SearchResultImage images={images} title={title} />;

  const buttonHeight = size === 'small' ? 7 : 10;
  const buttonPadding = size === 'small' ? 2 : 3;
  const maxImageLength = 9;

  if (images.length > maxImageLength)
    images = images.slice(0, maxImageLength + 1);

  return (
    <Box
      position="relative"
      height={sliderHeight}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      zIndex={zIndex}
    >
      <Link key={`${title}-${index}`} href={path} isExternal={openInNewTab}>
        <WhichImage
          path={images[index]}
          title={title}
          height={imageHeight}
          width={imageWidth}
          style={{
            objectFit: 'cover',
            height: '100%',
            filter: index === maxImageLength ? 'brightness(0.5)' : 'none'
          }}
          onClick={() => {
            elementClicked({
              element: {
                type: ElementTypes.LINK,
                action: ElementActions.INTERNAL_LINK,
                name: ElementNames.PROVIDER_CARD,
                text: title,
                color: '',
                textColor: ''
              },
              destinationUrl: path,
              query: { ...query }
            });
          }}
        />
        {index === maxImageLength && (
          <Button
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            variant="outline"
            color="white"
            size="sm"
            _hover={{ backgroundColor: 'white', color: 'gray.800' }}
            elementAction={ElementActions.VIEW_PROVIDER}
            elementName={ElementNames.PROVIDER_CARD}
            elementType={ElementTypes.BUTTON}
            destinationUrl={path}
            query={{
              locationId: query.locationId,
              queryId: query.queryId,
              listId: query.listId
            }}
          >
            View more photos
          </Button>
        )}
        <Box
          position="absolute"
          bottom="0px"
          left="50%"
          transform="translateX(-50%)"
          width="100%"
          textAlign="center"
        >
          {images.map((_, i) => (
            <Box
              key={i}
              mx="1"
              h="6px"
              w="6px"
              borderRadius="full"
              backgroundColor="white"
              opacity={i === index ? '1' : '.50'}
              display="inline-block"
            />
          ))}
        </Box>
      </Link>
      <Icon
        {...ButtonIconProps}
        height={buttonHeight}
        width={buttonHeight}
        padding={buttonPadding}
        as={MdOutlineArrowBackIos}
        left={0}
        marginLeft={1}
        onClick={prevSlide}
      />
      <Icon
        {...ButtonIconProps}
        height={buttonHeight}
        width={buttonHeight}
        padding={buttonPadding}
        as={MdOutlineArrowForwardIos}
        right={0}
        marginRight={1}
        onClick={nextSlide}
      />
    </Box>
  );
};

const ImageSlider: React.FC<ImageSliderProps> = ({
  domain,
  path,
  title,
  images,
  query,
  zIndex = 1,
  size = 'large',
  openInNewTab = false
}) => {
  switch (domain) {
    case 'caring.com':
      return (
        <Slider
          images={images}
          title={title}
          path={path}
          openInNewTab={openInNewTab}
          query={query}
          zIndex={zIndex}
          size={size}
        />
      );
    default:
      return <SearchResultImage images={images} title={title} />;
  }
};

export default ImageSlider;
