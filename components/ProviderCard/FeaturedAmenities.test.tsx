import { render } from '@utils/test-utils';

import SiteContext, { findSiteForDomain } from '~/contexts/SiteContext';
import { CaringDomains, SeniorHomesDomains } from '~/types/Domains';

import FeaturedAmenities, { getOfferingListingNode } from './FeaturedAmenities';

describe('FeaturedAmenities', () => {
  const customRender = (component: React.ReactNode, domain: string) => {
    const siteProps = {
      site: findSiteForDomain(domain)
    };

    return render(
      <SiteContext.Provider value={siteProps}>{component}</SiteContext.Provider>
    );
  };

  it('renders the correct icons based on the provided amenities for Caring.com', () => {
    const { getByTestId } = customRender(
      <FeaturedAmenities
        amenities={{
          pets: true,
          internet: true,
          wheelchair: true,
          transportation: true
        }}
      />,
      CaringDomains.LIVE
    );
    expect(getByTestId('wifi-icon')).toBeInTheDocument();
    expect(getByTestId('pets-icon')).toBeInTheDocument();
    expect(getByTestId('wheelchair-icon')).toBeInTheDocument();
    expect(getByTestId('transportation-icon')).toBeInTheDocument();
  });

  it('renders the correct icons based on the provided amenities for Caring.com', () => {
    const { getByTestId, queryByTestId } = customRender(
      <FeaturedAmenities
        amenities={{
          pets: true,
          internet: true,
          wheelchair: false,
          transportation: true
        }}
      />,
      CaringDomains.LIVE
    );
    expect(getByTestId('wifi-icon')).toBeInTheDocument();
    expect(getByTestId('pets-icon')).toBeInTheDocument();
    expect(queryByTestId('wheelchair-icon')).toBeNull();
    expect(getByTestId('transportation-icon')).toBeInTheDocument();
  });

  it('does not render any icons when the domain is Seniorhomes.com', () => {
    const { queryByTestId } = customRender(
      <FeaturedAmenities
        amenities={{
          pets: true,
          internet: true,
          wheelchair: true,
          transportation: true
        }}
      />,
      SeniorHomesDomains.LIVE
    );
    expect(queryByTestId('wifi-icon')).toBeNull();
    expect(queryByTestId('pets-icon')).toBeNull();
    expect(queryByTestId('wheelchair-icon')).toBeNull();
    expect(queryByTestId('transportation-icon')).toBeNull();
  });

  it('does not render any icons if no amenities are provided', () => {
    const { queryByTestId } = customRender(
      <FeaturedAmenities
        amenities={{
          pets: false,
          internet: false,
          wheelchair: false,
          transportation: false
        }}
      />,
      CaringDomains.LIVE
    );
    expect(queryByTestId('wifi-icon')).toBeNull();
    expect(queryByTestId('pets-icon')).toBeNull();
    expect(queryByTestId('wheelchair-icon')).toBeNull();
    expect(queryByTestId('transportation-icon')).toBeNull();
  });
  describe('getOfferingListingNode', () => {
    it('returns the offering listing node when it exists', () => {
      const page = {
        main: {
          '@nodes': ['node1', 'node2', 'node3'],
          node1: {
            'mgnl:template': 'spa-lm:components/offeringListing'
          },
          node2: {
            'mgnl:template': 'spa-lm:components/sectionWithTitle'
          },
          node3: {
            'mgnl:template': 'spa-lm:components/image'
          }
        }
      };

      const offeringListingNode = getOfferingListingNode(page);

      expect(offeringListingNode).toEqual(page.main.node1);
    });

    it('returns -1 when an offering listing node does not exist', () => {
      const page = {
        main: {
          '@nodes': ['node1', 'node2', 'node3'],
          node1: {
            'mgnl:template': 'spa-lm:components/paragraph'
          },
          node2: {
            'mgnl:template': 'spa-lm:components/sectionWithTitle'
          },
          node3: {
            'mgnl:template': 'spa-lm:components/image'
          }
        }
      };

      const offeringListingNode = getOfferingListingNode(page);

      expect(offeringListingNode).toEqual(-1);
    });
  });
});
