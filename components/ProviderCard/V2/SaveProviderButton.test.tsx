import { ChakraProvider } from '@chakra-ui/react';
import useElementClicked from '@components/Analytics/events/ElementClicked';
import useCookieStorageValue from '@hooks/use-cookie-storage-value';
import { fireEvent, render, screen } from '@utils/test-utils';
import CookieStorage from 'utils/cookieStorage';

import {
  MAX_SAVED_PROVIDERS,
  MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS,
  SAVED_PROVIDERS_COOKIE_NAME
} from '~/constants';

import SaveProviderButton from './SaveProviderButton';

jest.mock('utils/cookieStorage');
jest.mock('@components/Analytics/events/ElementClicked');
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn()
  })
}));

jest.mock('@hooks/use-cookie-storage-value', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('SaveProviderButton', () => {
  const mockProviderId = 'test-provider-123';
  const mockElementClicked = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (CookieStorage.enabled as jest.Mock) = jest.fn().mockReturnValue(true);
    (CookieStorage.get as jest.Mock) = jest.fn().mockReturnValue(null);
    (CookieStorage.set as jest.Mock) = jest.fn();
    (useElementClicked as jest.Mock).mockReturnValue(mockElementClicked);
    (useCookieStorageValue as jest.Mock).mockReturnValue(null);
  });

  it('renders the button with unsaved state initially', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(null);

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('aria-label', 'Save provider');
  });

  it('shows saved state if provider is in cookie', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(
      JSON.stringify([mockProviderId])
    );

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    expect(button).toHaveAttribute('aria-label', 'Unsave provider');
  });

  it('saves a provider when clicked in unsaved state', async () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(null);

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    fireEvent.click(button);

    expect(CookieStorage.set).toHaveBeenCalledWith(
      SAVED_PROVIDERS_COOKIE_NAME,
      JSON.stringify([mockProviderId]),
      { expires: MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS }
    );

    expect(mockElementClicked).toHaveBeenCalledWith({
      element: expect.objectContaining({
        action: 'save_provider',
        name: 'save_provider'
      })
    });
  });

  it('removes a provider when clicked in saved state', async () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(
      JSON.stringify([mockProviderId])
    );

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    fireEvent.click(button);

    expect(CookieStorage.set).toHaveBeenCalledWith(
      SAVED_PROVIDERS_COOKIE_NAME,
      JSON.stringify([]),
      { expires: MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS }
    );

    expect(mockElementClicked).toHaveBeenCalledWith({
      element: expect.objectContaining({
        action: 'unsave_provider',
        name: 'save_provider'
      })
    });
  });

  it('shows error toast when max providers limit is reached', async () => {
    const maxProviders = Array(MAX_SAVED_PROVIDERS)
      .fill(null)
      .map((_, i) => `provider-${i}`);
    (CookieStorage.get as jest.Mock).mockReturnValue(
      JSON.stringify(maxProviders)
    );

    const mockShowToast = jest.fn();
    jest.mock('@hooks/useToast', () => ({
      useToast: () => mockShowToast
    }));

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    fireEvent.click(button);

    expect(CookieStorage.set).not.toHaveBeenCalled();
    expect(mockElementClicked).not.toHaveBeenCalled();
  });

  it('handles cookie parsing errors gracefully', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue('invalid-json');
    jest.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    expect(console.error).toHaveBeenCalled();
    const button = screen.getByTestId('save-provider-button');
    expect(button).toHaveAttribute('aria-label', 'Save provider');
  });

  it('handles empty providerId gracefully', () => {
    (useCookieStorageValue as jest.Mock).mockReturnValue(null);

    render(
      <ChakraProvider>
        <SaveProviderButton providerId="" />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    fireEvent.click(button);

    expect(CookieStorage.set).not.toHaveBeenCalled();
    expect(mockElementClicked).not.toHaveBeenCalled();
  });

  it('preserves other saved providers when saving a new one', () => {
    const existingProvider = 'existing-provider-456';
    (useCookieStorageValue as jest.Mock).mockReturnValue(
      JSON.stringify([existingProvider])
    );

    (CookieStorage.get as jest.Mock).mockImplementation((key) => {
      if (key === SAVED_PROVIDERS_COOKIE_NAME) {
        return JSON.stringify([existingProvider]);
      }
      return null;
    });

    render(
      <ChakraProvider>
        <SaveProviderButton providerId={mockProviderId} />
      </ChakraProvider>
    );

    const button = screen.getByTestId('save-provider-button');
    fireEvent.click(button);

    expect(CookieStorage.set).toHaveBeenCalledWith(
      SAVED_PROVIDERS_COOKIE_NAME,
      JSON.stringify([existingProvider, mockProviderId]),
      { expires: MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS }
    );
  });
});
