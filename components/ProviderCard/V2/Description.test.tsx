import { <PERSON><PERSON><PERSON>rovider } from '@chakra-ui/react';
import { fireEvent, render, screen } from '@testing-library/react';
import { FALLBACK_DICE_ROLL_UUID } from '@utils/diceRollUuid';

import Description from './Description';

const renderWithChakra = (ui: React.ReactElement) => {
  return render(<ChakraProvider>{ui}</ChakraProvider>);
};

describe('Description', () => {
  const mockAnalytics = { track: jest.fn() };

  beforeEach(() => {
    window.tracking = mockAnalytics;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    description: 'Short description',
    path: '/provider/path',
    domain: 'caring.com'
  };

  const mockResizeObserver = jest.fn(function (callback) {
    return {
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn()
    };
  });

  beforeAll(() => {
    window.ResizeObserver = mockResizeObserver;
  });

  describe('Basic Rendering', () => {
    it('renders description text', () => {
      renderWithChakra(<Description {...defaultProps} />);
      expect(screen.getByText(/Short description/)).toBeInTheDocument();
    });

    it('does not render content when no description or review snippet is provided', () => {
      renderWithChakra(<Description />);
      const content = screen.queryByText(/description|review/i);
      expect(content).not.toBeInTheDocument();
    });

    it('renders last review snippet when provided, with quotations', () => {
      const props = {
        ...defaultProps,
        lastReviewSnippet: 'Latest review content'
      };
      renderWithChakra(<Description {...props} />);
      expect(screen.getByText('"Latest review content"')).toBeInTheDocument();
    });
  });

  describe('LEARN MORE Button', () => {
    it('renders redirect button with correct link', () => {
      const props = {
        ...defaultProps,
        path: '/provider/path'
      };
      renderWithChakra(<Description {...props} />);
      const link = screen.getByText('LEARN MORE');
      expect(link.closest('a')).toHaveAttribute('href', '/provider/path');
    });

    it('handles external links correctly', () => {
      const props = {
        ...defaultProps,
        openInNewTab: true
      };
      renderWithChakra(<Description {...props} />);
      const link = screen.getByText('LEARN MORE');
      expect(link.closest('a')).toHaveAttribute('target', '_blank');
    });
  });

  describe('HTML Sanitization', () => {
    it('strips HTML tags from description', () => {
      const props = {
        ...defaultProps,
        description: '<p>Test <strong>description</strong></p>'
      };
      renderWithChakra(<Description {...props} />);
      expect(screen.getByText('Test description')).toBeInTheDocument();
    });
  });

  describe('Analytics Properties', () => {
    it('includes analytics properties in redirect button', () => {
      const props = {
        ...defaultProps,
        legacyId: '123',
        queryId: '456',
        listId: '789'
      };
      renderWithChakra(<Description {...props} />);
      const link = screen.getByText('LEARN MORE');

      fireEvent.click(link);
      expect(mockAnalytics.track).toHaveBeenCalledWith('Element Clicked', {
        destination_url: '/provider/path',
        element: {
          action: 'view_provider',
          name: 'provider_card',
          type: 'link',
          color: 'primary',
          text_color: 'white',
          text: 'LEARN MORE',
          id: undefined
        },
        query: {
          location_id: '123',
          query_id: '456',
          list_id: '789'
        },
        page_session_id: '',
        session_id: '',
        dice_roll_uuid: FALLBACK_DICE_ROLL_UUID
      });
    });
  });
});
