.container {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  gap: 0.5rem;
  overflow: hidden;
  margin-top: 0.25rem;
}
  
.card {
  background: var(--chakra-colors-primary-50);
  color: var(--chakra-colors-gray-900);
  padding: 0.25rem 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  flex: 0 0 auto;
}

.card:last-child {
  text-overflow: ellipsis;
  flex: 0 1 auto;
}