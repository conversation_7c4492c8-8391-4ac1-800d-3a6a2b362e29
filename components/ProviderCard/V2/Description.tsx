import { Box, Link, Text } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { useMemo, useRef } from 'react';

interface DescriptionProps {
  description?: string;
  lastReviewSnippet?: string;
  path?: string;
  openInNewTab?: boolean;
  legacyId?: string;
  queryId?: string;
  listId?: string;
}

const Description: React.FC<DescriptionProps> = ({
  description,
  lastReviewSnippet,
  path,
  openInNewTab = false,
  legacyId,
  queryId,
  listId
}) => {
  const textRef = useRef<HTMLDivElement>(null);

  const finalText = useMemo(() => {
    const cleanedDescription = description?.replace(/(<([^>]+)>)/gi, '') ?? '';
    const hasDescription = !!cleanedDescription || !!lastReviewSnippet;
    if (!hasDescription) {
      return '';
    }

    if (lastReviewSnippet) {
      return `"${lastReviewSnippet}"`;
    }

    return cleanedDescription;
  }, [description, lastReviewSnippet]);

  if (!finalText) {
    return null;
  }

  return (
    <Box position="relative" sx={{ width: '100%' }}>
      <Text
        ref={textRef}
        noOfLines={2}
        wordBreak="break-word"
        fontSize={{ base: 'xs', md: 'sm' }}
        color="gray.700"
        className="magnolia-text"
        overflow="hidden"
      >
        {finalText}
      </Text>
      <Box
        right="0px"
        height={{ sm: '18px', md: '24px' }}
        mx="2px"
        bottom={{ base: 0, sm: '1px', md: '-1px' }}
        position="absolute"
        display="flex"
        flexDirection="row"
      >
        <Box
          width="50px"
          bgGradient="linear(to-r, transparent 0%, white 80%)"
        />
        <Box
          sx={{
            backgroundColor: 'white',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <Button
            as={Link}
            href={path}
            colorScheme="primary"
            color="primary.700"
            size="xs"
            fontSize="xs"
            variant="link"
            isExternal={openInNewTab}
            elementAction={ElementActions.VIEW_PROVIDER}
            elementName={ElementNames.PROVIDER_CARD}
            elementType={ElementTypes.LINK}
            destinationUrl={path}
            ml={1}
            query={{
              locationId: legacyId || '',
              queryId: queryId || '',
              listId: listId || ''
            }}
          >
            LEARN MORE
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
export default Description;
