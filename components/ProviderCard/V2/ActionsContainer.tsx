import { GridItem, GridItemProps } from '@chakra-ui/layout';

interface ActionsContainerProps {
  domain: string;
  children: React.ReactNode;
  colSpan: GridItemProps['colSpan'];
}

const ActionsContainer: React.FC<ActionsContainerProps> = ({
  domain,
  colSpan,
  children
}) => {
  return (
    <GridItem rounded="md" minWidth={90} colSpan={colSpan} py={0}>
      {children}
    </GridItem>
  );
};

export default ActionsContainer;
