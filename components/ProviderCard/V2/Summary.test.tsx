import { fireEvent, render, screen } from '@testing-library/react';

import Summary from './Summary';

const mockElementClicked = jest.fn();
jest.mock('@components/Analytics/events/ElementClicked', () => ({
  __esModule: true,
  ElementTypes: {
    LINK: 'LINK'
  },
  ElementActions: {
    INTERNAL_LINK: 'INTERNAL_LINK'
  },
  ElementNames: {
    PROVIDER_CARD: 'PROVIDER_CARD'
  },
  default: () => mockElementClicked
}));

describe('Summary', () => {
  const defaultProps = {
    domain: 'caring.com',
    path: '/provider/test-path',
    address: '123 Test St, City, State 12345',
    title: 'Test Provider',
    titleColor: 'blue.500',
    query: {
      locationId: 'loc123',
      queryId: 'query123',
      listId: 'list123'
    },
    dontOpenInNewTab: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders title and address', () => {
      render(<Summary {...defaultProps} />);

      expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
      expect(screen.getByText(defaultProps.address)).toBeInTheDocument();
    });

    it('renders verified badge when showVerifiedBadge is true', () => {
      render(<Summary {...defaultProps} showVerifiedBadge={true} />);

      expect(screen.getByRole('presentation')).toBeInTheDocument();
    });

    it('does not render verified badge by default', () => {
      render(<Summary {...defaultProps} />);

      expect(screen.queryByRole('presentation')).not.toBeInTheDocument();
    });
  });

  describe('Phone Number', () => {
    it('renders phone number when provided', () => {
      const phoneNumber = '1234567890';
      render(<Summary {...defaultProps} phoneNumber={phoneNumber} />);

      const phoneLink = screen.getByRole('link', { name: phoneNumber });
      expect(phoneLink).toBeInTheDocument();
      expect(phoneLink).toHaveAttribute('href', `tel:+${phoneNumber}`);
    });

    it('does not render phone section when phone number is not provided', () => {
      render(<Summary {...defaultProps} />);

      expect(screen.queryByText('Call')).not.toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: /\d+/ })
      ).not.toBeInTheDocument();
    });
  });

  describe('Link Behavior', () => {
    it('opens in new tab by default', () => {
      render(<Summary {...defaultProps} />);

      const link = screen.getByRole('link', { name: defaultProps.title });
      expect(link).toHaveAttribute('target', '_blank');
    });

    it('opens in same tab when dontOpenInNewTab is true', () => {
      render(<Summary {...defaultProps} dontOpenInNewTab={true} />);

      const link = screen.getByRole('link', { name: defaultProps.title });
      expect(link).not.toHaveAttribute('target');
    });

    it('adds nofollow rel when isIndexed is false', () => {
      render(<Summary {...defaultProps} isIndexed={false} />);

      const link = screen.getByRole('link', { name: defaultProps.title });
      expect(link).toHaveAttribute('rel', 'nofollow');
    });

    it('triggers analytics event on click', () => {
      render(<Summary {...defaultProps} />);

      const link = screen.getByRole('link', { name: defaultProps.title });
      fireEvent.click(link);

      expect(mockElementClicked).toHaveBeenCalledWith({
        element: {
          type: 'LINK',
          action: 'INTERNAL_LINK',
          name: 'PROVIDER_CARD',
          text: defaultProps.title,
          color: 'white',
          textColor: defaultProps.titleColor
        },
        destinationUrl: defaultProps.path,
        query: defaultProps.query
      });
    });
  });
});
