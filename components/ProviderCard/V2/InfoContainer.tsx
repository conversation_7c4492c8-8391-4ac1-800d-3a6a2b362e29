import { GridItem, GridItemProps } from '@chakra-ui/layout';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';

interface InfoContainerProps extends GridItemProps {
  children: React.ReactNode;
}

const InfoContainer: React.FC<InfoContainerProps> = ({ children }) => {
  return (
    <GridItem
      css={containerQueryAdapter({
        properties: [
          {
            property: 'grid-column',
            values: {
              base: 'span 12',
              md: 'span 6'
            }
          },
          {
            property: 'margin-top',
            values: {
              base: '0px',
              md: '8px'
            }
          },
          {
            property: 'margin-bottom',
            values: {
              base: '0px',
              md: '8px'
            }
          }
        ]
      })}
      rounded="md"
      alignItems="start"
      display="flex"
      flexDirection="column"
      gap={1}
    >
      {children}
    </GridItem>
  );
};

export default InfoContainer;
