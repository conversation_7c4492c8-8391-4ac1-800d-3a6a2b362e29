import { GridItem } from '@chakra-ui/layout';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';

interface ImageContainerProps {
  domain: string;
  children: React.ReactNode;
}

const ImageContainer: React.FC<ImageContainerProps> = ({
  domain,
  children
}) => {
  return (
    <GridItem
      bg="gray.200"
      css={containerQueryAdapter({
        properties: [
          {
            property: 'grid-column',
            values: {
              base: 'span 12',
              md: 'span 6'
            }
          },
          {
            property: 'height',
            values: {
              base: '160px',
              md: '205px'
            }
          }
        ]
      })}
      overflow="hidden"
      position="relative"
      rounded="md"
    >
      {children}
    </GridItem>
  );
};

export default ImageContainer;
