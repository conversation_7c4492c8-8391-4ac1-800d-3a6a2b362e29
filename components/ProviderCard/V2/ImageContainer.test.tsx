import { render, screen } from '@testing-library/react';

import ImageContainer from './ImageContainer';

describe('ImageContainer', () => {
  it('renders children correctly', () => {
    const testChild = <div data-testid="test-child">Test Content</div>;

    render(<ImageContainer domain="test-domain">{testChild}</ImageContainer>);

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
});
