import { Icon } from '@chakra-ui/icon';
import {
  Flex,
  Grid,
  GridItem,
  Heading,
  Text,
  TextProps
} from '@chakra-ui/layout';
import { chakra, shouldForwardProp } from '@chakra-ui/react';
import { formatRating } from '@utils/number';
import { MdStar } from 'react-icons/md';

interface RatingProps {
  domain: string;
  rating: number;
  starColor: TextProps['color'];
  reviewCount?: number;
}

const CustomIcon = chakra(Icon, {
  shouldForwardProp: (prop) => {
    const isChakraProp = shouldForwardProp(prop);
    return isChakraProp || prop === 'viewBox';
  }
});

const RatingLabel = ({ domain, rating, ratingColor }) => {
  return (
    <Heading
      as="p"
      color={ratingColor}
      aria-label={`${rating} star rating`}
      fontSize={{ base: 'sm', lg: 'md' }}
      lineHeight={{ base: '120%', lg: 'unset' }}
    >
      {rating}
    </Heading>
  );
};

const RatingStars = ({ domain, starCount, starColor }) => {
  switch (domain) {
    case 'caring.com':
      return (
        <Flex
          alignItems="center"
          justifyContent="center"
          gap={{ base: 0, lg: 1 }}
        >
          {Array.from({ length: starCount }, (_, i) => (
            <CustomIcon
              key={i}
              as={MdStar}
              boxSize={{ base: '12.5px', lg: '16.67px' }}
              data-testid="star-icon"
              color={starColor}
              role="presentation"
              viewBox="2 2.5 20 20"
            />
          ))}
        </Flex>
      );
    default:
      return (
        <Flex alignItems="center" justifyContent="center" gap={1}>
          {Array.from({ length: starCount }, (_, i) => (
            <Icon
              key={i}
              as={MdStar}
              boxSize={5}
              data-testid="star-icon"
              color={starColor}
              role="presentation"
            />
          ))}
        </Flex>
      );
  }
};

const ReviewCount = ({ domain, reviewCount, ratingColor }) => {
  switch (domain) {
    case 'caring.com':
      return (
        <>
          <Text
            fontSize={{ base: 'xs', lg: 'md' }}
            lineHeight={{ base: '150%', lg: 'unset' }}
            color="gray.800"
          >
            (
            <Text as="span" color={ratingColor}>
              {reviewCount} reviews
            </Text>
            )
          </Text>
        </>
      );
    default:
      return (
        <Text color="gray.800" fontSize="lg" lineHeight="unset">
          ({reviewCount})
        </Text>
      );
  }
};

const Rating: React.FC<RatingProps> = ({
  domain,
  rating,
  starColor,
  reviewCount
}) => {
  const formattedRating = formatRating(rating);
  const starCount = Math.trunc(Number(formattedRating));
  return (
    <Grid
      templateAreas={`"left center right"`}
      alignItems="center"
      pt={{ base: 1, lg: 1.5 }}
      pb={{ base: 0, lg: 1 }}
      gap={{ base: 0.5, lg: 1 }}
    >
      <GridItem area={'left'}>
        <RatingLabel
          domain={domain}
          rating={formattedRating}
          ratingColor={starColor}
        />
      </GridItem>

      <GridItem>
        <RatingStars
          domain={domain}
          starCount={starCount}
          starColor={starColor}
        />
      </GridItem>

      {reviewCount && (
        <GridItem>
          <ReviewCount
            domain={domain}
            reviewCount={reviewCount}
            ratingColor={starColor}
          />
        </GridItem>
      )}
    </Grid>
  );
};

export default Rating;
