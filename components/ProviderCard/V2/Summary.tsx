import {
  Flex,
  Heading,
  HeadingProps,
  Link,
  LinkOverlay,
  Text,
  VStack
} from '@chakra-ui/layout';
import { Icon } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { MdVerified } from 'react-icons/md';

interface SummaryProps {
  domain: string;
  path: string;
  address: string;
  title: string;
  isIndexed?: boolean;
  titleColor: HeadingProps['color'];
  phoneNumber?: string;
  query: {
    locationId: string;
    queryId: string;
    listId: string;
  };
  dontOpenInNewTab: boolean;
  showVerifiedBadge?: boolean;
}

const Title = ({
  domain,
  title,
  titleColor
}: Pick<SummaryProps, 'domain' | 'title' | 'titleColor'>) => {
  return (
    <Heading as={'h3'} color={titleColor} size={{ base: 'sm', md: 'md' }}>
      {title}
    </Heading>
  );
};

const LeftColumn = ({
  domain,
  children
}: {
  domain: SummaryProps['domain'];
  children: React.ReactNode;
}) => {
  return (
    <VStack
      alignItems="start"
      flex="1"
      spacing={{ base: 2.5, lg: 1 }}
      gap={{ base: 0 }}
    >
      {children}
    </VStack>
  );
};

const Summary: React.FC<SummaryProps> = ({
  domain,
  path,
  address,
  title,
  isIndexed,
  titleColor,
  phoneNumber,
  query,
  dontOpenInNewTab,
  showVerifiedBadge
}) => {
  const elementClicked = useElementClicked();
  return (
    <Flex alignItems="start" gap={2.5} width="full">
      <LeftColumn domain={domain}>
        <LinkOverlay
          href={path}
          rel={isIndexed === false ? 'nofollow' : undefined}
          target={dontOpenInNewTab ? undefined : '_blank'}
          color="gray.700"
          _hover={{ textDecoration: 'underline' }}
          onClick={(e) => {
            elementClicked({
              element: {
                type: ElementTypes.LINK,
                action: ElementActions.INTERNAL_LINK,
                name: ElementNames.PROVIDER_CARD,
                text: title,
                color: 'white',
                textColor: titleColor?.toString() || ''
              },
              destinationUrl: path,
              query: { ...query }
            });
          }}
        >
          <Heading
            size="md"
            as={'span'}
            color={titleColor}
            display="inline-block"
            width="100%"
          >
            {title}&nbsp;
            {showVerifiedBadge && (
              <Icon
                as={MdVerified}
                w="24px"
                h="24px"
                role="presentation"
                color="primary.700"
                verticalAlign="top"
              />
            )}
          </Heading>
        </LinkOverlay>

        <Text fontSize={{ base: 'xs', md: 'sm' }} color={'gray.700'}>
          {address}
        </Text>

        {phoneNumber && (
          <Text fontSize="sm" pt={2}>
            Call{' '}
            <Link color="link.500" href={`tel:+${phoneNumber}`}>
              {phoneNumber}
            </Link>{' '}
            for details
          </Text>
        )}
      </LeftColumn>
    </Flex>
  );
};

export default Summary;
