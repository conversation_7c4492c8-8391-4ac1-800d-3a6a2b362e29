import { render, screen } from '@testing-library/react';

import InfoContainer from './InfoContainer';

describe('InfoContainer', () => {
  const defaultProps = {
    domain: 'caring.com',
    colSpan: { base: 12, lg: 6 },
    children: <div>Test Content</div>
  };

  it('renders children content', () => {
    render(<InfoContainer {...defaultProps} />);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders multiple children', () => {
    const props = {
      ...defaultProps,
      children: (
        <>
          <div>First Child</div>
          <div>Second Child</div>
        </>
      )
    };

    render(<InfoContainer {...props} />);
    expect(screen.getByText('First Child')).toBeInTheDocument();
    expect(screen.getByText('Second Child')).toBeInTheDocument();
  });
});
