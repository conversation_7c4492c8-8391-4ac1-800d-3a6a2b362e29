import '@testing-library/jest-dom';

import { render, screen } from '@utils/test-utils';

import CareTypes, { FEATURED_CARE_TYPES } from './CareTypes';

describe('CareTypes', () => {
  it('renders nothing when no care types are provided', () => {
    const { container } = render(<CareTypes careTypes={[]} />);
    expect(container.querySelector('[role="list"]')).not.toBeInTheDocument();
  });

  it('renders nothing when no care types match featured types or other types', () => {
    const { container } = render(<CareTypes careTypes={[]} />);
    expect(container.querySelector('[role="list"]')).not.toBeInTheDocument();
  });

  it('renders all matching care types in the correct order', () => {
    render(<CareTypes careTypes={FEATURED_CARE_TYPES} />);

    const careTypeElements = screen.getAllByRole('listitem');
    expect(careTypeElements).toHaveLength(3);

    expect(careTypeElements[0]).toHaveTextContent('Assisted Living');
    expect(careTypeElements[1]).toHaveTextContent('Memory Care');
    expect(careTypeElements[2]).toHaveTextContent('Nursing Homes');
  });

  it('renders only up to 3 care types', () => {
    const allCareTypes = [
      ...FEATURED_CARE_TYPES,
      'Other Care Type 1',
      'Other Care Type 2'
    ];
    render(<CareTypes careTypes={allCareTypes} />);

    const careTypeElements = screen.getAllByRole('listitem');
    expect(careTypeElements).toHaveLength(3);
  });

  it('renders care types in the correct order regardless of input order', () => {
    const reversedCareTypes = [...FEATURED_CARE_TYPES].reverse();
    render(<CareTypes careTypes={reversedCareTypes} />);

    const careTypeElements = screen.getAllByRole('listitem');

    expect(careTypeElements[0]).toHaveTextContent('Assisted Living');
    expect(careTypeElements[1]).toHaveTextContent('Memory Care');
    expect(careTypeElements[2]).toHaveTextContent('Nursing Homes');
  });

  it('renders a subset of care types when only some match', () => {
    const partialCareTypes = ['Memory Care', 'Assisted Living'];
    render(<CareTypes careTypes={partialCareTypes} />);

    const careTypeElements = screen.getAllByRole('listitem');
    expect(careTypeElements).toHaveLength(2);

    expect(careTypeElements[0]).toHaveTextContent('Assisted Living');
    expect(careTypeElements[1]).toHaveTextContent('Memory Care');
  });

  it('includes non-featured care types after featured ones', () => {
    const mixedCareTypes = [
      'Other Care Type',
      'Assisted Living',
      'Another Type'
    ];
    render(<CareTypes careTypes={mixedCareTypes} />);

    const careTypeElements = screen.getAllByRole('listitem');
    expect(careTypeElements).toHaveLength(3);

    expect(careTypeElements[0]).toHaveTextContent('Assisted Living');
    expect(careTypeElements[1]).toHaveTextContent('Other Care Type');
    expect(careTypeElements[2]).toHaveTextContent('Another Type');
  });

  it('prioritizes featured care types even when there are many non-featured types', () => {
    const mixedCareTypes = [
      'Type 1',
      'Type 2',
      'Memory Care',
      'Type 3',
      'Assisted Living'
    ];
    render(<CareTypes careTypes={mixedCareTypes} />);

    const careTypeElements = screen.getAllByRole('listitem');
    expect(careTypeElements).toHaveLength(3);

    expect(careTypeElements[0]).toHaveTextContent('Assisted Living');
    expect(careTypeElements[1]).toHaveTextContent('Memory Care');
    expect(careTypeElements[2]).toHaveTextContent('Type 1');
  });

  it('has proper accessibility attributes', () => {
    render(<CareTypes careTypes={['Assisted Living']} />);

    const container = screen.getByRole('list');
    expect(container).toHaveAttribute('aria-label', 'Available care types');

    const listItem = screen.getByRole('listitem');
    expect(listItem).toHaveAttribute(
      'aria-label',
      'Care type: Assisted Living'
    );
  });
});
