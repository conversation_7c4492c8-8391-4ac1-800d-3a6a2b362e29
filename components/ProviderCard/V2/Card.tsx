import { Card as ChakraCard } from '@chakra-ui/card';
import { Card<PERSON>ody, CardFooter } from '@chakra-ui/react';

interface CardProps {
  domain: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
}

const Card: React.FC<CardProps> = ({ domain, children, footer }) => {
  return (
    <ChakraCard
      variant="elevated"
      transition={'all ease 0.5s'}
      _hover={{
        transform: { base: 'scale(1.02)', md: 'scale(1.05)' },
        boxShadow: '0px 0px 25px 0px rgba(0, 0, 0, 0.35)'
      }}
      p={0}
      gap={0}
      borderRadius={6}
      height="100%"
    >
      <CardBody
        display="grid"
        gridTemplateColumns="repeat(12, 1fr)"
        minHeight="56"
        justifyContent="space-between"
        padding={4}
        gap={{ base: 4, lg: 2 }}
      >
        {children}
      </CardBody>
      {footer && (
        <CardFooter px={2} py={0}>
          {footer}
        </CardFooter>
      )}
    </ChakraCard>
  );
};

export default Card;
