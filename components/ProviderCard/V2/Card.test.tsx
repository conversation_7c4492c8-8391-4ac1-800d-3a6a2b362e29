import { render, screen } from '@testing-library/react';

import Card from './Card';

describe('Card', () => {
  const defaultProps = {
    domain: 'test-domain',
    children: <div data-testid="test-child">Test Content</div>
  };

  it('renders children correctly', () => {
    render(<Card {...defaultProps} />);
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders without footer when footer prop is not provided', () => {
    const { container } = render(<Card {...defaultProps} />);
    expect(
      container.querySelector('.chakra-card__footer')
    ).not.toBeInTheDocument();
  });

  it('renders footer when footer prop is provided', () => {
    const props = {
      ...defaultProps,
      footer: <div data-testid="test-footer">Footer Content</div>
    };

    render(<Card {...props} />);
    expect(screen.getByTestId('test-footer')).toBeInTheDocument();
    expect(screen.getByText('Footer Content')).toBeInTheDocument();
  });

  it('maintains consistent structure with both children and footer', () => {
    const props = {
      ...defaultProps,
      footer: <div data-testid="test-footer">Footer Content</div>
    };

    const { container } = render(<Card {...props} />);

    expect(container.querySelector('.chakra-card__body')).toBeInTheDocument();
    expect(container.querySelector('.chakra-card__footer')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByTestId('test-footer')).toBeInTheDocument();
  });
});
