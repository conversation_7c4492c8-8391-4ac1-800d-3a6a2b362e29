import { render, screen } from '@testing-library/react';

import Rating from './Rating';

describe('Rating', () => {
  const defaultProps = {
    domain: 'caring.com',
    rating: 4.5,
    starColor: 'yellow.400',
    reviewCount: 100
  };

  describe('Rating component', () => {
    it('renders with all elements for caring.com domain', () => {
      render(<Rating {...defaultProps} />);

      expect(screen.getByText('4.5')).toBeInTheDocument();
      expect(screen.getAllByTestId('star-icon')).toHaveLength(4);
      expect(screen.getByText('100 reviews')).toBeInTheDocument();
    });

    it('renders with all elements for non-caring.com domain', () => {
      render(<Rating {...defaultProps} domain="other.com" />);

      expect(screen.getByText('4.5')).toBeInTheDocument();
      expect(screen.getAllByTestId('star-icon')).toHaveLength(4);
      expect(screen.getByText('(100)')).toBeInTheDocument();
    });

    it('renders without review count when not provided', () => {
      const propsWithoutReviews = {
        domain: 'caring.com',
        rating: 4.5,
        starColor: 'yellow.400'
      };

      render(<Rating {...propsWithoutReviews} />);

      expect(screen.getByText('4.5')).toBeInTheDocument();
      expect(screen.getAllByTestId('star-icon')).toHaveLength(4);
      expect(screen.queryByText('reviews')).not.toBeInTheDocument();
    });

    it('truncates decimal places in rating', () => {
      render(<Rating {...defaultProps} rating={4.567} />);

      expect(screen.getByText('4.5')).toBeInTheDocument();
    });

    it('renders correctly with 0 star rating', () => {
      render(<Rating {...defaultProps} rating={0} />);

      expect(screen.getByText('0.0')).toBeInTheDocument();
      expect(screen.queryByTestId('star-icon')).not.toBeInTheDocument();
      expect(screen.getByText('100 reviews')).toBeInTheDocument();
    });

    it('renders correctly with 1 star rating', () => {
      render(<Rating {...defaultProps} rating={1} />);

      expect(screen.getByText('1.0')).toBeInTheDocument();
      expect(screen.getAllByTestId('star-icon')).toHaveLength(1);
      expect(screen.getByText('100 reviews')).toBeInTheDocument();
    });
  });

  describe('RatingLabel', () => {
    it('renders with correct aria-label', () => {
      render(<Rating {...defaultProps} />);

      expect(screen.getByLabelText('4.5 star rating')).toBeInTheDocument();
    });

    it('renders with correct aria-label for 0 stars', () => {
      render(<Rating {...defaultProps} rating={0} />);

      expect(screen.getByLabelText('0.0 star rating')).toBeInTheDocument();
    });

    it('renders with correct aria-label for 1 star', () => {
      render(<Rating {...defaultProps} rating={1} />);

      expect(screen.getByLabelText('1.0 star rating')).toBeInTheDocument();
    });
  });

  describe('ReviewCount', () => {
    it('renders review count correctly for caring.com', () => {
      render(<Rating {...defaultProps} />);

      expect(screen.getByText('100 reviews')).toBeInTheDocument();
    });

    it('renders review count correctly for other domains', () => {
      render(<Rating {...defaultProps} domain="other.com" />);

      expect(screen.getByText('(100)')).toBeInTheDocument();
    });
  });
});
