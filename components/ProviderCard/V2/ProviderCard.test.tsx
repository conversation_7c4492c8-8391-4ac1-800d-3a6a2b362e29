import { ChakraProvider } from '@chakra-ui/react';

import { useModalControls } from '~/contexts/ModalContext';
import SiteContext from '~/contexts/SiteContext';
import { CaringDomains } from '~/types/Domains';
import { fireEvent, render, screen } from '~/utils/test-utils';

import ProviderCard from './ProviderCard';

jest.mock('~/contexts/ModalContext');

describe('ProviderCard', () => {
  const mockTrack = jest.fn();
  const mockShowInquiryForm = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    window.tracking = {
      track: mockTrack
    };
    (useModalControls as jest.Mock).mockReturnValue({
      show: mockShowInquiryForm
    });
  });

  const defaultProps = {
    id: 'test-id',
    legacyId: 'legacy-123',
    title: 'Test Provider',
    address: '123 Test St',
    description: 'Test description',
    path: '/provider/test',
    images: ['/image1.jpg', '/image2.jpg'],
    reviewCount: 5,
    averageRating: 4.5,
    caringStars: [],
    price: 5000,
    showPrice: true,
    displayRequestInfoButton: true,
    displayLearnMoreButton: true,
    modalId: 'inquiry-modal',
    requestInfoButtonText: 'Request Info',
    learnMoreButtonText: 'Learn More',
    requestInfoButtonColorScheme: 'accent',
    learnMoreButtonColorScheme: 'accent',
    ratingStarsColor: 'accent',
    ratingStarsColorRange: '500',
    providerTitleColor: 'primary',
    providerTitleColorRange: '600',
    boxShadow: 'lg',
    border: '1px solid',
    borderColor: 'gray',
    borderColorRange: '200',
    displayBadges: true,
    displayProviderPhoneNumber: false,
    showVerifiedBadge: false,
    isHidden: false,
    dontOpenInNewTab: false,
    isIndexed: false,
    queryId: 'query-123',
    listId: 'list-123',
    handleCompare: () => {},
    displayCompareOption: false,
    isChecked: false,
    phoneNumber: undefined,
    promotions: [],
    providerPhoneNumberSource: undefined
  };

  const mockSiteContext = {
    site: {
      name: 'Caring.com',
      domains: ['caring.com'],
      path: CaringDomains.LIVE,
      segmentWriteKey: 'mock-segment-key',
      segmentCdnURL: 'mock-segment-cdn',
      partnerToken: 'mock-partner-token',
      publicFolder: 'caring_public',
      storyPaths: []
    }
  };

  const renderComponent = (props = {}) => {
    return render(
      <ChakraProvider>
        <SiteContext.Provider value={mockSiteContext}>
          <ProviderCard {...defaultProps} {...props} />
        </SiteContext.Provider>
      </ChakraProvider>
    );
  };

  it('renders basic provider information', () => {
    renderComponent();

    expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
    expect(screen.getByText(defaultProps.address)).toBeInTheDocument();
    expect(screen.getByText(defaultProps.description)).toBeInTheDocument();
  });

  it('shows formatted price when price is greater than 0', () => {
    renderComponent({ price: 5000, showPrice: true });
    expect(screen.getByText('$')).toBeInTheDocument();
    expect(screen.getByText('50')).toBeInTheDocument();
  });

  it('shows blurred price when showPrice is false', () => {
    renderComponent({ price: 5000, showPrice: false });
    const priceElement = screen.getByText('50');
    expect(priceElement).toHaveStyle({ filter: 'blur(10px)' });
  });

  it('shows "Pricing not available" when price is 0', () => {
    renderComponent({ price: 0 });
    expect(screen.getByText('Pricing not available')).toBeInTheDocument();
  });

  it('renders request info button and handles click', async () => {
    renderComponent();

    const button = screen.getByRole('button', { name: 'Request Info' });
    expect(button).toBeInTheDocument();

    fireEvent.click(button);
    expect(mockShowInquiryForm).toHaveBeenCalled();
    expect(mockTrack).toHaveBeenCalled();
  });

  it('renders rating information when available', () => {
    renderComponent();
    const ratingElement = screen.getByText('4.5', { exact: false });
    expect(ratingElement).toBeInTheDocument();
    expect(screen.getByText('5 reviews')).toBeInTheDocument();
  });

  it('hides rating when averageRating is 0', () => {
    renderComponent({ averageRating: 0 });
    expect(screen.queryByTestId('rating')).not.toBeInTheDocument();
  });

  it('renders verified badge when showVerifiedBadge is true', () => {
    renderComponent({ showVerifiedBadge: true });
    const verifiedBadge = screen.getAllByRole('presentation')[0];
    expect(verifiedBadge).toBeInTheDocument();
  });

  describe('Description display behavior', () => {
    const mockServices = [
      {
        category: {
          name: 'Assisted Living',
          description: 'Assisted living description',
          imageURL: ''
        }
      },
      {
        category: {
          name: 'Memory Care',
          description: 'Memory care description',
          imageURL: ''
        }
      }
    ];

    beforeEach(() => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation((query) => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn()
        }))
      });
    });

    it('shows description when shouldDisplayCareTypes is false', () => {
      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: false,
        services: mockServices
      });

      expect(screen.getByText('Test description')).toBeInTheDocument();
    });

    it('hides description on mobile when shouldDisplayCareTypes is true', () => {
      // Mock the responsiveQueryAdapter to simulate mobile width
      jest.mock('@utils/responsiveQueryAdapter', () => ({
        responsiveQueryAdapter: jest
          .fn()
          .mockImplementation(
            ({ responsiveQuery }) => responsiveQuery.base || ''
          ),
        containerQueryAdapter: jest.fn().mockImplementation(() => '')
      }));

      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: true,
        services: mockServices,
        shouldHideDescription: true // Force hide description for mobile
      });

      // Check that the description text is not in the document
      expect(screen.queryByText('Test description')).not.toBeInTheDocument();
    });

    it('shows description on desktop when shouldDisplayCareTypes is true and shouldHideDescription is false', () => {
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query.includes('min-width'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
      }));

      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: true,
        services: mockServices,
        shouldHideDescription: false
      });

      expect(screen.getByText('Test description')).toBeInTheDocument();
    });

    it('hides description on desktop when shouldDisplayCareTypes is true and shouldHideDescription is true', () => {
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query.includes('min-width'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
      }));

      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: true,
        services: mockServices,
        shouldHideDescription: true
      });

      // Check that the description text is not in the document
      expect(screen.queryByText('Test description')).not.toBeInTheDocument();
    });

    it('shows description when services array is empty even if shouldDisplayCareTypes is true', () => {
      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: true,
        services: []
      });

      expect(screen.getByText('Test description')).toBeInTheDocument();
    });

    it('shows description when services have no category names even if shouldDisplayCareTypes is true', () => {
      const servicesWithoutNames = [
        { category: { description: 'No name service' } }
      ];

      renderComponent({
        description: 'Test description',
        shouldDisplayCareTypes: true,
        services: servicesWithoutNames
      });

      expect(screen.getByText('Test description')).toBeInTheDocument();
    });

    it('hides description when shouldHideDescription is true', () => {
      renderComponent({
        description: 'Test description',
        shouldHideDescription: true
      });

      // Check that the description text is not in the document
      expect(screen.queryByText('Test description')).not.toBeInTheDocument();
    });
  });

  describe('Care Types display', () => {
    const mockServices = [
      {
        category: {
          name: 'Assisted Living'
        }
      },
      {
        category: {
          name: 'Memory Care'
        }
      },
      {
        category: {
          name: 'Independent Living'
        }
      },
      {
        category: {
          name: 'Other Care Type'
        }
      }
    ];

    it('does not render care types when shouldDisplayCareTypes is false', () => {
      renderComponent({
        shouldDisplayCareTypes: false,
        services: mockServices
      });

      expect(
        screen.queryByRole('list', { name: 'Available care types' })
      ).not.toBeInTheDocument();
    });

    it('does not render care types when services array is empty', () => {
      renderComponent({
        shouldDisplayCareTypes: true,
        services: []
      });

      expect(
        screen.queryByRole('list', { name: 'Available care types' })
      ).not.toBeInTheDocument();
    });

    it('renders care types when shouldDisplayCareTypes is true and services are provided', () => {
      renderComponent({
        shouldDisplayCareTypes: true,
        services: mockServices
      });

      const careTypesList = screen.getByRole('list', {
        name: 'Available care types'
      });
      expect(careTypesList).toBeInTheDocument();

      const careTypes = screen.getAllByRole('listitem');
      expect(careTypes).toHaveLength(3);

      expect(careTypes[0]).toHaveTextContent('Assisted Living');
      expect(careTypes[1]).toHaveTextContent('Memory Care');
      expect(careTypes[2]).toHaveTextContent('Independent Living');
    });

    it('renders care types with non-featured types when not enough featured types', () => {
      const limitedServices = [
        {
          category: {
            name: 'Assisted Living',
            description: 'Assisted living description',
            imageURL: ''
          }
        },
        {
          category: {
            name: 'Other Care Type',
            description: 'Other care type description',
            imageURL: ''
          }
        }
      ];

      renderComponent({
        shouldDisplayCareTypes: true,
        services: limitedServices
      });

      const careTypes = screen.getAllByRole('listitem');
      expect(careTypes).toHaveLength(2);

      expect(careTypes[0]).toHaveTextContent('Assisted Living');
      expect(careTypes[1]).toHaveTextContent('Other Care Type');
    });
  });

  describe('SaveProviderButton integration', () => {
    it('renders SaveProviderButton with correct providerId', () => {
      renderComponent();
      const saveButton = screen.getByTestId('save-provider-button');

      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toHaveAttribute(
        'aria-label',
        expect.stringMatching(/Save provider|Unsave provider/)
      );
    });

    it('passes the correct provider ID to SaveProviderButton', () => {
      const customId = 'custom-provider-id-123';
      renderComponent({ id: customId });
      const saveButton = screen.getByTestId('save-provider-button');

      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toHaveAttribute(
        'aria-label',
        expect.stringMatching(/Save provider|Unsave provider/)
      );
    });

    it('ensures SaveProviderButton is visible and clickable', () => {
      renderComponent();
      const saveButton = screen.getByTestId('save-provider-button');

      expect(saveButton).toBeVisible();
      expect(saveButton).toHaveStyle({
        width: '2.125rem',
        height: '2.125rem'
      });
    });
  });
});
