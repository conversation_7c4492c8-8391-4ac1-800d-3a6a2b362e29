import { Box, Flex, LinkBox, Text } from '@chakra-ui/layout';
import { Heading } from '@chakra-ui/react';
import { Checkbox, Divider, Stack, VStack } from '@chakra-ui/react';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import { SearchResultFullWidthProps } from '@components/FacetedSearch/types';
import ActionsContainer from '@components/ProviderCard/V2/ActionsContainer';
import Card from '@components/ProviderCard/V2/Card';
import CareTypes from '@components/ProviderCard/V2/CareTypes';
import Description from '@components/ProviderCard/V2/Description';
import ImageContainer from '@components/ProviderCard/V2/ImageContainer';
import ImageSlider from '@components/ProviderCard/V2/ImageSlider';
import InfoContainer from '@components/ProviderCard/V2/InfoContainer';
import Rating from '@components/ProviderCard/V2/Rating';
import SaveProviderButton from '@components/ProviderCard/V2/SaveProviderButton';
import Summary from '@components/ProviderCard/V2/Summary';
import { getActivePromotion } from '@utils/getActivePromotion';
import { getColor } from '@utils/getColor';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';
import { formatPhoneNumber } from '@utils/strings';
import { useCallback, useContext } from 'react';
import CookieStorage from 'utils/cookieStorage';

import { useModalControls } from '~/contexts/ModalContext';
import SiteContext from '~/contexts/SiteContext';
import { formatNumberLocale } from '~/utils/number';

const ProviderCard: React.FC<SearchResultFullWidthProps> = ({
  id,
  legacyId,
  images,
  title,
  isIndexed,
  address,
  reviewCount,
  displayBadges,
  averageRating,
  description: descriptionFromProps,
  caringStars = [],
  isCaringStar,
  path,
  displayRequestInfoButton = true,
  modalId = '',
  requestInfoButtonText = 'Request Info',
  readMoreButton = 'redirect_to_provider_page',
  isHidden = false,
  requestInfoButtonColorScheme = 'secondary',
  ratingStarsColor = 'info',
  ratingStarsColorRange = '400',
  providerTitleColor = 'black',
  providerTitleColorRange = '500',
  boxShadow = 'lg',
  border,
  borderColor,
  borderColorRange,
  displayProviderPhoneNumber,
  providerPhoneNumberSource,
  phoneNumber,
  promotions = [],
  hasActivePromotion,
  careTypes,
  legacyResourceId,
  lastReviewSnippet,
  queryId = '',
  listId = '',
  isChecked = false,
  displayCompareOption,
  handleCompare,
  dontOpenInNewTab,
  promotionColorScheme,
  showVerifiedBadge = false,
  price,
  showPrice = false,
  isFirstProvider = false,
  shouldDisplayCareTypes = false,
  services = [],
  shouldHideDescription = false,
  shouldUseContainmentContext = false
}) => {
  const activePromotion = getActivePromotion(promotions || []);
  const { show: showInquiryForm } = useModalControls(modalId);
  const siteProps = useContext(SiteContext);
  const domain = siteProps.site?.path ?? '';
  const howManyStars = Math.trunc(averageRating);
  const hasBadges = (isCaringStar || caringStars?.length > 0) && displayBadges;
  const shouldRenderHelpers = displayRequestInfoButton || hasBadges;
  const shouldRenderRating = howManyStars > 0;

  const careTypesList =
    (careTypes ??
      services?.map((service) => service?.category?.name).filter(Boolean)) ||
    [];
  const shouldShowCareTypes =
    shouldDisplayCareTypes && careTypesList.length > 0;

  const openInquiryForm = useCallback(() => {
    const selectedProvider = {
      slug: path,
      id,
      services: [
        {
          legacyResourceId
        }
      ]
    };

    // Defer the heavier operations to the next frame to improve INP
    requestAnimationFrame(() => {
      if (CookieStorage.enabled) {
        CookieStorage.set('provider', JSON.stringify(selectedProvider));
      }
      showInquiryForm();
    });
  }, [path, id, legacyResourceId, showInquiryForm]);

  const getProviderPhoneNumber = () => {
    if (providerPhoneNumberSource?.field === 'providersDatabase' && phoneNumber)
      return formatPhoneNumber(1 + phoneNumber);
    if (
      providerPhoneNumberSource?.field === 'globalCatalog' &&
      providerPhoneNumberSource.providerPhoneNumber
    )
      return formatPhoneNumber(providerPhoneNumberSource.providerPhoneNumber);
  };

  return (
    <LinkBox
      className="provider-card"
      as="article"
      display={isHidden ? 'none' : ''}
      css={
        shouldUseContainmentContext
          ? `
        container-type: inline-size;
      `
          : ''
      }
    >
      <Card
        domain={domain}
        footer={
          <>
            {displayCompareOption && (
              <VStack w="full" mx={4} gap={0}>
                <Divider borderColor="gray.300" w="full" />
                <Stack
                  spacing={5}
                  direction="row"
                  justifyContent="center"
                  pt={2.5}
                  pb={3}
                  mt={0}
                >
                  <Checkbox
                    mt={0}
                    isChecked={isChecked}
                    onChange={() => handleCompare(id)}
                  >
                    Compare
                  </Checkbox>
                </Stack>
              </VStack>
            )}
          </>
        }
      >
        <ImageContainer domain={domain}>
          <Box position="absolute" top="0.5rem" right="0.5rem">
            <SaveProviderButton
              providerId={id}
              iconColor="gray.500"
              providerName={title}
              providerSlug={path}
            />
          </Box>
          <ImageSlider
            domain={domain}
            path={path}
            title={title}
            images={images}
            query={{ locationId: legacyId, queryId: queryId, listId: listId }}
            openInNewTab={!dontOpenInNewTab}
            hasBadges={hasBadges}
            activePromotion={hasActivePromotion ?? !!activePromotion}
            promotionColorScheme={promotionColorScheme ?? 'info'}
            isFirstProvider={isFirstProvider}
          />
        </ImageContainer>

        <InfoContainer>
          <Summary
            domain={domain}
            path={path}
            address={address}
            title={title}
            isIndexed={isIndexed}
            titleColor={getColor(providerTitleColor, providerTitleColorRange)}
            phoneNumber={
              displayProviderPhoneNumber ? getProviderPhoneNumber() : undefined
            }
            query={{ locationId: legacyId, queryId: queryId, listId: listId }}
            dontOpenInNewTab={dontOpenInNewTab}
            showVerifiedBadge={showVerifiedBadge}
          />

          {shouldShowCareTypes && <CareTypes careTypes={careTypesList} />}

          {shouldRenderRating && (
            <Rating
              domain={domain}
              starColor={getColor(ratingStarsColor, ratingStarsColorRange)}
              rating={averageRating}
              reviewCount={reviewCount}
            />
          )}

          {!shouldHideDescription && (
            <Box data-testid="providercard-desc-container">
              <Description
                description={descriptionFromProps}
                lastReviewSnippet={lastReviewSnippet}
                path={path}
                openInNewTab={!dontOpenInNewTab}
                legacyId={legacyId}
                queryId={queryId}
                listId={listId}
              />
            </Box>
          )}

          <Flex justify="space-between" align="center" width="full" mt={1}>
            {price > 0 ? (
              <Heading
                as="p"
                css={containerQueryAdapter({
                  properties: [
                    {
                      property: 'font-size',
                      values: {
                        base: '16px',
                        md: '20px'
                      }
                    }
                  ]
                })}
              >
                $
                <Text
                  as="span"
                  filter={showPrice ? '' : 'blur(10px)'}
                  css={containerQueryAdapter({
                    properties: [
                      {
                        property: 'font-size',
                        values: {
                          base: '16px',
                          md: '20px'
                        }
                      }
                    ]
                  })}
                >
                  {formatNumberLocale(price / 100)}
                </Text>
              </Heading>
            ) : (
              <Text color="gray.600" filter={showPrice ? '' : 'blur(10px)'}>
                Pricing not available
              </Text>
            )}
            {shouldRenderHelpers && (
              <ActionsContainer domain={domain} colSpan={{ base: 12, md: 2 }}>
                <Box className="ButtonsContainer">
                  {displayRequestInfoButton && (
                    <Button
                      data-testid="request-info-button"
                      colorScheme={requestInfoButtonColorScheme}
                      size={'sm'}
                      onClick={openInquiryForm}
                      elementType={ElementTypes.BUTTON}
                      elementAction={ElementActions.OPEN_MODAL}
                      elementName={ElementNames.INFO_REQUEST_SECTION}
                      destinationUrl={path}
                      query={{
                        locationId: legacyId,
                        queryId: queryId,
                        listId: listId
                      }}
                    >
                      {requestInfoButtonText}
                    </Button>
                  )}
                </Box>
              </ActionsContainer>
            )}
          </Flex>
        </InfoContainer>
      </Card>
    </LinkBox>
  );
};

export default ProviderCard;
