import { useMemo } from 'react';

import { MAX_VISIBLE_CARE_TYPES } from '~/constants';

import styles from './CareTypes.module.css';

interface CareTypesProps {
  careTypes: string[];
}

export const FEATURED_CARE_TYPES = [
  'Assisted Living',
  'Memory Care',
  'Nursing Homes',
  'Independent Living',
  'Continuing Care Communities',
  'Home Care',
  'Home Health Agencies',
  'Hospice'
];

const CareTypes: React.FC<CareTypesProps> = ({ careTypes }) => {
  const careTypeList = useMemo(() => {
    //Limit to 3 care types, sorted by featured priority then others
    const featured = careTypes
      .filter((type) => FEATURED_CARE_TYPES.includes(type))
      .sort(
        (a, b) =>
          FEATURED_CARE_TYPES.indexOf(a) - FEATURED_CARE_TYPES.indexOf(b)
      );

    const others = careTypes.filter(
      (type) => !FEATURED_CARE_TYPES.includes(type)
    );

    return [...featured, ...others].slice(0, MAX_VISIBLE_CARE_TYPES);
  }, [careTypes]);

  if (!careTypeList.length) {
    return null;
  }

  return (
    <div
      className={styles.container}
      role="list"
      aria-label="Available care types"
    >
      {careTypeList.map((careType) => (
        <div
          key={careType}
          className={styles.card}
          role="listitem"
          aria-label={`Care type: ${careType}`}
        >
          {careType}
        </div>
      ))}
    </div>
  );
};

export default CareTypes;
