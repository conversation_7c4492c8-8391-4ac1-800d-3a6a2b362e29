import { render, screen } from '@testing-library/react';

import ActionsContainer from './ActionsContainer';

describe('ActionsContainer', () => {
  const defaultProps = {
    domain: 'test-domain',
    colSpan: { base: 12, md: 6 },
    children: <div data-testid="test-child">Test Content</div>
  };

  it('renders children correctly', () => {
    render(<ActionsContainer {...defaultProps} />);
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('applies correct grid item props', () => {
    const { container } = render(<ActionsContainer {...defaultProps} />);
    const gridItem = container.firstChild;

    expect(gridItem).toHaveStyle({
      'min-width': '90px',
      'padding-top': '0px',
      'padding-bottom': '0px'
    });
  });

  it('renders with different colSpan values', () => {
    const props = {
      ...defaultProps,
      colSpan: { base: 6, md: 3 }
    };

    render(<ActionsContainer {...props} />);
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
  });
});
