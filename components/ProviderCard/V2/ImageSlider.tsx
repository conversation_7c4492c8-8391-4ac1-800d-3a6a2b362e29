import { IconProps } from '@chakra-ui/icons';
import { Box, Flex, Icon, Text } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import WhichImage from '@components/Image/WhichImage';
import SearchResultImage from '@components/Search/SearchResultImage';
import { useSwipe } from '@hooks/useSwipe';
import { containerQueryAdapter } from '@utils/responsiveQueryAdapter';
import { useState } from 'react';
import {
  MdOutlineArrowBackIos,
  MdOutlineArrowForwardIos
} from 'react-icons/md';

interface ImageSliderProps {
  domain: string;
  images: string[];
  title: string;
  path: string;
  openInNewTab?: boolean;
  query: {
    locationId: string;
    queryId: string;
    listId: string;
  };
  zIndex?: number;
  size?: 'small' | 'large';
  hasBadges?: boolean;
  activePromotion?: boolean;
  promotionColorScheme?: string;
  isFirstProvider: boolean;
}

type SliderProps = Omit<ImageSliderProps, 'domain'>;

const ButtonIconProps: IconProps = {
  color: 'gray.900',
  backgroundColor: 'white',
  opacity: '.75',
  _hover: { color: 'gray.700' },
  cursor: 'pointer',
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  borderRadius: '100%',
  padding: 3,
  width: 10,
  height: 10,
  fontSize: '16px',
  zIndex: 1
};
const Slider: React.FC<SliderProps> = ({
  images,
  title,
  path,
  openInNewTab,
  query,
  zIndex,
  size,
  hasBadges,
  activePromotion,
  promotionColorScheme,
  isFirstProvider = false
}) => {
  const [index, setIndex] = useState(0);
  const elementClicked = useElementClicked();

  const sliderHeight =
    size === 'small' ? '152px' : { base: '160px', md: '205px' };
  const imageHeight = size === 'small' ? 152 : 400;
  const imageWidth = size === 'small' ? 172 : 600;

  const nextSlide = () => {
    const newIndex = index === images.length - 1 ? 0 : index + 1;
    setIndex(newIndex);
  };

  const prevSlide = () => {
    const newIndex = index === 0 ? images.length - 1 : index - 1;
    setIndex(newIndex);
  };
  const { handleTouchStart, handleTouchEnd } = useSwipe({
    onSwipeLeft: prevSlide,
    onSwipeRight: nextSlide
  });

  if (images.length <= 1)
    return <SearchResultImage images={images} title={title} />;

  const buttonHeight = size === 'small' ? 6 : 9;
  const buttonPadding = size === 'small' ? 2 : 3;
  const maxImageLength = 9;

  if (images.length > maxImageLength)
    images = images.slice(0, maxImageLength + 1);

  return (
    <Box
      position="relative"
      height={sliderHeight}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      css={
        size === 'small'
          ? undefined
          : containerQueryAdapter({
              properties: [
                {
                  property: 'height',
                  values: {
                    base: '160px',
                    md: '205px'
                  }
                }
              ]
            })
      }
      zIndex={zIndex}
    >
      <Box
        key={`${title}-${index}`}
        cursor="pointer"
        height="100%"
        onClick={() => {
          elementClicked({
            element: {
              type: ElementTypes.LINK,
              action: ElementActions.INTERNAL_LINK,
              name: ElementNames.PROVIDER_CARD,
              text: title,
              color: '',
              textColor: ''
            },
            destinationUrl: path,
            query: { ...query }
          });

          if (openInNewTab) {
            window.open(path, '_blank');
          } else {
            window.location.href = path;
          }
        }}
      >
        <WhichImage
          path={images[index]}
          title={title}
          height={imageHeight}
          width={imageWidth}
          loading={isFirstProvider && index === 0 ? 'eager' : 'lazy'}
          style={{
            objectFit: 'cover',
            height: '100%',
            filter: index === maxImageLength ? 'brightness(0.5)' : 'none'
          }}
        />
        {index === maxImageLength && (
          <Button
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            variant="outline"
            color="white"
            size="sm"
            _hover={{ backgroundColor: 'white', color: 'gray.800' }}
            elementAction={ElementActions.VIEW_PROVIDER}
            elementName={ElementNames.PROVIDER_CARD}
            elementType={ElementTypes.BUTTON}
            destinationUrl={path}
            query={{
              locationId: query.locationId,
              queryId: query.queryId,
              listId: query.listId
            }}
          >
            View more photos
          </Button>
        )}
        <Box
          position="absolute"
          bottom="0"
          left="0"
          right="0"
          width="100%"
          textAlign="center"
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          background="linear-gradient(to top, rgba(0,0,0,0.3), transparent)"
        >
          <Flex direction="row">
            {images.map((_, i) => (
              <Box
                key={i}
                mx="1"
                h="6px"
                w="6px"
                borderRadius="full"
                backgroundColor="white"
                opacity={i === index ? '1' : '.50'}
                marginBottom={2}
              />
            ))}
          </Flex>
          {(hasBadges || activePromotion) && (
            <Box display="flex" width="100%">
              {hasBadges && (
                <Text
                  flex={1}
                  height="24px"
                  bg="primary.600"
                  color="white"
                  fontSize="12px"
                  fontWeight="700"
                  lineHeight="24px"
                >
                  CARING STARS WINNER
                </Text>
              )}
              {activePromotion && (
                <Text
                  flex={1}
                  height="24px"
                  bg={`${promotionColorScheme}.500`}
                  color="white"
                  fontSize="12px"
                  fontWeight="700"
                  lineHeight="24px"
                >
                  PROMOTION!
                </Text>
              )}
            </Box>
          )}
        </Box>
      </Box>
      <Icon
        {...ButtonIconProps}
        height={buttonHeight}
        width={buttonHeight}
        padding={buttonPadding}
        as={MdOutlineArrowBackIos}
        left={0}
        marginLeft={1}
        onClick={prevSlide}
      />
      <Icon
        {...ButtonIconProps}
        height={buttonHeight}
        width={buttonHeight}
        padding={buttonPadding}
        as={MdOutlineArrowForwardIos}
        right={0}
        marginRight={1}
        onClick={nextSlide}
      />
    </Box>
  );
};

const ImageSlider: React.FC<ImageSliderProps> = ({
  domain,
  path,
  title,
  images,
  query,
  zIndex = 1,
  size = 'large',
  openInNewTab = false,
  hasBadges = false,
  activePromotion,
  promotionColorScheme,
  isFirstProvider
}) => {
  switch (domain) {
    case 'caring.com':
      return (
        <Slider
          images={images}
          title={title}
          path={path}
          openInNewTab={openInNewTab}
          query={query}
          size={size}
          hasBadges={hasBadges}
          activePromotion={activePromotion}
          promotionColorScheme={promotionColorScheme}
          isFirstProvider={isFirstProvider}
        />
      );
    default:
      return <SearchResultImage images={images} title={title} />;
  }
};

export default ImageSlider;
