import { Icon, IconButton } from '@chakra-ui/react';
import useElementClicked, {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import { trackLocationListingFavorited } from '@components/Analytics/events/LocationListingFavorited';
import { trackLocationListingUnfavorited } from '@components/Analytics/events/LocationListingUnfavorited';
import { ToastStatus } from '@components/Toast/V2/constants';
import useCookieStorageValue from '@hooks/use-cookie-storage-value';
import { useToast } from '@hooks/useToast';
import { useCallback, useEffect, useState } from 'react';
import { MdFavorite, MdFavoriteBorder } from 'react-icons/md';
import CookieStorage from 'utils/cookieStorage';

import {
  MAX_SAVED_PROVIDERS,
  MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS,
  SAVED_PROVIDERS_COOKIE_NAME
} from '~/constants';
import { useSessionContext } from '~/contexts/SessionContext';

interface SaveProviderButtonProps {
  providerId: string;
  iconColor?: string;
  providerName?: string;
  providerSlug?: string;
}

const SaveProviderButton = ({
  providerId,
  iconColor = 'gray.700',
  providerName,
  providerSlug
}: SaveProviderButtonProps) => {
  const savedCookieValue = useCookieStorageValue(SAVED_PROVIDERS_COOKIE_NAME);
  const [isProviderSaved, setIsProviderSaved] = useState(false);
  const showToast = useToast();
  const elementClicked = useElementClicked();
  const session = useSessionContext();

  const handleViewSavedPage = () => {
    window.location.href = '/saved';
  };

  useEffect(() => {
    if (!CookieStorage.enabled || !savedCookieValue) return;
    try {
      const savedProvidersList = JSON.parse(savedCookieValue);
      setIsProviderSaved(savedProvidersList.includes(providerId));
    } catch (e) {
      console.error('Error getting saved providers', e);
    }
  }, [savedCookieValue, providerId]);

  const handleSaveProvider = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (!providerId || !CookieStorage.enabled) return;

      try {
        const savedProviders = CookieStorage.get(SAVED_PROVIDERS_COOKIE_NAME);
        let savedProvidersList: string[] = savedProviders
          ? JSON.parse(savedProviders)
          : [];
        const isSaved = isProviderSaved;

        if (!isSaved) {
          if (savedProvidersList.length >= MAX_SAVED_PROVIDERS) {
            showToast({
              description: 'Saved Providers Limit Reached',
              status: ToastStatus.ERROR,
              actionText: 'View Saved Providers',
              onAction: handleViewSavedPage
            });
            return;
          }
          savedProvidersList.push(providerId);
          showToast({
            description: 'Provider Successfully Saved',
            status: ToastStatus.SUCCESS,
            actionText: 'View Saved Providers',
            onAction: handleViewSavedPage
          });

          // Track favorited event
          trackLocationListingFavorited({
            session,
            providerId,
            providerName,
            providerSlug
          });
        } else {
          savedProvidersList = savedProvidersList.filter(
            (id) => id !== providerId
          );
          showToast({
            description: 'Provider Unsaved',
            status: ToastStatus.INFO,
            actionText: 'View Saved Providers',
            onAction: handleViewSavedPage
          });

          // Track unfavorited event
          trackLocationListingUnfavorited({
            session,
            providerId,
            providerName,
            providerSlug
          });
        }
        setIsProviderSaved((prevState) => !prevState);

        CookieStorage.set(
          SAVED_PROVIDERS_COOKIE_NAME,
          JSON.stringify(savedProvidersList),
          { expires: MAX_SAVED_PROVIDERS_COOKIE_EXPIRATION_DAYS }
        );

        elementClicked({
          element: {
            type: ElementTypes.BUTTON,
            action: isSaved
              ? ElementActions.UNSAVE_PROVIDER
              : ElementActions.SAVE_PROVIDER,
            name: ElementNames.SAVE_PROVIDER,
            text: isSaved ? 'Unsave Provider' : 'Save Provider',
            color: 'primary',
            textColor: 'primary'
          }
        });
      } catch (e) {
        showToast({
          description: 'Unable to complete action. Please try again.',
          status: ToastStatus.ERROR,
          actionText: 'View Saved Providers',
          onAction: handleViewSavedPage
        });
        console.error(e);
      }
    },
    [
      providerId,
      isProviderSaved,
      elementClicked,
      showToast,
      session,
      providerName,
      providerSlug
    ]
  );

  return (
    <IconButton
      icon={
        <Icon
          as={isProviderSaved ? MdFavorite : MdFavoriteBorder}
          boxSize={6}
          color={isProviderSaved ? 'red.500' : iconColor}
        />
      }
      aria-label={isProviderSaved ? 'Unsave provider' : 'Save provider'}
      onClick={handleSaveProvider}
      variant="ghost"
      w="2.125rem"
      h="2.125rem"
      minW="2.125rem"
      zIndex={1}
      isRound
      bg="white"
      data-testid="save-provider-button"
    />
  );
};

export default SaveProviderButton;
