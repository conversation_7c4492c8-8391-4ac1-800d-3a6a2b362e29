import { Box, Divider, Text } from '@chakra-ui/react';
import Container from '@components/LayoutStructure/Container';
import isEmpty from 'lodash/isEmpty';
import { FaFacebook, FaLinkedinIn } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';

interface Props {
  address1?: string;
  address2?: string;
  phoneNumber?: string;
  facebookLink?: string;
  linkedInLink?: string;
  xLink?: string;
  newMediaPhone?: string;
  newsMediaEmail?: string;
}
interface ContactInfoSectionProps {
  title?: string;
  children: React.ReactElement;
}

const iconsStyle = { height: '24px', width: '24px' };

const ContactInfoSection = ({ title, children }: ContactInfoSectionProps) => {
  return (
    <Box>
      {!isEmpty(title) && (
        <Text as="h3" fontSize="xl" fontWeight={700} pb={2.5} lineHeight="24px">
          {title}
        </Text>
      )}
      {children}
      <Divider my={2} />
    </Box>
  );
};

const ContactInfo = ({
  address1,
  address2,
  phoneNumber,
  facebookLink,
  linkedInLink,
  xLink,
  newMediaPhone,
  newsMediaEmail
}: Props) => {
  return (
    <Container>
      {(address1 || address2 || phoneNumber) && (
        <ContactInfoSection
          title={address1 || address2 ? 'Mailing Address' : 'Phone Number'}
        >
          <Box fontSize="lg" lineHeight="27px">
            {address1 && <Text>{address1}</Text>}
            {address2 && <Text>{address2}</Text>}
            {phoneNumber && (
              <Text fontWeight={700} color="primary.700">
                {phoneNumber}
              </Text>
            )}
          </Box>
        </ContactInfoSection>
      )}
      {(facebookLink || linkedInLink || xLink) && (
        <ContactInfoSection title="Follow Us">
          <Box display="flex" flexDirection="row" color="primary.700" gap={4}>
            {facebookLink && (
              <a href={facebookLink} target="_blank">
                <FaFacebook style={iconsStyle} />
              </a>
            )}
            {linkedInLink && (
              <a href={linkedInLink} target="_blank">
                <FaLinkedinIn style={iconsStyle} />
              </a>
            )}
            {xLink && (
              <a href={xLink} target="_blank">
                <FaXTwitter style={iconsStyle} />
              </a>
            )}
          </Box>
        </ContactInfoSection>
      )}
      {(newMediaPhone || newsMediaEmail) && (
        <ContactInfoSection title="News Media and Journalist Contact">
          <Box fontSize="lg" lineHeight="27px">
            {newMediaPhone && (
              <Text>
                Phone:{' '}
                <Text as="span" fontWeight={700} color="primary.700">
                  {newMediaPhone}
                </Text>
              </Text>
            )}
            {newsMediaEmail && (
              <Text>
                Email:{' '}
                <Text as="span" fontWeight={700} color="primary.700">
                  {newsMediaEmail}
                </Text>
              </Text>
            )}
          </Box>
        </ContactInfoSection>
      )}
    </Container>
  );
};
export default ContactInfo;
