import { render, screen } from '@testing-library/react';

import ContactInfo from './ContactInfo';

const mockData = {
  address1: 'PostBox Address',
  address2: 'Address',
  phoneNumber: '(999)999-9999'
};

describe('Contactinfo', () => {
  it('Render witouth violation', () => {
    render(<ContactInfo {...mockData} />);

    expect(screen.getByText(mockData.address1)).toBeInTheDocument();
    expect(screen.getByText(mockData.address2)).toBeInTheDocument();
    expect(screen.getByText(mockData.phoneNumber)).toBeInTheDocument();
    expect(screen.queryByText(/Follow Us/i)).not.toBeInTheDocument();
    expect(
      screen.queryByText(/News Media and Journalist Contact/i)
    ).not.toBeInTheDocument();
  });
  it('Render social media links', () => {
    mockData.facebookLink = 'www.facebook.com';

    render(<ContactInfo {...mockData} />);

    expect(screen.getByText(mockData.address1)).toBeInTheDocument();
    expect(screen.getByText(mockData.address2)).toBeInTheDocument();
    expect(screen.getByText(mockData.phoneNumber)).toBeInTheDocument();
    expect(screen.queryByText(/Follow Us/i)).toBeInTheDocument();
    expect(
      screen.queryByText(/News Media and Journalist Contact/i)
    ).not.toBeInTheDocument();
  });
  it('Render media and journalist links', () => {
    mockData.facebookLink = undefined;
    mockData.newMediaPhone = '************';

    render(<ContactInfo {...mockData} />);

    expect(screen.getByText(mockData.address1)).toBeInTheDocument();
    expect(screen.getByText(mockData.address2)).toBeInTheDocument();
    expect(screen.getByText(mockData.phoneNumber)).toBeInTheDocument();
    expect(
      screen.queryByText(/News Media and Journalist Contact/i)
    ).toBeInTheDocument();
    expect(screen.queryByText(/Follow Us/i)).not.toBeInTheDocument();
  });
});
