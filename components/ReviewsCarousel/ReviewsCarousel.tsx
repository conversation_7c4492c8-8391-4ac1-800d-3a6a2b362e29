import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';

import { Box, Heading, Icon, Text } from '@chakra-ui/react';
import Container from '@components/LayoutStructure/Container';
import ReviewStars from '@components/ReviewStars';
import styled from '@emotion/styled';
import {
  MdOutlineArrowBackIos,
  MdOutlineArrowForwardIos
} from 'react-icons/md';
import SlickSlider from 'react-slick';

import { Metadata } from '~/types/Magnolia';

interface NodeProps extends Metadata {
  author: string;
  review: string;
  stars: number;
  title: string;
}

interface ReviewsProps extends Metadata {
  [nodeName: string]: string | Array<string> | NodeProps | undefined;
}

interface Props {
  title?: string;
  body?: string;
  reviews?: ReviewsProps;
  singleColumn?: boolean;
  textAlignment?: 'left' | 'center' | 'right';
  mobileTextAligment?: 'left' | 'center' | 'right';
}

interface ReviewCardProps {
  title: string;
  stars: number;
  review: string;
  author: string;
  singleColumn: boolean;
}

const DEFAULT_TITLE = '350,000+ Authentic Reviews';
const DEFAULT_BODY =
  'We publish firsthand reviews that are positive, negative and in-between that meet our guidelines — because it’s important that you can trust the resources we provide as you decide about a senior’s new home or caregiver.';

interface ArrowProps {
  className?: string;
  onClick?: () => void;
}

const DotsWrapper = styled(Box)`
  .slick-dots li button::before {
    font-size: 0.5rem;
    color: var(--chakra-colors-gray-600);
  }
`;

const NextArrow = ({ className, onClick }: ArrowProps) => {
  return (
    <Box className={'slick-arrow-provider'} onClick={onClick}>
      <Icon
        className={className}
        color={'black'}
        backgroundColor={'white'}
        right={-5}
        padding={3}
        _hover={{ backgroundColor: 'white', color: 'gray.800' }}
        borderRadius={100}
        height={10}
        width={10}
        as={MdOutlineArrowForwardIos}
        boxShadow={'lg'}
      />
    </Box>
  );
};

const PrevArrow = ({ className, onClick }: ArrowProps) => {
  return (
    <Box className={'slick-arrow-provider'} onClick={onClick}>
      <Icon
        className={className}
        color="black"
        backgroundColor="white"
        left={-5}
        zIndex={1}
        padding={3}
        _hover={{ backgroundColor: 'white', color: 'gray.800' }}
        borderRadius={100}
        height={10}
        width={10}
        as={MdOutlineArrowBackIos}
        boxShadow={'lg'}
      />
    </Box>
  );
};
const ReviewCards = ({
  title,
  stars,
  review,
  author,
  singleColumn
}: ReviewCardProps) => {
  return (
    <Box
      display={'flex'}
      flexDirection={'column'}
      alignItems={'center'}
      gap={'16px'}
      paddingY={'24px'}
      paddingX={'34px'}
      borderRadius={'12px'}
    >
      <ReviewStars rating={stars.toString()} color={'info.500'} size={'22px'} />
      <Text
        color={'primary.900'}
        fontSize={'3xl'}
        textAlign={'center'}
        fontWeight={700}
      >
        {title}
      </Text>
      <Box
        color={'gray.700'}
        fontSize="sm"
        fontWeight="400"
        textAlign={'center'}
        className="magnolia-text"
        overflow={'auto'}
        maxHeight={singleColumn ? '126px' : { base: '126px', md: '105px' }}
        dangerouslySetInnerHTML={{ __html: review }}
      />
      <Text
        fontWeight={400}
        fontSize={'sm'}
        textAlign={'center'}
        textColor={'primary.700'}
      >
        - {author}
      </Text>
    </Box>
  );
};

const ReviewsCarousel = ({
  title = DEFAULT_TITLE,
  body = DEFAULT_BODY,
  reviews,
  singleColumn = false,
  textAlignment = 'left',
  mobileTextAligment = 'center'
}: Props) => {
  const reviewsList =
    reviews && reviews['@nodes']
      ? reviews['@nodes'].map((node) => reviews[node])
      : [];

  const settings = {
    dots: true,
    infinite: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    style: { zIndex: 1, height: '100%' }
  };
  return (
    <Box backgroundColor={'primary.900'}>
      <Container
        paddingY={'48px'}
        display={'flex'}
        flexDirection={singleColumn ? 'column' : { base: 'column', md: 'row' }}
        alignItems={'center'}
        justifyContent={'space-evenly'}
        gap={{ base: 8, md: 0 }}
      >
        <Box
          marginY={{ base: 0, md: 12 }}
          display={'flex'}
          flexDirection={'column'}
          gap={'28px'}
        >
          <Heading
            color={'white'}
            textAlign={
              singleColumn
                ? mobileTextAligment
                : { base: mobileTextAligment, md: textAlignment }
            }
          >
            {title}
          </Heading>
          <Text
            color={'white'}
            textAlign={
              singleColumn
                ? mobileTextAligment
                : { base: mobileTextAligment, md: textAlignment }
            }
          >
            {body}
          </Text>
        </Box>
        <Box
          paddingX={{ base: 0, md: '20px' }}
          width={singleColumn ? '100%' : { base: '100%', md: '432px' }}
          minWidth={singleColumn ? 'auto' : { base: 'auto', md: '432px' }}
        >
          <Box backgroundColor={'white'} borderRadius={'12px'}>
            <DotsWrapper>
              <SlickSlider {...settings}>
                {reviewsList.map((reviewRaw) => {
                  const { title, stars, review, author } =
                    reviewRaw as NodeProps;

                  return (
                    <ReviewCards
                      key={title}
                      title={title}
                      stars={stars}
                      review={review}
                      author={author}
                      singleColumn={singleColumn}
                    />
                  );
                })}
              </SlickSlider>
            </DotsWrapper>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};
export default ReviewsCarousel;
