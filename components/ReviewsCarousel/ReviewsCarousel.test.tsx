import { render, screen } from '@testing-library/react';

import ReviewsCarousel from './ReviewsCarousel';

const mockData = {
  title: 'Some title for the body',
  body: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis vel suscipit nunc.',
  reviews: {
    '@name': 'reviews',
    '@path': '/reviews',
    '@id': '0',
    '@nodeType': '',
    'mgnl:lastModified': '',
    'mgnl:created': '',
    'mgnl:template': '',
    '@nodes': ['rev1'],
    rev1: {
      '@name': 'rev1',
      '@path': '/rev1',
      '@id': '0',
      '@nodeType': '',
      'mgnl:lastModified': '',
      'mgnl:created': '',
      '@nodes': [],
      'mgnl:template': '',
      title: 'Some Card Title',
      stars: 3,
      review: 'Some cool review',
      author: 'Tester McTest'
    }
  },
  singleColumn: false
};

describe('ReviewsCarousel', () => {
  it('renders without violation', async () => {
    const { container } = render(<ReviewsCarousel {...mockData} />);

    expect(screen.getByText(mockData.title)).toBeVisible();
    expect(screen.getByText(mockData.body)).toBeVisible();
    expect(screen.getByText(mockData.reviews.rev1.title)).toBeVisible();
    expect(screen.getByText(mockData.reviews.rev1.review)).toBeVisible();
  });
});
