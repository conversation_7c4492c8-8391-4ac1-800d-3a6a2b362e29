import Script from 'next/script';

function GTMScript() {
  return `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','${process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID}');`;
}

function GTMNoScript() {
  return `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID}" height="0" width="0" style="display:none;visibility:hidden"></iframe>`;
}

function AnalyticsConfig() {
  return `window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_TAG_MEASUREMENT_ID}', {
    page_path: window.location.pathname,
  });`;
}

const GTM = (): React.ReactElement => {
  return (
    <>
      <Script
        id="gtagInstallation"
        strategy="lazyOnload"
        type="application/javascript"
        dangerouslySetInnerHTML={{ __html: GTMScript() }}
        key="website-gtaginstallation"
      />
      <Script
        id="analyticsConfig"
        strategy="lazyOnload"
        type="application/javascript"
        dangerouslySetInnerHTML={{ __html: AnalyticsConfig() }}
        key="website-analyticsConfig"
      />
      <noscript
        dangerouslySetInnerHTML={{ __html: GTMNoScript() }}
        key="website-gtm-no-script"
      />
    </>
  );
};

export default GTM;
