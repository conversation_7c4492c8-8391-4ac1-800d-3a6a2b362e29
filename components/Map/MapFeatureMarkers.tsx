import { useQueryMapFeature } from '@hooks/use-query-map-feature';
import { memo } from 'react';

import { MapFeature } from '~/types/MapFeature';

import MapCustomMarker from './MapCustomMarker';

type Props = {
  feature: MapFeature;
  lat: number;
  lng: number;
};

const MapFeatureMarkers: React.FC<Props> = ({ feature, lat, lng }) => {
  const { featureId, keywords } = feature;

  const { data: places } = useQueryMapFeature({
    featureId,
    keywords,
    lat,
    lng
  });

  if (!Array.isArray(places)) return null;

  return (
    <>
      {places.map((place, index) => (
        <MapCustomMarker
          key={`${place.reference}-${index}`}
          lat={place.geometry.location.lat}
          lng={place.geometry.location.lng}
          pinColor={feature.pinColor}
          icon={feature.icon}
        />
      ))}
    </>
  );
};

export default memo(MapFeatureMarkers);
