import { STRING_TO_ICON_CLASS } from '@components/RenderIcon';
import { memo } from 'react';

type Props = {
  lat: number;
  lng: number;
  pinColor: string;
  icon: string;
};

const MapCustomMarker: React.FC<Props> = ({ lat, lng, pinColor, icon }) => {
  const IconClass = STRING_TO_ICON_CLASS[icon];
  const iconComponentStyled = (
    <IconClass style={{ color: '#fff', width: '20px', height: '20px' }} />
  );

  return (
    <div style={{ cursor: 'pointer', transform: 'translate(-50%, -100%)' }}>
      <div
        style={{
          backgroundColor: `var(--chakra-colors-${pinColor}-500)`,
          borderRadius: '50% 50% 50% 0',
          transform: 'rotate(-45deg)',
          width: '32px',
          height: '32px'
        }}
      />
      <div
        style={{
          position: 'absolute',
          top: '5px',
          left: '50%',
          transform: 'translateX(-50%)'
        }}
      >
        {iconComponentStyled}
      </div>
    </div>
  );
};

export default memo(MapCustomMarker);
