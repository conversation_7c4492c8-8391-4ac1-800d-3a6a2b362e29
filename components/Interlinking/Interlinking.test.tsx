import { render, screen } from '@utils/test-utils';
import capitalize from 'lodash/capitalize';

import axe from '~/axe-helper';

import InterlinkingCard from './Interlinking';

const mockInterlinking = {
  title: 'Assisted Living',
  bgColor: 'gray',
  titleColorRange: '700',
  titleColor: 'Primary',
  columns: 2,
  linksColor: 'link',
  type: 'state',
  linksColorRange: '600',
  linksTextDecoration: 'none',
  data: [
    {
      careType: 'memory-care',
      state: 'arizona',
      urlPath: '/arizona/memory-care'
    }
  ]
};

describe('InterlinkingCard', () => {
  beforeEach(() => {
    const mockAnalytics = {
      track: jest.fn()
    };
    window.tracking = mockAnalytics;
  });
  it('renders the card without violations', async () => {
    const { container } = render(
      <InterlinkingCard>
        <InterlinkingCard.Container bgColor={mockInterlinking.bgColor}>
          <InterlinkingCard.Heading
            titleSize={'sm'}
            titleAlignment="left"
            headingElement="h3"
            titleColor={mockInterlinking.titleColor}
            titleColorRange={mockInterlinking.titleColorRange}
            title={mockInterlinking.title}
          />
          <InterlinkingCard.ListContainer
            align="stretch"
            borderRadius={12}
            backgroundColor="transparent"
          >
            <InterlinkingCard.LinkGrid columns={mockInterlinking.columns}>
              <InterlinkingCard.LinksList
                data={mockInterlinking.data}
                type={mockInterlinking.type}
                linksColor={mockInterlinking.linksColor}
                linksColorRange={mockInterlinking.linksColorRange}
                linksTextDecoration={mockInterlinking.linksTextDecoration}
              />
            </InterlinkingCard.LinkGrid>
          </InterlinkingCard.ListContainer>
        </InterlinkingCard.Container>
      </InterlinkingCard>
    );

    expect(screen.getByText(mockInterlinking.title)).toBeVisible();
    expect(
      screen.getByText(capitalize(mockInterlinking.data[0].state))
    ).toBeVisible();
    expect(await axe(container)).toHaveNoViolations();
  });
});
