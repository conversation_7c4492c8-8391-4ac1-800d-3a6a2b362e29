import {
  Box,
  Link,
  SimpleGridProps,
  StackProps,
  VStack
} from '@chakra-ui/layout';
import {
  ElementActions,
  ElementNames,
  ElementTypes
} from '@components/Analytics/events/ElementClicked';
import Button from '@components/Button';
import Heading from '@components/Heading';
import { HeadingProps } from '@components/Heading/Heading';
import { CatalogLink } from '@components/NearbyCare/types';
import { DEFAULT_BUTTON_ALIGNMENT } from '@utils/buttons';
import { getColor } from '@utils/getColor';
import { generateLinkText } from '@utils/interlinking';
import { useContext } from 'react';

import { HeadingElements, HeadingSizes } from '~/@types/heading';
import SiteContext from '~/contexts/SiteContext';

import {
  StyledInterlinkingContainerLinks,
  StyledInterlinkingListLinks,
  StyledInterlinkingModuleLinks
} from './InterlinkingStyles';

export type InterlinkingProps = {
  title: string;
  titleSize: HeadingSizes;
  titleAlignment: 'left' | 'center' | 'right';
  headingElement: HeadingElements | 'h2';
  columns: string;
  bgColor: string;
  bgColorRange: string;
  titleColor: string;
  titleColorRange: string;
  linksColor: string;
  linksColorRange: string;
  linksTextDecoration: string;
  hideOnMobile?: boolean;
};

interface InterlinkingCardProps extends StackProps {
  padding?: any;
  align?: any;
  borderRadius?: any;
  backgroundColor?: string;
  children?: React.ReactNode;
}

interface CompoundComponents {
  Heading: typeof InterlinkingHeading;
  LinkGrid: typeof LinkGrid;
  LinksList: typeof LinksList;
  ApiLinkList: typeof ApiLinkList;
  Container: typeof InterlinkingContainer;
  Button: typeof InterlinkingButton;
  ListContainer: typeof ListContainer;
}

const InterlinkingCard: React.FC<InterlinkingCardProps> &
  CompoundComponents = ({
  align = 'stretch',
  borderRadius = 12,
  backgroundColor = 'transparent',
  children,
  ...rest
}) => {
  return (
    <VStack
      align={align}
      borderRadius={borderRadius}
      backgroundColor={backgroundColor}
      flex={{ md: '1 1 0px', base: '1 1 100%' }}
      {...rest}
    >
      {children}
    </VStack>
  );
};

interface InterlinkingHeadingProps extends HeadingProps {
  title: string;
  titleColor: string;
  titleColorRange: string;
  titleSize?: HeadingSizes;
}

const InterlinkingHeading: React.FC<InterlinkingHeadingProps> = ({
  title,
  titleColor,
  titleColorRange,
  titleSize = 'md',
  headingElement = 'h2',
  titleAlignment = 'left',
  ...rest
}) => {
  return (
    <Heading
      title={title}
      lineHeight={1}
      fontWeight={700}
      fontSize={titleSize}
      withContainer={false}
      titleAlignment={titleAlignment}
      headingElement={headingElement}
      color={getColor(titleColor, titleColorRange)}
      {...rest}
    />
  );
};

const buildLinks = (type, catalogLinks) => {
  const links: Array<CatalogLink> = [];
  if (catalogLinks) {
    catalogLinks.forEach((link) => {
      links.push({
        text: generateLinkText(type, link),
        href: `${link.urlPath}`
      });
    });
  }
  return links;
};

interface LinksProps {
  data: any;
  limit?: number;
  type: string;
  linksColor: string;
  linksColorRange: string;
  linksTextDecoration: string;
}

const LinksList: React.FC<LinksProps> = ({
  data,
  limit = 0,
  type,
  linksColor,
  linksColorRange,
  linksTextDecoration
}) => {
  let catalogLinks: CatalogLink[] = [];

  if (data && data.length > 0) {
    catalogLinks = buildLinks(type, data);
  }
  return (
    <>
      {catalogLinks.map(({ text, href }, index) => {
        if (limit > 0 && index >= limit) return;
        return (
          <Box key={`${text}-${index}`}>
            <Link
              textDecoration={linksTextDecoration}
              color={getColor(linksColor, linksColorRange)}
              href={href}
            >
              {text}
            </Link>
          </Box>
        );
      })}
    </>
  );
};

interface ApiLinksProps {
  data: any;
  linksColor: string;
  linksColorRange: string;
  linksTextDecoration: string;
}

const ApiLinkList: React.FC<ApiLinksProps> = ({
  data,
  linksColor,
  linksColorRange,
  linksTextDecoration
}) => {
  return (
    <>
      {data.map(({ name, link, display = 'block' }, index) => {
        return (
          <Box key={`${name}-${index}`} style={{ display: display }}>
            <Link
              textDecoration={linksTextDecoration}
              color={getColor(linksColor, linksColorRange)}
              href={link}
            >
              {name}
            </Link>
          </Box>
        );
      })}
    </>
  );
};

interface LinkGridProps extends SimpleGridProps {
  children: React.ReactNode;
}

const LinkGrid: React.FC<LinkGridProps> = ({ columns, children }) => {
  const siteProps = useContext(SiteContext);

  const domain = siteProps.site?.path ?? '';

  const StyledLinks = StyledInterlinkingModuleLinks(domain);

  return <StyledLinks columns={columns}>{children}</StyledLinks>;
};

interface InterlinkingContainerProps {
  children: React.ReactNode;
  bgColor?: string;
}

const InterlinkingContainer: React.FC<InterlinkingContainerProps> = ({
  children,
  bgColor = ''
}) => {
  const siteProps = useContext(SiteContext);

  const domain = siteProps.site?.path ?? '';

  const StyledContainer = StyledInterlinkingContainerLinks(domain, bgColor);

  return <StyledContainer>{children}</StyledContainer>;
};

interface InterlinkingButtonProps {
  label?: string;
  bgColor: string;
  textColor?: string;
  buttonState?: 'solid' | 'outline' | 'ghost' | 'link';
  onClick: () => void;
}

const InterlinkingButton: React.FC<InterlinkingButtonProps> = ({
  label = '',
  bgColor,
  textColor,
  buttonState = 'ghost',
  onClick = () => {}
}) => (
  <Box paddingTop={10} display="flex" justifyContent={DEFAULT_BUTTON_ALIGNMENT}>
    <Button
      colorScheme={bgColor}
      variant={buttonState}
      width={{ base: 'full', sm: 'auto' }}
      onClick={onClick}
      elementAction={ElementActions.INTERNAL_LINK}
      elementName={ElementNames.GENERIC_BUTTON}
      elementType={ElementTypes.BUTTON}
    >
      {label}
    </Button>
  </Box>
);

interface ListContainerProps {
  align?: any;
  borderRadius?: any;
  backgroundColor?: string;
  children?: React.ReactNode;
}

const ListContainer: React.FC<ListContainerProps> = ({
  align = 'stretch',
  borderRadius = 12,
  backgroundColor = 'transparent',
  children
}) => {
  const siteProps = useContext(SiteContext);

  const domain = siteProps.site?.path ?? '';

  const StyledListContainer = StyledInterlinkingListLinks(domain);

  return (
    <VStack
      align={align}
      borderRadius={borderRadius}
      backgroundColor={backgroundColor}
      flex={{ md: '1 1 0px', base: '1 1 100%' }}
    >
      <StyledListContainer>{children}</StyledListContainer>
    </VStack>
  );
};

InterlinkingCard.LinkGrid = LinkGrid;
InterlinkingCard.LinksList = LinksList;
InterlinkingCard.ApiLinkList = ApiLinkList;
InterlinkingCard.Button = InterlinkingButton;
InterlinkingCard.ListContainer = ListContainer;
InterlinkingCard.Heading = InterlinkingHeading;
InterlinkingCard.Container = InterlinkingContainer;

export default InterlinkingCard;
