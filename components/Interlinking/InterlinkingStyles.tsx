import { GridProps, SimpleGrid } from '@chakra-ui/layout';
import { chakra, ChakraStyledOptions } from '@chakra-ui/react';

const SiteStyles = (domain): ChakraStyledOptions => {
  const baseStyle: GridProps = {
    gap: 2
  };
  switch (domain) {
    case 'seniorhomes.com':
      return {
        baseStyle: {
          ...baseStyle
        }
      };
    case 'caring.com':
      return {
        baseStyle: {
          ...baseStyle,
          gap: 4
        }
      };
    default:
      return baseStyle;
  }
};

const ContainerStyles = (domain, bgColor): ChakraStyledOptions => {
  const baseStyle: GridProps = {
    gap: 2
  };
  switch (domain) {
    case 'seniorhomes.com':
      return {
        baseStyle: {
          ...baseStyle,
          borderRadius: 12,
          backgroundColor: bgColor,
          p: 8
        }
      };
    case 'caring.com':
      return {
        baseStyle: {
          ...baseStyle
        }
      };
    default:
      return baseStyle;
  }
};

const StyledInterlinkingModuleLinks = (domain): typeof SimpleGrid => {
  return chakra(SimpleGrid, { ...SiteStyles(domain) });
};

const StyledInterlinkingContainerLinks = (
  domain,
  bgColor
): typeof SimpleGrid => {
  return chakra(SimpleGrid, { ...ContainerStyles(domain, bgColor) });
};

const ListContainerStyles = (domain): ChakraStyledOptions => {
  const baseStyle: GridProps = {
    p: 0
  };
  switch (domain) {
    case 'seniorhomes.com':
      return {
        baseStyle: {
          ...baseStyle
        }
      };
    case 'caring.com':
      return {
        baseStyle: {
          ...baseStyle,
          p: 8
        }
      };
    default:
      return baseStyle;
  }
};

const StyledInterlinkingListLinks = (domain): typeof SimpleGrid => {
  return chakra(SimpleGrid, { ...ListContainerStyles(domain) });
};

export {
  StyledInterlinkingContainerLinks,
  StyledInterlinkingListLinks,
  StyledInterlinkingModuleLinks
};
